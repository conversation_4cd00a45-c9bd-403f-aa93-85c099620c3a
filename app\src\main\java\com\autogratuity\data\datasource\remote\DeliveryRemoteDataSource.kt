package com.autogratuity.data.datasource.remote

import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.util_kt.documentSnapshotToDeliveryDtoOnly
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.SetOptions
import com.google.firebase.firestore.Transaction
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.time.OffsetDateTime
import java.util.Date
import javax.inject.Inject
import com.autogratuity.data.model.generated_kt.Delivery as DeliveryDto

/**
 * Interface for remote data operations related to Deliveries.
 * All methods operate with DeliveryDto objects.
 */
interface DeliveryRemoteDataSource {
    suspend fun getDeliveryById(userId: String, deliveryId: String): Result<DeliveryDto?>
    suspend fun getDeliveries(userId: String, limit: Int, startAfter: DocumentSnapshot?): Result<List<DeliveryDto>>
    suspend fun getAllDeliveries(userId: String): Result<List<DeliveryDto>>
    suspend fun saveDelivery(userId: String, delivery: DeliveryDto): Result<String>
    suspend fun updateDelivery(userId: String, deliveryId: String, deliveryData: DeliveryDto.DeliveryData): Result<Unit>
    suspend fun updateDeliveryFields(userId: String, deliveryId: String, fields: Map<String, Any>): Result<Unit>
    suspend fun deleteDelivery(userId: String, deliveryId: String): Result<Unit>

    fun observeDeliveryById(userId: String, deliveryId: String): Flow<DeliveryDto?>
    fun observeAllDeliveries(userId: String): Flow<List<DeliveryDto>>
    fun observeDeliveriesByTimeRange(userId: String, startDate: Date, endDate: Date): Flow<List<DeliveryDto>>
    fun observeDeliveriesByTippedStatus(userId: String, isTipped: Boolean): Flow<List<DeliveryDto>>
    fun observeCompletedAndUntippedDeliveries(userId: String): Flow<List<DeliveryDto>>

    // Advanced query methods
    suspend fun findDeliveriesByField(userId: String, fieldName: String, value: Any, limit: Int): Result<List<DeliveryDto>>
    suspend fun findDeliveriesByCriteria(userId: String, limit: Int, orderByField: String, descending: Boolean, filterCriteria: Map<String, Any>): Result<List<DeliveryDto>>
    suspend fun getOldestDeliveryTimestamp(userId: String): Result<OffsetDateTime?>
    fun observeImportedVerifiedDeliveriesForDndCheck(userId: String, importedBeforeDate: Date): Flow<List<DeliveryDto>>

    // Transaction support
    fun addDeliveryToTransaction(transaction: Transaction, userId: String, deliveryData: DeliveryDto.DeliveryData): DocumentReference

    // Data normalization for legacy documents
    suspend fun normalizeDeliveryMetadata(userId: String, deliveryId: String): Result<Unit>
    suspend fun batchNormalizeUserDeliveries(userId: String): Result<Int>

    // Reference helpers
    fun getDeliveryReference(userId: String, deliveryId: String): DocumentReference
    fun getUserDeliveriesCollection(userId: String): com.google.firebase.firestore.CollectionReference
}

@ExperimentalCoroutinesApi
class DeliveryRemoteDataSourceImpl @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val ioDispatcher: CoroutineDispatcher
) : DeliveryRemoteDataSource {

    private val TAG = "DeliveryRemoteDataSourceImpl"

    companion object {
        private const val USERS_COLLECTION = "users"
        private const val USER_DELIVERIES_SUBCOLLECTION = "user_deliveries"
        private const val FIELD_DELIVERY_DATA_ROOT = "deliveryData"
        private const val FIELD_METADATA_CREATED_AT = "deliveryData.metadata.createdAt"
        private const val FIELD_METADATA_UPDATED_AT = "deliveryData.metadata.updatedAt"
        private const val FIELD_STATUS_IS_TIPPED = "deliveryData.status.isTipped"
        private const val FIELD_STATUS_IS_COMPLETED = "deliveryData.status.isCompleted"
        private const val FIELD_STATUS_VERIFIED = "deliveryData.status.isVerified"
        private const val FIELD_METADATA_SOURCE = "deliveryData.metadata.source"
    }

    override fun getUserDeliveriesCollection(userId: String): com.google.firebase.firestore.CollectionReference {
        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for getUserDeliveriesCollection.")
        }
        return firestore.collection(USERS_COLLECTION).document(userId)
            .collection(USER_DELIVERIES_SUBCOLLECTION)
    }

    override fun getDeliveryReference(userId: String, deliveryId: String): DocumentReference {
        if (userId.isBlank() || deliveryId.isBlank()) {
            throw IllegalArgumentException("User ID and Delivery ID cannot be blank for getDeliveryReference.")
        }
        return getUserDeliveriesCollection(userId).document(deliveryId)
    }

    private fun DocumentSnapshot.toDeliveryDto(): DeliveryDto? {
        return if (exists()) {
            try {
                documentSnapshotToDeliveryDtoOnly(this)
            } catch (e: Exception) {
                Log.e(TAG, "Error converting document ${this.id} to DeliveryDto", e)
                null
            }
        } else {
            null
        }
    }

    override suspend fun getDeliveryById(userId: String, deliveryId: String): Result<DeliveryDto?> = withContext(ioDispatcher) {
        if (userId.isBlank() || deliveryId.isBlank()) {
            Log.w(TAG, "Cannot fetch delivery: userId or deliveryId is blank")
            return@withContext Result.Error(IllegalArgumentException("User ID and Delivery ID cannot be blank"))
        }
        
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$USERS_COLLECTION/$userId/$USER_DELIVERIES_SUBCOLLECTION/$deliveryId"
        
        try {
            Log.d(TAG, "getDeliveryById: Fetching delivery $deliveryId for user $userId from path: $fullPath")
            
            val deliveryRef = getDeliveryReference(userId, deliveryId)
            val snapshot = deliveryRef.get().await()
            val firestoreDuration = firestoreStartTime.elapsedNow()
            val documentExists = snapshot.exists()
            val dataSize = if (documentExists) snapshot.data?.toString()?.length ?: 0 else 0
            val deliveryDto = snapshot.toDeliveryDto()
            
            // 🚨 COMPREHENSIVE FIRESTORE READ MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = USER_DELIVERIES_SUBCOLLECTION,
                documentId = deliveryId,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                queryType = "GET_DOCUMENT",
                queryFilters = listOf("delivery_by_id"),
                resultCount = if (documentExists && deliveryDto != null) 1 else 0,
                userId = userId,
                cacheSource = "SERVER"
            )
            
            // 🔍 SESSION CORRELATION: Add to current session
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("delivery_fetch:$deliveryId")
            
            if (documentExists && deliveryDto != null) {
                // 📊 LOG RAW FIRESTORE DOCUMENT CONTENT
                Log.d(TAG, "getDeliveryById: Successfully fetched delivery for user $userId")
                Log.d(TAG, "  Firestore Path: $fullPath")
                Log.d(TAG, "  Document Size: $dataSize bytes")
                Log.d(TAG, "  Firestore Duration: ${firestoreDuration.inWholeMilliseconds}ms")
                Log.d(TAG, "  Raw Document Data: ${snapshot.data}")
                Log.d(TAG, "  Parsed DeliveryDto: $deliveryDto")
            } else {
                Log.d(TAG, "No delivery found or parsing failed for ID $deliveryId for user $userId")
            }
            
            Result.Success(deliveryDto)
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // 🚨 FIRESTORE ERROR MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = USER_DELIVERIES_SUBCOLLECTION,
                documentId = deliveryId,
                duration = firestoreDuration,
                success = false,
                error = e,
                fullPath = fullPath,
                queryType = "GET_DOCUMENT",
                queryFilters = listOf("delivery_by_id"),
                userId = userId,
                cacheSource = "ERROR"
            )
            
            Log.e(TAG, "Error fetching delivery $deliveryId for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun getDeliveries(userId: String, limit: Int, startAfter: DocumentSnapshot?): Result<List<DeliveryDto>> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            Log.w(TAG, "Cannot get deliveries: userId is blank")
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank"))
        }
        try {
            var query = getUserDeliveriesCollection(userId)
                .orderBy(FIELD_METADATA_CREATED_AT, Query.Direction.DESCENDING)
                .limit(limit.toLong())

            startAfter?.let {
                query = query.startAfter(it)
            }
            val querySnapshot = query.get().await()
            val deliveries = querySnapshot.documents.mapNotNull { it.toDeliveryDto() }
            Result.Success(deliveries)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting deliveries for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun getAllDeliveries(userId: String): Result<List<DeliveryDto>> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            Log.w(TAG, "Cannot get all deliveries: userId is blank")
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank"))
        }
        try {
            // ARCHITECTURE-COMPLIANT SOLUTION: Handle legacy documents without metadata.createdAt
            // First attempt: Query with orderBy for documents that have metadata.createdAt
            val modernDeliveries = try {
                val modernQuery = getUserDeliveriesCollection(userId)
                    .orderBy(FIELD_METADATA_CREATED_AT, Query.Direction.DESCENDING)
                    .get()
                    .await()
                Log.d(TAG, "🔍 MODERN QUERY: Found ${modernQuery.documents.size} documents with metadata.createdAt")
                val modernDtos = modernQuery.documents.mapNotNull { doc ->
                    val dto = doc.toDeliveryDto()
                    if (dto == null) {
                        Log.w(TAG, "⚠️ CONVERSION FAILED: Modern document ${doc.id} failed to convert to DTO")
                    }
                    dto
                }
                Log.d(TAG, "✅ MODERN CONVERSION: ${modernDtos.size}/${modernQuery.documents.size} documents successfully converted")
                modernDtos
            } catch (e: Exception) {
                Log.d(TAG, "Modern query failed (expected for legacy documents): ${e.message}")
                emptyList()
            }

            // Second attempt: Query without orderBy to capture legacy documents
            val legacyDeliveries = try {
                val legacyQuery = getUserDeliveriesCollection(userId)
                    .get()
                    .await()
                Log.d(TAG, "🔍 LEGACY QUERY: Found ${legacyQuery.documents.size} total documents")
                val allDocs = legacyQuery.documents.mapNotNull { doc ->
                    val dto = doc.toDeliveryDto()
                    if (dto == null) {
                        Log.w(TAG, "⚠️ CONVERSION FAILED: Legacy document ${doc.id} failed to convert to DTO")
                    }
                    dto
                }
                Log.d(TAG, "✅ LEGACY CONVERSION: ${allDocs.size}/${legacyQuery.documents.size} documents successfully converted")
                // Filter out documents already captured by modern query
                val modernIds = modernDeliveries.map { it.id }.toSet()
                val legacyOnly = allDocs.filter { it.id !in modernIds }
                Log.d(TAG, "📋 LEGACY FILTER: ${legacyOnly.size} legacy-only documents after removing ${modernIds.size} modern duplicates")
                legacyOnly
            } catch (e: Exception) {
                Log.e(TAG, "Legacy query also failed for user $userId", e)
                emptyList()
            }

            // Combine and sort results (modern first, then legacy)
            val allDeliveries = modernDeliveries + legacyDeliveries
            Log.d(TAG, "📊 FIRESTORE FETCH: Found ${allDeliveries.size} deliveries for user $userId (${modernDeliveries.size} modern, ${legacyDeliveries.size} legacy)")

            // Log delivery IDs for debugging
            allDeliveries.forEachIndexed { index, delivery ->
                Log.d(TAG, "📋 DELIVERY $index: ID=${delivery.id}, userId=${delivery.deliveryData.userId}, orderId=${delivery.deliveryData.orderId}")
            }

            Result.Success(allDeliveries)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting all deliveries for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun saveDelivery(userId: String, delivery: DeliveryDto): Result<String> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank."))
        }
        if (false) {
            return@withContext Result.Error(IllegalArgumentException("DeliveryData cannot be null for saving."))
        }

        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        
        try {
            val deliveryRef = if (delivery.id.isNotBlank()) {
                getDeliveryReference(userId, delivery.id)
            } else {
                getUserDeliveriesCollection(userId).document()
            }
            
            val fullPath = "$USERS_COLLECTION/$userId/$USER_DELIVERIES_SUBCOLLECTION/${deliveryRef.id}"
            val dataToSave = mapOf(FIELD_DELIVERY_DATA_ROOT to delivery.deliveryData)
            val dataSize = dataToSave.toString().length
            val isNewDelivery = delivery.id.isBlank()
            
            Log.d(TAG, "saveDelivery: ${if (isNewDelivery) "Creating" else "Updating"} delivery ${deliveryRef.id} for user $userId at path: $fullPath")
            
            // 📊 LOG RAW WRITE DATA
            Log.d(TAG, "saveDelivery: Writing delivery data:")
            Log.d(TAG, "  Document ID: ${deliveryRef.id}")
            Log.d(TAG, "  Data Size: $dataSize bytes")
            Log.d(TAG, "  Is New: $isNewDelivery")
            Log.d(TAG, "  Write Data: $dataToSave")
            
            deliveryRef.set(dataToSave, SetOptions.merge()).await()
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // 🚨 COMPREHENSIVE FIRESTORE WRITE MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USER_DELIVERIES_SUBCOLLECTION,
                documentId = deliveryRef.id,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                writeType = if (isNewDelivery) "SET" else "MERGE",
                fieldsUpdated = listOf("deliveryData"),
                userId = userId,
                documentData = dataToSave
            )
            
            // 🔍 SESSION CORRELATION: Add to current session
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent(
                if (isNewDelivery) "delivery_create:${deliveryRef.id}" else "delivery_save:${deliveryRef.id}"
            )
            
            Log.d(TAG, "Delivery ${deliveryRef.id} saved for user $userId in ${firestoreDuration.inWholeMilliseconds}ms.")
            Result.Success(deliveryRef.id)
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // 🚨 FIRESTORE WRITE ERROR MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USER_DELIVERIES_SUBCOLLECTION,
                documentId = delivery.id.ifBlank { "unknown" },
                duration = firestoreDuration,
                success = false,
                error = e,
                writeType = if (delivery.id.isBlank()) "SET" else "MERGE",
                userId = userId
            )
            
            Log.e(TAG, "Error saving delivery for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun updateDelivery(userId: String, deliveryId: String, deliveryData: DeliveryDto.DeliveryData): Result<Unit> = withContext(ioDispatcher) {
        if (userId.isBlank() || deliveryId.isBlank()) {
            return@withContext Result.Error(IllegalArgumentException("User ID and Delivery ID cannot be blank for update."))
        }
        try {
            val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
            val fullPath = "$USERS_COLLECTION/$userId/$USER_DELIVERIES_SUBCOLLECTION/$deliveryId"

            // 🔧 CRITICAL FIX: Use Firestore transaction to ensure cloud function triggers
            firestore.runTransaction { transaction ->
                val deliveryRef = getDeliveryReference(userId, deliveryId)
                transaction.update(deliveryRef, FIELD_DELIVERY_DATA_ROOT, deliveryData)
                Log.d(TAG, "Transaction: Updated delivery $deliveryId with new deliveryData for user $userId")
                null // Return null for successful transaction
            }.await()

            val firestoreDuration = firestoreStartTime.elapsedNow()

            // 🚨 COMPREHENSIVE FIRESTORE WRITE MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USER_DELIVERIES_SUBCOLLECTION,
                documentId = deliveryId,
                duration = firestoreDuration,
                success = true,
                fullPath = fullPath,
                writeType = "TRANSACTION_UPDATE",
                fieldsUpdated = listOf("deliveryData"),
                userId = userId
            )

            Log.d(TAG, "Delivery $deliveryId updated successfully using transaction for user $userId in ${firestoreDuration.inWholeMilliseconds}ms.")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating delivery $deliveryId for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun updateDeliveryFields(userId: String, deliveryId: String, fields: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        if (userId.isBlank() || deliveryId.isBlank()) {
            return@withContext Result.Error(IllegalArgumentException("User ID and Delivery ID must be provided."))
        }
        if (fields.isEmpty()) {
            Log.w(TAG, "No fields provided to update for delivery $deliveryId.")
            return@withContext Result.Success(Unit)
        }
        try {
            val deliveryRef = getDeliveryReference(userId, deliveryId)
            val prefixedUpdates = fields.mapKeys { "$FIELD_DELIVERY_DATA_ROOT.${it.key}" }

            val finalUpdates = prefixedUpdates.toMutableMap()
            finalUpdates[FIELD_METADATA_UPDATED_AT] = OffsetDateTime.now().toString()

            deliveryRef.update(finalUpdates).await()
            Log.d(TAG, "Successfully updated fields for delivery $deliveryId.")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating fields for delivery $deliveryId", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteDelivery(userId: String, deliveryId: String): Result<Unit> = withContext(ioDispatcher) {
        if (userId.isBlank() || deliveryId.isBlank()) {
            return@withContext Result.Error(IllegalArgumentException("User ID and Delivery ID cannot be blank for deletion."))
        }
        try {
            val deliveryRef = getDeliveryReference(userId, deliveryId)
            deliveryRef.delete().await()
            Log.d(TAG, "Delivery $deliveryId deleted for user $userId.")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting delivery $deliveryId for user $userId", e)
            Result.Error(e)
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeDeliveryById(userId: String, deliveryId: String): Flow<DeliveryDto?> {
        if (userId.isBlank() || deliveryId.isBlank()) {
            Log.w(TAG, "Cannot observe delivery: userId or deliveryId is blank")
            return flowOf(null)
        }
        return callbackFlow {
            val deliveryRef = getDeliveryReference(userId, deliveryId)
            val listenerRegistration = deliveryRef.addSnapshotListener { snapshot, error ->
                if (error != null) {
                    Log.e(TAG, "Error observing delivery $deliveryId for user $userId", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null) {
                    val delivery = snapshot.toDeliveryDto()
                    trySend(delivery).isSuccess
                } else {
                    trySend(null).isSuccess
                }
            }
            awaitClose {
                Log.d(TAG, "ObserveDeliveryById: Cancelling listener for $deliveryId, user $userId")
                listenerRegistration.remove()
            }
        }.flowOn(ioDispatcher)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeAllDeliveries(userId: String): Flow<List<DeliveryDto>> {
        if (userId.isBlank()) {
            Log.w(TAG, "Cannot observe all deliveries: userId is blank")
            return flowOf(emptyList())
        }
        return callbackFlow {
            // ARCHITECTURE-COMPLIANT SOLUTION: Use unordered query to handle legacy documents
            // For real-time observation, we use a single query without orderBy to capture all documents
            val query = getUserDeliveriesCollection(userId)
            val listenerRegistration = query.addSnapshotListener { snapshots, error ->
                if (error != null) {
                    Log.e(TAG, "Error observing all deliveries for user $userId", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshots != null) {
                    val deliveries = snapshots.documents.mapNotNull { it.toDeliveryDto() }
                    // Sort in memory by metadata.createdAt (nulls last) for consistent ordering
                    val sortedDeliveries = deliveries.sortedWith(compareByDescending<DeliveryDto> {
                        it.deliveryData.metadata?.createdAt
                    }.thenByDescending {
                        it.id // Fallback to document ID for legacy documents
                    })
                    trySend(sortedDeliveries).isSuccess
                } else {
                    trySend(emptyList()).isSuccess
                }
            }
            awaitClose {
                Log.d(TAG, "ObserveAllDeliveries: Cancelling listener for user $userId")
                listenerRegistration.remove()
            }
        }.flowOn(ioDispatcher)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeDeliveriesByTimeRange(userId: String, startDate: Date, endDate: Date): Flow<List<DeliveryDto>> {
        if (userId.isBlank()) return flowOf(emptyList())
        return callbackFlow {
            val query = getUserDeliveriesCollection(userId)
                .whereGreaterThanOrEqualTo(FIELD_METADATA_CREATED_AT, startDate)
                .whereLessThanOrEqualTo(FIELD_METADATA_CREATED_AT, endDate)
                .orderBy(FIELD_METADATA_CREATED_AT, Query.Direction.DESCENDING)

            val listenerRegistration = query.addSnapshotListener { snapshots, error ->
                if (error != null) {
                    Log.e(TAG, "Error in observeDeliveriesByTimeRange for user $userId", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshots != null) {
                    val deliveries = snapshots.documents.mapNotNull { it.toDeliveryDto() }
                    trySend(deliveries).isSuccess
                } else {
                    trySend(emptyList()).isSuccess
                }
            }
            awaitClose {
                listenerRegistration.remove()
            }
        }.flowOn(ioDispatcher)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeDeliveriesByTippedStatus(userId: String, isTipped: Boolean): Flow<List<DeliveryDto>> {
        if (userId.isBlank()) return flowOf(emptyList())
        return callbackFlow {
            val query = getUserDeliveriesCollection(userId)
                .whereEqualTo(FIELD_STATUS_IS_TIPPED, isTipped)
                .orderBy(FIELD_METADATA_CREATED_AT, Query.Direction.DESCENDING)

            val listenerRegistration = query.addSnapshotListener { snapshots, error ->
                if (error != null) {
                    Log.e(TAG, "Error in observeDeliveriesByTippedStatus for user $userId", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshots != null) {
                    val deliveries = snapshots.documents.mapNotNull { it.toDeliveryDto() }
                    trySend(deliveries).isSuccess
                } else {
                    trySend(emptyList()).isSuccess
                }
            }
            awaitClose {
                listenerRegistration.remove()
            }
        }.flowOn(ioDispatcher)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeCompletedAndUntippedDeliveries(userId: String): Flow<List<DeliveryDto>> {
        if (userId.isBlank()) return flowOf(emptyList())
        return callbackFlow {
            val query = getUserDeliveriesCollection(userId)
                .whereEqualTo(FIELD_STATUS_IS_COMPLETED, true)
                .whereEqualTo(FIELD_STATUS_IS_TIPPED, false)
                .orderBy(FIELD_METADATA_CREATED_AT, Query.Direction.DESCENDING)

            val listenerRegistration = query.addSnapshotListener { snapshots, error ->
                if (error != null) {
                    Log.e(TAG, "Error in observeCompletedAndUntippedDeliveries for user $userId", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshots != null) {
                    val deliveries = snapshots.documents.mapNotNull { it.toDeliveryDto() }
                    trySend(deliveries).isSuccess
                } else {
                    trySend(emptyList()).isSuccess
                }
            }
            awaitClose {
                listenerRegistration.remove()
            }
        }.flowOn(ioDispatcher)
    }

    override suspend fun findDeliveriesByField(userId: String, fieldName: String, value: Any, limit: Int): Result<List<DeliveryDto>> = withContext(ioDispatcher) {
        if (userId.isBlank()) return@withContext Result.Success(emptyList())
        try {
            val query = getUserDeliveriesCollection(userId)
                .whereEqualTo("$FIELD_DELIVERY_DATA_ROOT.$fieldName", value)
                .limit(limit.toLong())
            val snapshot = query.get().await()
            val deliveries = snapshot.documents.mapNotNull { it.toDeliveryDto() }
            Result.Success(deliveries)
        } catch (e: Exception) {
            Log.e(TAG, "Error finding deliveries by field $fieldName for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun findDeliveriesByCriteria(
        userId: String,
        limit: Int,
        orderByField: String,
        descending: Boolean,
        filterCriteria: Map<String, Any>
    ): Result<List<DeliveryDto>> = withContext(ioDispatcher) {
        if (userId.isBlank()) return@withContext Result.Success(emptyList())
        try {
            var query = getUserDeliveriesCollection(userId)
                .orderBy("$FIELD_DELIVERY_DATA_ROOT.$orderByField", if (descending) Query.Direction.DESCENDING else Query.Direction.ASCENDING)

            filterCriteria.forEach { (field, value) ->
                query = query.whereEqualTo("$FIELD_DELIVERY_DATA_ROOT.$field", value)
            }
            query = query.limit(limit.toLong())

            val snapshot = query.get().await()
            val deliveries = snapshot.documents.mapNotNull { it.toDeliveryDto() }
            Result.Success(deliveries)
        } catch (e: Exception) {
            Log.e(TAG, "Error finding deliveries by criteria for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun getOldestDeliveryTimestamp(userId: String): Result<OffsetDateTime?> = withContext(ioDispatcher) {
        if (userId.isBlank()) return@withContext Result.Success(null)
        try {
            val query = getUserDeliveriesCollection(userId)
                .orderBy(FIELD_METADATA_CREATED_AT, Query.Direction.ASCENDING)
                .limit(1)
            val snapshot = query.get().await()
            val document = snapshot.documents.firstOrNull()
            val delivery = document?.toDeliveryDto()
            val timestamp = delivery?.deliveryData?.metadata?.createdAt
            Result.Success(timestamp)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting oldest delivery timestamp for user $userId", e)
            Result.Error(e)
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeImportedVerifiedDeliveriesForDndCheck(userId: String, importedBeforeDate: Date): Flow<List<DeliveryDto>> {
        if (userId.isBlank()) return flowOf(emptyList())
        return callbackFlow {
            val query = getUserDeliveriesCollection(userId)
                .whereEqualTo(FIELD_METADATA_SOURCE, "import")
                .whereEqualTo(FIELD_STATUS_VERIFIED, true)
                .whereLessThan(FIELD_METADATA_CREATED_AT, importedBeforeDate)
                .orderBy(FIELD_METADATA_CREATED_AT, Query.Direction.DESCENDING)

            val listenerRegistration = query.addSnapshotListener { snapshots, error ->
                if (error != null) {
                    Log.e(TAG, "Error in observeImportedVerifiedDeliveriesForDndCheck for user $userId", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshots != null) {
                    val deliveries = snapshots.documents.mapNotNull { it.toDeliveryDto() }
                    trySend(deliveries).isSuccess
                } else {
                    trySend(emptyList()).isSuccess
                }
            }
            awaitClose {
                listenerRegistration.remove()
            }
        }.flowOn(ioDispatcher)
    }

    override fun addDeliveryToTransaction(transaction: Transaction, userId: String, deliveryData: DeliveryDto.DeliveryData): DocumentReference {
        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for adding delivery to transaction.")
        }
        val newDeliveryRef = getUserDeliveriesCollection(userId).document()
        transaction.set(newDeliveryRef, mapOf(FIELD_DELIVERY_DATA_ROOT to deliveryData))
        return newDeliveryRef
    }

    override suspend fun normalizeDeliveryMetadata(userId: String, deliveryId: String): Result<Unit> = withContext(ioDispatcher) {
        if (userId.isBlank() || deliveryId.isBlank()) {
            return@withContext Result.Error(IllegalArgumentException("User ID and Delivery ID cannot be blank"))
        }

        try {
            val deliveryRef = getDeliveryReference(userId, deliveryId)
            val snapshot = deliveryRef.get().await()

            if (!snapshot.exists()) {
                return@withContext Result.Error(IllegalArgumentException("Delivery document does not exist: $deliveryId"))
            }

            val data = snapshot.data
            val deliveryData = data?.get(FIELD_DELIVERY_DATA_ROOT) as? Map<*, *>

            if (deliveryData?.get("metadata") == null) {
                val now = OffsetDateTime.now()
                val defaultMetadata = mapOf(
                    "createdAt" to now.toString(),
                    "updatedAt" to now.toString(),
                    "source" to "legacy_migration",
                    "version" to 1L
                )

                val updates = mapOf("$FIELD_DELIVERY_DATA_ROOT.metadata" to defaultMetadata)
                deliveryRef.update(updates).await()

                Log.d(TAG, "Normalized metadata for delivery $deliveryId (user: $userId)")
            }

            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error normalizing delivery metadata for $deliveryId (user: $userId)", e)
            Result.Error(e)
        }
    }

    override suspend fun batchNormalizeUserDeliveries(userId: String): Result<Int> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank"))
        }

        try {
            val querySnapshot = getUserDeliveriesCollection(userId).get().await()
            var normalizedCount = 0

            for (document in querySnapshot.documents) {
                val data = document.data
                val deliveryData = data?.get(FIELD_DELIVERY_DATA_ROOT) as? Map<*, *>

                if (deliveryData?.get("metadata") == null) {
                    val normalizeResult = normalizeDeliveryMetadata(userId, document.id)
                    if (normalizeResult is Result.Success) {
                        normalizedCount++
                    }
                }
            }

            Log.d(TAG, "Batch normalization completed for user $userId: $normalizedCount documents updated")
            Result.Success(normalizedCount)
        } catch (e: Exception) {
            Log.e(TAG, "Error in batch normalization for user $userId", e)
            Result.Error(e)
        }
    }
}
