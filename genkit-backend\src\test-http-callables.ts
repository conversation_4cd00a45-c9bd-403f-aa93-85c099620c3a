import { initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import * as admin from 'firebase-admin';
import { runFlow } from '@genkit-ai/flow';

// Import the flows directly instead of the trigger wrappers
import { parseImportDataWithLogging } from './flows/import-parsing';
import { setManualAddressDndOverrideFlow } from './flows/set-manual-address-dnd-override';

// Import schemas for validation
import { SetManualAddressDndInputSchema } from './flows/set-manual-address-dnd-override';

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
    initializeApp();
}

interface HttpTestResult {
    functionName: string;
    testName: string;
    success: boolean;
    result?: any;
    error?: string;
    duration: number;
    statusCode?: number;
}

class HttpCallableTester {
    private results: HttpTestResult[] = [];

    async runAllHttpTests(): Promise<HttpTestResult[]> {
        console.log('🌐 Starting HTTP Callable Function Testing...\n');

        await this.testParseImportCallable();
        await this.testManualDndOverrideCallable();

        this.printHttpSummary();
        return this.results;
    }

    private async testParseImportCallable(): Promise<void> {
        console.log('📥 Testing testParseImportFlow Callable...');

        // Test valid input
        await this.runHttpTest(
            'testParseImportFlow',
            'Valid Import Text',
            async () => {
                const testText = `
                Order #12345
                Delivery to: 123 Main St, City, State 12345
                Tip: $5.50
                Platform: Shipt
                Date: 2024-01-15
                `;

                // Test the flow directly
                return await runFlow(parseImportDataWithLogging, testText.trim());
            }
        );

        // Test invalid input (empty text)
        await this.runHttpTest(
            'testParseImportFlow',
            'Empty Text Input',
            async () => {
                try {
                    return await runFlow(parseImportDataWithLogging, '');
                } catch (error: any) {
                    return { error: error.message, expected: true };
                }
            }
        );

        // Test malformed text
        await this.runHttpTest(
            'testParseImportFlow',
            'Malformed Text Input',
            async () => {
                const malformedText = 'This is not a valid delivery format';
                return await runFlow(parseImportDataWithLogging, malformedText);
            }
        );
    }

    // Google API and Dashboard batch callable testing removed - unused functionality

    private async testManualDndOverrideCallable(): Promise<void> {
        console.log('🚫 Testing setManualAddressDndOverride Callable...');

        // Test valid DND override
        await this.runHttpTest(
            'setManualAddressDndOverride',
            'Valid DND Override (FORCE_DND)',
            async () => {
                const testInput = {
                    userId: 'test-user-123',
                    addressId: 'test-address-456',
                    desiredState: 'FORCE_DND'
                };

                const validation = SetManualAddressDndInputSchema.safeParse(testInput);
                if (!validation.success) {
                    throw new Error(`Invalid test data: ${validation.error.message}`);
                }

                return await runFlow(setManualAddressDndOverrideFlow, validation.data);
            }
        );

        // Test FORCE_ALLOW state
        await this.runHttpTest(
            'setManualAddressDndOverride',
            'Valid DND Override (FORCE_ALLOW)',
            async () => {
                const testInput = {
                    userId: 'test-user-123',
                    addressId: 'test-address-456',
                    desiredState: 'FORCE_ALLOW'
                };

                const validation = SetManualAddressDndInputSchema.safeParse(testInput);
                if (!validation.success) {
                    throw new Error(`Invalid test data: ${validation.error.message}`);
                }

                return await runFlow(setManualAddressDndOverrideFlow, validation.data);
            }
        );

        // Test invalid state
        await this.runHttpTest(
            'setManualAddressDndOverride',
            'Invalid DND State',
            async () => {
                const testInput = {
                    userId: 'test-user-123',
                    addressId: 'test-address-456',
                    desiredState: 'INVALID_STATE'
                };

                const validation = SetManualAddressDndInputSchema.safeParse(testInput);
                if (!validation.success) {
                    return { error: 'Invalid DND state', expected: true };
                }

                return await runFlow(setManualAddressDndOverrideFlow, validation.data);
            }
        );

        // Test missing required fields
        await this.runHttpTest(
            'setManualAddressDndOverride',
            'Missing Required Fields',
            async () => {
                const testInput = {
                    userId: 'test-user-123'
                    // Missing addressId and desiredState
                };

                const validation = SetManualAddressDndInputSchema.safeParse(testInput);
                if (!validation.success) {
                    return { error: 'Missing required fields', expected: true };
                }

                return await runFlow(setManualAddressDndOverrideFlow, validation.data);
            }
        );
    }

    private async runHttpTest(
        functionName: string,
        testName: string,
        testFunction: () => Promise<any>
    ): Promise<void> {
        const startTime = Date.now();
        
        try {
            console.log(`  ⏳ Running: ${testName}...`);
            const result = await testFunction();
            const duration = Date.now() - startTime;
            
            // Determine if this is a success based on the result structure
            const isSuccess = this.isSuccessfulResult(result);
            
            this.results.push({
                functionName,
                testName,
                success: isSuccess,
                result,
                duration,
                statusCode: this.extractStatusCode(result)
            });
            
            const status = isSuccess ? '✅' : '⚠️';
            console.log(`  ${status} ${testName} - ${isSuccess ? 'Success' : 'Expected Error'} (${duration}ms)`);
            
            if (result && typeof result === 'object') {
                console.log(`    Result: ${JSON.stringify(result, null, 2).substring(0, 200)}...`);
            }
            
        } catch (error: any) {
            const duration = Date.now() - startTime;
            
            this.results.push({
                functionName,
                testName,
                success: false,
                error: error.message,
                duration
            });
            
            console.log(`  ❌ ${testName} - Failed: ${error.message} (${duration}ms)`);
        }
        
        console.log(''); // Add spacing
    }

    private isSuccessfulResult(result: any): boolean {
        if (!result || typeof result !== 'object') {
            return false;
        }

        // Check for expected errors (validation working correctly)
        if (result.expected === true) return true;

        // Check for common success indicators
        if (result.success === true) return true;
        if (result.status === 'SUCCESS') return true;
        if (result.status === 'OK') return true;

        // Check for error indicators that are actual failures
        if (result.error && result.expected !== true) return false;
        if (result.status === 'ERROR') return false;
        if (result.status === 'INVALID_INPUT') return false;
        if (result.status === 'INTERNAL_ERROR') return false;

        // If no clear indicators, consider it a success if it has meaningful data
        return Object.keys(result).length > 0;
    }

    private extractStatusCode(result: any): number | undefined {
        if (result && typeof result === 'object') {
            if (result.statusCode) return result.statusCode;
            if (result.status === 'SUCCESS' || result.success === true) return 200;
            if (result.status === 'INVALID_INPUT') return 400;
            if (result.status === 'INTERNAL_ERROR') return 500;
        }
        return undefined;
    }

    private printHttpSummary(): void {
        console.log('📋 HTTP Callable Test Summary:');
        console.log('==============================');
        
        const successful = this.results.filter(r => r.success).length;
        const failed = this.results.filter(r => !r.success).length;
        const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
        
        console.log(`Total HTTP Tests: ${this.results.length}`);
        console.log(`Successful: ${successful}`);
        console.log(`Failed: ${failed}`);
        console.log(`Total Duration: ${totalDuration}ms`);
        console.log('');
        
        // Group by function
        const byFunction = this.results.reduce((acc, result) => {
            if (!acc[result.functionName]) {
                acc[result.functionName] = [];
            }
            acc[result.functionName].push(result);
            return acc;
        }, {} as Record<string, HttpTestResult[]>);
        
        Object.entries(byFunction).forEach(([functionName, results]) => {
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            console.log(`${functionName}: ${successCount}/${totalCount} tests passed`);
            
            results.forEach(result => {
                const status = result.success ? '✅' : result.error ? '❌' : '⚠️';
                const statusCode = result.statusCode ? ` [${result.statusCode}]` : '';
                console.log(`  ${status} ${result.testName}${statusCode} (${result.duration}ms)`);
                if (result.error) {
                    console.log(`    Error: ${result.error}`);
                }
            });
            console.log('');
        });
    }
}

// Main execution function
async function main() {
    const tester = new HttpCallableTester();
    const results = await tester.runAllHttpTests();
    
    // Exit with error code if any tests failed
    const hasFailures = results.some(r => !r.success);
    process.exit(hasFailures ? 1 : 0);
}

// Export for use in other scripts
export { HttpCallableTester, HttpTestResult };

// Run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}
