/**
 * Cache Invalidation Trigger for User Preferences
 *
 * Automatically invalidates Redis cache when user DND preferences or subscription status changes.
 * This ensures cache consistency across all cloud functions.
 */
export declare const onUserPreferencesChange: import("firebase-functions/core").CloudFunction<import("firebase-functions/v2/firestore").FirestoreEvent<import("firebase-functions/v2/firestore").Change<import("firebase-functions/v2/firestore").QueryDocumentSnapshot> | undefined, {
    userId: string;
}>>;
