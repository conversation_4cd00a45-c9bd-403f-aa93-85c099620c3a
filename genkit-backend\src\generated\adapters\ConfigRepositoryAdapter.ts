// Auto-generated from ConfigRepository.kt
// IMPORTS FROM EXISTING GENERATED MODELS (not recreated)

import type { Result } from '../types/Result';
import type {
  Delivery as TsDeliveryInterface,
  Address as TsAddressInterface,
  User_profile as TsUserProfileInterface
} from '../../models/generated/delivery.schema';
import type {
  DeliveryStats as TsDeliveryStatsInterface
} from '../../models/generated/address.schema';

/**
 * TypeScript adapter interface generated from ConfigRepository.kt
 * Maintains identical contract to Kotlin implementation
 * Uses existing generated models from schemas
 */
export interface ConfigRepositoryAdapter {
  getAppConfig(forceRefresh: boolean = false): Result<AppConfig?>;
  updateAppConfig(appConfig: AppConfig): Promise<Result<void>>;
  updateAppConfigFields(fields: Map<string): Promise<Result<void>>;
  getNotificationPatterns(): Result<NotificationPatterns?>;
  updateNotificationPatterns(patterns: NotificationPatterns): Promise<Result<void>>;
  observeAppConfig(): Flow<Result<AppConfig?>>;
  observeNotificationPatterns(): Flow<Result<NotificationPatterns?>>;
  getConfigValue(key: string, defaultValue: string): Result<string>;
  getConfigBoolean(key: string, defaultValue: boolean): Result<boolean>;
  incrementCounter(counterKey: string): Promise<Result<void>>;
  updateDeviceLastActive(): Promise<Result<void>>;
  prefetchCriticalData(): Promise<Result<void>>;
  clearCache(): Promise<Result<void>>;
  invalidateCache(key: string): Promise<Result<void>>;
  initialize(): Promise<Result<void>>;
  cleanup(): Promise<Result<void>>;
  validateAppConfig(appConfig: AppConfig): Promise<Result<void>>;
  createDefaultAppConfig(): Result<AppConfig>;
  createDefaultNotificationPatterns(): Result<NotificationPatterns>;
}