 I  com.autogratuity:b21cd91a: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{3486c0d VFED..... .F...... 0,0-840,1154 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  Autofill popup isn't shown because autofill is not available.

Did you set up autofill?
1. Go to Settings > System > Languages&input > Advanced > Autofill Service
2. Pick a service

Did you add an account?
1. Go to Settings > System > Languages&input > Advanced
2. Click on the settings icon next to the Autofill Service
3. Add your account
 D  show(ime(), fromIme=true)
 D  app_time_stats: avg=56.62ms min=5.97ms max=659.18ms count=18
 I  com.autogratuity:b21cd91a: onShown
 D  app_time_stats: avg=49.33ms min=1.88ms max=463.37ms count=20
 D  app_time_stats: avg=132.12ms min=2.80ms max=501.75ms count=11
 D  show(ime(), fromIme=false)
 I  com.autogratuity:59fdf1aa: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:59fdf1aa: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  Flattened final assist data: 1500 bytes, containing 2 windows, 10 views
 I  com.autogratuity:8cb33efd: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{3486c0d VFED..... .F....ID 0,0-840,1154 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  show(ime(), fromIme=true)
 I  com.autogratuity:********: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:8cb33efd: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  Autofill popup isn't shown because autofill is not available.

Did you set up autofill?
1. Go to Settings > System > Languages&input > Advanced > Autofill Service
2. Pick a service

Did you add an account?
1. Go to Settings > System > Languages&input > Advanced
2. Click on the settings icon next to the Autofill Service
3. Add your account
 D  app_time_stats: avg=70.52ms min=3.22ms max=497.49ms count=15
 D  app_time_stats: avg=111.32ms min=10.62ms max=240.75ms count=9
 D  app_time_stats: avg=63.87ms min=9.39ms max=288.53ms count=17
 D  app_time_stats: avg=91.30ms min=9.48ms max=299.14ms count=14
 D  app_time_stats: avg=259.80ms min=12.80ms max=499.99ms count=5
 I  Flattened final assist data: 1516 bytes, containing 2 windows, 10 views
 D  show(ime(), fromIme=false)
 I  com.autogratuity:933a5a53: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:933a5a53: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:b10a690c: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{3486c0d VFED..... .F....ID 0,0-840,1154 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  Autofill popup isn't shown because autofill is not available.

Did you set up autofill?
1. Go to Settings > System > Languages&input > Advanced > Autofill Service
2. Pick a service

Did you add an account?
1. Go to Settings > System > Languages&input > Advanced
2. Click on the settings icon next to the Autofill Service
3. Add your account
 D  show(ime(), fromIme=true)
 I  com.autogratuity:cf88818e: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:b10a690c: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=90.56ms min=11.97ms max=470.71ms count=15
 D  app_time_stats: avg=95.28ms min=8.16ms max=189.18ms count=12
 D  app_time_stats: avg=242.54ms min=14.03ms max=499.95ms count=5
 D  show(ime(), fromIme=false)
 I  com.autogratuity:d0c305bf: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:d0c305bf: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:ef8cb9c: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{3486c0d VFED..... .F....ID 0,0-840,1154 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  show(ime(), fromIme=true)
 I  com.autogratuity:8c179248: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:ef8cb9c: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=58.46ms min=1.49ms max=317.72ms count=15
 D  app_time_stats: avg=134.59ms min=12.02ms max=499.42ms count=8
 D  app_time_stats: avg=111.24ms min=10.82ms max=484.68ms count=9
 D  app_time_stats: avg=113.40ms min=13.67ms max=380.95ms count=10
 D  app_time_stats: avg=121.31ms min=12.79ms max=517.26ms count=10
 I  Creating <NAME_EMAIL> with empty reCAPTCHA token
 W  Ignoring header X-Firebase-Locale because its value was null.
 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@d013aec
 D  endAllActiveAnimators on 0x758d33a3e630 (UnprojectedRipple) with handle 0x758ca3a802f0
 D  app_time_stats: avg=447.54ms min=1.66ms max=18571.03ms count=43
 I  com.autogratuity:a3c438d2: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT fromUser false
 I  com.autogratuity:a3c438d2: onFailed at PHASE_CLIENT_VIEW_SERVED
 D  hide(ime(), fromIme=true)
 I  com.autogratuity:f1c6e00b: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 W  requestCursorUpdates on inactive InputConnection
 E  Ime callback not found. Ignoring unregisterReceivedCallback. callbackId: 255721015
 W  Error getting App Check token; using placeholder token instead. Error: com.google.firebase.FirebaseException: Error returned from API. code: 403 body: App attestation failed.
 D  tagSocket(143) with statsTag=0xffffffff, statsUid=-1
 W  Ignoring header X-Firebase-Locale because its value was null.
 W  Error getting App Check token; using placeholder token instead. Error: com.google.firebase.FirebaseException: Too many attempts.
 D  Notifying id token listeners about user ( qOhr05X5ySh5d1s6sqa43vImBE92 ).
 D  Notifying auth state listeners about user ( qOhr05X5ySh5d1s6sqa43vImBE92 ).
 D  ✅ User authenticated: qOhr05X5ySh5d1s6sqa43vImBE92
 I  Global AuthStateListener: User signed IN (qOhr05X5ySh5d1s6sqa43vImBE92)
 D  Authentication state: Unauthenticated
 D  Authentication ready for user: qOhr05X5ySh5d1s6sqa43vImBE92
 I  ✅ Registration successful: qOhr05X5ySh5d1s6sqa43vImBE92
 D  🔧 Creating user profile and setting up preferences for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  User validation passed for qOhr05X5ySh5d1s6sqa43vImBE92
 D  Created and validated default user for qOhr05X5ySh5d1s6sqa43vImBE92
 D  Scheduling task addUser_qOhr05X5ySh5d1s6sqa43vImBE92 with priority HIGH
 D  Executing task addUser_qOhr05X5ySh5d1s6sqa43vImBE92 (HIGH)
 D  onCreate: START (Compose)
 I  === CLARITY MONITORING DISABLED FOR PERFORMANCE ===
 I  === MONITORING DISABLED TO IMPROVE APP PERFORMANCE ===
 D  executeWithProfiling: Starting addUser for entity qOhr05X5ySh5d1s6sqa43vImBE92 (session: null)
 D  addUser: Starting user creation for qOhr05X5ySh5d1s6sqa43vImBE92 (session: null)
 D  🔧 LENIENT User validation (creation mode): 0 errors, 0 warnings
 D  ✅ User creation validation PASSED - signup can proceed
 D  ✅ User validation passed with no warnings during creation for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [MAPPER] === USER SSOT TO DTO MAPPING START ===
 D  [MAPPER] SSoT User ID: qOhr05X5ySh5d1s6sqa43vImBE92
 D  [MAPPER] SSoT User ID null check: true
 D  [MAPPER] SSoT User email: <EMAIL>
 D  [MAPPER] SSoT User displayName: yesme
 D  🚀 CRITICAL PERFORMANCE: Monitoring dependencies in background (non-blocking) (COMPOSE)
 D  ✅ MainActivityCompose instant initialization completed - dashboard loading immediately
 D  waitForAuthentication: Starting authentication readiness check (timeout: 100ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  ✅ Background auth ready for user: qOhr05X5ySh5d1s6sqa43vImBE92 (COMPOSE)
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  MainActivityCompose onCreate completed in 27.765800ms
 I  SESSION STARTED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 with cache warming and app state monitoring
 D  SESSION: Repository initialization state: true for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 D  SESSION: UI performance monitoring started for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 D  Encrypted 17 chars in 14ms
 I  CACHE WARMING: Starting real cache warming for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [CACHE_WARMING] Starting critical data preload for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  [SESSION] Started: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 [TRACKING]
 D  [MAPPER] === DTO CREATION VALIDATION ===
 I  Created new session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  [MAPPER] DTO userId: qOhr05X5ySh5d1s6sqa43vImBE92
 D  [MAPPER] DTO userId null check: true
 D  [MAPPER] DTO userId matches SSoT: true
 D  [MAPPER] DTO email encrypted: QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e
 D  [MAPPER] DTO displayName encrypted: yesme
 D  [MAPPER] DTO accountStatus: active
 D  [MAPPER] DTO version: 1
 D  [MAPPER] Critical fields valid for Firestore: true
 D  [MAPPER] === USER SSOT TO DTO MAPPING END ===
 D  addUser: Adding user to remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'user_create:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness before Firestore write...
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  Authentication confirmed for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  Converting complex DTO to simple Map structure for Firestore serialization
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  Converted to simple Map - userId: qOhr05X5ySh5d1s6sqa43vImBE92, fields: 22
 D  [DEBUG] === FIRESTORE WRITE DIAGNOSIS START ===
 D  [DEBUG] Writing to Firestore Path: users/qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DEBUG] Auth UID: qOhr05X5ySh5d1s6sqa43vImBE92
 D  SettingsViewModel created with modern StateFlow architecture
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: false
 I  Background concurrent mark compact GC freed 7610KB AllocSpace bytes, 23(848KB) LOS objects, 49% free, 10MB/20MB, paused 6.895ms,3.449ms total 83.642ms
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DEBUG] Auth UID Match: true
 D  SubscriptionViewModel created with modern StateFlow architecture
 D  [DEBUG] Profile DTO userId: qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: User qOhr05X5ySh5d1s6sqa43vImBE92 not in cache, fetching from remote with deduplication
 D  [DEBUG] Profile DTO userId null check: true
 D  [DEBUG] UserId comparison: DTO[qOhr05X5ySh5d1s6sqa43vImBE92] == Path[qOhr05X5ySh5d1s6sqa43vImBE92] = true
 D  [DEBUG] Wrapped Structure Keys: [profileData]
 D  DEDUPLICATION: Starting new request for key 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [DEBUG] Has 'profileData' key: true
 D  [DEBUG] ProfileData Keys: [userId, email, displayName, defaultAddressId, photoUrl, accountStatus, timezone, authProviders, createdAt, lastLoginAt, privacyPolicyAccepted, termsAccepted, subscription, preferences, permissions, usage, syncInfo, appSettings, communication, usageStats, metadata, version]
 D  [DEBUG] ProfileData has 'userId': true
 D  [DEBUG] ProfileData userId value: qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DEBUG] ProfileData userId null check: true
 D  [DEBUG] ProfileData userId matches auth: true
 D  [DEBUG] === SECURITY RULES VALIDATION ===
 D  [DEBUG] Rule Check 1 - hasRequiredFields(request.resource.data, ['profileData']): true
 D  [DEBUG] Rule Check 2 - hasRequiredFields(request.resource.data.profileData, ['userId']): true
 D  getUserProfileById: Fetching user profile for user qOhr05X5ySh5d1s6sqa43vImBE92 from path: users/qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DEBUG] Rule Check 3 - request.resource.data.profileData.userId not null: true
 D  [DEBUG] Rule Check 4 - request.resource.data.profileData.userId == request.auth.uid: true
 D  [DEBUG] All Security Rules Passed: true
 D  [DEBUG] === FIELD DIAGNOSTICS ===
 D  [DEBUG] Field 'userId': exists=true, value=qOhr05X5ySh5d1s6sqa43vImBE92, isNull=false
 D  [DEBUG] Field 'email': exists=true, value=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e
, isNull=false
 D  [DEBUG] Field 'displayName': exists=true, value=yesme, isNull=false
 D  [DEBUG] Field 'accountStatus': exists=true, value=active, isNull=false
 D  [DEBUG] Field 'version': exists=true, value=1, isNull=false
 D  [DEBUG] ⚠️ NULL FIELDS (will be removed by Firestore): [defaultAddressId, photoUrl, timezone, privacyPolicyAccepted, termsAccepted, usage, syncInfo]
 D  [DEBUG] Data Size: 1048 bytes
 D  [DEBUG] === FIRESTORE SERIALIZATION FIX ===
 D  [DEBUG] Using simple Map structure (legacy Java approach)
 D  [DEBUG] Converted complex DTO to primitive types for Firestore
 D  [DEBUG] === FIRESTORE WRITE DIAGNOSIS END ===
 D  saveUserProfile: Saving user profile with simple Map structure for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DEBUG] === FIRESTORE WRITE OPERATION START ===
 D  [DEBUG] About to call Firestore .set() operation
 D  [DEBUG] Document path: users/qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DEBUG] Current thread: DefaultDispatcher-worker-1
 D  ⏱️ FIRESTORE WRITE: Starting .set() operation at *************
 D  DashboardViewModel created with modern StateFlow architecture
 D  Pro user status: false
 D  Profile state updated: Loading
 D  Core data ready state: true
 I  Repositories ready, UI visible
 D  🚀 HERO DASHBOARD: Starting render at 1749809019597
 D  User session ready - initializing dashboard data
 I  SYSTEM HEALTH CHECK: Starting comprehensive monitoring
 I  SYSTEM STATE:
 I  - Repository Initialized: true
 I  - User Session Ready: false
 I  - Core Data Ready: true
 D  🔍 STATE CHANGED: showAddressDetails = false, selectedAddressId = null
 I  SYSTEM COMPONENTS:
 I  - AuthManager: AuthenticationManagerImpl
 I  - EncryptionUtils: EncryptionUtils
 I  - FirebaseAuth: zzad
 I  - CacheLifecycleManager: CacheLifecycleManager
 I  Skipped 31 frames!  The application may be doing too much work on its main thread.
 I  CACHE METRICS: {delivery={hitRate=0.0, missRate=0.0, totalEntries=0, completedDeliveries=0, pendingTips=0, totalCachedTips=0.0, uniqueUsers=0, uniqueAddresses=0, avgTipPerDelivery=0.0}, address={size=0, maxSize=500, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=2h, enableMetrics=true, uniqueUsers=0, dndAddresses=0, addressesWithStats=0, avgTipsPerAddress=0.0}, userProfile={size=0, maxSize=50, metrics=CacheMetrics(hits=0, misses=1, puts=0, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=1d, enableMetrics=true, hitRate=0.0, missRate=1.0, totalEntries=0, activeSubscriptions=0, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.0, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=0, maxSize=20, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1h, enableMetrics=true}, config={size=1, maxSize=500, metrics=CacheMetrics(hits=1, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=3), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 I  UI PERFORMANCE MONITORING: Started/Verified
 D  [SYSTEM_HEALTH] Repository:true UserSession:false CoreData:true
 D  [SYSTEM_HEALTH] Cache metrics: {delivery={hitRate=0.0, missRate=0.0, totalEntries=0, completedDeliveries=0, pendingTips=0, totalCachedTips=0.0, uniqueUsers=0, uniqueAddresses=0, avgTipPerDelivery=0.0}, address={size=0, maxSize=500, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=2h, enableMetrics=true, uniqueUsers=0, dndAddresses=0, addressesWithStats=0, avgTipsPerAddress=0.0}, userProfile={size=0, maxSize=50, metrics=CacheMetrics(hits=0, misses=1, puts=0, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=1d, enableMetrics=true, hitRate=0.0, missRate=1.0, totalEntries=0, activeSubscriptions=0, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.0, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=0, maxSize=20, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1h, enableMetrics=true}, config={size=1, maxSize=500, metrics=CacheMetrics(hits=1, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=3), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 I  SYSTEM HEALTH CHECK: Completed successfully
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 562ms, result: true
 I  ✅ Background session managed: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 (COMPOSE)
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  Authentication ready, loading data for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  app_time_stats: avg=30.07ms min=6.33ms max=654.78ms count=47
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 (age: 562ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUserIdSuspend: Checking authentication state
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Scheduling task isTrialAvailable_qOhr05X5ySh5d1s6sqa43vImBE92_1749809019850 with priority NORMAL
 D  Executing task isTrialAvailable_qOhr05X5ySh5d1s6sqa43vImBE92_1749809019850 (NORMAL)
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  observeUserSubscription: Setting up observation for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  Repository initialization state: true
 D  executeFirestoreGetOperation: Starting Firestore fetch for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  UI frame monitoring started
 D  🔗 Auth state: User authenticated (qOhr05X5ySh5d1s6sqa43vImBE92)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  🚀 HERO PERFORMANCE: Starting priority cache warming for instant map
 D  Scheduling task getCurrentUserSubscription_1749809019860 with priority HIGH
 D  Scheduling task DashboardMapCacheWarming with priority CRITICAL
 D  Executing task DashboardMapCacheWarming (CRITICAL)
 D  Executing task getCurrentUserSubscription_1749809019860 (HIGH)
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 (age: 597ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  🗺️ Testing simple geocoding for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  🧪 Testing native geocoding with: 1600 Amphitheatre Parkway, Mountain View, CA
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUser: Authentication ready for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:2 [EFFICIENT]: 0ms [OK]
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  loadPage: Loading address page for user qOhr05X5ySh5d1s6sqa43vImBE92 with size: 50, key: null
 D  loadAddressPage: Loading address page with size: 50, key: null
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: false
 D  loadAddressPage: Executing Firestore query for addresses
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription', waiting for result
 D  getUserById: User qOhr05X5ySh5d1s6sqa43vImBE92 not in cache, fetching from remote with deduplication
 D  DEDUPLICATION: Found existing request for key 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  🗺️ Loading addresses from repository for map display
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Found existing request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription', waiting for result
 D  Scheduling task getDefaultAddressSummary_current_user with priority LOW
 D  Executing task getDefaultAddressSummary_current_user (LOW)
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  DEDUPLICATION: Found existing request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  observeCurrentUser: Authentication ready for user qOhr05X5ySh5d1s6sqa43vImBE92, starting observation (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: false
 D  getUserById: User qOhr05X5ySh5d1s6sqa43vImBE92 not in cache, fetching from remote with deduplication
 D  DEDUPLICATION: Found existing request for key 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  observeUserById: Starting observation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  DEDUPLICATION: Found existing request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  executeWithProfiling: Starting getDefaultAddressSummary for entity current_user (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription', waiting for result
 D  getDefaultAddressSummary: Getting default address summary for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: false
 D  getUserById: User qOhr05X5ySh5d1s6sqa43vImBE92 not in cache, fetching from remote with deduplication
 D  DEDUPLICATION: Found existing request for key 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  observeUserById: Triggering cache refresh for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: false
 D  getUserById: User qOhr05X5ySh5d1s6sqa43vImBE92 not in cache, fetching from remote with deduplication
 D  DEDUPLICATION: Found existing request for key 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  Starting backpressure handling for dashboard_delivery_observation with THROTTLE_FIRST
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 121ms, result: true
 D  ✅ SESSION MANAGER: Created/retrieved session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 D  observeDeliveriesByUserId: Starting observation for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 (age: 704ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 10ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 (age: 705ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  observeDeliveriesByUserId: Cache empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Found existing request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription', waiting for result
 W  observeCurrentUser: Authentication not available: Flow was aborted, no more elements needed (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 (age: 712ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 W  User data is null, retrying... (attempt 1/3)
 D  Retrying user profile load (attempt 2) after 1000ms delay
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 19ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  Successfully extracted profileData from document qOhr05X5ySh5d1s6sqa43vImBE92
 D  ProfileData fields: [authProviders, preferences, metadata, displayName, timezone, usage, subscription, userId, syncInfo, version, accountStatus, photoUrl, createdAt, appSettings, lastLoginAt, termsAccepted, permissions, defaultAddressId, communication, usageStats, email, privacyPolicyAccepted]
 D  [Firestore] READ GET_DOCUMENT:users/qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Results:1 Size:1048bytes Source:SERVER Filters:[user_profile_access]: 707ms [OK]
 I  FIRESTORE READ: GET_DOCUMENT:users/qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Results:1 Size:1048bytes Source:SERVER Filters:[user_profile_access] (707ms)
 D  executeFirestoreGetOperation: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92 in 207ms
 D  [Firestore] READ GET_DOCUMENT:users/qOhr05X5ySh5d1s6sqa43vImBE92 Size:1370bytes Source:false: 207ms [OK]
 I  FIRESTORE READ: GET_DOCUMENT:users/qOhr05X5ySh5d1s6sqa43vImBE92 Size:1370bytes Source:false (207ms)
 D  dtoToSsot: Converting DTO to SSoT domain model
 D  dtoToSsot: Successfully converted DTO to SSoT
 D  === SUBSCRIPTION MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_status=free, dto_level=free, dto_isActive=true, dto_startDate=2025-06-13T05:03:39.241072-05:00, dto_expiryDate=null, dto_isLifetime=false, dto_provider=null, dto_orderId=null}
 D  Output Domain State: {domain_status=free, domain_level=free, domain_isActive=true, domain_startDate=2025-06-13T05:03:39.241072-05:00, domain_expiryDate=null, domain_isLifetime=false, domain_provider=null, domain_orderId=null}
 D  Field Transformations: [startDate: DATE_CONVERSION, expiryDate: DATE_CONVERSION, verification: NESTED_MAPPING, status: DIRECT_MAPPING, level: DIRECT_MAPPING]
 D  Business Logic Applied: [date_conversion, verification_mapping, subscription_validation]
 D  Mapping Duration: 0ms
 D  saveUserSubscription: Saving subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [SubscriptionMapper] toSsot(UserSubscription) ID:unknown User:subscription_mapping Size:183bytes Fields:5 Logic:[date_conversion,verification_mapping,subscription_validation] UserSubscription: 0ms [OK]
 D  Successfully extracted profileData from document qOhr05X5ySh5d1s6sqa43vImBE92
 D  ProfileData fields: [authProviders, preferences, metadata, displayName, timezone, usage, subscription, userId, syncInfo, version, accountStatus, photoUrl, createdAt, appSettings, lastLoginAt, termsAccepted, permissions, defaultAddressId, communication, usageStats, email, privacyPolicyAccepted]
 D  getUserProfileById: Successfully fetched user profile for user qOhr05X5ySh5d1s6sqa43vImBE92
 D    Firestore Path: users/qOhr05X5ySh5d1s6sqa43vImBE92
 D    Document Size: 1048 bytes
 D    Firestore Duration: 707ms
 D  DEDUPLICATION: Cleaned up completed request for key 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92' in 737ms, result: true
 D  User validation (update mode): 0 errors, 0 warnings
 D  DEDUPLICATION: Completed waiting for 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92' in 177ms, result: true
 D  === USER MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=qOhr05X5ySh5d1s6sqa43vImBE92, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_displayName=yesme, dto_email=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e
, dto_defaultAddressId=null, dto_subscription_active=true}
 D  User validation (update mode): 0 errors, 0 warnings
 D  Output Domain State: {domain_id=qOhr05X5ySh5d1s6sqa43vImBE92, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_displayName=yesme, domain_email=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e, domain_defaultAddressId=null, domain_subscription_active=true}
 D  === USER MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=qOhr05X5ySh5d1s6sqa43vImBE92, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_displayName=yesme, dto_email=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e
, dto_defaultAddressId=null, dto_subscription_active=true}
 D  Output Domain State: {domain_id=qOhr05X5ySh5d1s6sqa43vImBE92, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_displayName=yesme, domain_email=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e, domain_defaultAddressId=null, domain_subscription_active=true}
 D  Field Transformations: [name: DECRYPT, email: DECRYPT, phone: DECRYPT, preferences: COMPLEX_MAPPING, subscription: NESTED_MAPPING, verification: STATUS_MAPPING]
 D  Business Logic Applied: [pii_decryption, field_validation, subscription_mapping, preferences_parsing, verification_status]
 D  PII Fields Processed: 2
 D  Mapping Duration: 7ms
 D  Cached subscription for qOhr05X5ySh5d1s6sqa43vImBE92 from FIRESTORE (status: FREE)
 D  saveUserSubscription: Successfully saved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Field Transformations: [name: DECRYPT, email: DECRYPT, phone: DECRYPT, preferences: COMPLEX_MAPPING, subscription: NESTED_MAPPING, verification: STATUS_MAPPING]
 D  Business Logic Applied: [pii_decryption, field_validation, subscription_mapping, preferences_parsing, verification_status]
 D  PII Fields Processed: 2
 D  Mapping Duration: 0ms
 D  DEDUPLICATION: Completed waiting for 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92' in 169ms, result: true
 D  DEDUPLICATION: Completed waiting for 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92' in 155ms, result: true
 D  [UserMapper] toSsot(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1325bytes Fields:6 PII:2 Logic:[pii_decryption,field_validation,subscription_mapping,preferences_parsing,verification_status] [CACHE_UPDATED] User: 0ms [OK]
 D  User validation (update mode): 0 errors, 0 warnings
 D  [UserMapper] toSsot(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1325bytes Fields:6 PII:2 Logic:[pii_decryption,field_validation,subscription_mapping,preferences_parsing,verification_status] [CACHE_UPDATED] User: 7ms [OK]
 D  === USER MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=qOhr05X5ySh5d1s6sqa43vImBE92, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_displayName=yesme, dto_email=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e
, dto_defaultAddressId=null, dto_subscription_active=true}
 D  Output Domain State: {domain_id=qOhr05X5ySh5d1s6sqa43vImBE92, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_displayName=yesme, domain_email=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e, domain_defaultAddressId=null, domain_subscription_active=true}
 D  Field Transformations: [name: DECRYPT, email: DECRYPT, phone: DECRYPT, preferences: COMPLEX_MAPPING, subscription: NESTED_MAPPING, verification: STATUS_MAPPING]
 D  Business Logic Applied: [pii_decryption, field_validation, subscription_mapping, preferences_parsing, verification_status]
 D  PII Fields Processed: 2
 D  Mapping Duration: 5ms
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 104ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  User validation (update mode): 0 errors, 0 warnings
 D  === USER MAPPING TRANSFORMATION ===
 D  Saved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 140ms, result: true
 D  Task getCurrentUserSubscription_1749809019860 completed in 261ms
 D  Input DTO State: {dto_id=qOhr05X5ySh5d1s6sqa43vImBE92, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_displayName=yesme, dto_email=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e
, dto_defaultAddressId=null, dto_subscription_active=true}
 D  Output Domain State: {domain_id=qOhr05X5ySh5d1s6sqa43vImBE92, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_displayName=yesme, domain_email=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e, domain_defaultAddressId=null, domain_subscription_active=true}
 D  Field Transformations: [name: DECRYPT, email: DECRYPT, phone: DECRYPT, preferences: COMPLEX_MAPPING, subscription: NESTED_MAPPING, verification: STATUS_MAPPING]
 D  Business Logic Applied: [pii_decryption, field_validation, subscription_mapping, preferences_parsing, verification_status]
 D  PII Fields Processed: 2
 D  Mapping Duration: 14ms
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 276ms, result: true
 I  CACHE WARMING: Subscription loaded successfully in 819ms
 D  Cached user qOhr05X5ySh5d1s6sqa43vImBE92 with atomic cache system
 D  Saved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Cached user qOhr05X5ySh5d1s6sqa43vImBE92 with atomic cache system
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:9ms Remote:219ms Map:6ms Store:21ms Hit:false ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 255ms [OK]
 D  [data.SubscriptionRepository] getCurrentUserSubscription(UserSubscription) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:197bytes Source:cache_or_remote Strategy:cache-first [CACHE_HIT]: 278ms [OK]
 D  [UserMapper] toSsot(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1325bytes Fields:6 PII:2 Logic:[pii_decryption,field_validation,subscription_mapping,preferences_parsing,verification_status] [CACHE_UPDATED] User: 5ms [OK]
 D  DEDUPLICATION: Completed waiting for 'user_profile:qOhr05X5ySh5d1s6sqa43vImBE92' in 243ms, result: true
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 255ms, result: true
 D  User validation (update mode): 0 errors, 0 warnings
 D  Subscription cache warming successful in 256ms
 D  === USER MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=qOhr05X5ySh5d1s6sqa43vImBE92, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_displayName=yesme, dto_email=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e
, dto_defaultAddressId=null, dto_subscription_active=true}
 D  Output Domain State: {domain_id=qOhr05X5ySh5d1s6sqa43vImBE92, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_displayName=yesme, domain_email=QUdUMDE6cqOw5YyPaIBBkAbhmH6a2KJyO8a6fmSLAbDNaJFlEP3YiVh9CLkiNeyM+W2e, domain_defaultAddressId=null, domain_subscription_active=true}
 D  Field Transformations: [name: DECRYPT, email: DECRYPT, phone: DECRYPT, preferences: COMPLEX_MAPPING, subscription: NESTED_MAPPING, verification: STATUS_MAPPING]
 D  Business Logic Applied: [pii_decryption, field_validation, subscription_mapping, preferences_parsing, verification_status]
 D  PII Fields Processed: 2
 D  Mapping Duration: 0ms
 D  getUserById: Fetched user qOhr05X5ySh5d1s6sqa43vImBE92 from remote and updated cache
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 308ms, result: true
 D  Subscription cache warming successful in 312ms
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 283ms, result: true
 D  Subscription cache warming successful in 284ms
 D  Saved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Cached user qOhr05X5ySh5d1s6sqa43vImBE92 with atomic cache system
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Cached user qOhr05X5ySh5d1s6sqa43vImBE92 with atomic cache system
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1325bytes Source:remote Strategy:cache-first [CACHE_MISS]: 881ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 895ms
 I  CACHE WARMING: User profile loaded successfully in 904ms
 D  [UserMapper] toSsot(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1325bytes Fields:6 PII:2 Logic:[pii_decryption,field_validation,subscription_mapping,preferences_parsing,verification_status] [CACHE_UPDATED] User: 0ms [OK]
 D  [UserMapper] toSsot(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1325bytes Fields:6 PII:2 Logic:[pii_decryption,field_validation,subscription_mapping,preferences_parsing,verification_status] [CACHE_UPDATED] User: 14ms [OK]
 D  getUserById: Fetched user qOhr05X5ySh5d1s6sqa43vImBE92 from remote and updated cache
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 221ms, result: true
 D  getUserById: Fetched user qOhr05X5ySh5d1s6sqa43vImBE92 from remote and updated cache
 D  isTrialAvailable: Trial available = true
 D  [cache_system.UserRepository] cache_breakdown Check:8ms Remote:177ms Map:13ms Store:102ms Hit:false ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 300ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 254ms
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 308ms
 D  Saved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Fetched user qOhr05X5ySh5d1s6sqa43vImBE92 from remote and updated cache
 D  [cache_system.UserRepository] cache_breakdown Check:1ms Remote:243ms Map:0ms Store:7ms Hit:false ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 251ms [OK]
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1325bytes Source:remote Strategy:cache-first [CACHE_MISS]: 253ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 295ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1325bytes Source:remote Strategy:cache-first [CACHE_MISS]: 306ms [OK]
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1325bytes Source:remote Strategy:cache-first [CACHE_MISS]: 294ms [OK]
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 316ms, result: true
 D  User profile cache warming successful in 322ms
 D  [data.UserRepository] getCurrentUser(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1349bytes Source:cache Strategy:cache-first [CACHE_HIT]: 327ms [OK]
 D  Task isTrialAvailable_qOhr05X5ySh5d1s6sqa43vImBE92_1749809019850 completed in 353ms
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 296ms, result: true
 D  User profile cache warming successful in 297ms
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 302ms, result: true
 D  Subscription cache warming successful in 303ms
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 226ms, result: true
 D  User profile cache warming successful in 226ms
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 271ms, result: true
 D  User profile cache warming successful in 272ms
 D  [cache_system.UserRepository] cache_breakdown Check:9ms Remote:169ms Map:22ms Store:80ms Hit:false ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 280ms [OK]
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 296ms, result: true
 D  User profile cache warming successful in 296ms
 D  [CACHE_WARMING] CriticalDataSystems for user qOhr05X5ySh5d1s6sqa43vImBE92: 2 ops in 938ms [PRELOAD]
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 I  CACHE WARMING: Completed for user qOhr05X5ySh5d1s6sqa43vImBE92 in 938ms
 I  CACHE WARMING: Results - subscription:success:819ms, user_profile:success:904ms
 D  [CACHE_WARMING] Completed for user qOhr05X5ySh5d1s6sqa43vImBE92: subscription:success:819ms, user_profile:success:904ms
 D  [correlation.SessionTracker] correlation Trigger:session_start Related:2 Total:938ms [SLOW_CORRELATION]: 938ms [OK]
 I  OPERATION CORRELATION in qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277: session_start -> subscription_preload, user_profile_preload (938ms)
 D  [cache_system.UserRepository] cache_breakdown Check:6ms Remote:739ms Map:16ms Store:96ms Hit:false ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 [SLOW_TOTAL]: 857ms [OK]
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 239ms, result: true
 D  Subscription cache warming successful in 241ms
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeUserProfile Load:371ms Steps:[repository_call,data_mapping,state_emission] UserDelay:371ms [CACHE_HIT] [FAST_UX]: 371ms [OK]
 D  Saved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Cached user qOhr05X5ySh5d1s6sqa43vImBE92 with atomic cache system
 D  getUserById: Fetched user qOhr05X5ySh5d1s6sqa43vImBE92 from remote and updated cache
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 292ms
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1325bytes Source:remote Strategy:cache-first [CACHE_MISS]: 291ms [OK]
 D  executeWithProfiling: Completed getDefaultAddressSummary in 301ms
 D  Task getDefaultAddressSummary_current_user completed in 320ms
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:3 Size:1349bytes [EFFICIENT]: 0ms [OK]
 D  [data.UserRepository] getDefaultAddressSummary(User) ID:current_user User:current_user Count:1 Size:0bytes Source:repository Strategy:cache-first [CACHE_MISS]: 301ms [OK]
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Remote:155ms Map:32ms Store:98ms Hit:false ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 285ms [OK]
 D  DEDUPLICATION: Starting new request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  [Firestore] QUERY user_addresses: 356ms [OK]
 D  executeGetRecentDeliveriesOperation: Fetching 10 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  loadAddressPage: Successfully loaded 0 DTOs (362ms total, query: 356ms)
 D  loadPage: Successfully loaded 0 addresses
 D  Retrieved 0 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Fetching from remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [data.AddressPageLoader] loadPage(Address) User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:0 Source:RemoteDataSource Strategy:none [CACHE_MISS]: 371ms [OK]
 D  getAddresses (SSoT) - No addresses found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 424ms, result: true
 D  Addresses cache warming successful in 430ms
 D  DEDUPLICATION: Completed waiting for 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 375ms, result: true
 D  Addresses cache warming successful in 378ms
 D  DEDUPLICATION: Completed waiting for 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 408ms, result: true
 D  Addresses cache warming successful in 409ms
 D  DEDUPLICATION: Completed waiting for 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 331ms, result: true
 D  Addresses cache warming successful in 333ms
 D  DEDUPLICATION: Completed waiting for 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 399ms, result: true
 D  Addresses cache warming successful in 403ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:272ms, subscription:success:256ms, addresses:success:378ms, total:382ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:226ms, subscription:success:241ms, addresses:success:333ms, total:337ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:296ms, subscription:success:284ms, addresses:success:403ms, total:407ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:322ms, subscription:success:312ms, addresses:success:430ms, total:439ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Found existing request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:297ms, subscription:success:303ms, addresses:success:409ms, total:420ms
 D  getCurrentUser: Cache warming initiated for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 0 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getDeliveriesByUserId (SSoT) - Deliveries not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  🗺️ MAP-CRITICAL WARMING COMPLETE: 454ms - addresses: Success(cachedAt=1749809020319, durationMs=430), user: Success(cachedAt=1749809020209, durationMs=322)
 D  🎯 SIMPLE GEOCODING: Testing native geocoding capability
 D  observeCurrentUser: Cache warming initiated for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  getAddresses (SSoT) - No addresses found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Fallback: Loaded 0 addresses from repository
 D  getAddresses (SSoT) - No addresses found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  CACHE WARMING: Addresses loaded in 147ms
 D  visibilityChanged oldVisibility=true newVisibility=false
 D  ✅ Firebase Auth StateListener removed
 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@d89e337
 D  getAddresses (SSoT) - No addresses found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  com.autogratuity:250ef5ab: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT fromUser false
 I  com.autogratuity:250ef5ab: onFailed at PHASE_CLIENT_VIEW_SERVED
 D  Complete cache warming: 573ms total (warming: 454ms)
 D  Task DashboardMapCacheWarming completed in 592ms
 D  🚀 PRIORITY TASK SUCCESS: Cache warming and maps pre-fetch completed successfully
 D  ✅ FIRESTORE WRITE SUCCESS: Document written in 1108ms
 D  [DEBUG] ✅ FIRESTORE WRITE COMPLETED SUCCESSFULLY
 D  [DEBUG] Write duration: 1108ms
 D  [DEBUG] Document successfully written to: users/qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DEBUG] === FIRESTORE WRITE OPERATION END (SUCCESS) ===
 D  🎉 COMPLETE SUCCESS: User profile save operation completed successfully
 D  [SUCCESS] 🎉 ENTIRE USER PROFILE SAVE OPERATION COMPLETED
 D  [SUCCESS] Total operation duration: 1156ms
 D  [SUCCESS] User document saved at: users/qOhr05X5ySh5d1s6sqa43vImBE92
 D  User profile saved for ID: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'user_create:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'user_create:qOhr05X5ySh5d1s6sqa43vImBE92' in 1163ms, result: true
 D  [Firestore] WRITE SET:users/qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1048bytes Fields:[profileData] DataFields:1 [profileData]: 1156ms [OK]
 I  FIRESTORE WRITE: SET:users/qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1048bytes Fields:[profileData] DataFields:1 [profileData] (1156ms)
 I  addUser: User added remotely for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Saved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeWithProfiling: Completed addUser in 1231ms
 D  executeWithEnhancedErrorHandling: addUser completed successfully for entity qOhr05X5ySh5d1s6sqa43vImBE92 (session: null)
 D  Task addUser_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 1236ms
 I  ✅ User profile created successfully: qOhr05X5ySh5d1s6sqa43vImBE92
 D  [data.UserRepository] addUser(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:0bytes Source:repository Strategy:cache-first [CACHE_MISS]: 1231ms [OK]
 D  Cached user qOhr05X5ySh5d1s6sqa43vImBE92 with atomic cache system
 D  [Firestore] WRITE SET:users/qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1325bytes Fields:[profileData] DataFields:1 [profileData]: 1163ms [OK]
 D  🔧 Setting up initial preferences for user: qOhr05X5ySh5d1s6sqa43vImBE92
 I  FIRESTORE WRITE: SET:users/qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1325bytes Fields:[profileData] DataFields:1 [profileData] (1163ms)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getConfigValue: Getting value for key=default_preferences, defaultValue={} (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  DEDUPLICATION: Starting new request for key 'config_value:default_preferences'
 D  Scheduling task getAppConfig_app_config with priority HIGH
 D  Executing task getAppConfig_app_config (HIGH)
 D  getAppConfig: Starting operation forceRefresh=false (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  Retrieved AppConfig from cache: false
 D  getAppConfig: AppConfig not in cache, fetching from remote with deduplication
 D  DEDUPLICATION: Starting new request for key 'app_config:global'
 D  Batch normalization completed for user qOhr05X5ySh5d1s6sqa43vImBE92: 0 documents updated
 D  getAllDeliveries: Found 0 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92 (0 modern, 0 legacy)
 D  executeGetRecentDeliveriesOperation: No deliveries found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Empty result for recent deliveries (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277, user: qOhr05X5ySh5d1s6sqa43vImBE92, limit: 10)
 D  DEDUPLICATION: Cleaned up completed request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  DEDUPLICATION: Completed new request 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10' in 346ms, result: true
 I  CACHE WARMING: Deliveries loaded in 382ms
 W  App_config document ('app_config_default') not found.
 D  getAppConfigDto: Successfully fetched app config
 D    Document exists: false
 D    Data size: 0 bytes
 D    Duration: 144ms
 D  DEDUPLICATION: Cleaned up completed request for key 'app_config:global'
 D  [Firestore] READ GET_DOCUMENT:system_config/app_config_default Results:0 Source:SERVER Filters:[app_config_access]: 144ms [OK]
 I  FIRESTORE READ: GET_DOCUMENT:system_config/app_config_default Results:0 Source:SERVER Filters:[app_config_access] (144ms)
 D  DEDUPLICATION: Completed new request 'app_config:global' in 146ms, result: true
 D  getAppConfig: No AppConfig found in remote, creating local default
 D  Cached configuration: app_config (domain: app_config, version: 1)
 D  Saved AppConfig to cache
 D  getAppConfig: Created and cached local default config
 D  Task getAppConfig_app_config completed in 164ms
 D  DEDUPLICATION: Cleaned up completed request for key 'config_value:default_preferences'
 D  DEDUPLICATION: Completed new request 'config_value:default_preferences' in 167ms, result: true
 D  ✅ Default preferences retrieved: {}
 D  [data.ConfigRepository] getConfigValue(ConfigValue) ID:default_preferences Count:1 Source:config Strategy:cache-first [CACHE_HIT]: 167ms [OK]
 D  ✅ MODERN: Notification patterns loaded/updated
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  loadPage: Loading address page for user qOhr05X5ySh5d1s6sqa43vImBE92 with size: 50, key: null
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  loadAddressPage: Loading address page with size: 50, key: null
 D  loadAddressPage: Executing Firestore query for addresses
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 2ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 11ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 14ms, result: true
 D  User profile cache warming successful in 15ms
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 (age: 1409ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 9ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 19ms, result: true
 D  Subscription cache warming successful in 23ms
 D  app_time_stats: avg=13.46ms min=1.61ms max=114.79ms count=48
 D  Native geocoding success: 1600 Amphitheatre Parkway, Mountain View, CA -> 37.4207967, -122.0852688
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  observeCurrentUser: Authentication ready for user qOhr05X5ySh5d1s6sqa43vImBE92, starting observation (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  observeUserById: Starting observation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  observeUserById: Triggering cache refresh for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  DEDUPLICATION: Found existing request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  [cache_system.UserRepository] cache_breakdown Check:2ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 2ms [OK]
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 (age: 1742ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 5ms
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 5ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 7ms, result: true
 D  User profile cache warming successful in 8ms
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 3ms [OK]
 D  [cache_system.UserRepository] cache_breakdown Check:2ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 2ms [OK]
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 4ms [OK]
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 W  observeCurrentUser: Authentication not available: Flow was aborted, no more elements needed (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 7ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 16ms, result: true
 D  Subscription cache warming successful in 17ms
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:6ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 6ms [OK]
 D  [presentation.SettingsViewModel] loadUserProfile Deps:3 Repos:[UserRepository] Data:User:qOhr05X5ySh5d1s6sqa43vImBE92 Name:yesme Pro:true State:Success [STATEFLOW_UPDATED]: 1127ms [OK]
 W  SLOW ViewModel Operation: SettingsViewModel.loadUserProfile took 1127ms (threshold: 30ms)
 W     Repositories involved: UserRepository
 D  Profile state updated: Success(data=User(id=qOhr05X5ySh5d1s6sqa43vImBE92, userId=qOhr05X5ySh5d1s6sqa43vImBE92, email=<EMAIL>, displayName=yesme, defaultAddressId=null, photoUrl=null, authProviders=[], accountStatus=active, timezone=null, createdAt=2025-06-13T05:03:39.238475-05:00, lastLoginAt=2025-06-13T05:03:39.238475-05:00, privacyPolicyAccepted=null, termsAccepted=null, version=1, subscription=UserSubscription(status=free, level=free, isActive=true, startDate=2025-06-13T05:03:39.241072-05:00, expiryDate=null, isLifetime=false, provider=null, orderId=null, verification=null), preferences=UserPreferences(notificationsEnabled=true, theme=system, useLocation=true, dnd=null), permissions=UserPermissions(bypassLimits=false, maxUploads=50), usage=null, syncInfo=null, appSettings=UserAppSettings(dataCollectionOptIn=null, lastVersion=null, onboardingCompleted=false), communication=UserCommunication(emailOptIn=false, marketingOptIn=false, pushNotificationsEnabled=true), usageStats=UserUsageStats(deliveryCount=0, addressCount=0, lastUsageDate=null, totalRuns=0, activeDaysCount=0, totalTips=0.0, featureUsage={}), metadata=Metadata(createdAt=2025-06-13T05:03:39.238475-05:00, updatedAt=2025-06-13T05:03:39.238475-05:00, importedAt=null, source=app_creation, importId=null, captureId=null, version=1, customData=null)))
 D  getAddresses (SSoT) - No addresses found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  loadAddressPage: Successfully loaded 0 DTOs (1019ms total, query: 1019ms)
 D  DEDUPLICATION: Completed waiting for 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 674ms, result: true
 D  Addresses cache warming successful in 674ms
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 1019ms, result: true
 D  Addresses cache warming successful in 1020ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:8ms, subscription:success:17ms, addresses:success:674ms, total:676ms
 D  [Firestore] QUERY user_addresses: 1019ms [OK]
 D  observeCurrentUser: Cache warming initiated for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:15ms, subscription:success:23ms, addresses:success:1020ms, total:1021ms
 D  loadPage: Successfully loaded 0 addresses
 D  [data.AddressPageLoader] loadPage(Address) User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:0 Source:RemoteDataSource Strategy:none [CACHE_MISS]: 1020ms [OK]
 D  DEDUPLICATION: Found existing request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  getAllDeliveries: Found 0 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92 (0 modern, 0 legacy)
 D  executeGetDeliveriesByUserIdOperation - No deliveries found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277: No deliveries found for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed waiting for 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 1480ms, result: true
 D  DEDUPLICATION: Completed waiting for 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 109ms, result: true
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 1480ms, result: true
 V  ThrottleFirst: emitted value
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:4 [EFFICIENT]: 0ms [OK]
 D  app_time_stats: avg=2.30ms min=1.24ms max=25.17ms count=59
 W  setSaveUiState(1582024471, false) called on existing session 1582024471; cancelling it
 D  app_time_stats: avg=2.08ms min=1.37ms max=3.54ms count=62
 D  app_time_stats: avg=1.90ms min=1.34ms max=3.25ms count=61
 D  app_time_stats: avg=2.05ms min=1.38ms max=3.83ms count=61
 D  app_time_stats: avg=2.03ms min=1.41ms max=3.99ms count=61
 D  app_time_stats: avg=2.69ms min=1.41ms max=21.70ms count=58
 D  app_time_stats: avg=3.44ms min=1.51ms max=21.94ms count=59
 D  app_time_stats: avg=2.36ms min=1.67ms max=5.67ms count=60
 D  app_time_stats: avg=2.12ms min=1.60ms max=4.07ms count=61
 D  app_time_stats: avg=2.29ms min=1.59ms max=5.47ms count=61
 D  app_time_stats: avg=2.17ms min=1.49ms max=3.67ms count=60
 D  app_time_stats: avg=2.30ms min=1.52ms max=6.11ms count=61
 D  app_time_stats: avg=2.41ms min=1.62ms max=5.46ms count=60
 D  app_time_stats: avg=2.28ms min=1.55ms max=3.39ms count=61
 D  app_time_stats: avg=2.32ms min=1.66ms max=3.90ms count=61
 D  app_time_stats: avg=2.24ms min=1.61ms max=3.47ms count=60
 D  app_time_stats: avg=2.34ms min=1.61ms max=3.46ms count=61
 D  app_time_stats: avg=2.25ms min=1.66ms max=5.40ms count=60
 D  app_time_stats: avg=2.08ms min=1.55ms max=2.83ms count=61
 D  app_time_stats: avg=2.11ms min=1.56ms max=3.09ms count=61
 D  app_time_stats: avg=2.23ms min=1.55ms max=3.36ms count=60
 D  app_time_stats: avg=2.32ms min=1.61ms max=3.57ms count=60
 D  app_time_stats: avg=2.41ms min=1.49ms max=3.25ms count=61
 D  app_time_stats: avg=2.23ms min=1.55ms max=3.28ms count=61
 D  app_time_stats: avg=2.28ms min=1.66ms max=3.72ms count=61
 D  app_time_stats: avg=2.25ms min=1.65ms max=3.61ms count=60
 D  app_time_stats: avg=2.20ms min=1.52ms max=3.19ms count=61
 D  app_time_stats: avg=2.32ms min=1.65ms max=3.48ms count=61
 D  app_time_stats: avg=2.13ms min=1.55ms max=3.09ms count=61
 D  app_time_stats: avg=2.02ms min=1.51ms max=2.72ms count=61
 D  app_time_stats: avg=2.15ms min=1.56ms max=4.42ms count=60
 D  app_time_stats: avg=2.11ms min=1.53ms max=3.11ms count=60
 D  app_time_stats: avg=2.29ms min=1.61ms max=4.18ms count=60
 D  app_time_stats: avg=2.14ms min=1.60ms max=3.30ms count=60
 D  app_time_stats: avg=2.16ms min=1.46ms max=4.32ms count=61
 D  app_time_stats: avg=2.15ms min=1.53ms max=4.06ms count=60
 D  app_time_stats: avg=2.23ms min=1.59ms max=3.25ms count=61
 D  app_time_stats: avg=2.20ms min=1.59ms max=3.85ms count=60
 D  app_time_stats: avg=2.10ms min=1.59ms max=3.65ms count=61
 D  app_time_stats: avg=2.33ms min=1.53ms max=8.48ms count=57
 D  app_time_stats: avg=2.11ms min=1.42ms max=5.38ms count=61
 D  app_time_stats: avg=1.80ms min=1.37ms max=2.92ms count=61
 D  app_time_stats: avg=1.84ms min=1.40ms max=3.47ms count=60
 D  app_time_stats: avg=1.90ms min=1.34ms max=6.77ms count=61
 D  app_time_stats: avg=1.83ms min=1.39ms max=3.50ms count=61
 D  app_time_stats: avg=1.79ms min=1.36ms max=3.26ms count=60
 D  app_time_stats: avg=1.78ms min=1.37ms max=2.68ms count=60
 D  app_time_stats: avg=1.83ms min=1.43ms max=3.59ms count=60
 D  app_time_stats: avg=2.07ms min=1.43ms max=3.77ms count=61
 D  app_time_stats: avg=1.85ms min=1.41ms max=4.23ms count=61
 D  app_time_stats: avg=1.96ms min=1.34ms max=4.48ms count=60
 D  app_time_stats: avg=2.10ms min=1.39ms max=4.34ms count=61
 D  app_time_stats: avg=2.10ms min=1.38ms max=5.68ms count=61
 D  Navigating to destination: deliveries
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 I  Compiler allocated 7814KB to compile void com.autogratuity.ui.delivery.compose.DeliveriesScreenKt.DeliveriesScreen(java.lang.String, com.autogratuity.ui.delivery.DeliveryViewModel, androidx.compose.ui.Modifier, kotlin.jvm.functions.Function0, kotlin.jvm.functions.Function1, kotlin.jvm.functions.Function1, androidx.compose.runtime.Composer, int, int)
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277)
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 4ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 8ms, result: true
 D  User profile cache warming successful in 9ms
 D  Closed previous session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277
 I  SESSION STARTED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818 with cache warming and app state monitoring
 D  SESSION: Repository initialization state: true for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 D  SESSION: UI performance monitoring started for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818
 D  [SESSION] Started: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818 [TRACKING]
 I  Created new session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 I  CACHE WARMING: Starting real cache warming for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [CACHE_WARMING] Starting critical data preload for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 3ms [OK]
 I  SESSION ENDED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277 with 49 operations
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 I  SESSION FINAL METRICS: {delivery={hitRate=0.0, missRate=0.0, totalEntries=0, completedDeliveries=0, pendingTips=0, totalCachedTips=0.0, uniqueUsers=0, uniqueAddresses=0, avgTipPerDelivery=0.0}, address={size=0, maxSize=500, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=2h, enableMetrics=true, uniqueUsers=0, dndAddresses=0, addressesWithStats=0, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=11, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=17), defaultTtl=1d, enableMetrics=true, hitRate=0.6470588235294118, missRate=0.35294117647058826, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=3, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818)
 D  [SESSION_END] Final cache metrics: {delivery={hitRate=0.0, missRate=0.0, totalEntries=0, completedDeliveries=0, pendingTips=0, totalCachedTips=0.0, uniqueUsers=0, uniqueAddresses=0, avgTipPerDelivery=0.0}, address={size=0, maxSize=500, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=2h, enableMetrics=true, uniqueUsers=0, dndAddresses=0, addressesWithStats=0, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=11, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=17), defaultTtl=1d, enableMetrics=true, hitRate=0.6470588235294118, missRate=0.35294117647058826, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=3, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 20ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818 (age: 12ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818)
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 5ms [OK]
 D  [cache_system.UserRepository] cache_breakdown Check:3ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 3ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 7ms
 I  CACHE WARMING: User profile loaded successfully in 9ms
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 8ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 5ms, result: true
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:4ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 4ms [OK]
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 34ms, result: true
 D  Subscription cache warming successful in 35ms
 I  CACHE WARMING: Subscription loaded successfully in 10ms
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  [CACHE_WARMING] CriticalDataSystems for user qOhr05X5ySh5d1s6sqa43vImBE92: 2 ops in 23ms [PRELOAD]
 I  CACHE WARMING: Completed for user qOhr05X5ySh5d1s6sqa43vImBE92 in 23ms
 I  CACHE WARMING: Results - user_profile:success:9ms, subscription:success:10ms
 D  [correlation.SessionTracker] correlation Trigger:session_start Related:2 Total:23ms: 23ms [OK]
 I  OPERATION CORRELATION in qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818: session_start -> user_profile_preload, subscription_preload (23ms)
 D  [CACHE_WARMING] Completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:9ms, subscription:success:10ms
 D  DEDUPLICATION: Starting new request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  executeGetRecentDeliveriesOperation: Fetching 10 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 0 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Fetching from remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - No addresses found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  CACHE WARMING: Addresses loaded in 39ms
 D  getAddresses (SSoT) - No addresses found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 78ms, result: true
 D  Addresses cache warming successful in 78ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:9ms, subscription:success:35ms, addresses:success:78ms, total:80ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 0 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getDeliveriesByUserId (SSoT) - Deliveries not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  Batch normalization completed for user qOhr05X5ySh5d1s6sqa43vImBE92: 0 documents updated
 D  getAllDeliveries: Found 0 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92 (0 modern, 0 legacy)
 D  executeGetRecentDeliveriesOperation: No deliveries found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Empty result for recent deliveries (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818, user: qOhr05X5ySh5d1s6sqa43vImBE92, limit: 10)
 D  DEDUPLICATION: Cleaned up completed request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  DEDUPLICATION: Completed new request 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10' in 123ms, result: true
 I  CACHE WARMING: Deliveries loaded in 123ms
 D  getAllDeliveries: Found 0 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92 (0 modern, 0 legacy)
 D  executeGetDeliveriesByUserIdOperation - No deliveries found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Session qOhr05X5ySh5d1s6sqa43vImBE92_1749809019277: No deliveries found for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 255ms, result: true
 D  ✅ Deliveries loaded successfully: 0 (deduplicated from 0)
 D  app_time_stats: avg=4.93ms min=1.39ms max=32.54ms count=59
 D  app_time_stats: avg=181.08ms min=1.27ms max=1251.99ms count=7
 D  ✅ MODERN AddEditDeliveryViewModel initialized
 D  [presentation.AddEditDeliveryViewModel] init Deps:4 Data:new_delivery_created State:Initialized [STATEFLOW_UPDATED]: 2ms [OK]
 I  Compiler allocated 8066KB to compile void com.autogratuity.ui.delivery.compose.AddEditDeliveryScreenKt$AddEditDeliveryScreen$11.invoke(androidx.compose.foundation.layout.PaddingValues, androidx.compose.runtime.Composer, int)
 I  Compiler allocated 7993KB to compile void com.autogratuity.ui.delivery.compose.AddEditDeliveryScreenKt$AddEditDeliveryScreen$11.invoke(androidx.compose.foundation.layout.PaddingValues, androidx.compose.runtime.Composer, int)
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  LaunchedEffect triggered but selectedPlace is null
 D  tagSocket(136) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(163) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  getAddresses (SSoT) - No addresses found in remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Loaded 0 addresses
 D  show(ime(), fromIme=false)
 I  com.autogratuity:136f8c4c: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{b418958 VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
 D  app_time_stats: avg=37.67ms min=11.70ms max=975.50ms count=48
 I  Flattened final assist data: 900 bytes, containing 1 windows, 6 views
 I  com.autogratuity:57a9e7b2: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{b418958 VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  Autofill popup isn't shown because autofill is not available.

Did you set up autofill?
1. Go to Settings > System > Languages&input > Advanced > Autofill Service
2. Pick a service

Did you add an account?
1. Go to Settings > System > Languages&input > Advanced
2. Click on the settings icon next to the Autofill Service
3. Add your account
 D  show(ime(), fromIme=true)
 I  com.autogratuity:57a9e7b2: onShown
 D  app_time_stats: avg=22.07ms min=1.40ms max=498.79ms count=31
 D  app_time_stats: avg=499.72ms min=498.72ms max=501.19ms count=3
 D  app_time_stats: avg=499.99ms min=499.23ms max=501.26ms count=3
 D  app_time_stats: avg=500.33ms min=499.49ms max=501.17ms count=2
 D  app_time_stats: avg=507.54ms min=501.22ms max=513.85ms count=2
 D  app_time_stats: avg=500.20ms min=497.90ms max=502.49ms count=2
 D  app_time_stats: avg=499.64ms min=498.25ms max=500.83ms count=3
 D  app_time_stats: avg=500.29ms min=499.58ms max=500.99ms count=2
 D  app_time_stats: avg=499.83ms min=499.26ms max=500.24ms count=3
 D  app_time_stats: avg=508.54ms min=501.00ms max=516.08ms count=2
 D  app_time_stats: avg=499.86ms min=498.75ms max=500.45ms count=3
 D  app_time_stats: avg=500.32ms min=498.76ms max=502.31ms count=3
 D  app_time_stats: avg=499.22ms min=497.82ms max=501.65ms count=3
 D  app_time_stats: avg=500.12ms min=499.80ms max=500.44ms count=2
 D  app_time_stats: avg=508.37ms min=500.21ms max=516.53ms count=2
 D  app_time_stats: avg=500.29ms min=499.96ms max=500.63ms count=2
 D  app_time_stats: avg=499.70ms min=499.17ms max=500.57ms count=3
 D  app_time_stats: avg=499.62ms min=499.40ms max=500.00ms count=3
 D  app_time_stats: avg=500.04ms min=499.25ms max=500.82ms count=2
 D  app_time_stats: avg=508.23ms min=498.52ms max=517.94ms count=2
 D  app_time_stats: avg=500.16ms min=498.57ms max=501.75ms count=2
 D  app_time_stats: avg=499.93ms min=499.18ms max=500.91ms count=3
 D  app_time_stats: avg=499.66ms min=499.43ms max=499.93ms count=3
 D  app_time_stats: avg=508.13ms min=500.15ms max=516.12ms count=2
 D  app_time_stats: avg=499.96ms min=499.31ms max=500.60ms count=2
 D  app_time_stats: avg=500.15ms min=498.20ms max=501.33ms count=3
 D  app_time_stats: avg=499.98ms min=498.66ms max=501.30ms count=2
 D  app_time_stats: avg=499.54ms min=498.57ms max=500.24ms count=3
 D  app_time_stats: avg=500.44ms min=500.19ms max=500.69ms count=2
 D  app_time_stats: avg=507.88ms min=499.12ms max=516.64ms count=2
 D  app_time_stats: avg=500.09ms min=497.93ms max=502.26ms count=2
 D  app_time_stats: avg=499.75ms min=499.26ms max=500.07ms count=3
 D  app_time_stats: avg=500.15ms min=499.40ms max=500.89ms count=2
 D  app_time_stats: avg=499.98ms min=499.57ms max=500.38ms count=2
 D  app_time_stats: avg=507.65ms min=498.16ms max=517.13ms count=2
 D  app_time_stats: avg=500.43ms min=499.73ms max=501.13ms count=2
 D  app_time_stats: avg=500.71ms min=499.46ms max=501.96ms count=2
 D  app_time_stats: avg=499.22ms min=497.31ms max=501.07ms count=3
 D  app_time_stats: avg=500.08ms min=499.73ms max=500.72ms count=3
 D  app_time_stats: avg=508.46ms min=499.78ms max=517.14ms count=2
 D  app_time_stats: avg=500.04ms min=499.75ms max=500.35ms count=3
 D  app_time_stats: avg=499.74ms min=499.16ms max=500.38ms count=3
 D  app_time_stats: avg=499.83ms min=499.62ms max=500.07ms count=3
 D  app_time_stats: avg=499.89ms min=499.32ms max=500.46ms count=2
 D  app_time_stats: avg=508.36ms min=500.20ms max=516.52ms count=2
 D  app_time_stats: avg=499.96ms min=499.08ms max=500.58ms count=3
 D  app_time_stats: avg=499.96ms min=499.21ms max=500.39ms count=3
 D  app_time_stats: avg=502.49ms min=499.31ms max=505.66ms count=2
 D  app_time_stats: avg=503.61ms min=494.20ms max=516.36ms count=3
 D  app_time_stats: avg=500.54ms min=500.12ms max=500.96ms count=2
 D  app_time_stats: avg=499.79ms min=498.42ms max=501.01ms count=3
 D  app_time_stats: avg=499.73ms min=498.44ms max=500.54ms count=3
 D  app_time_stats: avg=500.16ms min=499.56ms max=500.75ms count=2
 D  app_time_stats: avg=500.45ms min=484.25ms max=516.65ms count=2
 D  app_time_stats: avg=507.38ms min=500.71ms max=514.05ms count=2
 D  app_time_stats: avg=499.95ms min=499.62ms max=500.27ms count=2
 D  validateOrderIdInput: input='9' -> filtered='9'
 D  getOrderIdErrorMessage: input='9', error='Order ID must be exactly 9 digits (currently 1)'
 D  app_time_stats: avg=90.98ms min=6.52ms max=499.50ms count=11
 D  validateOrderIdInput: input='99' -> filtered='99'
 D  getOrderIdErrorMessage: input='99', error='Order ID must be exactly 9 digits (currently 2)'
 D  validateOrderIdInput: input='999' -> filtered='999'
 D  getOrderIdErrorMessage: input='999', error='Order ID must be exactly 9 digits (currently 3)'
 D  validateOrderIdInput: input='9999' -> filtered='9999'
 D  getOrderIdErrorMessage: input='9999', error='Order ID must be exactly 9 digits (currently 4)'
 D  validateOrderIdInput: input='99999' -> filtered='99999'
 D  getOrderIdErrorMessage: input='99999', error='Order ID must be exactly 9 digits (currently 5)'
 D  validateOrderIdInput: input='999999' -> filtered='999999'
 D  getOrderIdErrorMessage: input='999999', error='Order ID must be exactly 9 digits (currently 6)'
 D  validateOrderIdInput: input='9999999' -> filtered='9999999'
 D  getOrderIdErrorMessage: input='9999999', error='Order ID must be exactly 9 digits (currently 7)'
 D  validateOrderIdInput: input='99999999' -> filtered='99999999'
 D  getOrderIdErrorMessage: input='99999999', error='Order ID must be exactly 9 digits (currently 8)'
 D  app_time_stats: avg=76.51ms min=9.25ms max=172.40ms count=14
 D  app_time_stats: avg=337.15ms min=12.98ms max=500.53ms count=3
 D  validateOrderIdInput: input='*********' -> filtered='*********'
 D  getOrderIdErrorMessage: input='*********', error='null'
 D  app_time_stats: avg=106.98ms min=11.37ms max=499.92ms count=14
 I  Flattened final assist data: 908 bytes, containing 1 windows, 6 views
 D  show(ime(), fromIme=false)
 I  com.autogratuity:be891a37: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:be891a37: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:a10c4b7e: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{b418958 VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  Autofill popup isn't shown because autofill is not available.

Did you set up autofill?
1. Go to Settings > System > Languages&input > Advanced > Autofill Service
2. Pick a service

Did you add an account?
1. Go to Settings > System > Languages&input > Advanced
2. Click on the settings icon next to the Autofill Service
3. Add your account
 D  show(ime(), fromIme=true)
 I  com.autogratuity:a10c4b7e: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=59.05ms min=1.75ms max=372.35ms count=15
 D  🔍 GOOGLE_PLACES_AUTOCOMPLETE_START: Starting autocomplete search for query='5...'
 E  ❌ Failed to get App Check token for Places SDK: Error returned from API. code: 403 body: App attestation failed. (Ask Gemini)
com.google.firebase.FirebaseException: Error returned from API. code: 403 body: App attestation failed.
	at com.google.firebase.appcheck.internal.NetworkClient.makeNetworkRequest(NetworkClient.java:190)
	at com.google.firebase.appcheck.internal.NetworkClient.exchangeAttestationForAppCheckToken(NetworkClient.java:122)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider.lambda$getToken$1$com-google-firebase-appcheck-debug-internal-DebugAppCheckProvider(DebugAppCheckProvider.java:121)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider$$ExternalSyntheticLambda2.call(D8$$SyntheticClass:0)
	at com.google.android.gms.tasks.zzz.run(com.google.android.gms:play-services-tasks@@18.1.0:1)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
	at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
	at java.lang.Thread.run(Thread.java:1012)
 D  tagSocket(138) with statsTag=0xffffffff, statsUid=-1
 D  ✅ GOOGLE_PLACES_AUTOCOMPLETE_SUCCESS: Found 5 predictions for query='5...'
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  app_time_stats: avg=190.83ms min=12.12ms max=1763.63ms count=10
 D  Fetching place details for: 515 Fieldhouse, Northeast Station Crossing Drive, Grimes, IA, USA
 D  🔍 GOOGLE_PLACES_DETAILS_START: Fetching place details for placeId='ChIJS_5xNo4p7IcRDLnUHWycn4Q', prediction='515 Fieldhouse, Northeast Station Crossing Drive, Grimes, IA, USA'
 D  🔍 GOOGLE_PLACES_DETAILS_REQUEST: Created request with fields=ID, DISPLAY_NAME, FORMATTED_ADDRESS, ADDRESS_COMPONENTS, LOCATION, sessionToken present
 E  ❌ Failed to get App Check token for Places SDK: Too many attempts. (Ask Gemini)
com.google.firebase.FirebaseException: Too many attempts.
	at com.google.firebase.appcheck.internal.NetworkClient.exchangeAttestationForAppCheckToken(NetworkClient.java:118)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider.lambda$getToken$1$com-google-firebase-appcheck-debug-internal-DebugAppCheckProvider(DebugAppCheckProvider.java:121)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider$$ExternalSyntheticLambda2.call(D8$$SyntheticClass:0)
	at com.google.android.gms.tasks.zzz.run(com.google.android.gms:play-services-tasks@@18.1.0:1)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
	at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
	at java.lang.Thread.run(Thread.java:1012)
 D  Place details fetched successfully: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA
 I  ✅ GOOGLE_PLACES_DETAILS_SUCCESS: Place details fetched successfully for placeId='ChIJS_5xNo4p7IcRDLnUHWycn4Q', address='1220 NE Station Crossing Dr, Grimes, IA 50111, USA'
 D  Selected place set for display only: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA
 D  ✅ GOOGLE_PLACES_SELECTION_COMPLETE: Place selected and stored for validation at save time
 D  🔄 GOOGLE_PLACES_SESSION_RENEWED: Session token regenerated for next search
 D  app_time_stats: avg=425.74ms min=12.77ms max=2092.16ms count=7
 D  LaunchedEffect triggered with selectedPlace: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA
 D  Address text updated to: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA
 D  show(ime(), fromIme=true)
 I  com.autogratuity:717d829b: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@c65da14
 D  endAllActiveAnimators on 0x758d33a71e30 (UnprojectedRipple) with handle 0x758ca3a87d00
 D  show(ime(), fromIme=false)
 I  com.autogratuity:551499e2: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:551499e2: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  Flattened final assist data: 956 bytes, containing 1 windows, 6 views
 I  com.autogratuity:406865c7: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{b418958 VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  app_time_stats: avg=110.17ms min=5.08ms max=501.82ms count=9
 D  show(ime(), fromIme=true)
 I  com.autogratuity:511ee1fc: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:406865c7: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=105.58ms min=1.82ms max=498.98ms count=13
 D  app_time_stats: avg=194.37ms min=9.26ms max=499.81ms count=6
 D  app_time_stats: avg=25.63ms min=6.60ms max=300.27ms count=39
 D  getOrderIdErrorMessage: input='*********', error='null'
 D  isValidOrderId: input='*********', valid=true
 D  getOrderIdErrorMessage: input='*********', error='null'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  findOrCreateAddressFromPlace (SSoT) - Looking up or creating address for Place ID: ChIJS_5xNo4p7IcRDLnUHWycn4Q, User ID: qOhr05X5ySh5d1s6sqa43vImBE92
 D  No address found for user qOhr05X5ySh5d1s6sqa43vImBE92 with placeId ChIJS_5xNo4p7IcRDLnUHWycn4Q.
 D  Adding new address for placeId ChIJS_5xNo4p7IcRDLnUHWycn4Q via remoteDataSource
 D  addAddress: Creating new address Uooc9WOehbUH5JYJxveq for user qOhr05X5ySh5d1s6sqa43vImBE92 at path: users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq
 D  addAddress: Writing address data:
 D    Document ID: Uooc9WOehbUH5JYJxveq
 D    Data Size: 1286 bytes
 D    Write Data: Address(id=Uooc9WOehbUH5JYJxveq, addressData=AddressData(userId=qOhr05X5ySh5d1s6sqa43vImBE92, fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, normalizedAddress=1220 ne station crossing dr grimes ia 50111 usa, placeId=ChIJS_5xNo4p7IcRDLnUHWycn4Q, isDefault=false, notes=null, tags=[], orderIds=[], searchTerms=[515 Fieldhouse, 1220 NE Station Crossing Dr, Grimes, IA 50111, USA], components=Components(streetNumber=1220, streetName=Northeast Station Crossing Drive, city=Grimes, state=IA, postalCode=50111, country=US), coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), searchFields=SearchFields(searchTerms=[515 Fieldhouse, 1220 NE Station Crossing Dr, Grimes, IA 50111, USA], normalizedKey=null), deliveryStats=Delivery_stats(deliveryCount=null, tipCount=null, totalTips=null, highestTip=null, pendingCount=null, averageTimeMinutes=null, lastDeliveryDate=null, averageTipAmount=null, lastDeliveryTimestamp=null), flags=Flags(isFavorite=null, isVerified=true, doNotDeliver=null, isApartment=null, isArchived=null, hasAccessIssues=null, manualDndState=null, dndSource=null), metadata=Metadata(createdAt=null, updatedAt=null, importedAt=null, source=google_places_api, importId=null, captureId=null, version=null, customData=null), platform=null))
 D  Successfully added new address Uooc9WOehbUH5JYJxveq for user qOhr05X5ySh5d1s6sqa43vImBE92 in 228ms
 D  Fetching newly created address Uooc9WOehbUH5JYJxveq via remoteDataSource
 D  [Firestore] WRITE SET:users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1286bytes Fields:[addressData,id] DataFields:2 [id,addressData]: 228ms [OK]
 I  FIRESTORE WRITE: SET:users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1286bytes Fields:[addressData,id] DataFields:2 [id,addressData] (228ms)
 D  getAddressById: Fetching address Uooc9WOehbUH5JYJxveq for user qOhr05X5ySh5d1s6sqa43vImBE92 from path: users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq
 D  app_time_stats: avg=21.58ms min=13.52ms max=250.79ms count=47
 D  getAddressById: Successfully fetched address for user qOhr05X5ySh5d1s6sqa43vImBE92
 D    Firestore Path: users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq
 D  [Firestore] READ GET_DOCUMENT:users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Results:1 Size:1198bytes Source:SERVER Filters:[address_by_id]: 107ms [OK]
 D    Document Size: 1198 bytes
 D    Firestore Duration: 107ms
 I  FIRESTORE READ: GET_DOCUMENT:users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Results:1 Size:1198bytes Source:SERVER Filters:[address_by_id] (107ms)
 D    Raw Document Data: {id=Uooc9WOehbUH5JYJxveq, addressData={deliveryStats={averageTipAmount=null, averageTimeMinutes=null, pendingCount=null, lastDeliveryTimestamp=null, totalTips=null, highestTip=null, deliveryCount=null, tipCount=null, lastDeliveryDate=null}, components={country=US, streetName=Northeast Station Crossing Drive, city=Grimes, streetNumber=1220, postalCode=50111, state=IA}, metadata={createdAt=null, importId=null, importedAt=null, captureId=null, customData=null, source=google_places_api, version=null, updatedAt=null}, notes=null, searchTerms=[515 Fieldhouse, 1220 NE Station Crossing Dr, Grimes, IA 50111, USA], coordinates={latitude=41.703128, longitude=-93.78315889999999}, flags={archived=null, hasAccessIssues=null, dndSource=null, manualDndState=null, verified=true, doNotDeliver=null, favorite=null, apartment=null}, placeId=ChIJS_5xNo4p7IcRDLnUHWycn4Q, userId=qOhr05X5ySh5d1s6sqa43vImBE92, platform=null, tags=[], default=false, normalizedAddress=1220 ne station crossing dr grimes ia 50111 usa, fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, searchFields={normalizedKey=null, searchTerms=[515 Fieldhouse, 1220 NE Station Crossing Dr, Grimes, IA 50111, USA]}, orderIds=[]}}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 3ms
 D  findOrCreateAddressFromPlace (SSoT) - Successfully found/created and mapped SSoT address: Uooc9WOehbUH5JYJxveq
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1239bytes Fields:7 PII:10 Decrypt:3ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 3ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Scheduling task addDelivery_1749809161060 with priority HIGH
 D  Executing task addDelivery_1749809161060 (HIGH)
 D  validateDelivery: Validating delivery 
 D  validateDelivery: Delivery  is valid
 D  CONVERSION: Delivery SSoT -> DTO | ID:  | User: qOhr05X5ySh5d1s6sqa43vImBE92
 D    Input: orderId=********* | status=COMPLETED | tipAmount=90.0
 D    Converting address: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA... (ID: Uooc9WOehbUH5JYJxveq)
 D    Created missing reference with addressId: Uooc9WOehbUH5JYJxveq
 D  === DELIVERY SSoT -> DTO TRANSFORMATION ===
 D  Input SSoT State: {ssot_id=, ssot_userId=qOhr05X5ySh5d1s6sqa43vImBE92, ssot_orderId=*********, ssot_tipAmount=90.0, ssot_status=COMPLETED, ssot_notes=empty, ssot_addressId=Uooc9WOehbUH5JYJxveq}
 D  Output DTO State: {dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_orderId=*********, dto_tipAmount=90.0, dto_status=COMPLETED, dto_notes=empty, dto_addressId=Uooc9WOehbUH5JYJxveq}
 D  Field Transformations: [notes: ENCRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, times: TIMESTAMP_MAPPING]
 D  Business Logic Applied: [notes_encryption, field_validation, address_plain_mapping, status_mapping, amounts_calculation]
 D  PII Fields Encrypted: 0
 D  Mapping Duration: 3ms
 D  CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: 
 D    Output: encrypted_fields=0 | validation_errors=0 | duration=3ms
 D    DTO size: 1173 bytes
 D    Business logic: address_mapping, status_mapping, amounts_calculation
 D  executeAddDeliveryOperation: Adding delivery with transaction for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DeliveryMapper] toDto Delivery ID: User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1173bytes Delivery: 3ms [OK]
 W  Cleared Reference was only reachable from finalizer (only reported once)
 D  Attempting to add new delivery (from map) and update stats for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  isAssociateAddressIfNotFound: false, rawAddressDetails provided: false
 D  Delivery (from map) added to transaction with ID: aysMyu7QjExLMl4eb4Ji
 D  First delivery to address Uooc9WOehbUH5JYJxveq - incrementing address count for qOhr05X5ySh5d1s6sqa43vImBE92.
 D  Incrementing user profile delivery count (both usage and usageStats) for qOhr05X5ySh5d1s6sqa43vImBE92.
 D  Incremented address stats for Uooc9WOehbUH5JYJxveq.
 I  Successfully added new delivery (from map) with ID: aysMyu7QjExLMl4eb4Ji and updated all stats for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  executeAddDeliveryOperation: Delivery added with transaction, ID aysMyu7QjExLMl4eb4Ji for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Saved delivery aysMyu7QjExLMl4eb4Ji for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeAddDeliveryOperation: Delivery added aysMyu7QjExLMl4eb4Ji (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818, user: qOhr05X5ySh5d1s6sqa43vImBE92)
 D  Task addDelivery_1749809161060 completed in 1082ms
 D  observeDeliveriesByUserId: Emitting 1 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Cached delivery aysMyu7QjExLMl4eb4Ji with atomic cache system
 D  [data.DeliveryRepository] addDelivery(Delivery) ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1173bytes Source:transaction Strategy:write-through [CACHE_MISS]: 1081ms [OK]
 D  Deduplication: Original 1 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:1ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 1ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:142294ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:142294ms [CACHE_HIT] [POOR_UX]: 142294ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 142294ms (user-visible)
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 D  app_time_stats: avg=47.58ms min=14.31ms max=499.54ms count=25
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818)
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 4ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 7ms, result: true
 D  User profile cache warming successful in 8ms
 D  Closed previous session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818
 I  SESSION STARTED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217 with cache warming and app state monitoring
 I  SESSION ENDED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818 with 12 operations
 D  [SESSION] Started: qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217 [TRACKING]
 I  Created new session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  SESSION: Repository initialization state: true for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 D  SESSION: UI performance monitoring started for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 6ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 I  CACHE WARMING: Starting real cache warming for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 9ms, result: true
 D  Subscription cache warming successful in 10ms
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  [CACHE_WARMING] Starting critical data preload for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 2ms [OK]
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217 (age: 7ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 4ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 I  SESSION FINAL METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=1, completedDeliveries=1, pendingTips=0, totalCachedTips=90.0, uniqueUsers=1, uniqueAddresses=0, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=1, misses=0, puts=1, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=14, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=20), defaultTtl=1d, enableMetrics=true, hitRate=0.7, missRate=0.3, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=4, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=6), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  [SESSION_END] Final cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=1, completedDeliveries=1, pendingTips=0, totalCachedTips=90.0, uniqueUsers=1, uniqueAddresses=0, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=1, misses=0, puts=1, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=14, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=20), defaultTtl=1d, enableMetrics=true, hitRate=0.7, missRate=0.3, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=4, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=6), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:4ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 4ms [OK]
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217)
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 7ms, result: true
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 4ms [OK]
 I  CACHE WARMING: Subscription loaded successfully in 16ms
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 13ms
 I  CACHE WARMING: User profile loaded successfully in 17ms
 D  [CACHE_WARMING] CriticalDataSystems for user qOhr05X5ySh5d1s6sqa43vImBE92: 2 ops in 19ms [PRELOAD]
 I  CACHE WARMING: Completed for user qOhr05X5ySh5d1s6sqa43vImBE92 in 19ms
 I  CACHE WARMING: Results - subscription:success:16ms, user_profile:success:17ms
 D  DEDUPLICATION: Starting new request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  executeGetRecentDeliveriesOperation: Fetching 10 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [CACHE_WARMING] Completed for user qOhr05X5ySh5d1s6sqa43vImBE92: subscription:success:16ms, user_profile:success:17ms
 D  Retrieved 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Fetching from remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [correlation.SessionTracker] correlation Trigger:session_start Related:2 Total:19ms: 19ms [OK]
 I  OPERATION CORRELATION in qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217: session_start -> subscription_preload, user_profile_preload (19ms)
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1289bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1289bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 128ms, result: true
 D  Addresses cache warming successful in 128ms
 I  CACHE WARMING: Addresses loaded in 98ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:8ms, subscription:success:10ms, addresses:success:128ms, total:130ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation - Found 1 deliveries in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809074818, count: 1)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  ✅ Deliveries loaded successfully: 1 (deduplicated from 1)
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065410Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065488Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065410Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065488Z
 D  getAllDeliveries: Found 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92 (1 modern, 0 legacy)
 D  Created domain delivery - ID: aysMyu7QjExLMl4eb4Ji, details null: false
 D  Domain details - userId: qOhr05X5ySh5d1s6sqa43vImBE92, orderId: *********, status: null, times null: false
 D  Domain times - completedAt null: false
 D  About to validate domain delivery - delivery.details null: false
 D  Pre-validation - Delivery identity: 182945097, details identity: 245771854
 D  Validation entry - Delivery identity: 182945097, details identity: 245771854
 W  Delivery validation failed: delivery.details is actually null for delivery aysMyu7QjExLMl4eb4Ji
 D  Delivery validation complete: 1 errors, 0 warnings
 W  Domain Delivery validation failed in mapper for delivery aysMyu7QjExLMl4eb4Ji: [Delivery details are required]
 D  === DELIVERY MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=aysMyu7QjExLMl4eb4Ji, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_orderId=*********, dto_addressId=Uooc9WOehbUH5JYJxveq, dto_tipAmount=90.0, dto_status=COMPLETED, dto_notes=empty}
 D  Output Domain State: {domain_id=aysMyu7QjExLMl4eb4Ji, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_orderId=*********, domain_addressId=Uooc9WOehbUH5JYJxveq, domain_tipAmount=90.0, domain_status=COMPLETED, domain_notes=empty}
 D  Field Transformations: [notes: DECRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, timestamps: DATE_PARSING]
 D  Business Logic Applied: [notes_decryption, field_validation, address_plain_mapping, tip_calculation, status_mapping, timestamp_conversion]
 D  PII Fields Processed: 2
 D  Mapping Duration: 1ms
 D  Cached delivery list: deliveries_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  Saved 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Cached 1 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Remote fetch for recent deliveries (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217, user: qOhr05X5ySh5d1s6sqa43vImBE92, limit: 10, count: 1)
 D  DEDUPLICATION: Cleaned up completed request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  [DeliveryMapper] toSsot(Delivery) ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1153bytes Fields:7 PII:2 Decrypt:1ms Logic:[notes_decryption,field_validation,address_plain_mapping,tip_calculation,status_mapping,timestamp_conversion] Issues:1 Delivery: 1ms [WARN] (1 issues)
 W  SSOT/DTO Alignment Issue in DeliveryMapper for Delivery:
 W     Validation Errors: Domain validation: Delivery details are required
 D  DEDUPLICATION: Completed new request 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10' in 226ms, result: true
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  CACHE WARMING: Deliveries loaded in 226ms
 D  Cached delivery ********* with atomic cache system
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:142608ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:142608ms [CACHE_HIT] [POOR_UX]: 142608ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 142608ms (user-visible)
 D  hide(ime(), fromIme=false)
 I  com.autogratuity:44dbbb15: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:a2745936: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_REQUEST_HIDE_WITH_CONTROL fromUser false
 W  requestCursorUpdates on inactive InputConnection
 D  hide(ime(), fromIme=true)
 I  com.autogratuity:a2745936: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=3.71ms min=1.22ms max=30.53ms count=61
 I  com.autogratuity:5942aaaf: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
 I  com.autogratuity:44dbbb15: onHidden
 I  Making request to: https://firebaselogging.googleapis.com/v0cc/log/batch?format=json_proto3
 D  tagSocket(140) with statsTag=0xffffffff, statsUid=-1
 I  Status Code: 200
 D  app_time_stats: avg=33744.46ms min=4.17ms max=67484.77ms count=2
 D  ✅ MODERN DeliveryDialogViewModel initialized with domain models
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  User session ended - clearing state
 D  User session ended - clearing dialog state
 D  Loading delivery details...
 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@cf3ccfd
 D  app_time_stats: avg=157.74ms min=1.82ms max=7151.54ms count=50
 D  Navigating to destination: dashboard
 D  🚀 HERO DASHBOARD: Starting render at 1749809238978
 D  User session ready - initializing dashboard data
 I  SYSTEM HEALTH CHECK: Starting comprehensive monitoring
 I  SYSTEM STATE:
 I  - Repository Initialized: true
 I  - User Session Ready: false
 I  - Core Data Ready: true
 D  🔍 STATE CHANGED: showAddressDetails = false, selectedAddressId = null
 I  SYSTEM COMPONENTS:
 I  - AuthManager: AuthenticationManagerImpl
 I  - EncryptionUtils: EncryptionUtils
 I  - FirebaseAuth: zzad
 I  - CacheLifecycleManager: CacheLifecycleManager
 I  CACHE METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=180.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=4, misses=0, puts=3, removes=0, expiredCleanups=0, totalOperations=4), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=90.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=15, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=21), defaultTtl=1d, enableMetrics=true, hitRate=0.7142857142857143, missRate=0.2857142857142857, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=6, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=8), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 I  UI PERFORMANCE MONITORING: Started/Verified
 D  [SYSTEM_HEALTH] Repository:true UserSession:false CoreData:true
 D  [SYSTEM_HEALTH] Cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=180.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=4, misses=0, puts=3, removes=0, expiredCleanups=0, totalOperations=4), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=90.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=15, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=21), defaultTtl=1d, enableMetrics=true, hitRate=0.7142857142857143, missRate=0.2857142857142857, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=6, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=8), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 I  SYSTEM HEALTH CHECK: Completed successfully
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Repository initialization state: true
 D  UI frame monitoring started
 D  🔗 Auth state: User authenticated (qOhr05X5ySh5d1s6sqa43vImBE92)
 D  Closed previous session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUser: Authentication ready for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:2 [EFFICIENT]: 0ms [OK]
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  SESSION: Repository initialization state: true for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809239031
 D  🚀 HERO PERFORMANCE: Starting priority cache warming for instant map
 I  SESSION ENDED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809162217 with 13 operations
 D  Scheduling task DashboardMapCacheWarming with priority CRITICAL
 D  Executing task DashboardMapCacheWarming (CRITICAL)
 I  SESSION FINAL METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=180.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=6, misses=0, puts=3, removes=0, expiredCleanups=0, totalOperations=6), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=90.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=15, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=21), defaultTtl=1d, enableMetrics=true, hitRate=0.7142857142857143, missRate=0.2857142857142857, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=6, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=8), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  🗺️ Testing simple geocoding for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  🧪 Testing native geocoding with: 1600 Amphitheatre Parkway, Mountain View, CA
 I  SESSION STARTED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239031 with cache warming and app state monitoring
 D  [SESSION] Started: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239031 [TRACKING]
 I  Created new session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  🗺️ Loading addresses from repository for map display
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  CACHE WARMING: Starting real cache warming for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [CACHE_WARMING] Starting critical data preload for user: qOhr05X5ySh5d1s6sqa43vImBE92
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  [SESSION_END] Final cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=180.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=6, misses=0, puts=3, removes=0, expiredCleanups=0, totalOperations=6), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=90.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=15, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=21), defaultTtl=1d, enableMetrics=true, hitRate=0.7142857142857143, missRate=0.2857142857142857, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=6, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=8), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  SESSION: UI performance monitoring started for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809239031
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030)
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription', waiting for result
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030)
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  loadPage: Loading address page for user qOhr05X5ySh5d1s6sqa43vImBE92 with size: 50, key: null
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  loadAddressPage: Loading address page with size: 50, key: null
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030)
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030)
 D  DEDUPLICATION: Found existing request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription', waiting for result
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  loadAddressPage: Executing Firestore query for addresses
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030)
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030)
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 18ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 7ms, result: true
 D  User profile cache warming successful in 8ms
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 27ms, result: true
 D  User profile cache warming successful in 28ms
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 10ms [OK]
 D  [cache_system.UserRepository] cache_breakdown Check:6ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 6ms [OK]
 D  [cache_system.UserRepository] cache_breakdown Check:13ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 13ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 13ms
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 16ms [OK]
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 15ms, result: true
 D  User profile cache warming successful in 21ms
 D  [data.UserRepository] getCurrentUser(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 35ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 20ms
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 19ms [OK]
 I  CACHE WARMING: User profile loaded successfully in 25ms
 D  [cache_system.UserRepository] cache_breakdown Check:5ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 5ms [OK]
 D  Starting backpressure handling for dashboard_delivery_observation with THROTTLE_FIRST
 D  observeDeliveriesByUserId: Starting observation for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 69ms, result: true
 D  ✅ SESSION MANAGER: Created/retrieved session qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030 (age: 67ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeUserProfile Load:68ms Steps:[repository_call,data_mapping,state_emission] UserDelay:68ms [CACHE_HIT] [FAST_UX]: 68ms [OK]
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 3ms, result: true
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:3 Size:1299bytes [EFFICIENT]: 0ms [OK]
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 66ms, result: true
 D  Subscription cache warming successful in 67ms
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030 (age: 74ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 57ms, result: true
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 50ms, result: true
 D  Subscription cache warming successful in 52ms
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Subscription cache warming successful in 58ms
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 4ms, result: true
 I  CACHE WARMING: Subscription loaded successfully in 67ms
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  [CACHE_WARMING] CriticalDataSystems for user qOhr05X5ySh5d1s6sqa43vImBE92: 2 ops in 81ms [PRELOAD]
 D  DEDUPLICATION: Starting new request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  executeGetRecentDeliveriesOperation: Fetching 10 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 I  CACHE WARMING: Completed for user qOhr05X5ySh5d1s6sqa43vImBE92 in 81ms
 D  [correlation.SessionTracker] correlation Trigger:session_start Related:2 Total:81ms: 81ms [OK]
 I  CACHE WARMING: Results - user_profile:success:25ms, subscription:success:67ms
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Fetching from remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  OPERATION CORRELATION in qOhr05X5ySh5d1s6sqa43vImBE92_1749809239031: session_start -> user_profile_preload, subscription_preload (81ms)
 D  [CACHE_WARMING] Completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:25ms, subscription:success:67ms
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:96ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:96ms [CACHE_HIT] [FAST_UX]: 96ms [OK]
 D  Native geocoding success: 1600 Amphitheatre Parkway, Mountain View, CA -> 37.4207967, -122.0852688
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065410Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065488Z
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1289bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1289bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  CACHE WARMING: Addresses loaded in 550ms
 D  DEDUPLICATION: Completed waiting for 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 606ms, result: true
 D  Addresses cache warming successful in 609ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:8ms, subscription:success:52ms, addresses:success:609ms, total:614ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 623ms, result: true
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation - Found 2 deliveries in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030, count: 2)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1289bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 3ms, result: true
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Addresses cache warming successful in 632ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:28ms, subscription:success:67ms, addresses:success:632ms, total:636ms
 D  loadAddressPage: Successfully loaded 1 DTOs (614ms total, query: 603ms)
 D  [Firestore] QUERY user_addresses: 603ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  loadPage: Successfully loaded 1 addresses
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1289bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [data.AddressPageLoader] loadPage(Address) User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Source:RemoteDataSource Strategy:none [CACHE_MISS]: 620ms [OK]
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1289bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getCurrentUser: Cache warming initiated for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 629ms, result: true
 D  Addresses cache warming successful in 629ms
 D  Fallback: Loaded 1 addresses from repository
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:WEEK User:stats_calculation Size:2bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:21ms, subscription:success:58ms, addresses:success:629ms, total:635ms
 D  🗺️ MAP-CRITICAL WARMING COMPLETE: 639ms - addresses: Success(cachedAt=1749809239675, durationMs=629), user: Success(cachedAt=1749809239068, durationMs=21)
 D  🎯 SIMPLE GEOCODING: Testing native geocoding capability
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:MONTH User:stats_calculation Size:2bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:TODAY User:stats_calculation Size:2bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  🚀 MAP DATA LOADED: 1 addresses in 713ms
 D  🚀 INSTANT: Map data loaded, hiding skeleton immediately
 D  preferredRenderer: null
 D  preferredRenderer: null
 I  Making Creator dynamically
 I  Considering local module com.google.android.gms.maps_core_dynamite:0 and remote module com.google.android.gms.maps_core_dynamite:251625202
 I  Selected remote version of com.google.android.gms.maps_core_dynamite, version >= 251625202
 V  Dynamite loader version >= 2, using loadModule2NoCrashUtils
 D  Compat change id reported: 312399441; UID 10209; state: ENABLED
 W  Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.dm': No such file or directory
 W  Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.dm': No such file or directory
 D  app_time_stats: avg=16.91ms min=8.62ms max=23.70ms count=59
 D  early loading native code
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065410Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065488Z
 D  getAllDeliveries: Found 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92 (1 modern, 0 legacy)
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Created domain delivery - ID: aysMyu7QjExLMl4eb4Ji, details null: false
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Domain details - userId: qOhr05X5ySh5d1s6sqa43vImBE92, orderId: *********, status: null, times null: false
 D  Domain times - completedAt null: false
 D  About to validate domain delivery - delivery.details null: false
 D  Pre-validation - Delivery identity: 151142121, details identity: 106297198
 D  Validation entry - Delivery identity: 151142121, details identity: 106297198
 W  Delivery validation failed: delivery.details is actually null for delivery aysMyu7QjExLMl4eb4Ji
 D  Delivery validation complete: 1 errors, 0 warnings
 W  Domain Delivery validation failed in mapper for delivery aysMyu7QjExLMl4eb4Ji: [Delivery details are required]
 D  === DELIVERY MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=aysMyu7QjExLMl4eb4Ji, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_orderId=*********, dto_addressId=Uooc9WOehbUH5JYJxveq, dto_tipAmount=90.0, dto_status=COMPLETED, dto_notes=empty}
 D  Output Domain State: {domain_id=aysMyu7QjExLMl4eb4Ji, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_orderId=*********, domain_addressId=Uooc9WOehbUH5JYJxveq, domain_tipAmount=90.0, domain_status=COMPLETED, domain_notes=empty}
 D  Field Transformations: [notes: DECRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, timestamps: DATE_PARSING]
 D  Business Logic Applied: [notes_decryption, field_validation, address_plain_mapping, tip_calculation, status_mapping, timestamp_conversion]
 D  PII Fields Processed: 2
 D  Mapping Duration: 1ms
 D  Cached delivery list: deliveries_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  Saved 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Cached 1 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Remote fetch for recent deliveries (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030, user: qOhr05X5ySh5d1s6sqa43vImBE92, limit: 10, count: 1)
 D  DEDUPLICATION: Cleaned up completed request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  DEDUPLICATION: Completed new request 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10' in 757ms, result: true
 D  🗺️ INSTANT MAP DATA: Loaded 1 addresses from warmed cache
 I  CACHE WARMING: Deliveries loaded in 758ms
 D  Complete cache warming: 836ms total (warming: 639ms)
 D  [DeliveryMapper] toSsot(Delivery) ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1153bytes Fields:7 PII:2 Decrypt:1ms Logic:[notes_decryption,field_validation,address_plain_mapping,tip_calculation,status_mapping,timestamp_conversion] Issues:1 Delivery: 1ms [WARN] (1 issues)
 W  SSOT/DTO Alignment Issue in DeliveryMapper for Delivery:
 W     Validation Errors: Domain validation: Delivery details are required
 D  Cached delivery ********* with atomic cache system
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1289bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 W  Loading native library 'gmm-jni' on thread main
 D  Configuring clns-12 for other apk . target_sdk_version=35, uses_libraries=ALL, library_path=/data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.apk!/lib/x86_64:/data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
 D  Load /data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.apk!/lib/x86_64/libgmm-jni.so using isolated ns clns-12 (caller=/data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.apk): ok
 I  I0000 00:00:1749809239.924415   11841 jni_init.cc:30] Initializing JNI...
 I  JNI initialized.
 I  Google Play services client version: 19020000
 D  SDK type: 1, version: 251625202
 D  maps_core_dynamite module version in use (0 represents standalone library): 251625202
 D  Added event: 109
 D  Added event: 112
 D  loadedRenderer: LATEST
 D  preferredRenderer: null
 D  SDK type: 1, version: 251625202
 I  Google Play services package version: 252037038
 I  Google Play services maps renderer version(maps_core): 251625202
 D  SDK type: 1, version: 251625202
 D  about to start loading native library asynchronously
 W  Suppressed StrictMode policy violation: StrictModeDiskReadViolation
 I  Using GMM server: https://clients4.google.com/glm/mmap
 W  Suppressed StrictMode policy violation: StrictModeDiskReadViolation
 W  Suppressed StrictMode policy violation: StrictModeDiskWriteViolation
 D  Using Non-null serverVersionMetadataManager to load previous metadata.
 W  Suppressed StrictMode policy violation: StrictModeDiskReadViolation
 D  Previous session server version metadata loaded: CggIBhCI76XCBgoICAMQoJmawgYKCggEEPuKqsIGGAEKCAgBEJKRncIG
 D  Compat change id reported: 63938206; UID 10209; state: ENABLED
 I  Compiler allocated 4219KB to compile void android.widget.TextView.<init>(android.content.Context, android.util.AttributeSet, int, int)
 I  getExecutor  CREATED  boc@3ee2dd[main]
 I  getExecutor  CREATED  ScheduledThreadPoolExecutor@7fdda20[Scheduler]
 I  getExecutor  CREATED  bof@44bcfd9[Background]
 I  marker files=[]
 I  getExecutor  CREATED  bof@67a6077[Lite]
 I  getExecutor  CREATED  bof@e8984e4[Blocking]
 I  getExecutor  CREATED  bof@c2e4d5a[TilePrep]
 I  Loaded cached client parameters
 I  finishInitialization ClientParametersManager.
 I  start()  account=Account {name=signedout@, type=com.google.android.apps.maps}  locale=en_US
 I  P/H: Scheduling next update in 8508.044000 seconds: initial refresh
 I  FpsProfiler MAIN created on main
 I  getExecutor  CREATED  bof@eb304db[Picker]
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 W  No current context - attempting to create off-screen context
 I  Created GlConstants: faa{gpuVendor=Google (NVIDIA Corporation), glVersion=OpenGL ES 3.1 (4.5.0 NVIDIA 571.96), glRenderer=Android Emulator OpenGL ES Translator (NVIDIA GeForce RTX 3060 Ti/PCIe/SSE2), maxTextureSize=32768, maxVertexTextureImageUnits=32, maxVertexUniformVectors=1024, maxSupportedLineWidth=10, maxVertexAttribs=16, nonPowerOfTwoTextureSupport=FULL}
 I  Map using legacy labeler
 I  Create or open database: /storage/emulated/0/Android/data/com.autogratuity/cache/diskcache; secure file path: /data/user/0/com.autogratuity/app_/testdata
 I  Network fetching: false
 I  Current epoch is now 736
 I  requestDrawingConfig for epoch 736 legend ROADMAP
 I  Database cache size: 4788224 bytes
 I  Model SDK_GPHONE64_X86_64, Product name SDK_GPHONE64_X86_64, Board name GOLDFISH_X86_64, Manufacturer GOOGLE
 W  Model is not recognized, and therefore using default settings.
 E  Failed to delete expired resources: (Ask Gemini)
m.eth: m.ayj: Database lock unavailable {canonicalCode=UNAVAILABLE, loggedCode=0, posixErrno=0}
	at com.google.android.libraries.geo.mapcore.internal.store.diskcache.NativeSqliteDiskCacheImpl.flushWrites(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:10)
	at m.eto.i(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:29)
	at m.etl.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:46)
	at m.gxz.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at m.bni$a.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:23)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at m.bnx.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:40)
	at java.lang.Thread.run(Thread.java:1012)
Caused by: m.ayj: Database lock unavailable {canonicalCode=UNAVAILABLE, loggedCode=0, posixErrno=0}
	at com.google.android.libraries.geo.mapcore.internal.store.diskcache.NativeSqliteDiskCacheImpl.nativeSqliteDiskCacheFlushWrites(Native Method)
	at com.google.android.libraries.geo.mapcore.internal.store.diskcache.NativeSqliteDiskCacheImpl.flushWrites(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:3)
	at m.eto.i(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:29) 
	at m.etl.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:46) 
	at m.gxz.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:68) 
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
	at m.bni$a.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:23) 
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
	at m.bnx.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:40) 
	at java.lang.Thread.run(Thread.java:1012) 
 I  Network fetching: true
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 I  requestDrawingConfig for epoch 736 legend ROADMAP
 I  styleTableCache inserted: 736 ROADMAP https://www.gstatic.com/maps/res/CompactLegend-Roadmap-cd3901dbe783483cd2a9161b23923afe
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 W  JNI critical lock held for 25.076ms on Thread[134,tid=12316,Runnable,Thread*=0x758c23b79ec0,peer=0x15167570,"androidmapsapi-TilePrep_1"]
 D  app_time_stats: avg=12.01ms min=1.61ms max=43.00ms count=61
 D  tagSocket(145) with statsTag=0x99e6744e, statsUid=-1
 I  Skipped 74 frames!  The application may be doing too much work on its main thread.
 I  Network fetching: true
 I  Network fetching: true
 I  Network fetching: true
 D  Task DashboardMapCacheWarming completed in 1952ms
 D  🚀 PRIORITY TASK SUCCESS: Cache warming and maps pre-fetch completed successfully
 I  Davey! duration=1290ms; Flags=0, FrameTimelineVsyncId=9416750, IntendedVsync=145397312921610, Vsync=145397312921610, InputEventId=0, HandleInputStart=145397314414700, AnimationStart=145397314430400, PerformTraversalsStart=145398504767400, DrawStart=145398521179500, FrameDeadline=145398596254892, FrameInterval=145397314388700, FrameStartTime=16666666, SyncQueued=145398552585600, SyncStart=145398558125000, IssueDrawCommandsStart=145398558430300, SwapBuffers=145398607656600, FrameCompleted=145398608786100, DequeueBufferDuration=14200, QueueBufferDuration=473100, GpuCompleted=145398608653200, SwapBuffersCompleted=145398608786100, DisplayPresentTime=0, CommandSubmissionCompleted=145398607656600, 
 D  🚀 BACKGROUND REFRESH: Skipping update, data already loaded
 D  🏠 HOME POSITION SET: lat/lng: (41.703128,-93.78315889999999) at zoom 15.0
 I  Compiler allocated 5627KB to compile void m.eph.o()
 I  Compiler allocated 4573KB to compile void m.ecn.c(m.djs, m.ebf, m.edi, m.ekf, boolean)
 D  🏠 HOME POSITION: Stored position lat/lng: (41.703128,-93.78315889999999) at zoom 15.0
 I  Compiler allocated 5046KB to compile void m.eho.W(m.ely, m.fdw, android.content.res.Resources, m.ejt, m.eum, boolean, m.dje, java.util.Map, boolean, boolean, boolean, boolean)
 I  getExecutor  CREATED  bof@2771f2a[Network]
 I  Using Paint server URL: https://www.google.com/maps/vt
 I  getExecutor  CREATED  bof@c3ad3f7[LocFresh]
 W  using the fallback Cronet Engine implementation. Performance will suffer and many HTTP client features, including caching, will not work.
 I  PaintParametersListeningServerChannelManager got a client parameters update event.
 I  Updating the server URL. Old URL: https://www.google.com/maps/vt New URL: https://www.google.com/maps/vt
 I  Creating a new server channel with URL: https://www.google.com/maps/vt, server channel factory class: class m.brz
 D  tagSocket(182) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(213) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(210) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(211) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(146) with statsTag=0xffffffff, statsUid=-1
 D  app_time_stats: avg=9.85ms min=1.66ms max=109.48ms count=47
 I  Compiler allocated 4278KB to compile m.elu m.ely.l(m.azn, m.hzf, m.elm, byte[], boolean, m.cdf, m.dev, java.lang.Iterable)
 W  Local module descriptor class for com.google.android.gms.googlecertificates not found.
 I  Considering local module com.google.android.gms.googlecertificates:0 and remote module com.google.android.gms.googlecertificates:7
 I  Selected remote version of com.google.android.gms.googlecertificates, version >= 7
 W  ClassLoaderContext classpath size mismatch. expected=1, found=9 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar*437079508]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]} | DLC[];PCL[/data/data/com.autogratuity/code_cache/.overlay/base.apk/classes11.dex***********:/data/data/com.autogratuity/code_cache/.overlay/base.apk/classes13.dex***********:/data/data/com.autogratuity/code_cache/.overlay/base.apk/classes14.dex***********:/data/data/com.autogratuity/code_cache/.overlay/base.apk/classes17.dex***********:/data/data/com.autogratuity/code_cache/.overlay/base.apk/classes3.dex***********:/data/data/com.autogratuity/code_cache/.overlay/base.apk/classes5.dex***********:/data/data/com.autogratuity/code_cache/.overlay/base.apk/classes6.dex***********:/data/data/com.autogratuity/code_cache/.overlay/base.apk/classes7.dex***********:/data/app/~~Up1dII4jRpGbugcMnYMmcg==/com.autogratuity-Sy1XG925L-oH2f9rXWU3qg==/base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar*437079508]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]})
 D  app_time_stats: avg=25.70ms min=2.99ms max=147.18ms count=40
 I  Initial labeling completed.
 D  🚀 MAP READY: Maps Compose initialized and ready
 D  🚀 MAP LOADED: 1 markers ready
 D  🔍 CLUSTER ITEM CLICKED: title=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, addressId=Uooc9WOehbUH5JYJxveq
 D  🔍 CALLING onAddressClick with addressId: Uooc9WOehbUH5JYJxveq
 D  🔍 MAP MARKER CLICKED: addressId = Uooc9WOehbUH5JYJxveq
 D  🔍 CURRENT STATE: selectedAddressId = null, showAddressDetails = false
 D  🔍 STATE UPDATED: selectedAddressId = Uooc9WOehbUH5JYJxveq, showAddressDetails = true
 D  🔍 SHOWING ModalBottomSheet for addressId: Uooc9WOehbUH5JYJxveq
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  ✅ MODERN AddressDetailsViewModel initialized with domain models
 D  🔍 BOTTOM SHEET CREATED for addressId: Uooc9WOehbUH5JYJxveq
 D  🔍 STATE CHANGED: showAddressDetails = true, selectedAddressId = Uooc9WOehbUH5JYJxveq
 D  🔍 LAUNCHED EFFECT STARTED for addressId: Uooc9WOehbUH5JYJxveq
 D  🔍 Loading address immediately for addressId: Uooc9WOehbUH5JYJxveq
 D  🔍 loadAddress called for ID: Uooc9WOehbUH5JYJxveq
 D  ⏳ Loading address with ID: Uooc9WOehbUH5JYJxveq
 D  🔍 SESSION STATE: isReady = false
 D  🔍 User session ended - clearing state
 D  User session ended - clearing address state
 D  ⏳ Loading address: Uooc9WOehbUH5JYJxveq
 D  🔍 BOTTOM SHEET CREATED for addressId: Uooc9WOehbUH5JYJxveq
 D  getAddressById: Address Uooc9WOehbUH5JYJxveq not in cache or local fetch failed for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  getAddressById: Fetching address Uooc9WOehbUH5JYJxveq for user qOhr05X5ySh5d1s6sqa43vImBE92 from path: users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq
 D  getAddressById: Successfully fetched address for user qOhr05X5ySh5d1s6sqa43vImBE92
 D    Firestore Path: users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq
 D  [Firestore] READ GET_DOCUMENT:users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Results:1 Size:1335bytes Source:SERVER Filters:[address_by_id]: 108ms [OK]
 I  FIRESTORE READ: GET_DOCUMENT:users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Results:1 Size:1335bytes Source:SERVER Filters:[address_by_id] (108ms)
 D    Document Size: 1335 bytes
 D    Firestore Duration: 108ms
 D    Raw Document Data: {id=Uooc9WOehbUH5JYJxveq, addressData={deliveryStats={averageTipAmount=90, averageTimeMinutes=null, pendingCount=null, lastDeliveryTimestamp=Timestamp(seconds=1749809160, nanoseconds=511000000), totalTips=90, highestTip=90, deliveryCount=1, tipCount=1, lastDeliveryDate=2025-06-13T05:06:01.894954-05:00}, metadata={createdAt=null, importId=null, importedAt=null, customData=null, captureId=null, source=google_places_api, version=1, updatedAt=Timestamp(seconds=1749809251, nanoseconds=341000000)}, components={country=US, streetName=Northeast Station Crossing Drive, streetNumber=1220, city=Grimes, postalCode=50111, state=IA}, notes=null, searchTerms=[515 Fieldhouse, 1220 NE Station Crossing Dr, Grimes, IA 50111, USA], coordinates={latitude=41.703128, longitude=-93.78315889999999}, placeId=ChIJS_5xNo4p7IcRDLnUHWycn4Q, flags={archived=null, hasAccessIssues=null, dndSource=null, isVerified=false, manualDndState=null, verified=true, doNotDeliver=false, favorite=null, apartment=null}, userId=qOhr05X5ySh5d1s6sqa43vImBE92, platform=null, tags=[], default=false, normalizedAddress=1220 ne station crossing dr grimes ia 50111 usa, fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, searchFields={normalizedKey=null, searchTerms=[515 Fieldhouse, 1220 NE Station Crossing Dr, Grimes, IA 50111, USA]}, orderIds=[*********]}}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  getAddressById: Fetched address Uooc9WOehbUH5JYJxveq from remote and updated cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [cache_system.AddressRepository] cache_breakdown Check:0ms Remote:111ms Map:1ms Store:0ms Hit:false ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92: 112ms [OK]
 D  🔍 Repository result type: Success
 D  ✅ Successfully loaded address: Uooc9WOehbUH5JYJxveq - 1220 NE Station Crossing Dr, Grimes, IA 50111, USA...
 D  🔍 BOTTOM SHEET CREATED for addressId: Uooc9WOehbUH5JYJxveq
 D  ✅ Successfully loaded address: Uooc9WOehbUH5JYJxveq
 D  🔍 BOTTOM SHEET CREATED for addressId: Uooc9WOehbUH5JYJxveq
 D  🔍 DELIVERY STATS: deliveryCount=1, totalTips=null, averageTipAmount=null, tipCount=1
 D  🔍 CLOUD-CALCULATED STATS: deliveries=1, tips=1, totalTips=0.0, cloudAvgTip=0.0, calculatedTipRate=100.0%
 D  🔍 DELIVERY STATS: deliveryCount=1, totalTips=null, averageTipAmount=null, tipCount=1
 D  🔍 CLOUD-CALCULATED STATS: deliveries=1, tips=1, totalTips=0.0, cloudAvgTip=0.0, calculatedTipRate=100.0%
 D  app_time_stats: avg=552.18ms min=1.14ms max=17517.58ms count=32
 D  🔍 MODAL BOTTOM SHEET DISMISSED
 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@21efadf
 D  🔍 STATE CHANGED: showAddressDetails = false, selectedAddressId = null
 D  app_time_stats: avg=5816.93ms min=2.21ms max=69498.40ms count=12
 D  Navigating to destination: deliveries
 W  Destroying egl surface
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Closed previous session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030
 I  SESSION STARTED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851 with cache warming and app state monitoring
 D  [SESSION] Started: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851 [TRACKING]
 I  Created new session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 I  CACHE WARMING: Starting real cache warming for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 D  SESSION: Repository initialization state: true for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 D  SESSION: UI performance monitoring started for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 2ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [CACHE_WARMING] Starting critical data preload for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 6ms, result: true
 D  Subscription cache warming successful in 7ms
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 4ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 6ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 13ms, result: true
 D  User profile cache warming successful in 14ms
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 I  SESSION ENDED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239031 with 30 operations
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 I  SESSION FINAL METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=180.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=14, misses=1, puts=9, removes=0, expiredCleanups=0, totalOperations=15), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=19, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=25), defaultTtl=1d, enableMetrics=true, hitRate=0.76, missRate=0.24, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=9, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=11), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851 (age: 13ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  [SESSION_END] Final cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=180.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=14, misses=1, puts=9, removes=0, expiredCleanups=0, totalOperations=15), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=19, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=25), defaultTtl=1d, enableMetrics=true, hitRate=0.76, missRate=0.24, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=9, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=11), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 9ms
 D  [cache_system.UserRepository] cache_breakdown Check:2ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 2ms [OK]
 I  CACHE WARMING: User profile loaded successfully in 10ms
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 2ms, result: true
 I  CACHE WARMING: Subscription loaded successfully in 13ms
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:1ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 1ms [OK]
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 7ms [OK]
 D  [CACHE_WARMING] CriticalDataSystems for user qOhr05X5ySh5d1s6sqa43vImBE92: 2 ops in 19ms [PRELOAD]
 D  DEDUPLICATION: Starting new request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 I  CACHE WARMING: Completed for user qOhr05X5ySh5d1s6sqa43vImBE92 in 19ms
 I  CACHE WARMING: Results - user_profile:success:10ms, subscription:success:13ms
 D  [CACHE_WARMING] Completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:10ms, subscription:success:13ms
 D  executeGetRecentDeliveriesOperation: Fetching 10 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [correlation.SessionTracker] correlation Trigger:session_start Related:2 Total:19ms: 19ms [OK]
 I  OPERATION CORRELATION in qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851: session_start -> user_profile_preload, subscription_preload (19ms)
 D  executeGetRecentDeliveriesOperation: Fetching from remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 135ms, result: true
 D  Addresses cache warming successful in 135ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  CACHE WARMING: Addresses loaded in 111ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:14ms, subscription:success:7ms, addresses:success:135ms, total:138ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation - Found 2 deliveries in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809239030, count: 2)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  ✅ Deliveries loaded successfully: 1 (deduplicated from 2)
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065410Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065488Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065410Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065488Z
 D  getAllDeliveries: Found 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92 (1 modern, 0 legacy)
 D  Created domain delivery - ID: aysMyu7QjExLMl4eb4Ji, details null: false
 D  Domain details - userId: qOhr05X5ySh5d1s6sqa43vImBE92, orderId: *********, status: null, times null: false
 D  Domain times - completedAt null: false
 D  About to validate domain delivery - delivery.details null: false
 D  Pre-validation - Delivery identity: 113116314, details identity: 225120715
 D  Validation entry - Delivery identity: 113116314, details identity: 225120715
 W  Delivery validation failed: delivery.details is actually null for delivery aysMyu7QjExLMl4eb4Ji
 D  Delivery validation complete: 1 errors, 0 warnings
 W  Domain Delivery validation failed in mapper for delivery aysMyu7QjExLMl4eb4Ji: [Delivery details are required]
 D  === DELIVERY MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=aysMyu7QjExLMl4eb4Ji, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_orderId=*********, dto_addressId=Uooc9WOehbUH5JYJxveq, dto_tipAmount=90.0, dto_status=COMPLETED, dto_notes=empty}
 D  Output Domain State: {domain_id=aysMyu7QjExLMl4eb4Ji, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_orderId=*********, domain_addressId=Uooc9WOehbUH5JYJxveq, domain_tipAmount=90.0, domain_status=COMPLETED, domain_notes=empty}
 D  Field Transformations: [notes: DECRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, timestamps: DATE_PARSING]
 D  Business Logic Applied: [notes_decryption, field_validation, address_plain_mapping, tip_calculation, status_mapping, timestamp_conversion]
 D  PII Fields Processed: 2
 D  Mapping Duration: 1ms
 D  Cached delivery list: deliveries_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  Saved 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Cached 1 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Remote fetch for recent deliveries (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851, user: qOhr05X5ySh5d1s6sqa43vImBE92, limit: 10, count: 1)
 D  DEDUPLICATION: Cleaned up completed request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  DEDUPLICATION: Completed new request 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10' in 231ms, result: true
 D  Cached delivery ********* with atomic cache system
 I  CACHE WARMING: Deliveries loaded in 233ms
 D  [DeliveryMapper] toSsot(Delivery) ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1153bytes Fields:7 PII:2 Decrypt:1ms Logic:[notes_decryption,field_validation,address_plain_mapping,tip_calculation,status_mapping,timestamp_conversion] Issues:1 Delivery: 1ms [WARN] (1 issues)
 W  SSOT/DTO Alignment Issue in DeliveryMapper for Delivery:
 W     Validation Errors: Domain validation: Delivery details are required
 D  For capability in capabilities, log:
"AdvancedMarkers: false: Capabilities unavailable without a Map ID."Data-driven styling: false
 W  Destroying egl context
 W  Shutting down renderer while it's not idle - phase is INVALID
 I  Trimming POINTS_LABELS's current size 3 to 0.00000, or 0
 I  Trimming OTHER: LabelSourceOp's current size 2 to 0.00000, or 0
 I  Trimming LINE_LABELS's current size 20 to 0.00000, or 0
 I  Trimming OTHER: LoggingOp's current size 16 to 0.00000, or 0
 I  Trimming OTHER: NativeTessellators's current size 1 to 0.00000, or 0
 I  Trimming OTHER: VertexBuilders's current size 1 to 0.00000, or 0
 D  app_time_stats: avg=19.89ms min=11.14ms max=200.09ms count=55
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  User session ended - clearing state
 D  User session ended - clearing dialog state
 D  Loading delivery details...
 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@9a5ec41
 D  app_time_stats: avg=54.36ms min=2.20ms max=1900.60ms count=52
 D  Navigating to destination: dashboard
 D  🚀 HERO DASHBOARD: Starting render at 1749809315793
 D  User session ready - initializing dashboard data
 D  🚀 MAP DATA LOADED: 1 addresses in 31ms
 D  🔍 STATE CHANGED: showAddressDetails = false, selectedAddressId = null
 I  SYSTEM HEALTH CHECK: Starting comprehensive monitoring
 I  SYSTEM STATE:
 I  - Repository Initialized: true
 I  - User Session Ready: false
 I  - Core Data Ready: true
 D  🚀 INSTANT: Map data loaded, hiding skeleton immediately
 I  SYSTEM COMPONENTS:
 I  - AuthManager: AuthenticationManagerImpl
 I  - EncryptionUtils: EncryptionUtils
 I  - FirebaseAuth: zzad
 I  - CacheLifecycleManager: CacheLifecycleManager
 I  CACHE METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=180.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=17, misses=1, puts=11, removes=0, expiredCleanups=0, totalOperations=18), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=20, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=26), defaultTtl=1d, enableMetrics=true, hitRate=0.7692307692307693, missRate=0.23076923076923078, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=10, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=12), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 I  UI PERFORMANCE MONITORING: Started/Verified
 D  [SYSTEM_HEALTH] Repository:true UserSession:false CoreData:true
 D  [SYSTEM_HEALTH] Cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=180.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=17, misses=1, puts=11, removes=0, expiredCleanups=0, totalOperations=18), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=20, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=26), defaultTtl=1d, enableMetrics=true, hitRate=0.7692307692307693, missRate=0.23076923076923078, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=10, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=12), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 I  SYSTEM HEALTH CHECK: Completed successfully
 D  preferredRenderer: null
 D  preferredRenderer: null
 I  Google Play services package version: 252037038
 I  Google Play services maps renderer version(maps_core): 251625202
 I  FpsProfiler MAIN created on main
 I  Map using legacy labeler
 I  Network fetching: false
 I  requestDrawingConfig for epoch 736 legend ROADMAP
 I  Network fetching: true
 I  Network fetching: true
 I  Network fetching: true
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Repository initialization state: true
 D  UI frame monitoring started
 D  🔗 Auth state: User authenticated (qOhr05X5ySh5d1s6sqa43vImBE92)
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851 (age: 4034ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUser: Authentication ready for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  🚀 HERO PERFORMANCE: Starting priority cache warming for instant map
 D  Scheduling task DashboardMapCacheWarming with priority CRITICAL
 D  Executing task DashboardMapCacheWarming (CRITICAL)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:2 [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  🗺️ Testing simple geocoding for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  🧪 Testing native geocoding with: 1600 Amphitheatre Parkway, Mountain View, CA
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  loadPage: Loading address page for user qOhr05X5ySh5d1s6sqa43vImBE92 with size: 50, key: null
 D  loadAddressPage: Loading address page with size: 50, key: null
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 D  🗺️ Loading addresses from repository for map display
 D  [cache_system.UserRepository] cache_breakdown Check:13ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 13ms [OK]
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  tagSocket(100) with statsTag=0x99e6744e, statsUid=-1
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 20ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 14ms [OK]
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 29ms, result: true
 D  User profile cache warming successful in 30ms
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  loadAddressPage: Executing Firestore query for addresses
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  tagSocket(140) with statsTag=0x99e6744e, statsUid=-1
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription', waiting for result
 D  DEDUPLICATION: Found existing request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Found existing request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 64ms, result: true
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  ✅ SESSION MANAGER: Created/retrieved session qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851
 D  Starting backpressure handling for dashboard_delivery_observation with THROTTLE_FIRST
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  observeDeliveriesByUserId: Starting observation for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [cache_system.UserRepository] cache_breakdown Check:4ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 4ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 20ms [OK]
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851 (age: 4107ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851 (age: 4108ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 68ms, result: true
 D  Subscription cache warming successful in 70ms
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 26ms, result: true
 D  Subscription cache warming successful in 27ms
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 34ms, result: true
 D  Subscription cache warming successful in 34ms
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 23ms
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 43ms
 D  [data.UserRepository] getCurrentUser(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 83ms [OK]
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 26ms [OK]
 D  [cache_system.UserRepository] cache_breakdown Check:8ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 8ms [OK]
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 45ms, result: true
 D  User profile cache warming successful in 46ms
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 53ms, result: true
 D  User profile cache warming successful in 54ms
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:3 Size:1299bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:119ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:119ms [CACHE_HIT] [FAST_UX]: 119ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeUserProfile Load:119ms Steps:[repository_call,data_mapping,state_emission] UserDelay:119ms [CACHE_HIT] [FAST_UX]: 119ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  loadAddressPage: Successfully loaded 1 DTOs (168ms total, query: 167ms)
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  loadPage: Successfully loaded 1 addresses
 D  [Firestore] QUERY user_addresses: 167ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 175ms, result: true
 D  Addresses cache warming successful in 179ms
 D  DEDUPLICATION: Completed waiting for 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 133ms, result: true
 D  Addresses cache warming successful in 141ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:30ms, subscription:success:70ms, addresses:success:179ms, total:186ms
 D  🗺️ MAP-CRITICAL WARMING COMPLETE: 186ms - addresses: Success(cachedAt=1749809316078, durationMs=179), user: Success(cachedAt=1749809315923, durationMs=30)
 D  🎯 SIMPLE GEOCODING: Testing native geocoding capability
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:54ms, subscription:success:34ms, addresses:success:141ms, total:179ms
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUser: Cache warming initiated for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed waiting for 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 130ms, result: true
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  Addresses cache warming successful in 131ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:46ms, subscription:success:27ms, addresses:success:131ms, total:179ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation - Found 2 deliveries in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851, count: 2)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [data.AddressPageLoader] loadPage(Address) User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Source:RemoteDataSource Strategy:none [CACHE_MISS]: 175ms [OK]
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  🚀 MAP DATA LOADED: 1 addresses in 314ms
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:MONTH User:stats_calculation Size:2bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:TODAY User:stats_calculation Size:2bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  Fallback: Loaded 1 addresses from repository
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:WEEK User:stats_calculation Size:2bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 I  Compiler allocated 5354KB to compile java.lang.Object com.autogratuity.data.mapper.AddressMapper.mapToDomain(java.lang.String, com.autogratuity.data.model.generated_kt.Address$AddressData, kotlin.coroutines.Continuation)
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  🗺️ INSTANT MAP DATA: Loaded 1 addresses from warmed cache
 D  Complete cache warming: 252ms total (warming: 186ms)
 D  Task DashboardMapCacheWarming completed in 257ms
 D  🚀 PRIORITY TASK SUCCESS: Cache warming and maps pre-fetch completed successfully
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 I  Initial labeling completed.
 D  🏠 HOME POSITION SET: lat/lng: (41.703128,-93.78315889999999) at zoom 15.0
 D  🏠 HOME POSITION: Stored position lat/lng: (41.703128,-93.78315889999999) at zoom 15.0
 I  Background concurrent mark compact GC freed 18MB AllocSpace bytes, 141(5132KB) LOS objects, 48% free, 25MB/49MB, paused 1.842ms,3.742ms total 115.252ms
 D  🚀 MAP READY: Maps Compose initialized and ready
 D  🚀 MAP LOADED: 1 markers ready
 D  Native geocoding success: 1600 Amphitheatre Parkway, Mountain View, CA -> 37.4207967, -122.0852688
 D  🚀 BACKGROUND REFRESH: Skipping update, data already loaded
 D  🔍 CLUSTER ITEM CLICKED: title=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, addressId=Uooc9WOehbUH5JYJxveq
 D  🔍 CALLING onAddressClick with addressId: Uooc9WOehbUH5JYJxveq
 D  🔍 MAP MARKER CLICKED: addressId = Uooc9WOehbUH5JYJxveq
 D  🔍 CURRENT STATE: selectedAddressId = null, showAddressDetails = false
 D  🔍 STATE UPDATED: selectedAddressId = Uooc9WOehbUH5JYJxveq, showAddressDetails = true
 D  🔍 SHOWING ModalBottomSheet for addressId: Uooc9WOehbUH5JYJxveq
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  🔍 BOTTOM SHEET CREATED for addressId: Uooc9WOehbUH5JYJxveq
 D  🔍 STATE CHANGED: showAddressDetails = true, selectedAddressId = Uooc9WOehbUH5JYJxveq
 D  🔍 LAUNCHED EFFECT STARTED for addressId: Uooc9WOehbUH5JYJxveq
 D  🔍 Loading address immediately for addressId: Uooc9WOehbUH5JYJxveq
 D  🔍 loadAddress called for ID: Uooc9WOehbUH5JYJxveq
 D  ⏳ Loading address with ID: Uooc9WOehbUH5JYJxveq
 D  🔍 SESSION STATE: isReady = false
 D  🔍 User session ended - clearing state
 D  User session ended - clearing address state
 D  ✅ Successfully loaded address: Uooc9WOehbUH5JYJxveq
 D  getAddressById: Address Uooc9WOehbUH5JYJxveq not in cache or local fetch failed for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  getAddressById: Fetching address Uooc9WOehbUH5JYJxveq for user qOhr05X5ySh5d1s6sqa43vImBE92 from path: users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq
 D  🔍 BOTTOM SHEET CREATED for addressId: Uooc9WOehbUH5JYJxveq
 D  ⏳ Loading address: Uooc9WOehbUH5JYJxveq
 D  [Firestore] READ GET_DOCUMENT:users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Results:1 Size:1335bytes Source:SERVER Filters:[address_by_id]: 83ms [OK]
 I  FIRESTORE READ: GET_DOCUMENT:users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Results:1 Size:1335bytes Source:SERVER Filters:[address_by_id] (83ms)
 D  getAddressById: Successfully fetched address for user qOhr05X5ySh5d1s6sqa43vImBE92
 D    Firestore Path: users/qOhr05X5ySh5d1s6sqa43vImBE92/user_addresses/Uooc9WOehbUH5JYJxveq
 D    Document Size: 1335 bytes
 D    Firestore Duration: 83ms
 D    Raw Document Data: {id=Uooc9WOehbUH5JYJxveq, addressData={deliveryStats={averageTipAmount=90, averageTimeMinutes=null, pendingCount=null, lastDeliveryTimestamp=Timestamp(seconds=1749809160, nanoseconds=511000000), totalTips=90, highestTip=90, deliveryCount=1, tipCount=1, lastDeliveryDate=2025-06-13T05:06:01.894954-05:00}, metadata={createdAt=null, importId=null, importedAt=null, customData=null, captureId=null, source=google_places_api, version=1, updatedAt=Timestamp(seconds=1749809251, nanoseconds=341000000)}, components={country=US, streetName=Northeast Station Crossing Drive, streetNumber=1220, city=Grimes, postalCode=50111, state=IA}, notes=null, searchTerms=[515 Fieldhouse, 1220 NE Station Crossing Dr, Grimes, IA 50111, USA], coordinates={latitude=41.703128, longitude=-93.78315889999999}, placeId=ChIJS_5xNo4p7IcRDLnUHWycn4Q, flags={archived=null, hasAccessIssues=null, dndSource=null, isVerified=false, manualDndState=null, verified=true, doNotDeliver=false, favorite=null, apartment=null}, userId=qOhr05X5ySh5d1s6sqa43vImBE92, platform=null, tags=[], default=false, normalizedAddress=1220 ne station crossing dr grimes ia 50111 usa, fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, searchFields={normalizedKey=null, searchTerms=[515 Fieldhouse, 1220 NE Station Crossing Dr, Grimes, IA 50111, USA]}, orderIds=[*********]}}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  getAddressById: Fetched address Uooc9WOehbUH5JYJxveq from remote and updated cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  🔍 Repository result type: Success
 D  ✅ Successfully loaded address: Uooc9WOehbUH5JYJxveq - 1220 NE Station Crossing Dr, Grimes, IA 50111, USA...
 D  [cache_system.AddressRepository] cache_breakdown Check:0ms Remote:87ms Map:1ms Store:0ms Hit:false ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92: 88ms [OK]
 D  🔍 BOTTOM SHEET CREATED for addressId: Uooc9WOehbUH5JYJxveq
 D  🔍 DELIVERY STATS: deliveryCount=1, totalTips=null, averageTipAmount=null, tipCount=1
 D  🔍 CLOUD-CALCULATED STATS: deliveries=1, tips=1, totalTips=0.0, cloudAvgTip=0.0, calculatedTipRate=100.0%
 D  ✅ Successfully loaded address: Uooc9WOehbUH5JYJxveq
 D  🔍 DELIVERY STATS: deliveryCount=1, totalTips=null, averageTipAmount=null, tipCount=1
 D  🔍 CLOUD-CALCULATED STATS: deliveries=1, tips=1, totalTips=0.0, cloudAvgTip=0.0, calculatedTipRate=100.0%
 D  app_time_stats: avg=2064.65ms min=0.95ms max=59701.70ms count=29
 D  🔍 MODAL BOTTOM SHEET DISMISSED
 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@8cb70d2
 D  🔍 STATE CHANGED: showAddressDetails = false, selectedAddressId = null
 D  app_time_stats: avg=1278.90ms min=2.94ms max=63124.45ms count=50
 D  app_time_stats: avg=146.88ms min=13.35ms max=1035.17ms count=8
 W  Destroying egl surface
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 E  *~*~*~ Previous channel {0} was garbage collected without being shut down! ~*~*~* (Ask Gemini)
    Make sure to call shutdown()/shutdownNow()
java.lang.RuntimeException: ManagedChannel allocation site
	at com.google.android.libraries.places.internal.zzbph.<init>(com.google.android.libraries.places:places@@4.3.1:3)
	at com.google.android.libraries.places.internal.zzbpi.<init>(com.google.android.libraries.places:places@@4.3.1:2)
	at com.google.android.libraries.places.internal.zzbpg.zzd(com.google.android.libraries.places:places@@4.3.1:6)
	at com.google.android.libraries.places.internal.zzbdr.zzd(com.google.android.libraries.places:places@@4.3.1:1)
	at com.google.android.libraries.places.internal.zzlr.zzb(com.google.android.libraries.places:places@@4.3.1:4)
	at com.google.android.libraries.places.internal.zzbbt.zzc(com.google.android.libraries.places:places@@4.3.1:1)
	at com.google.android.libraries.places.internal.zzbbt.zzb(com.google.android.libraries.places:places@@4.3.1:1)
	at com.google.android.libraries.places.internal.zzjb.zzb(com.google.android.libraries.places:places@@4.3.1:2)
	at com.google.android.libraries.places.internal.zzbbt.zzc(com.google.android.libraries.places:places@@4.3.1:1)
	at com.google.android.libraries.places.internal.zzbbt.zzb(com.google.android.libraries.places:places@@4.3.1:1)
	at com.google.android.libraries.places.internal.zzlo.zzc(com.google.android.libraries.places:places@@4.3.1:19)
	at com.google.android.libraries.places.api.Places.zzb(com.google.android.libraries.places:places@@4.3.1:9)
	at com.google.android.libraries.places.api.Places.createClient(com.google.android.libraries.places:places@@4.3.1:3)
	at com.autogratuity.ui.delivery.compose.AddEditDeliveryScreenKt$AddEditDeliveryScreen$3$1.invokeSuspend(AddEditDeliveryScreen.kt:196)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at androidx.compose.ui.platform.AndroidUiDispatcher.performTrampolineDispatch(AndroidUiDispatcher.android.kt:79)
	at androidx.compose.ui.platform.AndroidUiDispatcher.access$performTrampolineDispatch(AndroidUiDispatcher.android.kt:41)
	at androidx.compose.ui.platform.AndroidUiDispatcher$dispatchCallback$1.run(AndroidUiDispatcher.android.kt:57)
	at android.os.Handler.handleCallback(Handler.java:959)
	at android.os.Handler.dispatchMessage(Handler.java:100)
	at android.os.Looper.loopOnce(Looper.java:232)
	at android.os.Looper.loop(Looper.java:317)
	at android.app.ActivityThread.main(ActivityThread.java:8705)
	at java.lang.reflect.Method.invoke(Native Method)
	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:580)
	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:886)
 E  *~*~*~ Previous channel {0} was garbage collected without being shut down! ~*~*~* (Ask Gemini)
    Make sure to call shutdown()/shutdownNow()
java.lang.RuntimeException: ManagedChannel allocation site
	at com.google.android.libraries.places.internal.zzbph.<init>(com.google.android.libraries.places:places@@4.3.1:3)
	at com.google.android.libraries.places.internal.zzbpi.<init>(com.google.android.libraries.places:places@@4.3.1:2)
	at com.google.android.libraries.places.internal.zzbpg.zzd(com.google.android.libraries.places:places@@4.3.1:6)
 D  ✅ MODERN AddEditDeliveryViewModel initialized
 D  [presentation.AddEditDeliveryViewModel] init Deps:4 Data:new_delivery_created State:Initialized [STATEFLOW_UPDATED]: 0ms [OK]
 D  tagSocket(97) with statsTag=0xffffffff, statsUid=-1
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  LaunchedEffect triggered but selectedPlace is null
 D  tagSocket(187) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  [reactive_performance.AddEditDeliveryViewModel] stateflow_emission Flow:delivery Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Closed previous session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851
 I  SESSION STARTED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088 with cache warming and app state monitoring
 I  SESSION ENDED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851 with 40 operations
 I  SESSION FINAL METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=180.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=24, misses=2, puts=15, removes=0, expiredCleanups=0, totalOperations=26), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=23, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=29), defaultTtl=1d, enableMetrics=true, hitRate=0.7931034482758621, missRate=0.20689655172413793, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=11, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=13), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 I  CACHE WARMING: Starting real cache warming for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [SESSION_END] Final cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=180.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=90.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=24, misses=2, puts=15, removes=0, expiredCleanups=0, totalOperations=26), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=23, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=29), defaultTtl=1d, enableMetrics=true, hitRate=0.7931034482758621, missRate=0.20689655172413793, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=11, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=13), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088)
 D  SESSION: Repository initialization state: true for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 D  SESSION: UI performance monitoring started for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088)
 D  [SESSION] Started: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088 [TRACKING]
 D  [CACHE_WARMING] Starting critical data preload for user: qOhr05X5ySh5d1s6sqa43vImBE92
 I  Created new session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 11ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 12ms, result: true
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 20ms, result: true
 D  User profile cache warming successful in 22ms
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 9ms [OK]
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088)
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088 (age: 21ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [cache_system.UserRepository] cache_breakdown Check:1ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 1ms [OK]
 D  getUserSubscription: Getting subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Successfully retrieved subscription for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 6ms [OK]
 D  DEDUPLICATION: Completed new request 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 10ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 13ms
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 7ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 I  CACHE WARMING: Subscription loaded successfully in 19ms
 I  CACHE WARMING: User profile loaded successfully in 22ms
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 40ms, result: true
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:1ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 1ms [OK]
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  Subscription cache warming successful in 46ms
 D  [CACHE_WARMING] CriticalDataSystems for user qOhr05X5ySh5d1s6sqa43vImBE92: 2 ops in 35ms [PRELOAD]
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  CACHE WARMING: Completed for user qOhr05X5ySh5d1s6sqa43vImBE92 in 35ms
 I  CACHE WARMING: Results - user_profile:success:22ms, subscription:success:19ms
 D  DEDUPLICATION: Starting new request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  [CACHE_WARMING] Completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:22ms, subscription:success:19ms
 D  executeGetRecentDeliveriesOperation: Fetching 10 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [correlation.SessionTracker] correlation Trigger:session_start Related:2 Total:35ms: 35ms [OK]
 I  OPERATION CORRELATION in qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088: session_start -> user_profile_preload, subscription_preload (35ms)
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Fetching from remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  tagSocket(142) with statsTag=0xffffffff, statsUid=-1
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065410Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065488Z
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  CACHE WARMING: Addresses loaded in 350ms
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 401ms, result: true
 D  Addresses cache warming successful in 401ms
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:22ms, subscription:success:46ms, addresses:success:401ms, total:402ms
 D  DEDUPLICATION: Starting new request for key 'getDelivery_aysMyu7QjExLMl4eb4Ji'
 D  Cache hit for delivery aysMyu7QjExLMl4eb4Ji
 D  Retrieved delivery aysMyu7QjExLMl4eb4Ji for user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getDeliveryById: Found delivery aysMyu7QjExLMl4eb4Ji in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getDeliveryById: Cache hit for delivery aysMyu7QjExLMl4eb4Ji (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809311851)
 D  [data.DeliveryRepository] getDeliveryById(Delivery) ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:941bytes Source:cache Strategy:cache-first [CACHE_HIT]: 406ms [OK]
 D  DEDUPLICATION: Cleaned up completed request for key 'getDelivery_aysMyu7QjExLMl4eb4Ji'
 D  DEDUPLICATION: Completed new request 'getDelivery_aysMyu7QjExLMl4eb4Ji' in 4ms, result: true
 D  Loaded 1 addresses
 D  Updated selected place from loaded delivery
 D  LaunchedEffect triggered with selectedPlace: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA
 D  Address text updated to: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:00.511508Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065410Z
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T10:06:01.065488Z
 D  getAllDeliveries: Found 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92 (1 modern, 0 legacy)
 D  Created domain delivery - ID: aysMyu7QjExLMl4eb4Ji, details null: false
 D  Domain details - userId: qOhr05X5ySh5d1s6sqa43vImBE92, orderId: *********, status: null, times null: false
 D  Domain times - completedAt null: false
 D  About to validate domain delivery - delivery.details null: false
 D  Pre-validation - Delivery identity: 32977243, details identity: 246148600
 D  Validation entry - Delivery identity: 32977243, details identity: 246148600
 W  Delivery validation failed: delivery.details is actually null for delivery aysMyu7QjExLMl4eb4Ji
 D  Delivery validation complete: 1 errors, 0 warnings
 W  Domain Delivery validation failed in mapper for delivery aysMyu7QjExLMl4eb4Ji: [Delivery details are required]
 D  === DELIVERY MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=aysMyu7QjExLMl4eb4Ji, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_orderId=*********, dto_addressId=Uooc9WOehbUH5JYJxveq, dto_tipAmount=90.0, dto_status=COMPLETED, dto_notes=empty}
 D  Output Domain State: {domain_id=aysMyu7QjExLMl4eb4Ji, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_orderId=*********, domain_addressId=Uooc9WOehbUH5JYJxveq, domain_tipAmount=90.0, domain_status=COMPLETED, domain_notes=empty}
 D  Field Transformations: [notes: DECRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, timestamps: DATE_PARSING]
 D  Business Logic Applied: [notes_decryption, field_validation, address_plain_mapping, tip_calculation, status_mapping, timestamp_conversion]
 D  PII Fields Processed: 2
 D  Mapping Duration: 0ms
 D  Cached delivery list: deliveries_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  Saved 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Cached 1 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Remote fetch for recent deliveries (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088, user: qOhr05X5ySh5d1s6sqa43vImBE92, limit: 10, count: 1)
 D  DEDUPLICATION: Cleaned up completed request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  [DeliveryMapper] toSsot(Delivery) ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1153bytes Fields:7 PII:2 Decrypt:0ms Logic:[notes_decryption,field_validation,address_plain_mapping,tip_calculation,status_mapping,timestamp_conversion] Issues:1 Delivery: 0ms [WARN] (1 issues)
 W  SSOT/DTO Alignment Issue in DeliveryMapper for Delivery:
 W     Validation Errors: Domain validation: Delivery details are required
 D  DEDUPLICATION: Completed new request 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10' in 444ms, result: true
 I  CACHE WARMING: Deliveries loaded in 445ms
 D  Cached delivery ********* with atomic cache system
 D  For capability in capabilities, log:
"AdvancedMarkers: false: Capabilities unavailable without a Map ID."Data-driven styling: false
 W  Destroying egl context
 W  Shutting down renderer while it's not idle - phase is INVALID
 I  Trimming POINTS_LABELS's current size 3 to 0.00000, or 0
 I  Trimming OTHER: LoggingOp's current size 16 to 0.00000, or 0
 I  Trimming LINE_LABELS's current size 20 to 0.00000, or 0
 I  Trimming OTHER: VertexBuilders's current size 1 to 0.00000, or 0
 I  Trimming OTHER: NativeTessellators's current size 1 to 0.00000, or 0
 I  Trimming OTHER: LabelSourceOp's current size 2 to 0.00000, or 0
 D  app_time_stats: avg=23.82ms min=2.18ms max=601.19ms count=46
 D  show(ime(), fromIme=false)
 I  com.autogratuity:bcbbf881: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{b418958 VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
 I  Flattened final assist data: 860 bytes, containing 1 windows, 6 views
 D  app_time_stats: avg=36.38ms min=13.06ms max=612.75ms count=31
 I  com.autogratuity:29d4fee5: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{b418958 VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  Autofill popup isn't shown because autofill is not available.

Did you set up autofill?
1. Go to Settings > System > Languages&input > Advanced > Autofill Service
2. Pick a service

Did you add an account?
1. Go to Settings > System > Languages&input > Advanced
2. Click on the settings icon next to the Autofill Service
3. Add your account
 D  show(ime(), fromIme=true)
 I  com.autogratuity:29d4fee5: onShown
 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@ba72a8a
 D  app_time_stats: avg=24.75ms min=1.47ms max=411.95ms count=31
 D  app_time_stats: avg=146.61ms min=11.38ms max=471.24ms count=10
 D  app_time_stats: avg=212.52ms min=15.02ms max=500.35ms count=5
 D  getOrderIdErrorMessage: input='*********', error='null'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  🔧 PRESERVING EXISTING ADDRESS: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA... (ID: Uooc9WOehbUH5JYJxveq)
 D  🔒 PRESERVING IMMUTABLE ORDER ID: *********
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'updateDelivery_aysMyu7QjExLMl4eb4Ji'
 D  validateDelivery: Validating delivery aysMyu7QjExLMl4eb4Ji
 D  validateDelivery: Delivery aysMyu7QjExLMl4eb4Ji is valid
 D  CONVERSION: Delivery SSoT -> DTO | ID: aysMyu7QjExLMl4eb4Ji | User: qOhr05X5ySh5d1s6sqa43vImBE92
 D    Input: orderId=********* | status=COMPLETED | tipAmount=20.0
 D    Converting address: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA... (ID: Uooc9WOehbUH5JYJxveq)
 D    Created missing reference with addressId: Uooc9WOehbUH5JYJxveq
 D  === DELIVERY SSoT -> DTO TRANSFORMATION ===
 D  Input SSoT State: {ssot_id=aysMyu7QjExLMl4eb4Ji, ssot_userId=qOhr05X5ySh5d1s6sqa43vImBE92, ssot_orderId=*********, ssot_tipAmount=20.0, ssot_status=COMPLETED, ssot_notes=empty, ssot_addressId=Uooc9WOehbUH5JYJxveq}
 D  Output DTO State: {dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_orderId=*********, dto_tipAmount=20.0, dto_status=COMPLETED, dto_notes=empty, dto_addressId=Uooc9WOehbUH5JYJxveq}
 D  Field Transformations: [notes: ENCRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, times: TIMESTAMP_MAPPING]
 D  Business Logic Applied: [notes_encryption, field_validation, address_plain_mapping, status_mapping, amounts_calculation]
 D  PII Fields Encrypted: 0
 D  Mapping Duration: 0ms
 D  CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: aysMyu7QjExLMl4eb4Ji
 D    Output: encrypted_fields=0 | validation_errors=0 | duration=0ms
 D    DTO size: 1173 bytes
 D    Business logic: address_mapping, status_mapping, amounts_calculation
 D  executeUpdateDeliveryOperation: Updating delivery aysMyu7QjExLMl4eb4Ji with transaction for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DeliveryMapper] toDto Delivery ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1173bytes Delivery: 0ms [OK]
 D  Transaction: Updated delivery aysMyu7QjExLMl4eb4Ji with new deliveryData
 I  executeUpdateDeliveryOperation: Delivery aysMyu7QjExLMl4eb4Ji updated with transaction for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Saved delivery aysMyu7QjExLMl4eb4Ji for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'updateDelivery_aysMyu7QjExLMl4eb4Ji'
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 D  DEDUPLICATION: Completed new request 'updateDelivery_aysMyu7QjExLMl4eb4Ji' in 148ms, result: true
 D  Cached delivery aysMyu7QjExLMl4eb4Ji with atomic cache system
 D  [data.DeliveryRepository] updateDelivery(Delivery) ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1221bytes Source:firestore_transaction Strategy:write-through [CACHE_MISS]: 150ms [OK]
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:71890ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:71890ms [CACHE_HIT] [POOR_UX]: 71890ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 71890ms (user-visible)
 V  ThrottleFirst: emitted value
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 0ms [OK]
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 I  Ignoring popBackStack to route deliveries as it was not found on the current back stack
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:148754ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:148754ms [CACHE_HIT] [POOR_UX]: 148754ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 148754ms (user-visible)
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:367927ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:367927ms [CACHE_HIT] [POOR_UX]: 367927ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 367927ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 0ms [OK]
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088)
 D  Closed previous session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  SESSION: Repository initialization state: true for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 D  SESSION: UI performance monitoring started for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855
 I  CACHE WARMING: Starting real cache warming for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [CACHE_WARMING] Starting critical data preload for user: qOhr05X5ySh5d1s6sqa43vImBE92
 I  SESSION STARTED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855 with cache warming and app state monitoring
 D  [SESSION] Started: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855 [TRACKING]
 I  Created new session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 I  SESSION ENDED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088 with 19 operations
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 I  SESSION FINAL METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=110.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=55.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=28, misses=2, puts=18, removes=0, expiredCleanups=0, totalOperations=30), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=26, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=32), defaultTtl=1d, enableMetrics=true, hitRate=0.8125, missRate=0.1875, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=12, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=14), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088)
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855)
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855 (age: 4ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  [SESSION_END] Final cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=110.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=55.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=28, misses=2, puts=18, removes=0, expiredCleanups=0, totalOperations=30), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=26, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=32), defaultTtl=1d, enableMetrics=true, hitRate=0.8125, missRate=0.1875, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=12, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=14), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 I  CACHE WARMING: Subscription loaded successfully in 5ms
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 8ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 10ms, result: true
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 7ms [OK]
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 9ms, result: true
 D  Subscription cache warming successful in 10ms
 D  [cache_system.UserRepository] cache_breakdown Check:2ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 2ms [OK]
 D  User profile cache warming successful in 10ms
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855)
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 8ms
 D  [cache_system.UserRepository] cache_breakdown Check:5ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 5ms [OK]
 I  CACHE WARMING: User profile loaded successfully in 9ms
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 6ms [OK]
 D  [CACHE_WARMING] CriticalDataSystems for user qOhr05X5ySh5d1s6sqa43vImBE92: 2 ops in 11ms [PRELOAD]
 D  DEDUPLICATION: Starting new request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 I  CACHE WARMING: Completed for user qOhr05X5ySh5d1s6sqa43vImBE92 in 11ms
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  CACHE WARMING: Results - subscription:success:5ms, user_profile:success:9ms
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  [correlation.SessionTracker] correlation Trigger:session_start Related:2 Total:11ms: 11ms [OK]
 I  OPERATION CORRELATION in qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855: session_start -> subscription_preload, user_profile_preload (11ms)
 D  executeGetRecentDeliveriesOperation: Fetching 10 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [CACHE_WARMING] Completed for user qOhr05X5ySh5d1s6sqa43vImBE92: subscription:success:5ms, user_profile:success:9ms
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Fetching from remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.623584-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.623584-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.631012-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.631033-05:00
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 130ms, result: true
 D  Addresses cache warming successful in 131ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 I  CACHE WARMING: Addresses loaded in 117ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:10ms, subscription:success:10ms, addresses:success:131ms, total:132ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation - Found 2 deliveries in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809381088, count: 2)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  ✅ Deliveries loaded successfully: 1 (deduplicated from 2)
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.623584-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.623584-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.631012-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.631033-05:00
 D  getAllDeliveries: Found 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92 (1 modern, 0 legacy)
 D  Created domain delivery - ID: aysMyu7QjExLMl4eb4Ji, details null: false
 D  Domain details - userId: qOhr05X5ySh5d1s6sqa43vImBE92, orderId: *********, status: null, times null: false
 D  Domain times - completedAt null: false
 D  About to validate domain delivery - delivery.details null: false
 D  Pre-validation - Delivery identity: 111104514, details identity: 207533843
 D  Validation entry - Delivery identity: 111104514, details identity: 207533843
 W  Delivery validation failed: delivery.details is actually null for delivery aysMyu7QjExLMl4eb4Ji
 D  Delivery validation complete: 1 errors, 0 warnings
 W  Domain Delivery validation failed in mapper for delivery aysMyu7QjExLMl4eb4Ji: [Delivery details are required]
 D  === DELIVERY MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=aysMyu7QjExLMl4eb4Ji, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_orderId=*********, dto_addressId=Uooc9WOehbUH5JYJxveq, dto_tipAmount=20.0, dto_status=COMPLETED, dto_notes=empty}
 D  Output Domain State: {domain_id=aysMyu7QjExLMl4eb4Ji, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_orderId=*********, domain_addressId=Uooc9WOehbUH5JYJxveq, domain_tipAmount=20.0, domain_status=COMPLETED, domain_notes=empty}
 D  Field Transformations: [notes: DECRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, timestamps: DATE_PARSING]
 D  Business Logic Applied: [notes_decryption, field_validation, address_plain_mapping, tip_calculation, status_mapping, timestamp_conversion]
 D  PII Fields Processed: 2
 D  Mapping Duration: 0ms
 D  Cached delivery list: deliveries_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  Saved 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Cached 1 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Remote fetch for recent deliveries (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855, user: qOhr05X5ySh5d1s6sqa43vImBE92, limit: 10, count: 1)
 D  DEDUPLICATION: Cleaned up completed request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  [DeliveryMapper] toSsot(Delivery) ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1173bytes Fields:7 PII:2 Decrypt:0ms Logic:[notes_decryption,field_validation,address_plain_mapping,tip_calculation,status_mapping,timestamp_conversion] Issues:1 Delivery: 0ms [WARN] (1 issues)
 W  SSOT/DTO Alignment Issue in DeliveryMapper for Delivery:
 W     Validation Errors: Domain validation: Delivery details are required
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed new request 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10' in 206ms, result: true
 D  Cached delivery ********* with atomic cache system
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 I  CACHE WARMING: Deliveries loaded in 207ms
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:72190ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:72190ms [CACHE_HIT] [POOR_UX]: 72190ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 72190ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:149052ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:149052ms [CACHE_HIT] [POOR_UX]: 149052ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 149052ms (user-visible)
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:368222ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:368222ms [CACHE_HIT] [POOR_UX]: 368222ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 0ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 368222ms (user-visible)
 D  app_time_stats: avg=29.52ms min=1.76ms max=499.48ms count=31
 D  hide(ime(), fromIme=false)
 I  com.autogratuity:7fa72ba5: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:40d108a4: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_REQUEST_HIDE_WITH_CONTROL fromUser false
 W  requestCursorUpdates on inactive InputConnection
 D  hide(ime(), fromIme=true)
 I  com.autogratuity:40d108a4: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:91d99b7f: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
 I  com.autogratuity:7fa72ba5: onHidden
 D  app_time_stats: avg=20.18ms min=1.62ms max=492.13ms count=45
 D  Navigating to destination: dashboard
 D  app_time_stats: avg=27.57ms min=12.78ms max=599.11ms count=53
 D  Navigating to destination: deliveries
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855 (age: 3123ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855)
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 3ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  [cache_system.UserRepository] cache_breakdown Check:1ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 1ms [OK]
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 1ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 3ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 8ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 9ms, result: true
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 11ms, result: true
 D  User profile cache warming successful in 12ms
 D  Subscription cache warming successful in 10ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 97ms, result: true
 D  Addresses cache warming successful in 98ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:12ms, subscription:success:10ms, addresses:success:98ms, total:100ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation - Found 2 deliveries in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855, count: 2)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  ✅ Deliveries loaded successfully: 1 (deduplicated from 2)
 D  app_time_stats: avg=19.72ms min=12.76ms max=182.83ms count=53
 D  Navigating to destination: dashboard
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855)
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855 (age: 4136ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 3ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 6ms, result: true
 D  Subscription cache warming successful in 7ms
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 3ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 9ms, result: true
 D  User profile cache warming successful in 9ms
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 2ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 78ms, result: true
 D  Addresses cache warming successful in 78ms
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:9ms, subscription:success:7ms, addresses:success:78ms, total:80ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation - Found 2 deliveries in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855, count: 2)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  ✅ Deliveries loaded successfully: 1 (deduplicated from 2)
 D  app_time_stats: avg=22.51ms min=10.98ms max=328.91ms count=53
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  Updated selected place from loaded delivery
 D  LaunchedEffect triggered with selectedPlace: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA
 D  Address text updated to: 1220 NE Station Crossing Dr, Grimes, IA 50111, USA
 D  tagSocket(174) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [reactive_performance.AddEditDeliveryViewModel] stateflow_emission Flow:delivery Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUserIdSuspend: Checking authentication state
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855)
 D  Closed previous session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 I  SESSION STARTED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104 with cache warming and app state monitoring
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855)
 D  [SESSION] Started: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104 [TRACKING]
 I  Created new session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 4ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  SESSION: Repository initialization state: true for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 D  SESSION: UI performance monitoring started for session qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104
 I  CACHE WARMING: Starting real cache warming for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 6ms, result: true
 D  User profile cache warming successful in 8ms
 I  SESSION ENDED: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855 with 21 operations
 I  SESSION FINAL METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=40.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=20.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=35, misses=2, puts=22, removes=0, expiredCleanups=0, totalOperations=37), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=30, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=36), defaultTtl=1d, enableMetrics=true, hitRate=0.8333333333333334, missRate=0.16666666666666666, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=12, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=14), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 8ms, result: true
 D  Subscription cache warming successful in 9ms
 D  [SESSION_END] Final cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=40.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=20.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=35, misses=2, puts=22, removes=0, expiredCleanups=0, totalOperations=37), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=30, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=36), defaultTtl=1d, enableMetrics=true, hitRate=0.8333333333333334, missRate=0.16666666666666666, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=12, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=14), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 3ms [OK]
 D  [CACHE_WARMING] Starting critical data preload for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104)
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104 (age: 15ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104)
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 4ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104
 D  [cache_system.UserRepository] cache_breakdown Check:3ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 3ms [OK]
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 5ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 6ms
 I  CACHE WARMING: User profile loaded successfully in 7ms
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 I  CACHE WARMING: Subscription loaded successfully in 7ms
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  DEDUPLICATION: Starting new request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  executeGetRecentDeliveriesOperation: Fetching 10 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Fetching from remote for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [CACHE_WARMING] CriticalDataSystems for user qOhr05X5ySh5d1s6sqa43vImBE92: 2 ops in 18ms [PRELOAD]
 I  CACHE WARMING: Completed for user qOhr05X5ySh5d1s6sqa43vImBE92 in 18ms
 I  CACHE WARMING: Results - user_profile:success:7ms, subscription:success:7ms
 D  [correlation.SessionTracker] correlation Trigger:session_start Related:2 Total:18ms: 18ms [OK]
 I  OPERATION CORRELATION in qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104: session_start -> user_profile_preload, subscription_preload (18ms)
 D  [CACHE_WARMING] Completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:7ms, subscription:success:7ms
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.623584-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.623584-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.631012-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 108ms, result: true
 D  Addresses cache warming successful in 112ms
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:8ms, subscription:success:9ms, addresses:success:112ms, total:117ms
 D  DEDUPLICATION: Starting new request for key 'getDelivery_aysMyu7QjExLMl4eb4Ji'
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.631033-05:00
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 I  CACHE WARMING: Addresses loaded in 90ms
 D  Cache hit for delivery aysMyu7QjExLMl4eb4Ji
 D  Retrieved delivery aysMyu7QjExLMl4eb4Ji for user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getDeliveryById: Found delivery aysMyu7QjExLMl4eb4Ji in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getDeliveryById: Cache hit for delivery aysMyu7QjExLMl4eb4Ji (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809387855)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDelivery_aysMyu7QjExLMl4eb4Ji'
 D  DEDUPLICATION: Completed new request 'getDelivery_aysMyu7QjExLMl4eb4Ji' in 2ms, result: true
 D  [data.DeliveryRepository] getDeliveryById(Delivery) ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:941bytes Source:cache Strategy:cache-first [CACHE_HIT]: 119ms [OK]
 D  Loaded 1 addresses
 D  Updated selected place from loaded delivery
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.623584-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.623584-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.631012-05:00
 D  Parsing OffsetDateTime structure: year=2025, month=6, day=13
 D  Successfully parsed OffsetDateTime: 2025-06-13T05:09:47.631033-05:00
 D  getAllDeliveries: Found 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92 (1 modern, 0 legacy)
 D  Created domain delivery - ID: aysMyu7QjExLMl4eb4Ji, details null: false
 D  Domain details - userId: qOhr05X5ySh5d1s6sqa43vImBE92, orderId: *********, status: null, times null: false
 D  Domain times - completedAt null: false
 D  About to validate domain delivery - delivery.details null: false
 D  Pre-validation - Delivery identity: 74385985, details identity: 128801766
 D  Validation entry - Delivery identity: 74385985, details identity: 128801766
 W  Delivery validation failed: delivery.details is actually null for delivery aysMyu7QjExLMl4eb4Ji
 D  Delivery validation complete: 1 errors, 0 warnings
 W  Domain Delivery validation failed in mapper for delivery aysMyu7QjExLMl4eb4Ji: [Delivery details are required]
 D  === DELIVERY MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=aysMyu7QjExLMl4eb4Ji, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_orderId=*********, dto_addressId=Uooc9WOehbUH5JYJxveq, dto_tipAmount=20.0, dto_status=COMPLETED, dto_notes=empty}
 D  Output Domain State: {domain_id=aysMyu7QjExLMl4eb4Ji, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_orderId=*********, domain_addressId=Uooc9WOehbUH5JYJxveq, domain_tipAmount=20.0, domain_status=COMPLETED, domain_notes=empty}
 D  Field Transformations: [notes: DECRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, timestamps: DATE_PARSING]
 D  Business Logic Applied: [notes_decryption, field_validation, address_plain_mapping, tip_calculation, status_mapping, timestamp_conversion]
 D  PII Fields Processed: 2
 D  Mapping Duration: 0ms
 D  Cached delivery list: deliveries_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  Saved 1 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetRecentDeliveriesOperation: Cached 1 recent deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DeliveryMapper] toSsot(Delivery) ID:aysMyu7QjExLMl4eb4Ji User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1173bytes Fields:7 PII:2 Decrypt:0ms Logic:[notes_decryption,field_validation,address_plain_mapping,tip_calculation,status_mapping,timestamp_conversion] Issues:1 Delivery: 0ms [WARN] (1 issues)
 W  SSOT/DTO Alignment Issue in DeliveryMapper for Delivery:
 W     Validation Errors: Domain validation: Delivery details are required
 D  executeGetRecentDeliveriesOperation: Remote fetch for recent deliveries (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104, user: qOhr05X5ySh5d1s6sqa43vImBE92, limit: 10, count: 1)
 D  DEDUPLICATION: Cleaned up completed request for key 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10'
 D  DEDUPLICATION: Completed new request 'getRecentDeliveries_qOhr05X5ySh5d1s6sqa43vImBE92_10' in 157ms, result: true
 I  CACHE WARMING: Deliveries loaded in 157ms
 D  Cached delivery ********* with atomic cache system
 D  🚀 HERO DASHBOARD: Starting render at 1749809394343
 D  User session ready - initializing dashboard data
 I  SYSTEM HEALTH CHECK: Starting comprehensive monitoring
 D  🚀 MAP DATA LOADED: 1 addresses in 20ms
 I  SYSTEM STATE:
 I  - Repository Initialized: true
 I  - User Session Ready: false
 I  - Core Data Ready: true
 D  🔍 STATE CHANGED: showAddressDetails = false, selectedAddressId = null
 I  SYSTEM COMPONENTS:
 I  - AuthManager: AuthenticationManagerImpl
 D  🚀 INSTANT: Map data loaded, hiding skeleton immediately
 D  app_time_stats: avg=18.29ms min=1.23ms max=583.52ms count=43
 I  - EncryptionUtils: EncryptionUtils
 I  - FirebaseAuth: zzad
 I  - CacheLifecycleManager: CacheLifecycleManager
 I  CACHE METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=40.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=20.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=38, misses=2, puts=25, removes=0, expiredCleanups=0, totalOperations=40), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=31, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=37), defaultTtl=1d, enableMetrics=true, hitRate=0.8378378378378378, missRate=0.16216216216216217, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=12, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=14), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 I  UI PERFORMANCE MONITORING: Started/Verified
 D  [SYSTEM_HEALTH] Repository:true UserSession:false CoreData:true
 D  preferredRenderer: null
 D  preferredRenderer: null
 D  [SYSTEM_HEALTH] Cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=2, completedDeliveries=1, pendingTips=0, totalCachedTips=40.0, uniqueUsers=1, uniqueAddresses=1, avgTipPerDelivery=20.0}, address={size=1, maxSize=500, metrics=CacheMetrics(hits=38, misses=2, puts=25, removes=0, expiredCleanups=0, totalOperations=40), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=0, addressesWithStats=1, avgTipsPerAddress=0.0}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=31, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=37), defaultTtl=1d, enableMetrics=true, hitRate=0.8378378378378378, missRate=0.16216216216216217, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=12, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=14), defaultTtl=1h, enableMetrics=true}, config={size=2, maxSize=500, metrics=CacheMetrics(hits=2, misses=3, puts=2, removes=0, expiredCleanups=0, totalOperations=5), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=true, onboarding=false, dataCollection=false}}}
 I  SYSTEM HEALTH CHECK: Completed successfully
 I  Google Play services package version: 252037038
 I  Google Play services maps renderer version(maps_core): 251625202
 I  FpsProfiler MAIN created on main
 I  Map using legacy labeler
 I  Network fetching: false
 I  requestDrawingConfig for epoch 736 legend ROADMAP
 I  Network fetching: true
 I  Network fetching: true
 I  Network fetching: true
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Repository initialization state: true
 D  UI frame monitoring started
 D  🔗 Auth state: User authenticated (qOhr05X5ySh5d1s6sqa43vImBE92)
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104 (age: 1314ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUser: Authentication ready for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  getCurrentUserIdSuspend: Checking authentication state
 D  🚀 HERO PERFORMANCE: Starting priority cache warming for instant map
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  Scheduling task DashboardMapCacheWarming with priority CRITICAL
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104)
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  Scheduling task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 with priority CRITICAL
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104)
 D  Executing task DashboardMapCacheWarming (CRITICAL)
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  🗺️ Testing simple geocoding for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  🧪 Testing native geocoding with: 1600 Amphitheatre Parkway, Mountain View, CA
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  🗺️ Loading addresses from repository for map display
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 I  Starting enhanced cache warming for user: qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Found existing request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  loadPage: Loading address page for user qOhr05X5ySh5d1s6sqa43vImBE92 with size: 50, key: null
 D  DEDUPLICATION: Found existing request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  loadAddressPage: Loading address page with size: 50, key: null
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  loadAddressPage: Executing Firestore query for addresses
 D  DEDUPLICATION: Starting new request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Verifying authentication for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Managing session for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 34ms, result: true
 D  ✅ SESSION MANAGER: Created/retrieved session qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Starting backpressure handling for dashboard_delivery_observation with THROTTLE_FIRST
 D  observeDeliveriesByUserId: Starting observation for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Executing task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 (CRITICAL)
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 0ms [OK]
 D  DEDUPLICATION: Starting new request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 9ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:2 [EFFICIENT]: 0ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 11ms
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104 (age: 1348ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 8ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 34ms, result: true
 D  Subscription cache warming successful in 36ms
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  [data.UserRepository] getCurrentUser(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 41ms [OK]
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104 (age: 1360ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 4ms, result: true
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 42ms, result: true
 D  DEDUPLICATION: Starting new request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  Subscription cache warming successful in 43ms
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=qOhr05X5ySh5d1s6sqa43vImBE92)
 D  getCurrentUserIdSuspend: Authentication confirmed for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  getUserById: Starting operation for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104)
 D  Reusing existing session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104 (age: 1369ms)
 D  DEDUPLICATION: Cleaned up completed request for key 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'session_creation_qOhr05X5ySh5d1s6sqa43vImBE92' in 6ms, result: true
 D  getUserSubscription: Using session qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:qOhr05X5ySh5d1s6sqa43vImBE92' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription'
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92/subscription' in 37ms, result: true
 D  Subscription cache warming successful in 37ms
 D  Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved user qOhr05X5ySh5d1s6sqa43vImBE92 for current user qOhr05X5ySh5d1s6sqa43vImBE92: true
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeUserProfile Load:67ms Steps:[repository_call,data_mapping,state_emission] UserDelay:67ms [CACHE_HIT] [FAST_UX]: 67ms [OK]
 D  getUserById: Found user qOhr05X5ySh5d1s6sqa43vImBE92 in local cache (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:3 Size:1299bytes [EFFICIENT]: 0ms [OK]
 D  [cache_system.UserRepository] cache_breakdown Check:15ms Hit:true ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92: 15ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:75ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:75ms [CACHE_HIT] [FAST_UX]: 75ms [OK]
 D  Task getUserById_qOhr05X5ySh5d1s6sqa43vImBE92 completed in 77ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:943bytes [EFFICIENT]: 0ms [OK]
 D  [data.UserRepository] getUserById(User) ID:qOhr05X5ySh5d1s6sqa43vImBE92 User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Size:1299bytes Source:cache Strategy:cache-first [CACHE_HIT]: 24ms [OK]
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 62ms, result: true
 D  User profile cache warming successful in 63ms
 D  DEDUPLICATION: Completed waiting for 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 77ms, result: true
 D  DEDUPLICATION: Completed new request 'users/qOhr05X5ySh5d1s6sqa43vImBE92' in 87ms, result: true
 D  User profile cache warming successful in 89ms
 D  User profile cache warming successful in 90ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [Firestore] QUERY user_addresses: 139ms [OK]
 D  loadAddressPage: Successfully loaded 1 DTOs (141ms total, query: 139ms)
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed waiting for 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 163ms, result: true
 D  Addresses cache warming successful in 163ms
 D  loadPage: Successfully loaded 1 addresses
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:63ms, subscription:success:37ms, addresses:success:163ms, total:185ms
 D  PII Fields Processed: 10
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 188ms, result: true
 D  [data.AddressPageLoader] loadPage(Address) User:qOhr05X5ySh5d1s6sqa43vImBE92 Count:1 Source:RemoteDataSource Strategy:none [CACHE_MISS]: 178ms [OK]
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Fallback: Loaded 1 addresses from repository
 D  Addresses cache warming successful in 223ms
 D  🗺️ MAP-CRITICAL WARMING COMPLETE: 221ms - addresses: Success(cachedAt=1749809394605, durationMs=163), user: Success(cachedAt=1749809394507, durationMs=63)
 D  🎯 SIMPLE GEOCODING: Testing native geocoding capability
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:90ms, subscription:success:36ms, addresses:success:223ms, total:225ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  Retrieved 2 deliveries for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation - Found 2 deliveries in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user qOhr05X5ySh5d1s6sqa43vImBE92 (session: qOhr05X5ySh5d1s6sqa43vImBE92_1749809393104, count: 2)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92'
 D  getAddresses (SSoT) - Fetching all addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_qOhr05X5ySh5d1s6sqa43vImBE92' in 7ms, result: true
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  🚀 MAP DATA LOADED: 1 addresses in 306ms
 D  Mapping Duration: 0ms
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user qOhr05X5ySh5d1s6sqa43vImBE92, fetching from remote.
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92'
 D  DEDUPLICATION: Completed new request 'addresses/user/qOhr05X5ySh5d1s6sqa43vImBE92' in 239ms, result: true
 D  Addresses cache warming successful in 240ms
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 I  Cache warming completed for user qOhr05X5ySh5d1s6sqa43vImBE92: user_profile:success:89ms, subscription:success:43ms, addresses:success:240ms, total:243ms
 D  getCurrentUser: Cache warming initiated for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:TODAY User:stats_calculation Size:2bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:WEEK User:stats_calculation Size:2bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:MONTH User:stats_calculation Size:2bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  🏠 HOME POSITION SET: lat/lng: (41.703128,-93.78315889999999) at zoom 15.0
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=Uooc9WOehbUH5JYJxveq, dto_userId=qOhr05X5ySh5d1s6sqa43vImBE92, dto_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, dto_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=Uooc9WOehbUH5JYJxveq, domain_userId=qOhr05X5ySh5d1s6sqa43vImBE92, domain_fullAddress=1220 NE Station Crossing Dr, Grimes, IA 50111, USA, domain_coordinates=Coordinates(latitude=41.703128, longitude=-93.78315889999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_qOhr05X5ySh5d1s6sqa43vImBE92_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user qOhr05X5ySh5d1s6sqa43vImBE92
 D  🗺️ INSTANT MAP DATA: Loaded 1 addresses from warmed cache
 D  Complete cache warming: 357ms total (warming: 221ms)
 D  [AddressMapper] toSsot(Address) ID:Uooc9WOehbUH5JYJxveq User:qOhr05X5ySh5d1s6sqa43vImBE92 Size:1309bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Native geocoding success: 1600 Amphitheatre Parkway, Mountain View, CA -> 37.4207967, -122.0852688
 D  Task DashboardMapCacheWarming completed in 364ms
 D  🚀 PRIORITY TASK SUCCESS: Cache warming and maps pre-fetch completed successfully
 I  Background concurrent mark compact GC freed 23MB AllocSpace bytes, 58(1944KB) LOS objects, 44% free, 29MB/53MB, paused 2.906ms,4.148ms total 279.580ms
 D  🏠 HOME POSITION: Stored position lat/lng: (41.703128,-93.78315889999999) at zoom 15.0
 I  Initial labeling completed.
 D  🚀 MAP READY: Maps Compose initialized and ready
 D  🚀 MAP LOADED: 1 markers ready
 D  🚀 BACKGROUND REFRESH: Skipping update, data already loaded
 E  Operation not started: uid=10209 pkg=com.autogratuity(null) op=START_FOREGROUND
---------------------------- PROCESS STARTED (12573) for package com.autogratuity ----------------------------
