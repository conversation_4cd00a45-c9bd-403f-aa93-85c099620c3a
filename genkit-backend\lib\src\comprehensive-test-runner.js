"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FirestoreTriggerSimulator = exports.ComprehensiveTestRunner = void 0;
const app_1 = require("firebase-admin/app");
const firestore_1 = require("firebase-admin/firestore");
const admin = __importStar(require("firebase-admin"));
// Import our test classes
const test_cloud_functions_1 = require("./test-cloud-functions");
const test_http_callables_1 = require("./test-http-callables");
// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
    (0, app_1.initializeApp)();
}
class FirestoreTriggerSimulator {
    constructor() {
        this.results = [];
        this.firestore = (0, firestore_1.getFirestore)();
    }
    async runTriggerSimulations() {
        console.log('🔥 Starting Firestore Trigger Simulations...\n');
        await this.simulateDeliveryWrittenTriggers();
        await this.simulateUserPreferencesChangeTrigger();
        await this.simulateGcsFileTrigger();
        this.printTriggerSummary();
        return this.results;
    }
    async simulateDeliveryWrittenTriggers() {
        console.log('📦 Simulating Delivery Written Triggers...');
        const testUserId = 'test-user-123';
        const testDeliveryId = `test-delivery-${Date.now()}`;
        const testAddressId = 'test-address-456';
        // Create test delivery document
        const deliveryData = {
            deliveryData: {
                userId: testUserId,
                orderId: 'test-order-789',
                addressId: testAddressId,
                reference: {
                    addressId: testAddressId
                },
                status: {
                    isCompleted: false,
                    isTipped: false
                },
                amounts: {
                    tipAmount: null, // Pending tip
                    basePay: 10.00
                },
                platform: 'shipt',
                timestamp: new Date().toISOString()
            }
        };
        // Test 1: Create delivery (should trigger both pending tips and address stats)
        await this.runTriggerTest('onDeliveryWrittenCheckPendingTips', 'Create Delivery with Pending Tip', async () => {
            const docRef = this.firestore
                .collection('users')
                .doc(testUserId)
                .collection('user_deliveries')
                .doc(testDeliveryId);
            await docRef.set(deliveryData);
            // Wait a moment for triggers to process
            await new Promise(resolve => setTimeout(resolve, 1000));
            // Verify document was created
            const doc = await docRef.get();
            return { created: doc.exists, data: doc.data() };
        });
        // Test 2: Update delivery with tip (should trigger address stats but not pending tips)
        await this.runTriggerTest('onDeliveryWrittenUpdateAddressStats', 'Update Delivery with Tip Amount', async () => {
            const updatedData = {
                ...deliveryData,
                deliveryData: {
                    ...deliveryData.deliveryData,
                    status: {
                        isCompleted: true,
                        isTipped: true
                    },
                    amounts: {
                        ...deliveryData.deliveryData.amounts,
                        tipAmount: 5.50
                    }
                }
            };
            const docRef = this.firestore
                .collection('users')
                .doc(testUserId)
                .collection('user_deliveries')
                .doc(testDeliveryId);
            await docRef.update(updatedData);
            // Wait for triggers
            await new Promise(resolve => setTimeout(resolve, 1000));
            const doc = await docRef.get();
            return { updated: doc.exists, data: doc.data() };
        });
        // Test 3: Delete delivery (should trigger address stats)
        await this.runTriggerTest('onDeliveryWrittenUpdateAddressStats', 'Delete Delivery Document', async () => {
            const docRef = this.firestore
                .collection('users')
                .doc(testUserId)
                .collection('user_deliveries')
                .doc(testDeliveryId);
            await docRef.delete();
            // Wait for triggers
            await new Promise(resolve => setTimeout(resolve, 1000));
            const doc = await docRef.get();
            return { deleted: !doc.exists };
        });
    }
    async simulateUserPreferencesChangeTrigger() {
        console.log('⚙️ Simulating User Preferences Change Trigger...');
        const testUserId = 'test-user-123';
        const testDocId = `test-prefs-${Date.now()}`;
        await this.runTriggerTest('onUserPreferencesChange', 'Update User Preferences', async () => {
            const preferencesData = {
                notifications: {
                    tipNotifications: true,
                    emailNotifications: false
                },
                dndSettings: {
                    enableAutoDnd: true,
                    threshold: 0.50
                },
                lastUpdated: new Date().toISOString()
            };
            const docRef = this.firestore
                .collection('users')
                .doc(testUserId)
                .collection('preferences')
                .doc(testDocId);
            await docRef.set(preferencesData);
            // Wait for trigger
            await new Promise(resolve => setTimeout(resolve, 1000));
            // Clean up
            await docRef.delete();
            return { preferencesUpdated: true, data: preferencesData };
        });
    }
    async simulateGcsFileTrigger() {
        console.log('📁 Simulating GCS File Upload Trigger...');
        // Note: This simulates the trigger logic without actually uploading to GCS
        await this.runTriggerTest('onGeoJsonFileFinalized', 'Simulate GeoJSON File Upload', async () => {
            // This would normally be triggered by GCS, but we can test the flow logic
            const mockGcsEvent = {
                bucket: 'autogratuity-me.firebasestorage.app',
                file: 'user-uploads/test-user-123/test-geojson.json'
            };
            // The actual trigger would call the flow, but we're just simulating the event structure
            return {
                simulatedEvent: mockGcsEvent,
                note: 'GCS trigger simulation - actual file processing would require real GCS upload'
            };
        });
    }
    async runTriggerTest(triggerName, testName, testFunction) {
        const startTime = Date.now();
        try {
            console.log(`  ⏳ Running: ${testName}...`);
            const result = await testFunction();
            const duration = Date.now() - startTime;
            this.results.push({
                triggerName,
                testName,
                success: true,
                result,
                duration
            });
            console.log(`  ✅ ${testName} - Success (${duration}ms)`);
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.results.push({
                triggerName,
                testName,
                success: false,
                error: error.message,
                duration
            });
            console.log(`  ❌ ${testName} - Failed: ${error.message} (${duration}ms)`);
        }
        console.log(''); // Add spacing
    }
    printTriggerSummary() {
        console.log('📋 Firestore Trigger Test Summary:');
        console.log('==================================');
        const successful = this.results.filter(r => r.success).length;
        const failed = this.results.filter(r => !r.success).length;
        const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
        console.log(`Total Trigger Tests: ${this.results.length}`);
        console.log(`Successful: ${successful}`);
        console.log(`Failed: ${failed}`);
        console.log(`Total Duration: ${totalDuration}ms`);
        console.log('');
    }
}
exports.FirestoreTriggerSimulator = FirestoreTriggerSimulator;
class ComprehensiveTestRunner {
    async runAllTests() {
        console.log('🚀 Starting Comprehensive Cloud Function Testing Suite');
        console.log('=====================================================\n');
        const startTime = Date.now();
        try {
            // 1. Test individual flows
            console.log('Phase 1: Testing Individual Flows');
            console.log('----------------------------------');
            const flowTester = new test_cloud_functions_1.CloudFunctionTester();
            const flowResults = await flowTester.runAllTests();
            console.log('\n' + '='.repeat(50) + '\n');
            // 2. Test HTTP callable functions
            console.log('Phase 2: Testing HTTP Callable Functions');
            console.log('----------------------------------------');
            const httpTester = new test_http_callables_1.HttpCallableTester();
            const httpResults = await httpTester.runAllHttpTests();
            console.log('\n' + '='.repeat(50) + '\n');
            // 3. Test Firestore triggers
            console.log('Phase 3: Testing Firestore Triggers');
            console.log('-----------------------------------');
            const triggerSimulator = new FirestoreTriggerSimulator();
            const triggerResults = await triggerSimulator.runTriggerSimulations();
            console.log('\n' + '='.repeat(50) + '\n');
            // 4. Overall summary
            this.printOverallSummary(flowResults, httpResults, triggerResults, startTime);
        }
        catch (error) {
            console.error('❌ Test suite failed:', error.message);
            process.exit(1);
        }
    }
    printOverallSummary(flowResults, httpResults, triggerResults, startTime) {
        const totalDuration = Date.now() - startTime;
        console.log('🎯 OVERALL TEST SUITE SUMMARY');
        console.log('=============================');
        const flowSuccess = flowResults.filter(r => r.success).length;
        const httpSuccess = httpResults.filter(r => r.success).length;
        const triggerSuccess = triggerResults.filter(r => r.success).length;
        const totalTests = flowResults.length + httpResults.length + triggerResults.length;
        const totalSuccess = flowSuccess + httpSuccess + triggerSuccess;
        const totalFailed = totalTests - totalSuccess;
        console.log(`📊 Test Categories:`);
        console.log(`   Flow Tests: ${flowSuccess}/${flowResults.length} passed`);
        console.log(`   HTTP Tests: ${httpSuccess}/${httpResults.length} passed`);
        console.log(`   Trigger Tests: ${triggerSuccess}/${triggerResults.length} passed`);
        console.log('');
        console.log(`📈 Overall Results:`);
        console.log(`   Total Tests: ${totalTests}`);
        console.log(`   Successful: ${totalSuccess}`);
        console.log(`   Failed: ${totalFailed}`);
        console.log(`   Success Rate: ${((totalSuccess / totalTests) * 100).toFixed(1)}%`);
        console.log(`   Total Duration: ${(totalDuration / 1000).toFixed(2)}s`);
        if (totalFailed === 0) {
            console.log('\n🎉 All tests passed! Your cloud functions are working correctly.');
        }
        else {
            console.log(`\n⚠️ ${totalFailed} test(s) failed. Review the detailed results above.`);
        }
        console.log('\n📝 Next Steps:');
        console.log('   1. Review any failed tests and fix issues');
        console.log('   2. Deploy functions: npm run build && firebase deploy --only functions');
        console.log('   3. Test in production with real data');
        console.log('   4. Monitor cloud function logs for any runtime issues');
    }
}
exports.ComprehensiveTestRunner = ComprehensiveTestRunner;
// Main execution function
async function main() {
    const runner = new ComprehensiveTestRunner();
    await runner.runAllTests();
}
// Run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=comprehensive-test-runner.js.map