package com.autogratuity.data.datasource.remote

import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.util_kt.documentSnapshotToUserProfileDto
import com.autogratuity.data.util.PageResult
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.MetadataChanges
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.QuerySnapshot
import com.google.firebase.firestore.SetOptions
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.time.OffsetDateTime
import javax.inject.Inject
import com.autogratuity.data.model.generated_kt.User_profile as UserProfileDto

/**
 * Interface for remote data operations related to Subscription.
 * All methods operate with DTO objects (User_profile.UserSubscription).
 *
 * Following clarity.md: ALL Firestore interactions must be handled by RemoteDataSource.
 * This includes query building, execution, and pagination operations.
 */
interface SubscriptionRemoteDataSource {
    // === EXISTING CORE OPERATIONS ===
    suspend fun getUserSubscriptionDto(userId: String): Result<UserProfileDto.UserSubscription?>
    suspend fun saveUserSubscriptionDto(userId: String, subscriptionDto: UserProfileDto.UserSubscription): Result<Unit>
    suspend fun updateUserSubscriptionDto(userId: String, subscriptionDto: UserProfileDto.UserSubscription): Result<Unit>
    suspend fun updateUserSubscriptionFieldsDto(userId: String, fields: Map<String, Any>): Result<Unit>
    suspend fun deleteUserSubscriptionDto(userId: String): Result<Unit>
    fun observeUserSubscriptionDto(userId: String): Flow<UserProfileDto.UserSubscription?>

    // === NEW PAGINATION OPERATIONS ===
    /**
     * Load a page of subscription DTOs using Firestore pagination.
     * Handles all Firestore query building and execution for pagination.
     */
    suspend fun loadSubscriptionPage(pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<UserProfileDto.UserSubscription, DocumentSnapshot>>

    /**
     * Load a page of active subscription DTOs only.
     */
    suspend fun loadActiveSubscriptionPage(pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<UserProfileDto.UserSubscription, DocumentSnapshot>>

    // === NEW QUERY BUILDER OPERATIONS ===
    /**
     * Query user subscriptions by status using QueryBuilder.
     * Encapsulates all Firestore query logic per clarity.md requirements.
     */
    suspend fun queryUserSubscriptionsByStatus(userId: String, status: String, limit: Int = 10): Result<QuerySnapshot>

    /**
     * Query active subscriptions using QueryBuilder.
     * Encapsulates all Firestore query logic per clarity.md requirements.
     */
    suspend fun queryActiveSubscriptions(userId: String, limit: Int = 10): Result<QuerySnapshot>

    /**
     * Validate subscription with complex query.
     * Encapsulates all Firestore query logic per clarity.md requirements.
     */
    suspend fun validateSubscriptionWithQuery(userId: String): Result<QuerySnapshot>

    /**
     * Query user subscriptions by status and return DTOs.
     * Handles both querying and DTO conversion internally.
     */
    suspend fun queryUserSubscriptionDtosByStatus(userId: String, status: String, limit: Int = 10): Result<List<UserProfileDto.UserSubscription>>

    /**
     * Query active subscriptions and return DTOs.
     * Handles both querying and DTO conversion internally.
     */
    suspend fun queryActiveSubscriptionDtos(userId: String, limit: Int = 10): Result<List<UserProfileDto.UserSubscription>>
}

/**
 * Implementation of SubscriptionRemoteDataSource using Firestore.
 * Handles all remote subscription operations with DTOs.
 *
 * Enhanced to handle ALL Firestore interactions including pagination and QueryBuilder operations
 * following clarity.md principles.
 */
@OptIn(ExperimentalCoroutinesApi::class)
class SubscriptionRemoteDataSourceImpl @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val ioDispatcher: kotlinx.coroutines.CoroutineDispatcher,
    private val authStateCoordinator: com.autogratuity.data.util.AuthenticationStateCoordinator
) : SubscriptionRemoteDataSource {

    companion object {
        private const val TAG = "SubscriptionRemoteDataSource"
        private const val COLLECTION_USER_PROFILES = "users"
    }

    // === DTO CONVERSION UTILITIES ===
    
    /**
     * Creates UserSubscription DTO from Firestore document data.
     * This logic belongs in RemoteDataSource per clarity.md principles.
     */
    private fun createSubscriptionDtoFromData(data: Map<String, Any>): UserProfileDto.UserSubscription {
        return UserProfileDto.UserSubscription(
            status = data["status"] as? String,
            level = data["level"] as? String,
            isActive = data["isActive"] as? Boolean,
            startDate = data["startDate"] as? OffsetDateTime,
            expiryDate = data["expiryDate"] as? OffsetDateTime,
            isLifetime = data["isLifetime"] as? Boolean,
            provider = data["provider"] as? String,
            orderId = data["orderId"] as? String,
            verification = data["verification"]?.let { verificationRaw ->
                @Suppress("UNCHECKED_CAST")
                val verificationData = verificationRaw as? Map<String, Any> ?: return@let null
                UserProfileDto.Verification(
                    lastVerified = verificationData["lastVerified"] as? OffsetDateTime,
                    status = verificationData["status"] as? String,
                    error = verificationData["error"] as? String
                )
            }
        )
    }

    // === EXISTING CORE OPERATIONS ===

    override suspend fun getUserSubscriptionDto(userId: String): Result<UserProfileDto.UserSubscription?> {
        val fullPath = "$COLLECTION_USER_PROFILES/$userId"
        
        // === STEP 1: AUTHENTICATION READINESS CHECK ===
        val authState = authStateCoordinator.waitForAuthentication(timeoutMs = 3000)
        if (authState !is com.autogratuity.data.util.AuthenticationStateCoordinator.AuthReadyState.Authenticated) {
            Log.w(TAG, "getUserSubscriptionDto: Authentication not ready for user $userId: $authState")
            return Result.Error(Exception("Authentication not ready: $authState"))
        }
        
        // === STEP 2: NETWORK CONNECTIVITY CHECK ===
        // TEMPORARILY DISABLED: FlowNetworkMonitor giving false negatives in emulator
        // if (!networkMonitor.isNetworkAvailable()) {
        //     Log.w(TAG, "getUserSubscriptionDto: No network connectivity for user $userId")
        //     return Result.Error(Exception("No network connectivity"))
        // }
        
        // === STEP 3: DIRECT FIRESTORE OPERATION ===
        return withContext(ioDispatcher) {
            executeFirestoreGetOperation(userId, fullPath)
        }
    }
    
    private suspend fun executeFirestoreGetOperation(userId: String, fullPath: String): Result<UserProfileDto.UserSubscription?> {
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        
        try {
            Log.d(TAG, "executeFirestoreGetOperation: Starting Firestore fetch for user $userId")
            
            val document = firestore.collection(COLLECTION_USER_PROFILES)
                .document(userId)
                .get()
                .await()
            
            val firestoreDuration = firestoreStartTime.elapsedNow()

            if (document.exists()) {
                try {
                    val userProfileDto = documentSnapshotToUserProfileDto(document)
                    val subscriptionDto = userProfileDto.subscription
                    
                    // Monitor successful Firestore read
                    com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                        collection = COLLECTION_USER_PROFILES,
                        documentId = userId,
                        success = true,
                        duration = firestoreDuration,
                        dataSizeBytes = document.toString().length,
                        queryType = "GET_DOCUMENT",
                        fullPath = fullPath,
                        cacheSource = document.metadata.isFromCache.toString()
                    )
                    
                    Log.d(TAG, "executeFirestoreGetOperation: Successfully retrieved subscription for user $userId in ${firestoreDuration.inWholeMilliseconds}ms")
                    return Result.Success(subscriptionDto)
                } catch (e: Exception) {
                    Log.e(TAG, "executeFirestoreGetOperation: Error converting document to UserProfileDto for user $userId", e)
                    return Result.Error(e)
                }
            } else {
                Log.d(TAG, "executeFirestoreGetOperation: No document found for user $userId")
                return Result.Success(null)
            }
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // Monitor failed Firestore read
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = COLLECTION_USER_PROFILES,
                documentId = userId,
                success = false,
                duration = firestoreDuration,
                dataSizeBytes = 0,
                queryType = "GET_DOCUMENT",
                fullPath = fullPath,
                error = e
            )
            
            Log.e(TAG, "executeFirestoreGetOperation: Firestore error for user $userId", e)
            return Result.Error(e)
        }
    }

    override suspend fun saveUserSubscriptionDto(userId: String, subscriptionDto: UserProfileDto.UserSubscription): Result<Unit> {
        val fullPath = "$COLLECTION_USER_PROFILES/$userId"
        
        // === STEP 1: AUTHENTICATION READINESS CHECK ===
        val authState = authStateCoordinator.waitForAuthentication(timeoutMs = 3000)
        if (authState !is com.autogratuity.data.util.AuthenticationStateCoordinator.AuthReadyState.Authenticated) {
            Log.w(TAG, "saveUserSubscriptionDto: Authentication not ready for user $userId: $authState")
            return Result.Error(Exception("Authentication not ready: $authState"))
        }
        
        // === STEP 2: NETWORK CONNECTIVITY CHECK ===
        // TEMPORARILY DISABLED: FlowNetworkMonitor giving false negatives in emulator
        // if (!networkMonitor.isNetworkAvailable()) {
        //     Log.w(TAG, "saveUserSubscriptionDto: No network connectivity for user $userId")
        //     return Result.Error(Exception("No network connectivity"))
        // }
        
        // === STEP 3: DIRECT FIRESTORE OPERATION ===
        return withContext(ioDispatcher) {
            executeFirestoreSaveOperation(userId, subscriptionDto, fullPath)
        }
    }
    
    private suspend fun executeFirestoreSaveOperation(userId: String, subscriptionDto: UserProfileDto.UserSubscription, fullPath: String): Result<Unit> {
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        
        try {
            Log.d(TAG, "executeFirestoreSaveOperation: Starting Firestore save for user $userId")
            
            // ENHANCED: Use profileData prefix for nested Firestore structure
            val subscriptionData = convertSubscriptionDtoToMap(subscriptionDto)
            val updateData = mapOf("profileData.subscription" to subscriptionData)
            
            firestore.collection(COLLECTION_USER_PROFILES)
                .document(userId)
                .set(updateData, SetOptions.merge())
                .await()

            val firestoreDuration = firestoreStartTime.elapsedNow()
            val dataSize = subscriptionData.toString().length

            // ENHANCED: Comprehensive Firestore write monitoring
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = COLLECTION_USER_PROFILES,
                documentId = userId,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                writeType = "SET_MERGE",
                fieldsUpdated = listOf("profileData.subscription"),
                userId = userId,
                documentData = updateData
            )

            // Add session correlation for subscription saves
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_save:$userId")

            Log.d(TAG, "executeFirestoreSaveOperation: Successfully saved subscription for user $userId in ${firestoreDuration.inWholeMilliseconds}ms")
            return Result.Success(Unit)
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // Monitor failed Firestore write
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = COLLECTION_USER_PROFILES,
                documentId = userId,
                duration = firestoreDuration,
                success = false,
                dataSizeBytes = 0,
                error = e,
                fullPath = fullPath,
                writeType = "SET_MERGE",
                fieldsUpdated = listOf("profileData.subscription"),
                userId = userId
            )
            
            Log.e(TAG, "executeFirestoreSaveOperation: Firestore save error for user $userId", e)
            return Result.Error(e)
        }
    }

    override suspend fun updateUserSubscriptionDto(userId: String, subscriptionDto: UserProfileDto.UserSubscription): Result<Unit> = withContext(ioDispatcher) {
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$COLLECTION_USER_PROFILES/$userId"
        
        try {
            Log.d(TAG, "updateUserSubscriptionDto: Updating subscription for user $userId to path: $fullPath")
            
            // ENHANCED: Use profileData prefix for nested Firestore structure
            val subscriptionData = convertSubscriptionDtoToMap(subscriptionDto)
            val updateData = mapOf("profileData.subscription" to subscriptionData)
            
            firestore.collection(COLLECTION_USER_PROFILES)
                .document(userId)
                .update(updateData)
                .await()

            val firestoreDuration = firestoreStartTime.elapsedNow()
            val dataSize = subscriptionData.toString().length

            // ENHANCED: Comprehensive Firestore write monitoring
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = COLLECTION_USER_PROFILES,
                documentId = userId,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                writeType = "UPDATE",
                fieldsUpdated = listOf("profileData.subscription"),
                userId = userId,
                documentData = updateData
            )

            // Add session correlation for subscription updates
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_update:$userId")

            Log.d(TAG, "updateUserSubscriptionDto: Successfully updated subscription for user $userId (${firestoreDuration.inWholeMilliseconds}ms)")
            Result.Success(Unit)
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // Monitor failed Firestore write
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = COLLECTION_USER_PROFILES,
                documentId = userId,
                duration = firestoreDuration,
                success = false,
                dataSizeBytes = 0,
                error = e,
                fullPath = fullPath,
                writeType = "UPDATE",
                fieldsUpdated = listOf("profileData.subscription"),
                userId = userId
            )
            
            Log.e(TAG, "updateUserSubscriptionDto: Error updating subscription for user $userId to path: $fullPath", e)
            Result.Error(e)
        }
    }

    override suspend fun updateUserSubscriptionFieldsDto(userId: String, fields: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$COLLECTION_USER_PROFILES/$userId"
        
        try {
            Log.d(TAG, "updateUserSubscriptionFieldsDto: Updating subscription fields for user $userId to path: $fullPath")
            
            // ENHANCED: Prefix all field paths with profileData.subscription
            val prefixedFields = fields.mapKeys { "profileData.subscription.${it.key}" }
            
            firestore.collection(COLLECTION_USER_PROFILES)
                .document(userId)
                .update(prefixedFields)
                .await()

            val firestoreDuration = firestoreStartTime.elapsedNow()
            val dataSize = prefixedFields.toString().length

            // ENHANCED: Comprehensive Firestore write monitoring
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = COLLECTION_USER_PROFILES,
                documentId = userId,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                writeType = "UPDATE_FIELDS",
                fieldsUpdated = prefixedFields.keys.toList(),
                userId = userId,
                documentData = prefixedFields
            )

            // Add session correlation for subscription field updates
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_fields_update:$userId:${fields.keys.joinToString(",")}")

            Log.d(TAG, "updateUserSubscriptionFieldsDto: Successfully updated subscription fields for user $userId (${firestoreDuration.inWholeMilliseconds}ms)")
            Result.Success(Unit)
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // Monitor failed Firestore write
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = COLLECTION_USER_PROFILES,
                documentId = userId,
                duration = firestoreDuration,
                success = false,
                dataSizeBytes = 0,
                error = e,
                fullPath = fullPath,
                writeType = "UPDATE_FIELDS",
                fieldsUpdated = fields.keys.map { "profileData.subscription.$it" },
                userId = userId
            )
            
            Log.e(TAG, "updateUserSubscriptionFieldsDto: Error updating subscription fields for user $userId to path: $fullPath", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteUserSubscriptionDto(userId: String): Result<Unit> = withContext(ioDispatcher) {
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$COLLECTION_USER_PROFILES/$userId"
        
        try {
            Log.d(TAG, "deleteUserSubscriptionDto: Deleting subscription for user $userId from path: $fullPath")
            
            // ENHANCED: Use profileData prefix for nested Firestore structure
            val deleteData = mapOf("profileData.subscription" to null)
            
            firestore.collection(COLLECTION_USER_PROFILES)
                .document(userId)
                .update(deleteData)
                .await()

            val firestoreDuration = firestoreStartTime.elapsedNow()

            // ENHANCED: Comprehensive Firestore write monitoring
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = COLLECTION_USER_PROFILES,
                documentId = userId,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = 0,
                fullPath = fullPath,
                writeType = "DELETE_FIELD",
                fieldsUpdated = listOf("profileData.subscription"),
                userId = userId,
                documentData = deleteData
            )

            // Add session correlation for subscription deletes
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_delete:$userId")

            Log.d(TAG, "deleteUserSubscriptionDto: Successfully deleted subscription for user $userId (${firestoreDuration.inWholeMilliseconds}ms)")
            Result.Success(Unit)
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // Monitor failed Firestore write
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = COLLECTION_USER_PROFILES,
                documentId = userId,
                duration = firestoreDuration,
                success = false,
                dataSizeBytes = 0,
                error = e,
                fullPath = fullPath,
                writeType = "DELETE_FIELD",
                fieldsUpdated = listOf("profileData.subscription"),
                userId = userId
            )
            
            Log.e(TAG, "deleteUserSubscriptionDto: Error deleting subscription for user $userId from path: $fullPath", e)
            Result.Error(e)
        }
    }

    override fun observeUserSubscriptionDto(userId: String): Flow<UserProfileDto.UserSubscription?> {
        return callbackFlow {
            val observationStartTime = kotlin.time.TimeSource.Monotonic.markNow()
            val fullPath = "$COLLECTION_USER_PROFILES/$userId"
            
            Log.d(TAG, "observeUserSubscriptionDto: Setting up real-time listener for user $userId at path: $fullPath")
            
            // ENHANCED: Add session correlation for real-time subscriptions
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_realtime:start:$userId")
            
            val listener = firestore.collection(COLLECTION_USER_PROFILES)
                .document(userId)
                .addSnapshotListener(MetadataChanges.INCLUDE) { snapshot, error ->
                    val snapshotEventTime = kotlin.time.TimeSource.Monotonic.markNow()
                    val snapshotDuration = snapshotEventTime - observationStartTime
                    
                    if (error != null) {
                        // Monitor failed real-time subscription
                        com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                            collection = COLLECTION_USER_PROFILES,
                            documentId = userId,
                            duration = snapshotDuration,
                            success = false,
                            dataSizeBytes = 0,
                            error = error,
                            fullPath = fullPath,
                            queryType = "REALTIME_LISTENER",
                            queryFilters = listOf("subscription_realtime_error"),
                            resultCount = 0,
                            userId = userId,
                            cacheSource = "ERROR"
                        )
                        
                        Log.e(TAG, "observeUserSubscriptionDto: Error in snapshot listener for user $userId", error)
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null && snapshot.exists()) {
                        val documentExists = snapshot.exists()
                        val dataSize = if (documentExists) snapshot.data?.toString()?.length ?: 0 else 0
                        
                        // Monitor successful real-time update
                        com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                            collection = COLLECTION_USER_PROFILES,
                            documentId = userId,
                            duration = snapshotDuration,
                            success = true,
                            dataSizeBytes = dataSize,
                            fullPath = fullPath,
                            queryType = "REALTIME_LISTENER",
                            queryFilters = listOf("subscription_realtime_update"),
                            resultCount = if (documentExists) 1 else 0,
                            userId = userId,
                            cacheSource = if (snapshot.metadata.isFromCache) "CACHE" else "SERVER"
                        )
                        
                        // Add session correlation for real-time events
                        com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_realtime:update:$userId")
                        
                        try {
                            val userProfileDto = documentSnapshotToUserProfileDto(snapshot)
                            val subscription = userProfileDto.subscription
                            trySend(subscription)
                        } catch (e: Exception) {
                            Log.e(TAG, "observeUserSubscriptionDto: Error parsing UserProfile DTO", e)
                            close(e)
                        }
                    } else {
                        // Monitor null/missing document in real-time
                        com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                            collection = COLLECTION_USER_PROFILES,
                            documentId = userId,
                            duration = snapshotDuration,
                            success = true,
                            dataSizeBytes = 0,
                            fullPath = fullPath,
                            queryType = "REALTIME_LISTENER",
                            queryFilters = listOf("subscription_realtime_null"),
                            resultCount = 0,
                            userId = userId,
                            cacheSource = if (snapshot?.metadata?.isFromCache == true) "CACHE" else "SERVER"
                        )
                        
                        trySend(null)
                    }
                }

            awaitClose { 
                val totalObservationTime = observationStartTime.elapsedNow()
                
                // Monitor real-time subscription close
                com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_realtime:close:$userId:${totalObservationTime.inWholeMilliseconds}ms")
                
                Log.d(TAG, "observeUserSubscriptionDto: Closing real-time listener for user $userId (observed for ${totalObservationTime.inWholeMilliseconds}ms)")
                listener.remove() 
            }
        }.flowOn(ioDispatcher)
    }

    // === NEW PAGINATION OPERATIONS ===

    override suspend fun loadSubscriptionPage(pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<UserProfileDto.UserSubscription, DocumentSnapshot>> = withContext(ioDispatcher) {
        val operationStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        return@withContext try {
            Log.d(TAG, "loadSubscriptionPage: Loading subscription page with size: $pageSize, key: ${pageKey?.id}")

            // ENHANCED: Add session correlation for pagination
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_pagination:load:$pageSize")

            // Build the query for user subscriptions using QueryBuilder
            val queryResult = buildSubscriptionQuery(pageSize, pageKey)
            when (queryResult) {
                is Result.Success -> {
                    val query = queryResult.data
            
                    // Execute the query (monitoring handled by ClarityArchitectureMonitor)
                    val querySnapshot = query.get().await()
                    
                    // Map the results to DTOs
                    val subscriptions = mapQuerySnapshotToSubscriptionDtos(querySnapshot)
                    
                    // Determine next page key
                    val nextPageKey: DocumentSnapshot? = if (querySnapshot.documents.isNotEmpty()) {
                        querySnapshot.documents.last()
                    } else {
                        null
                    }

                    val totalDuration = operationStartTime.elapsedNow()
                    val resultCount = subscriptions.size
                    val dataSize = subscriptions.toString().length

                    // ENHANCED: Monitor pagination Firestore query with detailed context
                    com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreQuery(
                        collection = COLLECTION_USER_PROFILES,
                        queryDescription = "subscription_pagination(pageSize=$pageSize,key=${pageKey?.id ?: "none"})",
                        duration = totalDuration,
                        success = true,
                        resultCount = resultCount
                    )

                    // ENHANCED: Monitor remote datasource operation with pagination context
                    com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "SubscriptionRemoteDataSource",
                        operation = "loadSubscriptionPage",
                        duration = totalDuration,
                        success = true,
                        cacheHit = false, // Always remote
                        dataType = "UserSubscription",
                        entityId = "pagination_$pageSize",
                        resultCount = resultCount,
                        dataSource = "firestore_pagination",
                        dataSize = dataSize,
                        queryDetails = "pageSize=$pageSize,pageKey=${pageKey?.id ?: "none"}",
                        cacheStrategy = "paginated_fetch"
                    )

                    Log.d(TAG, "loadSubscriptionPage: Successfully loaded $resultCount subscriptions (${totalDuration.inWholeMilliseconds}ms)")
                    Result.Success(PageResult(subscriptions, nextPageKey))
                }
                is Result.Error -> {
                    Log.e(TAG, "loadSubscriptionPage: Error building query", queryResult.exception)
                    Result.Error(queryResult.exception)
                }
                is Result.Loading -> {
                    Log.w(TAG, "loadSubscriptionPage: Unexpected Loading state from buildSubscriptionQuery")
                    Result.Error(IllegalStateException("buildSubscriptionQuery returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            val totalDuration = operationStartTime.elapsedNow()
            
            // Monitor failed pagination query
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreQuery(
                collection = COLLECTION_USER_PROFILES,
                queryDescription = "subscription_pagination(pageSize=$pageSize,key=${pageKey?.id ?: "none"})",
                duration = totalDuration,
                success = false,
                resultCount = 0,
                error = e
            )
            
            Log.e(TAG, "loadSubscriptionPage: Error loading subscription page (${totalDuration.inWholeMilliseconds}ms)", e)
            Result.Error(e)
        }
    }

    override suspend fun loadActiveSubscriptionPage(pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<UserProfileDto.UserSubscription, DocumentSnapshot>> = withContext(ioDispatcher) {
        val operationStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        return@withContext try {
            Log.d(TAG, "loadActiveSubscriptionPage: Loading active subscription page with size: $pageSize, key: ${pageKey?.id}")

            // ENHANCED: Add session correlation for active subscription pagination
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_pagination:active:$pageSize")

            // Build the query for active subscriptions using QueryBuilder
            val queryResult = buildActiveSubscriptionQuery(pageSize, pageKey)
            when (queryResult) {
                is Result.Success -> {
                    val query = queryResult.data
            
                    // Execute the query (monitoring handled by ClarityArchitectureMonitor)
                    val querySnapshot = query.get().await()
                    
                    // Map the results to DTOs
                    val subscriptions = mapQuerySnapshotToSubscriptionDtos(querySnapshot)
                    
                    // Determine next page key
                    val nextPageKey: DocumentSnapshot? = if (querySnapshot.documents.isNotEmpty()) {
                        querySnapshot.documents.last()
                    } else {
                        null
                    }

                    val totalDuration = operationStartTime.elapsedNow()
                    val resultCount = subscriptions.size
                    val dataSize = subscriptions.toString().length

                    // ENHANCED: Monitor active subscription pagination Firestore query
                    com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreQuery(
                        collection = COLLECTION_USER_PROFILES,
                        queryDescription = "active_subscription_pagination(pageSize=$pageSize,key=${pageKey?.id ?: "none"})",
                        duration = totalDuration,
                        success = true,
                        resultCount = resultCount
                    )

                    // ENHANCED: Monitor remote datasource operation for active subscription pagination
                    com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "SubscriptionRemoteDataSource",
                        operation = "loadActiveSubscriptionPage",
                        duration = totalDuration,
                        success = true,
                        cacheHit = false, // Always remote
                        dataType = "ActiveUserSubscription",
                        entityId = "active_pagination_$pageSize",
                        resultCount = resultCount,
                        dataSource = "firestore_active_pagination",
                        dataSize = dataSize,
                        queryDetails = "pageSize=$pageSize,pageKey=${pageKey?.id ?: "none"},filter=isActive=true",
                        cacheStrategy = "paginated_active_fetch"
                    )

                    Log.d(TAG, "loadActiveSubscriptionPage: Successfully loaded $resultCount active subscriptions (${totalDuration.inWholeMilliseconds}ms)")
                    Result.Success(PageResult(subscriptions, nextPageKey))
                }
                is Result.Error -> {
                    Log.e(TAG, "loadActiveSubscriptionPage: Error building active query", queryResult.exception)
                    Result.Error(queryResult.exception)
                }
                is Result.Loading -> {
                    Log.w(TAG, "loadActiveSubscriptionPage: Unexpected Loading state from buildActiveSubscriptionQuery")
                    Result.Error(IllegalStateException("buildActiveSubscriptionQuery returned Loading unexpectedly"))
                }
            }

        } catch (e: Exception) {
            val totalDuration = operationStartTime.elapsedNow()
            
            // Monitor failed active subscription pagination query
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreQuery(
                collection = COLLECTION_USER_PROFILES,
                queryDescription = "active_subscription_pagination(pageSize=$pageSize,key=${pageKey?.id ?: "none"})",
                duration = totalDuration,
                success = false,
                resultCount = 0,
                error = e
            )
            
            Log.e(TAG, "loadActiveSubscriptionPage: Error loading active subscription page (${totalDuration.inWholeMilliseconds}ms)", e)
            Result.Error(e)
        }
    }

    // === NEW QUERY BUILDER OPERATIONS ===

    override suspend fun queryUserSubscriptionsByStatus(userId: String, status: String, limit: Int): Result<QuerySnapshot> = withContext(ioDispatcher) {
        return@withContext executeProfiledFirestoreQuery(
            operationName = "queryUserSubscriptionsByStatus",
            userId = userId,
            status = status,
            limit = limit
        )
    }

    override suspend fun queryActiveSubscriptions(userId: String, limit: Int): Result<QuerySnapshot> = withContext(ioDispatcher) {
        return@withContext executeProfiledFirestoreQuery(
            operationName = "queryActiveSubscriptions",
            userId = userId,
            limit = limit
        )
    }

    override suspend fun validateSubscriptionWithQuery(userId: String): Result<QuerySnapshot> = withContext(ioDispatcher) {
        return@withContext executeProfiledFirestoreQuery(
            operationName = "validateSubscriptionWithQuery",
            userId = userId
        )
    }

    override suspend fun queryUserSubscriptionDtosByStatus(
        userId: String,
        status: String,
        limit: Int
    ): Result<List<UserProfileDto.UserSubscription>> = withContext(ioDispatcher) {
        val operationStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        return@withContext try {
            Log.d(TAG, "queryUserSubscriptionDtosByStatus: Querying subscriptions for user $userId with status $status (limit: $limit)")

            // ENHANCED: Add session correlation for status-based queries
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_query_by_status:$userId:$status")

            // Execute the profiled Firestore query for subscriptions by status
            val queryResult = executeProfiledFirestoreQuery(
                operationName = "queryUserSubscriptionsByStatus",
                userId = userId,
                status = status,
                limit = limit
            )

            when (queryResult) {
                is Result.Success -> {
                    val querySnapshot = queryResult.data
                    
                    // Map the QuerySnapshot to UserSubscription DTOs
                    val subscriptionDtos = mapQuerySnapshotToSubscriptionDtos(querySnapshot)
                    
                    val totalDuration = operationStartTime.elapsedNow()
                    val resultCount = subscriptionDtos.size
                    val dataSize = subscriptionDtos.toString().length

                    // ENHANCED: Monitor successful DTO query operation
                    com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "SubscriptionRemoteDataSource",
                        operation = "queryUserSubscriptionDtosByStatus",
                        duration = totalDuration,
                        success = true,
                        cacheHit = false, // Always remote
                        dataType = "UserSubscriptionDto",
                        entityId = userId,
                        userId = userId,
                        resultCount = resultCount,
                        dataSource = "firestore_query_by_status",
                        dataSize = dataSize,
                        queryDetails = "status=$status,limit=$limit",
                        cacheStrategy = "direct_query"
                    )

                    Log.d(TAG, "queryUserSubscriptionDtosByStatus: Successfully found $resultCount subscriptions for user $userId with status $status (${totalDuration.inWholeMilliseconds}ms)")
                    Result.Success(subscriptionDtos)
                }
                is Result.Error -> {
                    val totalDuration = operationStartTime.elapsedNow()
                    
                    // Monitor failed DTO query operation
                    com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "SubscriptionRemoteDataSource",
                        operation = "queryUserSubscriptionDtosByStatus",
                        duration = totalDuration,
                        success = false,
                        error = queryResult.exception,
                        cacheHit = false,
                        dataType = "UserSubscriptionDto",
                        entityId = userId,
                        userId = userId,
                        resultCount = 0,
                        dataSource = "firestore_query_by_status_failed",
                        queryDetails = "status=$status,limit=$limit",
                        cacheStrategy = "direct_query"
                    )
                    
                    Log.e(TAG, "queryUserSubscriptionDtosByStatus: Query failed for user $userId with status $status (${totalDuration.inWholeMilliseconds}ms)", queryResult.exception)
                    queryResult
                }
                is Result.Loading -> {
                    Log.w(TAG, "queryUserSubscriptionDtosByStatus: Unexpected Loading state from executeProfiledFirestoreQuery")
                    Result.Error(IllegalStateException("executeProfiledFirestoreQuery returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            val totalDuration = operationStartTime.elapsedNow()
            
            // Monitor unexpected error in DTO query operation
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "SubscriptionRemoteDataSource",
                operation = "queryUserSubscriptionDtosByStatus",
                duration = totalDuration,
                success = false,
                error = e,
                cacheHit = false,
                dataType = "UserSubscriptionDto",
                entityId = userId,
                userId = userId,
                resultCount = 0,
                dataSource = "firestore_query_by_status_error",
                queryDetails = "status=$status,limit=$limit",
                cacheStrategy = "direct_query"
            )
            
            Log.e(TAG, "queryUserSubscriptionDtosByStatus: Unexpected error querying subscriptions for user $userId with status $status (${totalDuration.inWholeMilliseconds}ms)", e)
            Result.Error(e)
        }
    }

    override suspend fun queryActiveSubscriptionDtos(
        userId: String,
        limit: Int
    ): Result<List<UserProfileDto.UserSubscription>> = withContext(ioDispatcher) {
        val operationStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        return@withContext try {
            Log.d(TAG, "queryActiveSubscriptionDtos: Querying active subscriptions for user $userId (limit: $limit)")

            // ENHANCED: Add session correlation for active subscription queries
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_query_active:$userId")

            // Execute the profiled Firestore query for active subscriptions
            val queryResult = executeProfiledFirestoreQuery(
                operationName = "queryActiveSubscriptions",
                userId = userId,
                limit = limit
            )

            when (queryResult) {
                is Result.Success -> {
                    val querySnapshot = queryResult.data
                    
                    // Map the QuerySnapshot to UserSubscription DTOs
                    val subscriptionDtos = mapQuerySnapshotToSubscriptionDtos(querySnapshot)
                    
                    val totalDuration = operationStartTime.elapsedNow()
                    val resultCount = subscriptionDtos.size
                    val dataSize = subscriptionDtos.toString().length

                    // ENHANCED: Monitor successful active subscription DTO query operation
                    com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "SubscriptionRemoteDataSource",
                        operation = "queryActiveSubscriptionDtos",
                        duration = totalDuration,
                        success = true,
                        cacheHit = false, // Always remote
                        dataType = "ActiveUserSubscriptionDto",
                        entityId = userId,
                        userId = userId,
                        resultCount = resultCount,
                        dataSource = "firestore_query_active",
                        dataSize = dataSize,
                        queryDetails = "isActive=true,limit=$limit",
                        cacheStrategy = "active_query"
                    )

                    Log.d(TAG, "queryActiveSubscriptionDtos: Successfully found $resultCount active subscriptions for user $userId (${totalDuration.inWholeMilliseconds}ms)")
                    Result.Success(subscriptionDtos)
                }
                is Result.Error -> {
                    val totalDuration = operationStartTime.elapsedNow()
                    
                    // Monitor failed active subscription DTO query operation
                    com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "SubscriptionRemoteDataSource",
                        operation = "queryActiveSubscriptionDtos",
                        duration = totalDuration,
                        success = false,
                        error = queryResult.exception,
                        cacheHit = false,
                        dataType = "ActiveUserSubscriptionDto",
                        entityId = userId,
                        userId = userId,
                        resultCount = 0,
                        dataSource = "firestore_query_active_failed",
                        queryDetails = "isActive=true,limit=$limit",
                        cacheStrategy = "active_query"
                    )
                    
                    Log.e(TAG, "queryActiveSubscriptionDtos: Query failed for user $userId (${totalDuration.inWholeMilliseconds}ms)", queryResult.exception)
                    queryResult
                }
                is Result.Loading -> {
                    Log.w(TAG, "queryActiveSubscriptionDtos: Unexpected Loading state from executeProfiledFirestoreQuery")
                    Result.Error(IllegalStateException("executeProfiledFirestoreQuery returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            val totalDuration = operationStartTime.elapsedNow()
            
            // Monitor unexpected error in active subscription DTO query operation
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "SubscriptionRemoteDataSource",
                operation = "queryActiveSubscriptionDtos",
                duration = totalDuration,
                success = false,
                error = e,
                cacheHit = false,
                dataType = "ActiveUserSubscriptionDto",
                entityId = userId,
                userId = userId,
                resultCount = 0,
                dataSource = "firestore_query_active_error",
                queryDetails = "isActive=true,limit=$limit",
                cacheStrategy = "active_query"
            )
            
            Log.e(TAG, "queryActiveSubscriptionDtos: Unexpected error querying active subscriptions for user $userId (${totalDuration.inWholeMilliseconds}ms)", e)
            Result.Error(e)
        }
    }

    // === PRIVATE HELPER METHODS ===

    /**
     * Build Firestore query for subscription pagination using QueryBuilder
     */
    private fun buildSubscriptionQuery(pageSize: Int, pageKey: DocumentSnapshot?): Result<Query> {
        return try {
            var query = firestore.collection(COLLECTION_USER_PROFILES)
                .whereNotEqualTo("profileData.subscription", null)
                .orderBy("profileData.subscription.lastModified")
                .limit(pageSize.toLong())
            
            pageKey?.let { key ->
                query = query.startAfter(key)
            }
            
            Result.Success(query)
        } catch (e: Exception) {
            Log.e(TAG, "buildSubscriptionQuery: Error building query", e)
            Result.Error(e)
        }
    }

    /**
     * Build Firestore query for active subscription pagination using QueryBuilder
     */
    private fun buildActiveSubscriptionQuery(pageSize: Int, pageKey: DocumentSnapshot?): Result<Query> {
        return try {
            var query = firestore.collection(COLLECTION_USER_PROFILES)
                .whereEqualTo("profileData.subscription.isActive", true)
                .orderBy("profileData.subscription.expiryDate")
                .limit(pageSize.toLong())
            
            pageKey?.let { key ->
                query = query.startAfter(key)
            }
            
            Result.Success(query)
        } catch (e: Exception) {
            Log.e(TAG, "buildActiveSubscriptionQuery: Error building active query", e)
            Result.Error(e)
        }
    }

    /**
     * Enhanced helper method for executing Firestore queries with profiling and error handling
     */
    private suspend fun executeProfiledFirestoreQuery(
        operationName: String,
        userId: String,
        status: String? = null,
        limit: Int? = null
    ): Result<QuerySnapshot> = withContext(ioDispatcher) {
        val operationStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        
        return@withContext try {
            Log.d(TAG, "executeProfiledFirestoreQuery: Starting operation '$operationName' for user $userId")
            
            // ENHANCED: Add session correlation for query operations
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("subscription_query:$operationName:$userId")

            // Use direct Firestore queries with provided parameters
            val baseQuery = firestore.collection(COLLECTION_USER_PROFILES)
                .whereEqualTo("userId", userId)
            
            val query = when (operationName) {
                "queryUserSubscriptionsByStatus" -> {
                    var query = baseQuery
                    status?.let { query = query.whereEqualTo("profileData.subscription.status", it) }
                    query = query.orderBy("profileData.subscription.lastModified")
                    limit?.let { query = query.limit(it.toLong()) }
                    query
                }
                "queryActiveSubscriptions", "validateSubscriptionWithQuery" -> {
                    var query = baseQuery.whereEqualTo("profileData.subscription.isActive", true)
                    query = query.orderBy("profileData.subscription.expiryDate")
                    limit?.let { query = query.limit(it.toLong()) }
                    query
                }
                else -> {
                    var query = baseQuery
                    limit?.let { query = query.limit(it.toLong()) }
                    query
                }
            }
            
            // Execute query (monitoring handled by ClarityArchitectureMonitor)
            try {
                val querySnapshot = query.get().await()
                
                val totalDuration = operationStartTime.elapsedNow()
                val resultCount = querySnapshot.size()
                val dataSize = querySnapshot.documents.toString().length

                // ENHANCED: Monitor successful QueryBuilder operation with detailed context
                com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreQuery(
                    collection = COLLECTION_USER_PROFILES,
                    queryDescription = "$operationName(userId=$userId)",
                    duration = totalDuration,
                    success = true,
                    resultCount = resultCount
                )

                // ENHANCED: Monitor remote datasource operation for QueryBuilder queries
                com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                    repositoryClass = "SubscriptionRemoteDataSource",
                    operation = operationName,
                    duration = totalDuration,
                    success = true,
                    cacheHit = false, // Always remote
                    dataType = "UserSubscription",
                    entityId = userId,
                    userId = userId,
                    resultCount = resultCount,
                    dataSource = "firestore_querybuilder",
                    dataSize = dataSize,
                    queryDetails = "QueryBuilder:$operationName",
                    cacheStrategy = "querybuilder_fetch"
                )

                Log.d(TAG, "executeProfiledFirestoreQuery: Successfully completed '$operationName' for user $userId ($resultCount results, ${totalDuration.inWholeMilliseconds}ms)")
                Result.Success(querySnapshot)
            } catch (e: Exception) {
                
                val totalDuration = operationStartTime.elapsedNow()
                
                // Monitor failed query execution
                com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreQuery(
                    collection = COLLECTION_USER_PROFILES,
                    queryDescription = "$operationName(userId=$userId)",
                    duration = totalDuration,
                    success = false,
                    resultCount = 0,
                    error = e
                )
                
                Log.e(TAG, "executeProfiledFirestoreQuery: Error in '$operationName' for user $userId (${totalDuration.inWholeMilliseconds}ms)", e)
                Result.Error(e)
            }
        } catch (e: Exception) {
            val totalDuration = operationStartTime.elapsedNow()
            Log.e(TAG, "executeProfiledFirestoreQuery: Unexpected error in '$operationName' for user $userId (${totalDuration.inWholeMilliseconds}ms)", e)
            Result.Error(e)
        }
    }

    /**
     * Map Firestore QuerySnapshot to UserSubscription DTOs
     */
    private fun mapQuerySnapshotToSubscriptionDtos(querySnapshot: QuerySnapshot): List<UserProfileDto.UserSubscription> {
        val subscriptions = mutableListOf<UserProfileDto.UserSubscription>()

        for (document in querySnapshot.documents) {
            try {
                // Extract subscription data from user profile document
                @Suppress("UNCHECKED_CAST")
                val subscriptionData = document.get("profileData.subscription") as? Map<String, Any>

                if (subscriptionData != null) {
                    // Create DTO from raw Firestore data
                    val dto = createSubscriptionDtoFromFirestoreData(subscriptionData)
                    subscriptions.add(dto)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Error processing subscription document ${document.id}", e)
                // Continue processing other documents
            }
        }

        return subscriptions
    }

    /**
     * Create UserSubscription DTO from raw Firestore data
     */
    private fun createSubscriptionDtoFromFirestoreData(data: Map<String, Any>): UserProfileDto.UserSubscription {
        return UserProfileDto.UserSubscription(
            status = data["status"] as? String,
            level = data["level"] as? String,
            isActive = data["isActive"] as? Boolean,
            startDate = data["startDate"] as? OffsetDateTime,
            expiryDate = data["expiryDate"] as? OffsetDateTime,
            isLifetime = data["isLifetime"] as? Boolean,
            provider = data["provider"] as? String,
            orderId = data["orderId"] as? String,
            verification = data["verification"]?.let { verificationRaw ->
                @Suppress("UNCHECKED_CAST")
                val verificationData = verificationRaw as? Map<String, Any> ?: return@let null
                UserProfileDto.Verification(
                    lastVerified = verificationData["lastVerified"] as? OffsetDateTime,
                    status = verificationData["status"] as? String,
                    error = verificationData["error"] as? String
                )
            }
        )
    }

    /**
     * Converts UserSubscription DTO to Map for Firestore storage.
     * Following delivery domain pattern for DTO mapping utilities.
     */
    private fun convertSubscriptionDtoToMap(dto: UserProfileDto.UserSubscription): Map<String, Any?> {
        return mapOf(
            "status" to dto.status,
            "level" to dto.level,
            "isActive" to dto.isActive,
            "startDate" to dto.startDate,
            "expiryDate" to dto.expiryDate,
            "isLifetime" to dto.isLifetime,
            "provider" to dto.provider,
            "orderId" to dto.orderId,
            "verification" to dto.verification?.let { verification ->
                mapOf(
                    "lastVerified" to verification.lastVerified,
                    "status" to verification.status,
                    "error" to verification.error
                )
            }
        ).filterValues { it != null }
    }
}