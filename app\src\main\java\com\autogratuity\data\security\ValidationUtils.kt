package com.autogratuity.data.security

import android.util.Log
import android.util.Patterns
import java.util.regex.Pattern
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ValidationResult sealed class for structured validation responses
 * Provides a type-safe way to handle validation outcomes with specific error messages
 */
sealed class ValidationResult<out T> {
    data class Valid<T>(val data: T) : ValidationResult<T>()
    data class Invalid(val message: String, val errorCode: String = "VALIDATION_ERROR") : ValidationResult<Nothing>()
    
    companion object {
        fun <T> valid(data: T) = Valid(data)
        fun invalid(message: String, errorCode: String = "VALIDATION_ERROR") = Invalid(message, errorCode)
    }
    
    val isValid: Boolean get() = this is Valid
}

/**
 * Modern utility class for validating and sanitizing user input
 * Following 2025 Kotlin standards with structured validation results
 */
@Singleton
class ValidationUtils @Inject constructor() {

    companion object {
        private const val TAG = "ValidationUtils"
        
        // Validation patterns
        private val EMAIL_PATTERN: Pattern = Patterns.EMAIL_ADDRESS
        
        private val PHONE_PATTERN: Pattern = Pattern.compile(
            "^(\\+\\d{1,3}( )?)?((\\(\\d{3}\\))|\\d{3})[- .]?\\d{3}[- .]?\\d{4}$"
        )
        
        private val CARD_NUMBER_PATTERN: Pattern = Pattern.compile(
            "^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|6(?:011|5[0-9]{2})[0-9]{12})$"
        )
        
        private const val MAX_SANITIZED_STRING_LENGTH = 1000
        private const val MIN_SUBSCRIPTION_PAYLOAD_LENGTH = 10
    }

    /**
     * Validates if a string is a valid email address.
     * 2025 version using ValidationResult for structured error handling.
     * 
     * @param email The email to validate
     * @return ValidationResult indicating success or detailed error information
     */
    fun validateEmail(email: String?): ValidationResult<String> {
        if (email.isNullOrEmpty()) {
            return ValidationResult.invalid("Email cannot be empty", "EMAIL_EMPTY")
        }
        
        return if (EMAIL_PATTERN.matcher(email).matches()) {
            ValidationResult.valid(email)
        } else {
            ValidationResult.invalid("Invalid email format", "EMAIL_FORMAT")
        }
    }
    
    /**
     * Validates if a string is a valid phone number based on a common pattern.
     * 2025 version using ValidationResult for structured error handling.
     * 
     * @param phone The phone number to validate
     * @return ValidationResult indicating success or detailed error information
     */
    fun validatePhone(phone: String?): ValidationResult<String> {
        if (phone.isNullOrEmpty()) {
            return ValidationResult.invalid("Phone number cannot be empty", "PHONE_EMPTY")
        }
        
        return if (PHONE_PATTERN.matcher(phone).matches()) {
            ValidationResult.valid(phone)
        } else {
            ValidationResult.invalid("Invalid phone number format", "PHONE_FORMAT")
        }
    }
    
    /**
     * Validates if a string is a valid payment card number.
     * Checks against a common card pattern and then applies the Luhn algorithm.
     * 2025 version using ValidationResult for structured error handling.
     * 
     * @param cardNumber The card number to validate
     * @return ValidationResult indicating success or detailed error information
     */
    fun validateCardNumber(cardNumber: String?): ValidationResult<String> {
        if (cardNumber.isNullOrEmpty()) {
            return ValidationResult.invalid("Card number cannot be empty", "CARD_EMPTY")
        }

        val normalizedNumber = cardNumber.replace("[ -]".toRegex(), "")

        if (normalizedNumber.isEmpty()) {
            return ValidationResult.invalid("Card number contains only spaces or hyphens", "CARD_EMPTY_NORMALIZED")
        }
        
        if (!CARD_NUMBER_PATTERN.matcher(normalizedNumber).matches()) {
            return ValidationResult.invalid("Card number does not match expected format", "CARD_FORMAT")
        }

        // Validate using Luhn algorithm
        return try {
            val sum = normalizedNumber.reversed()
                .mapIndexed { index, char ->
                    val n = char.digitToInt()
                    if (index % 2 == 1) { // Corresponds to alternate digits starting from the second to last
                        val doubled = n * 2
                        if (doubled > 9) (doubled % 10) + 1 else doubled
                    } else {
                        n
                    }
                }.sum()
                
            if (sum % 10 == 0) {
                ValidationResult.valid(normalizedNumber)
            } else {
                ValidationResult.invalid("Card number failed checksum validation", "CARD_CHECKSUM")
            }
        } catch (e: IllegalArgumentException) {
            // If any character is not a digit after normalization and regex
            Log.w(TAG, "Card validation error: ${e.message}")
            ValidationResult.invalid("Card number contains invalid characters", "CARD_INVALID_CHARS")
        }
    }
    
    // Legacy compatibility extension functions
    
    /**
     * Validates if this string is a valid email address.
     * @receiver String? The string to validate.
     * @return True if the email is valid, false otherwise.
     * @deprecated Use validateEmail(email) instead for detailed validation results
     */
    @Deprecated("Use validateEmail() for structured validation results", ReplaceWith("ValidationUtils().validateEmail(this).isValid"))
    fun String?.isValidEmail(): Boolean {
        if (this.isNullOrEmpty()) return false
        return EMAIL_PATTERN.matcher(this).matches()
    }

    /**
     * Validates if this string is a valid phone number based on a common pattern.
     * @receiver String? The string to validate.
     * @return True if the phone number is valid, false otherwise.
     * @deprecated Use validatePhone(phone) instead for detailed validation results
     */
    @Deprecated("Use validatePhone() for structured validation results", ReplaceWith("ValidationUtils().validatePhone(this).isValid"))
    fun String?.isValidPhone(): Boolean {
        if (this.isNullOrEmpty()) return false
        return PHONE_PATTERN.matcher(this).matches()
    }

    /**
     * Validates if this string is a valid payment card number.
     * @receiver String? The string to validate.
     * @return True if the card number is valid, false otherwise.
     * @deprecated Use validateCardNumber(cardNumber) instead for detailed validation results
     */
    @Deprecated("Use validateCardNumber() for structured validation results", ReplaceWith("ValidationUtils().validateCardNumber(this).isValid"))
    fun String?.isValidCardNumber(): Boolean {
        // Delegate to the instance method for consistency
        return validateCardNumber(this).isValid
    }

/**
 * Sanitizes this string to remove a predefined set of potentially dangerous characters
 * and trims it to a maximum length. This is a basic sanitizer.
 * For robust XSS or SQL injection prevention, consider context-specific encoding/escaping
 * or dedicated libraries.
 * @receiver String? The string to sanitize.
 * @return Sanitized string, or null if the input receiver was null.
 */
fun String?.sanitize(): String? {
    return this?.let {
        var sanitized = it.replace("[<>&;`']".toRegex(), "")
        if (sanitized.length > MAX_SANITIZED_STRING_LENGTH) {
            sanitized = sanitized.take(MAX_SANITIZED_STRING_LENGTH)
        }
        sanitized
    }
}

/**
 * Validates if this string represents a structurally plausible subscription payload.
 * Checks for non-emptiness, minimum length, and presence of key terms.
 * Note: This is a very basic check. Proper validation would involve parsing (e.g., as JSON)
 * and verifying specific fields and values.
 * @receiver String? The string to validate.
 * @return True if the payload seems superficially valid, false otherwise.
 */
fun String?.isValidSubscriptionPayload(): Boolean {
    if (this.isNullOrEmpty() || this.length < MIN_SUBSCRIPTION_PAYLOAD_LENGTH) {
        return false
    }
    // Basic structural check. For JSON, checking for quoted keys is slightly more specific.
    return this.contains("receipt") &&
           (this.contains("purchaseToken") || this.contains("transactionId"))
}

}
