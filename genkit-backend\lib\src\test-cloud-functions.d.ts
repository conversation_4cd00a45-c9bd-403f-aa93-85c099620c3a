interface TestResult {
    functionName: string;
    testName: string;
    success: boolean;
    result?: any;
    error?: string;
    duration: number;
}
declare class CloudFunctionTester {
    private results;
    private firestore;
    runAllTests(): Promise<TestResult[]>;
    private testAddressStatsFlow;
    private testGeoJsonProcessing;
    private testImportParsing;
    private testManualDndOverride;
    private testFirestoreReadWrite;
    private runTest;
    private printSummary;
}
export { CloudFunctionTester, TestResult };
