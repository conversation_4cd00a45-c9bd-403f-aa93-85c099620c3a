/*
 * Coordinates.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

import kotlin.Double

/**
 * Represents geographic coordinates (latitude/longitude).
 */
data class Coordinates(
    /** The latitude value */
    val latitude: Double? = null,
    /** The longitude value */
    val longitude: Double? = null
)
