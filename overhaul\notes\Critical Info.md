---
Path: cd C:\Users\<USER>\AndroidStudioProjects\Autogratuity\genkit-backend
Path 2:
  - cd C:\Users\<USER>\AndroidStudioProjects\Autogratuity\
Firestore Start: firebase emulators:start --only firestore
Build & Test: npm run build ; npm test
Delete Function: firebase functions:delete parseGeoJsonPropertiesTrigger --region us-central1
Firestore Rules: firebase deploy --only firestore:rules
Script Delete Documents: node ./scripts/batch-delete-user-data.js
Firestore Fx: firebase deploy --only functions
Genkit Schemas: npm run generate:types
Script + Schemas: npx run regenerate-models-fully
Frontend Schemas: "& .\\frontend-tools\\jsonschema2pojo-1.2.2\\jsonschema2pojo-1.2.2\\bin\\jsonschema2pojo.bat --source schemas/ --target app/src/main/java --package com.autogratuity.data.model.generated --annotation-style JACKSON2 --source-type JSONSCHEMA --long-integers"
Fingerprints: cd C:\Users\<USER>\AndroidStudioProjects\Autogratuity ; .\gradlew.bat signingReport
Cloud Billing: https://console.cloud.google.com/billing?hl=en&invt=Abw6uw&inv=1
onGeoJsonFileFinalized: https://console.cloud.google.com/run/detail/us-central1/ongeojsonfilefinalized/source?authuser=0&hl=en&inv=1&invt=Abw60Q&project=autogratuity-me
ParseGeoJsonPropertiesTrigger: https://console.cloud.google.com/run/detail/us-central1/parsegeojsonpropertiestrigger/security?authuser=0&hl=en&inv=1&invt=Abw60g&project=autogratuity-me
Cloud Services Firebase: https://console.cloud.google.com/run?authuser=0&hl=en&inv=1&invt=Abw60g&project=autogratuity-me
Serp API: 685472cfc988e7dd05a0a7a91e48a5ea832dbcce768b509bab4688cd6ae00c83
Tavily API: 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
Perplexity API: 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
Brave API: BSA23gqEhg2gJrw3j76XV70ZWWSfBke
Deepseek API: ***********************************
Firefly API: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
N8N MCP Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJiMzY4MTQwYS1iYjEwLTRlMWEtYTZhZC1mZTE4ZDQ5NTY1NjUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ0NTgxOTg5fQ.60oNGecIPC0IyrWl9wt0tV1BNh7uf3M23IONDpk88_M
imagerouter: 26c3c57d7bf0bb1c2d8918875f663a1718b9d989640d3aa1e6622285e63e12ec
OpenRouter API: 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
MAVEN KT: "    cd C:\\Users\\<USER>\\AndroidStudioProjects\\Autogratuity\\frontend-tools\\codegen-runner"
MAVEN KT 2: "    .\\cleanup-generated-models.bat"
KT 3: mvn generate-sources
FIRESTORE new: npm run emulators:firestore
firestore new2: npx firebase-tools login
firestore new3: npx firebase-tools deploy --only functions
keystore certificate: keytool -genkey -v -keystore release-key.keystore -alias my-app-key -keyalg RSA -keysize 2048 -validity 10000
keystore pull: keytool -list -v -keystore release-key.keystore -alias my-app-key
app key: AIzaSyAXl0uMo2TsasiHCsgkdQzPjOBkUewKzyo
---
---

```shell
docker run -d `
  --name crawl4ai `
  --network fireflyiii_firefly_iii `
  --restart unless-stopped `
  --memory=6442450944 `
  --memory-reservation=1073741824 `
  --shm-size=2147483648 `
  --cgroupns=host `
  -p 11235:11235 `
  -e OPENAI_API_KEY="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" `
  -e GEMINI_API_KEY="AIzaSyAMY4FyoL6f8hEUYmE3QOPKGwWNpHJJI_s" `
  -e CRAWL4AI_API_TOKEN="yT42svZfCfr8FsvuQjJJJrd9NPAEdFT3PCdUlR5P" `
  -e MAX_CONCURRENT_TASKS=1 `
  -e REDIS_HOST="localhost" `
  -e REDIS_PORT="6379" `
  -e LANG="C.UTF-8" `
  -e GPG_KEY="A035C8C19219BA821ECEA86B64E628F8D684696D" `
  -e PYTHON_VERSION="3.10.17" `
  -e PYTHON_SHA256="4c68050f049d1b4ac5aadd0df5f27941c0350d2a9e7ab0907ee5eb5225d9d6b0" `
  -e PYTHONFAULTHANDLER=1 `
  -e PYTHONHASHSEED="random" `
  -e PYTHONUNBUFFERED=1 `
  -e PIP_NO_CACHE_DIR=1 `
  -e PYTHONDONTWRITEBYTECODE=1 `
  -e PIP_DISABLE_PIP_VERSION_CHECK=1 `
  -e PIP_DEFAULT_TIMEOUT=100 `
  -e DEBIAN_FRONTEND="noninteractive" `
  --label description="🔥🕷️ Crawl4AI: Open-source LLM Friendly Web Crawler & scraper" `
  --label maintainer="unclecode" `
  --label version="1.0" `
  crawl4ai:latest `
  supervisord -c supervisord.conf
```

```shell
docker run -d `
  -p 8080:8080 `
  --cpuset-cpus 0-1 `
  --hostname "dev-$(-join ((48..57) + (97..122) | Get-Random -Count 4 | % {[char]$_}))" `
  --mac-address "02:$(-join ((0..9) + (97..102) | Get-Random -Count 10 | % { if($_ -lt 10) {$_} else {[char]$_} }).Insert(2,':').Insert(5,':').Insert(8,':').Insert(11,':'))" `
  -e MACHINE_ID="$(-join ((65..90) + (97..122) | Get-Random -Count 16 | % {[char]$_}))" `
  -v C:\Users\<USER>\AndroidStudioProjects\Autogratuity:/home/<USER>/project `
  --name my-private-ide `
  private-vscode
```


  
/**  
 * Ensures that a delivery object has a valid reference object. * Moved from DtoUtils.kt to follow architectural pattern where mappers contain domain business logic. * * @param delivery The delivery DTO to validate and update.  
 * @return The delivery DTO with a valid reference object.  
 */fun ensureValidReferenceDto(delivery: DeliveryDto): DeliveryDto {  
    val deliveryData = delivery.deliveryData  
  
    if (deliveryData.reference != null) {  
        return delivery  
    }  
  
    val newReference = ReferenceDto(  
        addressId = null,  
        orderId = null,  
        externalId = null  
    )  
  
    val updatedDeliveryData = deliveryData.copy(reference = newReference)  
  
    return delivery.copy(deliveryData = updatedDeliveryData)  
}

