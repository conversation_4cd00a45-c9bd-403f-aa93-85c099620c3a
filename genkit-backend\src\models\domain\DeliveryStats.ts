// Auto-generated from DeliveryStats.kt
// TypeScript equivalent of Android domain.model.DeliveryStats

/**
 * Domain model generated from Kotlin DeliveryStats
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface DeliveryStats {
  deliveryCount: number = 0L;
  tipCount: number = 0L;
  totalTips: number = 0.0;
  averageTipAmount: number = 0.0;
  highestTip: number = 0.0;
  pendingCount: number = 0L;
  averageTimeMinutes: number = 0.0;
  lastDeliveryDate?: Date? = null;
  period: StatisticsPeriod = StatisticsPeriod.TODAY;
  tipRate: number;
  hasData: boolean;
}