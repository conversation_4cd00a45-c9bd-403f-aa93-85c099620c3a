package com.autogratuity.debug

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.serialization.json.*
import java.io.IOException
import java.net.ServerSocket
import java.net.Socket
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import androidx.core.net.toUri

/**
 * 🚀 SIMPLIFIED ONE-CLICK CLARITY ARCHITECTURE DASHBOARD
 *
 * Embedded HTTP server that provides real-time monitoring without JavaScript template literal issues.
 * Automatically starts when app runs in debug mode.
 * Access: http://localhost:8080 (auto-opens in browser)
 */
object SimpleClarityDashboard {

    private const val TAG = "SIMPLE_CLARITY_DASHBOARD"
    private const val PORT = 8080

    private var serverSocket: ServerSocket? = null
    private var isRunning = false
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 📊 MONITORING DATA
    private val events = mutableListOf<String>()
    private val metrics = ConcurrentHashMap<String, String>()

    // 🔢 COUNTERS
    val totalRequests = AtomicLong(0)
    val errorCount = AtomicLong(0)
    val cacheHits = AtomicLong(0)
    val cacheMisses = AtomicLong(0)
    val firestoreReads = AtomicLong(0)
    val firestoreWrites = AtomicLong(0)
    val viewModelInits = AtomicLong(0)
    val slowOperations = AtomicLong(0)


    // 🎯 ENHANCED PERFORMANCE COUNTERS
    val heavyInjections = AtomicLong(0)
    val complexStateFlowChains = AtomicLong(0)
    val slowCacheMisses = AtomicLong(0)
    val operationCorrelations = AtomicLong(0)
    val sessionOperations = AtomicLong(0)

    // 🎨 UI PERFORMANCE COUNTERS
    val uiJankEvents = AtomicLong(0)
    val frameDrops = AtomicLong(0)
    val slowStateFlowEmissions = AtomicLong(0)
    val poorLoadingExperience = AtomicLong(0)
    val uiThreadBlocking = AtomicLong(0)
    val totalSkippedFrames = AtomicLong(0)

    // 🚨 PROBLEMATIC UI TRACKING
    private val problematicUIFiles = ConcurrentHashMap<String, AtomicLong>()
    private val problematicUIComponents = mutableListOf<String>()

    // 📊 PERFORMANCE TRACKING
    private val performanceMetrics = ConcurrentHashMap<String, MutableList<Long>>()


    // 🎯 SESSION TRACKING
    private var currentSessionId: String? = null
    private val sessionEvents = mutableListOf<String>()
    private var sessionStartTime: Long = 0

    // 🔍 OPERATION CORRELATION
    private val operationChains = mutableMapOf<String, MutableList<String>>()
    private val performanceBaselines = mutableMapOf<String, Long>()

    fun start(context: Context) {
        if (isRunning) return

        scope.launch {
            try {
                serverSocket = ServerSocket(PORT, 50, java.net.InetAddress.getByName("0.0.0.0"))
                isRunning = true

                Log.i(TAG, "Simple Clarity Dashboard Server started on port $PORT")
                Log.i(TAG, "")
                Log.i(TAG, "DASHBOARD ACCESS:")
                Log.i(TAG, "   1. Run in terminal: adb forward tcp:$PORT tcp:$PORT")
                Log.i(TAG, "   2. Open PC browser: http://localhost:$PORT")
                Log.i(TAG, "   3. Dashboard will show real-time Android app data")
                Log.i(TAG, "")

                // Add some test events to verify dashboard is working
                logEvent("Dashboard server started successfully")
                logEvent("Monitoring system initialized")
                logArchitectureEvent("system", "SimpleClarityDashboard", "startup", 100, true)

                // Add test repository events to verify integration
                logArchitectureEvent("data", "AddressRepositoryImpl", "getAddressById", 150, true)
                logFirestoreOperation("READ", "addresses", 75, true)
                logCacheHit()
                logEvent("Repository monitoring integration active")

                // Note: Dashboard accessible via ADB port forwarding to PC browser
                // Auto-opening disabled - user needs to manually open on PC

                // Start accepting connections
                Log.i(TAG, "Server listening for connections...")
                while (isRunning) {
                    try {
                        val clientSocket = serverSocket?.accept()
                        clientSocket?.let { socket ->
                            Log.d(TAG, "Client connected from: ${socket.remoteSocketAddress}")
                            launch { handleClient(socket) }
                        }
                    } catch (e: IOException) {
                        if (isRunning) {
                            Log.e(TAG, "Error accepting client connection", e)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start dashboard server", e)
            }
        }
    }

    fun stop() {
        isRunning = false
        serverSocket?.close()
        scope.cancel()
        Log.i(TAG, "Simple Clarity Dashboard Server stopped")
    }

    private suspend fun handleClient(socket: Socket) {
        try {
            val input = socket.getInputStream().bufferedReader()
            val output = socket.getOutputStream().bufferedWriter()

            val requestLine = input.readLine() ?: return
            Log.d(TAG, "Request: $requestLine")

            val parts = requestLine.split(" ")
            if (parts.size < 2) return

            val path = parts[1]
            Log.d(TAG, "Path: $path")

            // Skip headers
            while (input.readLine()?.isNotEmpty() == true) { }

            when {
                path == "/" -> serveDashboardHTML(output)
                path == "/api/data" -> serveRealtimeData(output)
                path == "/ping" -> servePing(output)
                else -> serve404(output)
            }

            output.flush()
            socket.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error handling client request", e)
        }
    }

    private fun serveDashboardHTML(output: java.io.BufferedWriter) {
        val html = getSimpleDashboardHTML()

        output.write("HTTP/1.1 200 OK\r\n")
        output.write("Content-Type: text/html\r\n")
        output.write("Content-Length: ${html.length}\r\n")
        output.write("\r\n")
        output.write(html)
    }

    private fun serveRealtimeData(output: java.io.BufferedWriter) {
        try {
            val data = buildJsonObject {
                put("timestamp", System.currentTimeMillis())
                put("total_requests", totalRequests.get())
                put("error_count", errorCount.get())
                put("cache_hits", cacheHits.get())
                put("cache_misses", cacheMisses.get())
                put("cache_hit_ratio", if (cacheHits.get() + cacheMisses.get() > 0)
                    cacheHits.get().toDouble() / (cacheHits.get() + cacheMisses.get()) else 0.0)
                put("firestore_reads", firestoreReads.get())
                put("firestore_writes", firestoreWrites.get())
                put("viewmodel_inits", viewModelInits.get())
                put("slow_operations", slowOperations.get())
                put("total_skipped_frames", totalSkippedFrames.get())


                // Enhanced performance metrics
                put("heavy_injections", heavyInjections.get())
                put("complex_stateflow_chains", complexStateFlowChains.get())
                put("slow_cache_misses", slowCacheMisses.get())
                put("operation_correlations", operationCorrelations.get())
                put("session_operations", sessionOperations.get())
                put("current_session", currentSessionId ?: "none")

                // Cache system health metrics
                put("cache_health", buildJsonObject {
                    put("total_cache_systems", 5) // Delivery, Address, User, Subscription, Config
                    put("cache_warming_active", currentSessionId != null)
                    put("critical_cache_misses", slowCacheMisses.get())
                    put("cache_breakdown_tracking", true)
                })

                // Session analysis metrics
                put("session_analysis", buildJsonObject {
                    currentSessionId?.let { sessionId ->
                        put("session_id", sessionId)
                        put("session_events", sessionEvents.size)
                        put("session_duration_ms", System.currentTimeMillis() - sessionStartTime)
                        put("cache_warming_completed", true)
                    } ?: run {
                        put("session_active", false)
                        put("cache_warming_completed", false)
                    }
                })

                // Safely handle events with potential Unicode characters - EXPANDED for thousands of lines
                val safeEvents = events.takeLast(1000).map { event ->
                    // Replace problematic characters with safe alternatives
                    event.replace("✅", "[OK]")
                         .replace("❌", "[ERROR]")
                         .replace("⚠️", "[WARN]")
                         .replace("🚨", "[VIOLATION]")
                         .replace("📊", "[MONITOR]")
                         .replace("🐌", "[SLOW]")
                         .replace("🔥", "[FIRE]")
                         .replace("🎯", "[SESSION]")
                         .replace("🔗", "[CHAIN]")
                         .replace("🔄", "[TRANSFORM]")
                }
                put("recent_events", JsonArray(safeEvents.map { JsonPrimitive(it) }))

                // Problematic UI files data
                val problematicUIData = buildJsonObject {
                    put("total_files", problematicUIFiles.size)
                    put("total_skipped_frames", totalSkippedFrames.get())
                    val topProblematicFiles = problematicUIFiles.entries
                        .sortedByDescending { it.value.get() }
                        .take(10)
                        .map { "${it.key}:${it.value.get()}" }
                    put("top_files", JsonArray(topProblematicFiles.map { JsonPrimitive(it) }))
                }
                put("problematic_ui", problematicUIData)

                // Performance averages
                val avgPerformance = buildJsonObject {
                    performanceMetrics.forEach { (operation, times) ->
                        if (times.isNotEmpty()) {
                            put(operation, times.average())
                        }
                    }
                }
                put("avg_performance", avgPerformance)
            }

            val jsonString = data.toString()

            output.write("HTTP/1.1 200 OK\r\n")
            output.write("Content-Type: application/json\r\n")
            output.write("Access-Control-Allow-Origin: *\r\n")
            output.write("Content-Length: ${jsonString.length}\r\n")
            output.write("\r\n")
            output.write(jsonString)
        } catch (e: Exception) {
            Log.e(TAG, "Error serving realtime data", e)
            // Serve minimal safe JSON on error
            val errorJson = """{"error": "Failed to serialize data", "timestamp": ${System.currentTimeMillis()}}"""
            output.write("HTTP/1.1 200 OK\r\n")
            output.write("Content-Type: application/json\r\n")
            output.write("Access-Control-Allow-Origin: *\r\n")
            output.write("Content-Length: ${errorJson.length}\r\n")
            output.write("\r\n")
            output.write(errorJson)
        }
    }

    private fun servePing(output: java.io.BufferedWriter) {
        val response = "PONG - Clarity Dashboard Server is running!"

        output.write("HTTP/1.1 200 OK\r\n")
        output.write("Content-Type: text/plain\r\n")
        output.write("Content-Length: ${response.length}\r\n")
        output.write("\r\n")
        output.write(response)
    }

    private fun serve404(output: java.io.BufferedWriter) {
        output.write("HTTP/1.1 404 Not Found\r\n")
        output.write("Content-Type: text/plain\r\n")
        output.write("\r\n")
        output.write("404 - Not Found")
    }

    private fun openDashboardInBrowser(context: Context) {
        scope.launch {
            delay(1000) // Wait for server to be ready

            // Try multiple URLs for different environments
            val urls = listOf(
                "http://127.0.0.1:$PORT",   // Device localhost
                "http://localhost:$PORT",   // Standard localhost
                "http://********:$PORT"    // Android emulator (reverse)
            )

            for (url in urls) {
                try {
                    val intent = android.content.Intent(android.content.Intent.ACTION_VIEW).apply {
                        data = url.toUri()
                        flags = android.content.Intent.FLAG_ACTIVITY_NEW_TASK
                    }
                    context.startActivity(intent)
                    Log.i(TAG, "Dashboard opened in browser at $url")
                    return@launch
                } catch (e: Exception) {
                    Log.d(TAG, "Failed to open $url: ${e.message}")
                }
            }

            Log.w(TAG, "Could not auto-open browser. Manually navigate to:")
            Log.w(TAG, "  - http://********:$PORT (Android emulator)")
            Log.w(TAG, "  - http://localhost:$PORT (local development)")
        }
    }

    // 📊 MONITORING API

    fun logEvent(message: String) {
        val timestamp = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())
        // Sanitize message for JSON safety
        val sanitizedMessage = message.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r")
        events.add("[$timestamp] $sanitizedMessage")
        if (events.size > 2000) {
            events.removeAt(0)
        }
        Log.d(TAG, "$message")
    }

    fun logArchitectureEvent(layer: String, component: String, operation: String, durationMs: Long, success: Boolean) {
        totalRequests.incrementAndGet()
        if (!success) errorCount.incrementAndGet()

        val status = if (success) "[OK]" else "[ERROR]"
        logEvent("[$layer.$component] $operation: ${durationMs}ms $status")
    }

    fun logFirestoreOperation(operation: String, collection: String, durationMs: Long, success: Boolean) {
        when (operation.uppercase()) {
            "READ", "GET" -> firestoreReads.incrementAndGet()
            "WRITE", "SET", "UPDATE", "DELETE" -> firestoreWrites.incrementAndGet()
        }

        // Track performance
        val key = "firestore_${operation.lowercase()}"
        performanceMetrics.computeIfAbsent(key) { mutableListOf() }.add(durationMs)

        // Flag slow operations
        if (durationMs > 500) {
            slowOperations.incrementAndGet()
        }

        val status = if (success) "[OK]" else "[ERROR]"
        logEvent("[Firestore] $operation $collection: ${durationMs}ms $status")
    }

    fun logViewModelInit(viewModelClass: String, durationMs: Long, success: Boolean) {
        viewModelInits.incrementAndGet()

        // Track performance
        performanceMetrics.computeIfAbsent("viewmodel_init") { mutableListOf() }.add(durationMs)

        // Flag slow inits
        if (durationMs > 100) {
            slowOperations.incrementAndGet()
        }

        val status = if (success) "[OK]" else "[ERROR]"
        logEvent("[ViewModel] $viewModelClass init: ${durationMs}ms $status")
    }



    fun logSsotDtoMapping(mapper: String, operation: String, entity: String, durationMs: Long, success: Boolean, issues: Int = 0) {
        val status = if (success && issues == 0) "[OK]" else if (issues > 0) "[WARN]" else "[ERROR]"
        val issueText = if (issues > 0) " ($issues issues)" else ""
        logEvent("[$mapper] $operation $entity: ${durationMs}ms $status$issueText")
    }

    fun logCacheHit() {
        cacheHits.incrementAndGet()
    }

    fun logCacheMiss() {
        cacheMisses.incrementAndGet()
    }

    // ENHANCED MONITORING FUNCTIONS FOR SPECIFIC PERFORMANCE ISSUES

    fun logHeavyInjection(viewModelClass: String, dependenciesCount: Int, repositoriesCount: Int, durationMs: Long) {
        heavyInjections.incrementAndGet()
        if (durationMs > 20) slowOperations.incrementAndGet()

        logEvent("[INJECTION] $viewModelClass: $dependenciesCount deps ($repositoriesCount repos) ${durationMs}ms [HEAVY]")
    }

    fun logComplexStateFlowChain(viewModelClass: String, chainName: String, chainLength: Int, durationMs: Long) {
        complexStateFlowChains.incrementAndGet()
        if (durationMs > 30) slowOperations.incrementAndGet()

        logEvent("[STATEFLOW] $viewModelClass.$chainName: chain=$chainLength ${durationMs}ms [COMPLEX]")
    }

    fun logSlowCacheMiss(repositoryClass: String, operation: String, remoteDuration: Long, transformDuration: Long) {
        slowCacheMisses.incrementAndGet()
        slowOperations.incrementAndGet()

        logEvent("[CACHE_MISS] $repositoryClass.$operation: remote=${remoteDuration}ms transform=${transformDuration}ms [SLOW]")
    }

    fun logOperationCorrelation(triggerOperation: String, relatedCount: Int, totalDuration: Long) {
        operationCorrelations.incrementAndGet()
        if (totalDuration > 500) slowOperations.incrementAndGet()

        logEvent("[CORRELATION] $triggerOperation -> $relatedCount ops: ${totalDuration}ms [TRACED]")
    }

    fun startSession(sessionId: String) {
        currentSessionId = sessionId
        sessionStartTime = System.currentTimeMillis()
        sessionEvents.clear()
        logEvent("[SESSION] Started: $sessionId [TRACKING]")
    }

    fun endSession() {
        currentSessionId?.let { sessionId ->
            val duration = System.currentTimeMillis() - sessionStartTime
            logEvent("[SESSION] Ended: $sessionId (${duration}ms, ${sessionEvents.size} ops) [COMPLETE]")
        }
        currentSessionId = null
    }

    fun addSessionEvent(event: String) {
        currentSessionId?.let {
            sessionEvents.add(event)
            sessionOperations.incrementAndGet()
        }
    }

    /**
     * Log field-by-field parsing events from DtoUtils
     */
    fun logFieldExtraction(
        documentType: String,
        fieldName: String,
        fieldType: String,
        success: Boolean,
        value: String? = null
    ) {
        val status = if (success) "EXTRACTED" else "FAILED"
        val valueInfo = value?.let { " = $it" } ?: ""
        logEvent("[FIELD_$status] $documentType.$fieldName ($fieldType)$valueInfo")

        // Add to session events for correlation
        addSessionEvent("field_extraction:$documentType:$fieldName:$status")
    }

    /**
     * Log document parsing start/completion
     */
    fun logDocumentParsing(
        documentType: String,
        documentId: String,
        phase: String, // "START" or "COMPLETE"
        durationMs: Long? = null,
        fieldsExtracted: Int? = null
    ) {
        val details = buildString {
            append("[$phase] $documentType parsing")
            append(" ID:$documentId")
            durationMs?.let { append(" Duration:${it}ms") }
            fieldsExtracted?.let { append(" Fields:$it") }
        }
        logEvent("[DOCUMENT_PARSING] $details")

        // Add to session events
        addSessionEvent("document_parsing:$documentType:$documentId:${phase.lowercase()}")
    }

    // CACHE SYSTEM INTEGRATION FUNCTIONS

    fun logCacheSystemBreakdown(
        repositoryClass: String,
        operation: String,
        cacheCheckMs: Long,
        remoteFetchMs: Long?,
        mappingMs: Long?,
        storeMs: Long?,
        cacheHit: Boolean
    ) {
        val totalMs = cacheCheckMs + (remoteFetchMs ?: 0) + (mappingMs ?: 0) + (storeMs ?: 0)

        val details = buildString {
            append("Check:${cacheCheckMs}ms")
            remoteFetchMs?.let { append(" Remote:${it}ms") }
            mappingMs?.let { append(" Map:${it}ms") }
            storeMs?.let { append(" Store:${it}ms") }
            append(" Hit:$cacheHit")
            if (!cacheHit && (remoteFetchMs ?: 0) > 1000) append(" [SLOW_REMOTE]")
        }

        logEvent("[CACHE_BREAKDOWN] $repositoryClass.$operation: $details (${totalMs}ms)")

        if (!cacheHit && (remoteFetchMs ?: 0) > 2000) {
            slowCacheMisses.incrementAndGet()
        }
    }

    fun logAtomicCacheMetrics(cacheSystemName: String, metrics: Map<String, Any>) {
        val hitRate = metrics["hitRate"] as? Double ?: 0.0
        val totalOps = metrics["totalOperations"] as? Long ?: 0
        val cacheSize = metrics["cacheSize"] as? Int ?: 0

        val healthStatus = when {
            hitRate > 0.8 -> "HEALTHY"
            hitRate > 0.5 -> "MODERATE"
            else -> "POOR"
        }

        logEvent("[CACHE_METRICS] $cacheSystemName: HitRate:${String.format("%.1f", hitRate * 100)}% Ops:$totalOps Size:$cacheSize [$healthStatus]")
    }

    fun logCacheWarming(userId: String, cacheSystemName: String, operationsCount: Int, durationMs: Long) {
        logEvent("[CACHE_WARMING] $cacheSystemName for user $userId: $operationsCount ops in ${durationMs}ms [PRELOAD]")
    }

    // UI PERFORMANCE LOGGING FUNCTIONS

    fun logUIJankEvent(viewModelClass: String, operation: String, frameDrops: Int, skippedFrames: Int, renderingMs: Long) {
        uiJankEvents.incrementAndGet()
        frameDrops.let { this.frameDrops.addAndGet(it.toLong()) }
        totalSkippedFrames.addAndGet(skippedFrames.toLong())
        if (renderingMs > 16) slowOperations.incrementAndGet()

        val severity = when {
            frameDrops > 5 || skippedFrames > 10 -> "SEVERE"
            frameDrops > 2 || skippedFrames > 5 -> "MODERATE"
            else -> "MINOR"
        }

        // Track problematic UI files/components
        if (skippedFrames > 2) {
            val componentKey = "$viewModelClass.$operation"
            problematicUIFiles.computeIfAbsent(componentKey) { AtomicLong(0) }.addAndGet(skippedFrames.toLong())

            synchronized(problematicUIComponents) {
                val entry = "$componentKey (${skippedFrames} frames)"
                problematicUIComponents.add(entry)
                if (problematicUIComponents.size > 50) {
                    problematicUIComponents.removeAt(0)
                }
            }
        }

        logEvent("[UI_JANK] $viewModelClass.$operation: Drops:$frameDrops Skipped:$skippedFrames Render:${renderingMs}ms [$severity]")
    }

    fun logStateFlowPerformance(viewModelClass: String, stateFlowName: String, emissionMs: Long, subscriberCount: Int, chainLength: Int) {
        if (emissionMs > 30) {
            slowStateFlowEmissions.incrementAndGet()
            slowOperations.incrementAndGet()
        }
        if (chainLength > 5) complexStateFlowChains.incrementAndGet()

        val performance = when {
            emissionMs > 50 -> "SLOW"
            chainLength > 5 -> "COMPLEX"
            subscriberCount > 10 -> "HEAVY"
            else -> "EFFICIENT"
        }

        logEvent("[STATEFLOW] $viewModelClass.$stateFlowName: Emit:${emissionMs}ms Subs:$subscriberCount Chain:$chainLength [$performance]")
    }

    fun logLoadingPerformance(viewModelClass: String, operation: String, loadingMs: Long, userVisibleMs: Long, cacheHit: Boolean) {
        if (userVisibleMs > 2000) {
            poorLoadingExperience.incrementAndGet()
            slowOperations.incrementAndGet()
        }

        val uxQuality = when {
            userVisibleMs > 3000 -> "POOR"
            userVisibleMs > 1000 -> "SLOW"
            userVisibleMs > 500 -> "MODERATE"
            else -> "FAST"
        }

        val cacheStatus = if (cacheHit) "HIT" else "MISS"
        logEvent("[LOADING] $viewModelClass.$operation: Load:${loadingMs}ms UserVisible:${userVisibleMs}ms Cache:$cacheStatus [$uxQuality]")
    }

    fun logUIThreadBlocking(viewModelClass: String, operation: String, blockingMs: Long) {
        uiThreadBlocking.incrementAndGet()
        if (blockingMs > 16) slowOperations.incrementAndGet()

        val severity = when {
            blockingMs > 100 -> "CRITICAL"
            blockingMs > 50 -> "SEVERE"
            blockingMs > 16 -> "MODERATE"
            else -> "MINOR"
        }

        logEvent("[UI_BLOCKING] $viewModelClass.$operation: Blocked UI thread for ${blockingMs}ms [$severity]")
    }

    // MAPPER CONVERSION LOGGING FUNCTIONS

    fun logMapperConversion(
        mapperClass: String,
        conversionType: String, // "DTO->Domain", "Domain->DTO", "SSoT->DTO"
        entityType: String,
        entityId: String,
        success: Boolean,
        durationMs: Long,
        inputDetails: String = "",
        outputDetails: String = "",
        businessLogic: List<String> = emptyList(),
        validationErrors: List<String> = emptyList(),
        encryptedFields: Int = 0
    ) {
        val status = if (success) "SUCCESS" else "FAILED"
        val performance = when {
            durationMs > 100 -> "SLOW"
            durationMs > 50 -> "MODERATE"
            else -> "FAST"
        }

        val businessLogicStr = if (businessLogic.isNotEmpty()) " Logic:[${businessLogic.joinToString(",")}]" else ""
        val validationStr = if (validationErrors.isNotEmpty()) " Errors:${validationErrors.size}" else ""
        val encryptionStr = if (encryptedFields > 0) " Encrypted:$encryptedFields" else ""

        logEvent("[CONVERSION] $mapperClass: $entityType $conversionType | ID: $entityId$businessLogicStr$encryptionStr | ${durationMs}ms [$status] [$performance]")

        if (inputDetails.isNotEmpty()) {
            logEvent("  Input: $inputDetails")
        }
        if (outputDetails.isNotEmpty()) {
            logEvent("  Output: $outputDetails")
        }
        if (validationErrors.isNotEmpty()) {
            logEvent("  Validation Errors: ${validationErrors.joinToString(", ")}")
        }

        // Track slow conversions
        if (durationMs > 50) {
            slowOperations.incrementAndGet()
        }
    }

    fun logMapperConversionStart(mapperClass: String, conversionType: String, entityType: String, entityId: String, inputDetails: String = "") {
        logEvent("[CONVERSION_START] $mapperClass: $entityType $conversionType | ID: $entityId")
        if (inputDetails.isNotEmpty()) {
            logEvent("  Input: $inputDetails")
        }
    }

    fun logMapperBusinessLogic(mapperClass: String, entityType: String, entityId: String, logicType: String, details: String = "") {
        logEvent("[MAPPER_LOGIC] $mapperClass.$entityType: $logicType | ID: $entityId${if (details.isNotEmpty()) " | $details" else ""}")
    }

    private fun getSimpleDashboardHTML(): String {
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autogratuity - Clarity Architecture Dashboard</title>
    <style>
        :root {
            --color-background: rgba(31, 33, 33, 1);
            --color-surface: rgba(38, 40, 40, 1);
            --color-surface-light: rgba(58, 58, 58, 1);
            --color-text: rgba(245, 245, 245, 1);
            --color-text-secondary: rgba(167, 169, 169, 0.7);
            --color-primary: rgba(50, 184, 198, 1);
            --color-border: rgba(119, 124, 124, 0.3);
            --color-success: rgba(50, 184, 198, 1);
            --color-warning: rgba(230, 129, 97, 1);
            --color-error: rgba(255, 84, 89, 1);
            --color-info: rgba(167, 169, 169, 1);
            --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
            --radius-sm: 6px;
            --radius-base: 8px;
            --radius-lg: 12px;
            --space-8: 8px;
            --space-12: 12px;
            --space-16: 16px;
            --space-20: 20px;
            --space-24: 24px;
            --space-32: 32px;
        }

        body {
            background: var(--color-background);
            color: var(--color-text);
            font-family: var(--font-family-base);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .dashboard {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            gap: var(--space-24);
            padding: var(--space-24);
        }

        .dashboard-header {
            background: var(--color-surface);
            border-radius: var(--radius-lg);
            border: 1px solid var(--color-border);
            padding: var(--space-24);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--space-16);
        }

        .app-info .app-name {
            font-size: 24px;
            font-weight: 600;
            color: var(--color-text);
            margin: 0 0 var(--space-8) 0;
        }

        .app-details {
            display: flex;
            gap: var(--space-16);
            flex-wrap: wrap;
        }

        .app-details span {
            padding: var(--space-8) var(--space-12);
            background: var(--color-surface-light);
            border-radius: var(--radius-sm);
            font-size: 12px;
            color: var(--color-text-secondary);
        }

        .status-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: var(--space-8);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: var(--space-8);
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--color-success);
            animation: pulse 2s infinite;
        }

        .timestamp-info {
            text-align: right;
            font-size: 12px;
        }

        .current-time {
            font-weight: 500;
            color: var(--color-text);
        }

        .last-update {
            color: var(--color-text-secondary);
        }

        .overview-section {
            background: var(--color-surface);
            border-radius: var(--radius-lg);
            border: 1px solid var(--color-border);
            padding: var(--space-24);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-20);
        }

        .metric-card {
            background: var(--color-surface-light);
            border-radius: var(--radius-base);
            border: 1px solid var(--color-border);
            padding: var(--space-20);
            transition: transform 0.25s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-12);
        }

        .metric-header h3 {
            font-size: 12px;
            font-weight: 500;
            color: var(--color-text-secondary);
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-trend {
            font-size: 11px;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: var(--radius-sm);
        }

        .trend-good {
            background: rgba(50, 184, 198, 0.1);
            color: var(--color-success);
        }

        .trend-bad {
            background: rgba(255, 84, 89, 0.1);
            color: var(--color-error);
        }

        .trend-neutral {
            background: rgba(167, 169, 169, 0.1);
            color: var(--color-text-secondary);
        }

        .trend-warning {
            background: rgba(230, 129, 97, 0.1);
            color: var(--color-warning);
        }

        .metric-value {
            display: flex;
            align-items: baseline;
            gap: 4px;
            margin-bottom: var(--space-8);
        }

        .metric-value .value {
            font-size: 30px;
            font-weight: 600;
            color: var(--color-text);
        }

        .metric-value .unit {
            font-size: 16px;
            color: var(--color-text-secondary);
        }

        .metric-subtitle {
            font-size: 12px;
            color: var(--color-text-secondary);
        }

        .session-section {
            background: var(--color-surface);
            border-radius: var(--radius-lg);
            border: 1px solid var(--color-border);
            padding: var(--space-24);
            margin-bottom: var(--space-24);
        }

        .session-container {
            display: flex;
            flex-direction: column;
        }

        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-16);
        }

        .session-info {
            display: flex;
            gap: var(--space-16);
            font-size: 0.9em;
            color: var(--color-text-secondary);
        }

        .session-id {
            color: var(--color-primary);
            font-weight: 600;
        }

        .session-count {
            color: var(--color-success);
        }

        .session-events {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            line-height: 1.4;
            color: var(--color-text);
        }

        .session-event {
            display: block;
            padding: 2px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .logs-section {
            background: var(--color-surface);
            border-radius: var(--radius-lg);
            border: 1px solid var(--color-border);
            padding: var(--space-24);
        }

        .logs-container {
            display: flex;
            flex-direction: column;
            height: 400px;
        }

        .logs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-16);
        }

        .logs-controls {
            display: flex;
            align-items: center;
            gap: var(--space-12);
        }

        .log-filter {
            background: var(--color-surface-light);
            color: var(--color-text);
            border: 1px solid var(--color-border);
            padding: var(--space-8) var(--space-12);
            border-radius: var(--radius-sm);
            font-size: 12px;
            cursor: pointer;
        }

        .copy-logs-btn, .pause-logs-btn, .hide-jank-btn {
            background: var(--color-primary);
            color: var(--color-background);
            border: none;
            padding: var(--space-8) var(--space-12);
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .copy-logs-btn:hover, .pause-logs-btn:hover, .hide-jank-btn:hover {
            background: rgba(50, 184, 198, 0.8);
        }

        .hide-jank-btn.active {
            background: var(--color-success);
        }

        .hide-jank-btn.active:hover {
            background: rgba(76, 175, 80, 0.8);
        }

        .pause-logs-btn.paused {
            background: var(--color-warning);
        }

        .pause-logs-btn.paused:hover {
            background: rgba(230, 129, 97, 0.8);
        }

        .logs-header h3 {
            font-size: 16px;
            font-weight: 600;
            color: var(--color-text);
            margin: 0;
        }

        .logs-viewer {
            flex: 1;
            background: #1e1e1e;
            border-radius: var(--radius-base);
            border: 1px solid var(--color-border);
            padding: var(--space-12);
            overflow-y: auto;
            font-family: var(--font-family-mono);
            font-size: 12px;
            user-select: text;
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
        }

        .log-entry {
            display: flex;
            gap: var(--space-12);
            padding: 4px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            user-select: text;
            -webkit-user-select: text;
            cursor: text;
        }

        .log-entry:hover {
            background: rgba(255, 255, 255, 0.02);
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-level {
            font-weight: 600;
            min-width: 50px;
        }

        .log-level.INFO {
            color: var(--color-info);
        }

        .log-level.DEBUG {
            color: var(--color-text-secondary);
        }

        .log-level.WARN {
            color: var(--color-warning);
        }

        .log-level.ERROR {
            color: var(--color-error);
        }

        .log-timestamp {
            color: var(--color-text-secondary);
            min-width: 80px;
        }

        .log-component {
            color: var(--color-primary);
            min-width: 120px;
        }

        .log-message {
            color: var(--color-text);
            flex: 1;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .dashboard {
                padding: var(--space-16);
                gap: var(--space-16);
            }

            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }

            .status-info {
                align-items: flex-start;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- Header Section -->
        <header class="dashboard-header">
            <div class="header-content">
                <div class="app-info">
                    <h1 class="app-name">🏗️ Clarity Architecture Dashboard</h1>
                    <div class="app-details">
                        <span class="version">Autogratuity</span>
                        <span class="build">Debug Build</span>
                        <span class="package">com.autogratuity</span>
                    </div>
                </div>
                <div class="status-info">
                    <div class="status-indicator">
                        <span class="status-dot"></span>
                        <span>Monitoring Active</span>
                    </div>
                    <div class="timestamp-info">
                        <div class="current-time" id="currentTime"></div>
                        <div class="last-update">Last update: <span id="lastUpdate">--</span></div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Overview Cards Section -->
        <section class="overview-section">
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-header">
                        <h3>Total Requests</h3>
                        <span class="metric-trend trend-good" id="requestsTrend">--</span>
                    </div>
                    <div class="metric-value">
                        <span class="value" id="totalRequests">--</span>
                        <span class="unit">ops</span>
                    </div>
                    <div class="metric-subtitle">Architecture operations</div>
                </div>

                <div class="metric-card">
                    <div class="metric-header">
                        <h3>Error Rate</h3>
                        <span class="metric-trend trend-neutral" id="errorTrend">--</span>
                    </div>
                    <div class="metric-value">
                        <span class="value" id="errorCount">--</span>
                        <span class="unit">errors</span>
                    </div>
                    <div class="metric-subtitle">Failed operations</div>
                </div>

                <div class="metric-card">
                    <div class="metric-header">
                        <h3>Cache Hit Ratio</h3>
                        <span class="metric-trend trend-good" id="cacheTrend">--</span>
                    </div>
                    <div class="metric-value">
                        <span class="value" id="cacheHitRatio">--</span>
                        <span class="unit">%</span>
                    </div>
                    <div class="metric-subtitle">Cache efficiency</div>
                </div>

                <div class="metric-card">
                    <div class="metric-header">
                        <h3>Firestore Writes</h3>
                        <span class="metric-trend trend-warning" id="firestoreTrend">--</span>
                    </div>
                    <div class="metric-value">
                        <span class="value" id="firestoreWrites">--</span>
                        <span class="unit">writes</span>
                    </div>
                    <div class="metric-subtitle">Database operations</div>
                </div>

                <div class="metric-card">
                    <div class="metric-header">
                        <h3>ViewModel Inits</h3>
                        <span class="metric-trend trend-neutral" id="viewModelTrend">--</span>
                    </div>
                    <div class="metric-value">
                        <span class="value" id="viewModelInits">--</span>
                        <span class="unit">inits</span>
                    </div>
                    <div class="metric-subtitle">UI layer performance</div>
                </div>

                <div class="metric-card">
                    <div class="metric-header">
                        <h3>Problematic UI</h3>
                        <span class="metric-trend trend-bad" id="problematicUITrend">--</span>
                    </div>
                    <div class="metric-value">
                        <span class="value" id="totalSkippedFrames">--</span>
                        <span class="unit">frames</span>
                    </div>
                    <div class="metric-subtitle">Skipped frames this session</div>
                </div>


            </div>
        </section>

        <!-- Session Events Section -->
        <section class="session-section">
            <div class="session-container">
                <div class="session-header">
                    <h3>Current Session Events</h3>
                    <div class="session-info">
                        <span class="session-id">Session: ${currentSessionId ?: "None"}</span>
                        <span class="session-count">Events: ${sessionEvents.size}</span>
                    </div>
                </div>
                <div class="session-events" style="height: 200px; overflow-y: auto; background: #1a1a1a; padding: 10px; border-radius: 5px;">
                    ${sessionEvents.takeLast(50).joinToString("<br>") { event ->
                        "<span class='session-event'>[${java.time.LocalTime.now()}] $event</span>"
                    }}
                </div>
            </div>
        </section>

        <!-- Live Logs Section -->
        <section class="logs-section">
            <div class="logs-container" style="height: 600px;">
                <div class="logs-header">
                    <h3>Real-time Architecture Events</h3>
                    <div class="logs-controls">
                        <select class="log-filter" id="logFilter" onchange="filterLogs()">
                            <option value="all">All Logs</option>
                            <option value="ERROR">Errors Only</option>
                            <option value="WARN">Warnings Only</option>
                            <option value="INFO">Info Only</option>
                            <option value="firestore">Firestore Only</option>
                            <option value="repository">Repository Only</option>
                            <option value="viewmodel">ViewModel Only</option>
                            <option value="session">Session Events Only</option>
                            <option value="field_extraction">Field Extraction Only</option>
                            <option value="document_parsing">Document Parsing Only</option>
                            <option value="ui_performance">UI Performance Only</option>
                            <option value="no_ui_jank">Hide UI Jank/Frame Drops</option>
                        </select>
                        <button class="copy-logs-btn" onclick="copyAllLogs()">Copy All Logs</button>
                        <button class="pause-logs-btn" onclick="toggleLogUpdates()" id="pauseBtn">Pause</button>
                        <button class="hide-jank-btn" onclick="toggleUIJankFilter()" id="hideJankBtn">Hide UI Jank</button>
                        <div class="refresh-indicator">
                            <span class="status-dot" id="statusDot"></span>
                            <span id="statusText">Live</span>
                        </div>
                    </div>
                </div>
                <div class="logs-viewer" id="logsViewer">
                    <div class="log-entry">
                        <span class="log-level INFO">INFO</span>
                        <span class="log-timestamp">--:--:--</span>
                        <span class="log-component">[System]</span>
                        <span class="log-message">Waiting for events...</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Cache System Health Section -->
        <section class="logs-section" style="margin-top: var(--space-24);">
            <div class="logs-container">
                <div class="logs-header">
                    <h3>⚡ Cache System Performance</h3>
                    <div class="cache-health-summary">
                        <span id="cacheHealthSummary">Analyzing cache performance...</span>
                    </div>
                </div>
                <div class="cache-metrics-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 16px;">
                    <div class="cache-metric-card" style="background: var(--color-surface-light); padding: 12px; border-radius: 6px;">
                        <div style="font-size: 12px; color: var(--color-text-secondary);">Cache Hit Rate</div>
                        <div style="font-size: 18px; font-weight: 600; color: var(--color-success);" id="cacheHitRate">--</div>
                    </div>
                    <div class="cache-metric-card" style="background: var(--color-surface-light); padding: 12px; border-radius: 6px;">
                        <div style="font-size: 12px; color: var(--color-text-secondary);">Slow Cache Misses</div>
                        <div style="font-size: 18px; font-weight: 600; color: var(--color-warning);" id="slowCacheMisses">--</div>
                    </div>
                    <div class="cache-metric-card" style="background: var(--color-surface-light); padding: 12px; border-radius: 6px;">
                        <div style="font-size: 12px; color: var(--color-text-secondary);">Cache Warming</div>
                        <div style="font-size: 18px; font-weight: 600; color: var(--color-info);" id="cacheWarmingStatus">--</div>
                    </div>
                    <div class="cache-metric-card" style="background: var(--color-surface-light); padding: 12px; border-radius: 6px;">
                        <div style="font-size: 12px; color: var(--color-text-secondary);">Session Operations</div>
                        <div style="font-size: 18px; font-weight: 600; color: var(--color-primary);" id="sessionOpsCount">--</div>
                    </div>
                </div>
                <div class="logs-viewer" id="cacheLogsViewer" style="height: 200px;">
                    <div class="log-entry">
                        <span class="log-level INFO">INFO</span>
                        <span class="log-timestamp">--:--:--</span>
                        <span class="log-component">[CacheSystem]</span>
                        <span class="log-message">Cache monitoring active</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Problematic UI Section -->
        <section class="logs-section" style="margin-top: var(--space-24);">
            <div class="logs-container">
                <div class="logs-header">
                    <h3>Problematic UI Components</h3>
                    <div class="problematic-ui-summary">
                        <span id="problematicUISummary">Analyzing UI performance...</span>
                    </div>
                </div>
                <div class="problematic-ui-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px; margin-bottom: 16px;">
                    <div class="problematic-ui-card" style="background: var(--color-surface-light); padding: 12px; border-radius: 6px;">
                        <div style="font-size: 12px; color: var(--color-text-secondary);">Total Skipped Frames</div>
                        <div style="font-size: 18px; font-weight: 600; color: var(--color-error);" id="totalSkippedFramesDetail">--</div>
                    </div>
                    <div class="problematic-ui-card" style="background: var(--color-surface-light); padding: 12px; border-radius: 6px;">
                        <div style="font-size: 12px; color: var(--color-text-secondary);">Problematic Files</div>
                        <div style="font-size: 18px; font-weight: 600; color: var(--color-warning);" id="problematicFilesCount">--</div>
                    </div>
                </div>
                <div class="logs-viewer" id="problematicUIViewer" style="height: 300px;">
                    <div class="log-entry">
                        <span class="log-level INFO">INFO</span>
                        <span class="log-timestamp">--:--:--</span>
                        <span class="log-component">[UIMonitor]</span>
                        <span class="log-message">UI performance monitoring active</span>
                    </div>
                </div>
            </div>
        </section>

    </div>

    <script>
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                timeZone: 'UTC'
            });
            document.getElementById('currentTime').textContent = timeString + ' UTC';
        }

        let logUpdatesPaused = false;
        let allLogsData = [];
        let currentFilter = 'all';

        function copyAllLogs() {
            const logEntries = document.querySelectorAll('#logsViewer .log-entry:not([style*="display: none"])');
            let logText = '';

            logEntries.forEach(entry => {
                const level = entry.querySelector('.log-level').textContent;
                const timestamp = entry.querySelector('.log-timestamp').textContent;
                const component = entry.querySelector('.log-component').textContent;
                const message = entry.querySelector('.log-message').textContent;
                logText += level + ' ' + timestamp + ' ' + component + ' ' + message + '\\n';
            });

            navigator.clipboard.writeText(logText).then(() => {
                const btn = document.querySelector('.copy-logs-btn');
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.style.background = 'var(--color-success)';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = 'var(--color-primary)';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy logs:', err);
                alert('Copy failed. Please select text manually.');
            });
        }

        function toggleLogUpdates() {
            logUpdatesPaused = !logUpdatesPaused;
            const btn = document.getElementById('pauseBtn');
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');

            if (logUpdatesPaused) {
                btn.textContent = 'Resume';
                btn.classList.add('paused');
                statusDot.style.background = 'var(--color-warning)';
                statusText.textContent = 'Paused';
            } else {
                btn.textContent = 'Pause';
                btn.classList.remove('paused');
                statusDot.style.background = 'var(--color-success)';
                statusText.textContent = 'Live';
            }
        }

        function toggleUIJankFilter() {
            const btn = document.getElementById('hideJankBtn');
            const filterSelect = document.getElementById('logFilter');

            if (btn.classList.contains('active')) {
                // Turn off jank filter
                btn.classList.remove('active');
                btn.textContent = 'Hide UI Jank';
                filterSelect.value = 'all';
            } else {
                // Turn on jank filter
                btn.classList.add('active');
                btn.textContent = 'Show UI Jank';
                filterSelect.value = 'no_ui_jank';
            }

            // Apply the filter
            filterLogs();
        }

        function filterLogs() {
            currentFilter = document.getElementById('logFilter').value;
            const logEntries = document.querySelectorAll('#logsViewer .log-entry');

            logEntries.forEach(entry => {
                const level = entry.querySelector('.log-level').textContent;
                const component = entry.querySelector('.log-component').textContent.toLowerCase();
                const message = entry.querySelector('.log-message').textContent.toLowerCase();

                let shouldShow = true;

                switch(currentFilter) {
                    case 'ERROR':
                        shouldShow = level === 'ERROR';
                        break;
                    case 'WARN':
                        shouldShow = level === 'WARN';
                        break;
                    case 'INFO':
                        shouldShow = level === 'INFO';
                        break;
                    case 'firestore':
                        shouldShow = message.includes('firestore') || component.includes('firestore');
                        break;
                    case 'repository':
                        shouldShow = component.includes('repository') || message.includes('repository');
                        break;
                    case 'viewmodel':
                        shouldShow = component.includes('viewmodel') || message.includes('viewmodel') || component.includes('presentation');
                        break;
                    case 'session':
                        shouldShow = message.includes('session') || message.includes('correlation');
                        break;
                    case 'field_extraction':
                        shouldShow = message.includes('field_extracted') || message.includes('field_extraction');
                        break;
                    case 'document_parsing':
                        shouldShow = message.includes('document_parsing') || message.includes('firestore document parsing');
                        break;
                    case 'ui_performance':
                        // Show only UI performance, jank, and frame-related logs
                        shouldShow = (
                            message.includes('ui_frame_performance') ||
                            message.includes('jank') ||
                            message.includes('frame') ||
                            message.includes('render') ||
                            message.includes('drops') ||
                            message.includes('skipped') ||
                            component.includes('systemui') ||
                            component.includes('ui_performance') ||
                            message.includes('loading_performance') ||
                            message.includes('reactive_performance')
                        );
                        break;
                    case 'no_ui_jank':
                        // Hide UI jank, frame drops, and render performance logs
                        shouldShow = !(
                            message.includes('ui_frame_performance') ||
                            message.includes('jank') ||
                            message.includes('frame') ||
                            message.includes('render') ||
                            message.includes('drops') ||
                            message.includes('skipped') ||
                            component.includes('systemui') ||
                            component.includes('ui_performance')
                        );
                        break;
                    case 'all':
                    default:
                        shouldShow = true;
                        break;
                }

                entry.style.display = shouldShow ? 'flex' : 'none';
            });
        }

        function updateDashboard() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    // Update metrics
                    document.getElementById('totalRequests').textContent = data.total_requests;
                    document.getElementById('errorCount').textContent = data.error_count;
                    document.getElementById('cacheHitRatio').textContent = Math.round(data.cache_hit_ratio * 100);
                    document.getElementById('firestoreWrites').textContent = data.firestore_writes;
                    document.getElementById('viewModelInits').textContent = data.viewmodel_inits;
                    document.getElementById('totalSkippedFrames').textContent = data.total_skipped_frames;
                    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();

                    // Update events log only if not paused
                    if (!logUpdatesPaused) {
                        const eventLog = document.getElementById('logsViewer');
                        const wasScrolledToBottom = eventLog.scrollTop + eventLog.clientHeight >= eventLog.scrollHeight - 5;

                        eventLog.innerHTML = '';
                        allLogsData = data.recent_events;

                        data.recent_events.forEach(event => {
                        const timestamp = new Date().toLocaleTimeString('en-US', { hour12: false });
                        const div = document.createElement('div');
                        div.className = 'log-entry';

                        // Parse log level and component from event
                        let level = 'INFO';
                        let component = 'System';
                        let message = event;

                        if (event.includes('ERROR') || event.includes('❌')) {
                            level = 'ERROR';
                        } else if (event.includes('WARN') || event.includes('⚠️')) {
                            level = 'WARN';
                        } else if (event.includes('DEBUG')) {
                            level = 'DEBUG';
                        }

                        // Extract component from brackets
                        const componentMatch = event.match(/\[([^\]]+)\]/);
                        if (componentMatch) {
                            component = componentMatch[1];
                            message = event.replace(/\[[^\]]+\]/, '').trim();
                        }

                        div.innerHTML = `
                            <span class="log-level ${"$"}{level}">${"$"}{level}</span>
                            <span class="log-timestamp">${"$"}{timestamp}</span>
                            <span class="log-component">[${"$"}{component}]</span>
                            <span class="log-message">${"$"}{message}</span>
                        `;
                        eventLog.appendChild(div);
                    });

                    // Apply current filter to new logs
                    filterLogs();

                    // Only auto-scroll if user was already at bottom
                    if (wasScrolledToBottom) {
                        eventLog.scrollTop = eventLog.scrollHeight;
                    }
                }

                    // Update cache system metrics
                    if (data.cache_health) {
                        const cacheHitRatio = data.cache_hit_ratio || 0;
                        const slowCacheMisses = data.slow_cache_misses || 0;
                        const sessionOps = data.session_operations || 0;

                        document.getElementById('cacheHitRate').textContent = Math.round(cacheHitRatio * 100) + '%';
                        document.getElementById('slowCacheMisses').textContent = slowCacheMisses;
                        document.getElementById('sessionOpsCount').textContent = sessionOps;

                        // Update cache warming status
                        const cacheWarmingActive = data.cache_health.cache_warming_active || false;
                        document.getElementById('cacheWarmingStatus').textContent = cacheWarmingActive ? 'Active' : 'Inactive';

                        // Update cache health summary
                        let healthStatus = 'Good';
                        if (cacheHitRatio < 0.5) healthStatus = 'Poor';
                        else if (cacheHitRatio < 0.8) healthStatus = 'Moderate';

                        document.getElementById('cacheHealthSummary').textContent =
                            'Cache Health: ' + healthStatus + ' | Hit Rate: ' + Math.round(cacheHitRatio * 100) + '% | Slow Misses: ' + slowCacheMisses;
                    }

                    // Update cache-specific logs
                    const cacheLogsViewer = document.getElementById('cacheLogsViewer');
                    if (cacheLogsViewer && data.recent_events) {
                        const cacheEvents = data.recent_events.filter(event =>
                            event.includes('CACHE_') ||
                            event.includes('cache_') ||
                            event.includes('Cache') ||
                            event.includes('SESSION')
                        ).slice(-10); // Last 10 cache-related events

                        cacheLogsViewer.innerHTML = '';
                        cacheEvents.forEach(event => {
                            const timestamp = new Date().toLocaleTimeString('en-US', { hour12: false });
                            const div = document.createElement('div');
                            div.className = 'log-entry';

                            let level = 'INFO';
                            if (event.includes('SLOW') || event.includes('POOR')) level = 'WARN';
                            if (event.includes('CRITICAL') || event.includes('Failed')) level = 'ERROR';

                            const componentMatch = event.match(/\[([^\]]+)\]/);
                            const component = componentMatch ? componentMatch[1] : 'CacheSystem';
                            const message = event.replace(/\[[^\]]+\]/, '').trim();

                            div.innerHTML =
                                '<span class="log-level ' + level + '">' + level + '</span>' +
                                '<span class="log-timestamp">' + timestamp + '</span>' +
                                '<span class="log-component">[' + component + ']</span>' +
                                '<span class="log-message">' + message + '</span>';
                            cacheLogsViewer.appendChild(div);
                        });
                    }

                    // Update problematic UI trend
                    const problematicUITrend = document.getElementById('problematicUITrend');
                    if (data.total_skipped_frames > 100) {
                        problematicUITrend.textContent = 'HIGH';
                        problematicUITrend.className = 'metric-trend trend-bad';
                    } else if (data.total_skipped_frames > 50) {
                        problematicUITrend.textContent = 'MODERATE';
                        problematicUITrend.className = 'metric-trend trend-warning';
                    } else {
                        problematicUITrend.textContent = 'LOW';
                        problematicUITrend.className = 'metric-trend trend-good';
                    }

                    // Update problematic UI details
                    if (data.problematic_ui) {
                        const totalSkippedFramesDetail = document.getElementById('totalSkippedFramesDetail');
                        const problematicFilesCount = document.getElementById('problematicFilesCount');
                        const problematicUISummary = document.getElementById('problematicUISummary');

                        if (totalSkippedFramesDetail) {
                            totalSkippedFramesDetail.textContent = data.problematic_ui.total_skipped_frames || 0;
                        }
                        if (problematicFilesCount) {
                            problematicFilesCount.textContent = data.problematic_ui.total_files || 0;
                        }
                        if (problematicUISummary) {
                            const files = data.problematic_ui.total_files || 0;
                            const frames = data.problematic_ui.total_skipped_frames || 0;
                            problematicUISummary.textContent = files > 0 ?
                                files + ' problematic files, ' + frames + ' total skipped frames' :
                                'No UI performance issues detected';
                        }

                        // Update problematic UI logs
                        const problematicUIViewer = document.getElementById('problematicUIViewer');
                        if (problematicUIViewer && data.recent_events) {
                            const uiEvents = data.recent_events.filter(event =>
                                event.includes('UI_JANK') ||
                                event.includes('ui_performance') ||
                                event.includes('SEVERE_JANK') ||
                                event.includes('MODERATE_JANK') ||
                                event.includes('SLOW_RENDER')
                            ).slice(-15); // Last 15 UI-related events

                            problematicUIViewer.innerHTML = '';
                            uiEvents.forEach(event => {
                                const timestamp = new Date().toLocaleTimeString('en-US', { hour12: false });
                                const div = document.createElement('div');
                                div.className = 'log-entry';

                                let level = 'INFO';
                                if (event.includes('SEVERE') || event.includes('CRITICAL')) level = 'ERROR';
                                else if (event.includes('MODERATE') || event.includes('SLOW')) level = 'WARN';

                                const componentMatch = event.match(/\[([^\]]+)\]/);
                                const component = componentMatch ? componentMatch[1] : 'UIMonitor';
                                const message = event.replace(/\[[^\]]+\]/, '').trim();

                                div.innerHTML =
                                    '<span class="log-level ' + level + '">' + level + '</span>' +
                                    '<span class="log-timestamp">' + timestamp + '</span>' +
                                    '<span class="log-component">[' + component + ']</span>' +
                                    '<span class="log-message">' + message + '</span>';
                                problematicUIViewer.appendChild(div);
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching dashboard data:', error);
                });
        }

        // Update time every second
        setInterval(updateTime, 1000);

        // Start real-time updates
        updateTime();
        updateDashboard();
        setInterval(updateDashboard, 2000);
    </script>
</body>
</html>
        """.trimIndent()
    }
}
