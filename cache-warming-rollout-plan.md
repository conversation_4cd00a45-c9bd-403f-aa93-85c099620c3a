# Cache Warming Rollout Plan

## Overview

This document outlines the necessary rollouts to implement robust cache warming using the correct atomic caching architecture. The current CacheWarmingManager has proper deduplication but lacks actual cache population mechanisms.

## Current State Analysis

### ✅ What's Working
- **Top-level deduplication** implemented with `RequestKeys.cacheWarmingComplete(userId)`
- **Individual operation deduplication** for each cache system
- **Proper architectural pattern**: CacheWarmingManager → DomainCacheSystem → AtomicCacheSystem
- **Integration points** established (AuthViewModel, AutogratuityApp, ClarityArchitectureMonitor)

### ❌ What's Missing
- **Actual cache population**: Current methods just read existing cache data
- **Fresh data fetching**: No mechanism to trigger remote data retrieval
- **Cache system warming methods**: Cache systems lack proper warming operations
- **Repository integration**: No way to trigger repository refresh operations

## Architecture Principles

### Correct Pattern (Maintain This)
```
CacheWarmingManager → DomainCacheSystem → AtomicCacheSystem
                                      ↓
                              Repository (via warming triggers)
                                      ↓
                              RemoteDataSource → Cache Population
```

### Incorrect Pattern (Avoid This)
```
CacheWarmingManager → Repository (WRONG - breaks layer separation)
```

## Rollout Phases

## Phase 1: Cache System Warming Methods

### 1.1 Add Cache Population Methods to DomainCacheSystem

**Target Files:**
- `AddressCacheSystem.kt`
- `DeliveryCacheSystem.kt` 
- `UserProfileCacheSystem.kt`
- `SubscriptionCacheSystem.kt`

**Required Methods:**
```kotlin
// Each cache system needs these warming methods
suspend fun warmCache(userId: String): WarmingResult
suspend fun triggerCachePopulation(userId: String): Boolean
suspend fun refreshFromRepository(userId: String): List<T>
```

### 1.2 Repository Integration Points

**Target Files:**
- `AddressRepositoryImpl.kt`
- `DeliveryRepositoryImpl.kt`
- `UserProfileRepositoryImpl.kt`
- `SubscriptionRepositoryImpl.kt`

**Required Integration:**
```kotlin
// Add to each repository
suspend fun warmCache(userId: String): Result<Unit> {
    // Trigger refreshFromRemote and populate cache
    return refreshFromRemote(userId)
}
```

## Phase 2: Cache System Implementation

### 2.1 AddressCacheSystem Warming
```kotlin
suspend fun warmCache(userId: String): WarmingResult {
    return try {
        // Trigger repository refresh which will populate cache
        val addresses = addressRepository.getAllAddresses()
        when (addresses) {
            is Result.Success -> {
                // Cache is automatically populated by repository
                WarmingResult.Success(addresses.data.size)
            }
            is Result.Error -> WarmingResult.Failure(addresses.exception.message)
        }
    } catch (e: Exception) {
        WarmingResult.Failure(e.message)
    }
}
```

### 2.2 DeliveryCacheSystem Warming
```kotlin
suspend fun warmCache(userId: String): WarmingResult {
    return try {
        // Trigger repository refresh for recent deliveries
        val deliveries = deliveryRepository.getRecentDeliveries(userId, limit = 20)
        when (deliveries) {
            is Result.Success -> WarmingResult.Success(deliveries.data.size)
            is Result.Error -> WarmingResult.Failure(deliveries.exception.message)
        }
    } catch (e: Exception) {
        WarmingResult.Failure(e.message)
    }
}
```

### 2.3 UserProfileCacheSystem Warming
```kotlin
suspend fun warmCache(userId: String): WarmingResult {
    return try {
        val user = userRepository.getUserById(userId)
        when (user) {
            is Result.Success -> WarmingResult.Success(if (user.data != null) 1 else 0)
            is Result.Error -> WarmingResult.Failure(user.exception.message)
        }
    } catch (e: Exception) {
        WarmingResult.Failure(e.message)
    }
}
```

### 2.4 SubscriptionCacheSystem Warming
```kotlin
suspend fun warmCache(userId: String): WarmingResult {
    return try {
        val subscription = subscriptionRepository.getUserSubscription(userId)
        when (subscription) {
            is Result.Success -> WarmingResult.Success(if (subscription.data != null) 1 else 0)
            is Result.Error -> WarmingResult.Failure(subscription.exception.message)
        }
    } catch (e: Exception) {
        WarmingResult.Failure(e.message)
    }
}
```

## Phase 3: CacheWarmingManager Updates

### 3.1 Update Warming Methods
Replace current cache reading methods with actual warming calls:

```kotlin
private suspend fun warmUserProfile(userId: String): WarmingStatus {
    return try {
        val result = requestDeduplicationManager.deduplicateRequest(
            key = RequestKeys.cacheWarmingUser(userId),
            timeout = RequestTimeouts.CACHE_WARMING,
            operation = {
                // ✅ CORRECT: Trigger actual cache warming
                userProfileCacheSystem.warmCache(userId)
            }
        )
        // Convert WarmingResult to WarmingStatus
    } catch (e: Exception) {
        WarmingStatus.Failure(e.message ?: "Unknown error", duration)
    }
}
```

## Phase 4: Dependency Injection Updates

### 4.1 Add Repository Dependencies to Cache Systems
```kotlin
// In CacheModule.kt or DataModule.kt
single<AddressCacheSystem> {
    AddressCacheSystem(
        timeSource = get(),
        ioDispatcher = get(named(KoinQualifiers.IO_DISPATCHER)),
        applicationScope = get(named(KoinQualifiers.APPLICATION_SCOPE)),
        addressRepository = lazy { get<AddressRepository>() } // Add this
    )
}
```

### 4.2 Update Cache System Constructors
```kotlin
class AddressCacheSystem @Inject constructor(
    timeSource: TimeSource,
    ioDispatcher: CoroutineDispatcher,
    private val applicationScope: CoroutineScope,
    private val addressRepository: Lazy<AddressRepository> // Add this
) : AtomicCacheSystem<String, Address>(timeSource)
```

## Phase 5: Testing and Validation

### 5.1 Unit Tests
- Test each cache system's `warmCache()` method
- Verify repository integration works correctly
- Test deduplication still functions

### 5.2 Integration Tests
- Test complete cache warming flow
- Verify cache population actually occurs
- Test concurrent warming scenarios

### 5.3 Performance Validation
- Measure cache warming effectiveness
- Verify cold start improvements
- Monitor deduplication metrics

## Implementation Order

1. **Phase 1**: Add warming methods to cache systems (infrastructure)
2. **Phase 2**: Implement cache system warming logic
3. **Phase 3**: Update CacheWarmingManager to use new methods
4. **Phase 4**: Update DI configuration
5. **Phase 5**: Test and validate

## Success Metrics

- **Cache Hit Rate**: >80% for warmed data within 5 minutes of login
- **Cold Start Time**: <2 seconds for dashboard load after cache warming
- **Deduplication Rate**: >50% reduction in duplicate warming requests
- **Error Rate**: <5% warming operation failures

## Risk Mitigation

- **Circular Dependencies**: Use `Lazy<Repository>` injection
- **Performance Impact**: Implement warming timeouts and fallbacks
- **Cache Consistency**: Ensure warming doesn't interfere with normal operations
- **Memory Usage**: Monitor cache size during warming operations

## Alternative Approach: Repository-Triggered Warming

### Option B: Indirect Cache Warming
Instead of adding repository dependencies to cache systems, use an event-driven approach:

```kotlin
// CacheWarmingManager triggers repository operations
private suspend fun warmUserProfile(userId: String): WarmingStatus {
    return try {
        val result = requestDeduplicationManager.deduplicateRequest(
            key = RequestKeys.cacheWarmingUser(userId),
            timeout = RequestTimeouts.CACHE_WARMING,
            operation = {
                // Trigger repository operation that will populate cache as side effect
                userRepository.value.getUserById(userId)
            }
        )
        // Repository operation automatically populates cache via LocalDataSource
        when (result) {
            is Result.Success -> WarmingStatus.Success(System.currentTimeMillis(), duration)
            is Result.Error -> WarmingStatus.Failure(result.exception.message, duration)
            else -> WarmingStatus.Timeout(duration)
        }
    } catch (e: Exception) {
        WarmingStatus.Failure(e.message ?: "Unknown error", duration)
    }
}
```

### Benefits of Option B:
- **No circular dependencies**: CacheWarmingManager → Repository → LocalDataSource → CacheSystem
- **Leverages existing architecture**: Repository operations already populate caches
- **Simpler implementation**: No new cache system methods needed
- **Maintains separation**: Cache systems remain pure infrastructure

### Required Changes for Option B:
1. **Add repository dependencies to CacheWarmingManager**
2. **Update warming methods to call repository operations**
3. **Ensure repository operations trigger cache population**
4. **No changes to cache systems needed**

## Recommended Approach

**Option B (Repository-Triggered Warming)** is recommended because:
- Simpler to implement
- Leverages existing cache population mechanisms
- Avoids circular dependency issues
- Maintains clean architectural boundaries

## Rollback Plan

If issues arise:
1. Disable cache warming triggers in AuthViewModel/AutogratuityApp
2. Revert CacheWarmingManager to read-only operations
3. Remove repository dependencies from CacheWarmingManager
4. Fall back to normal cache-first repository operations
