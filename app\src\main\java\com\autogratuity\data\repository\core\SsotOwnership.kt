package com.autogratuity.data.repository.core

/**
 * SSOT (Single Source of Truth) field ownership tags for Firestore data management
 * 
 * This enum defines which component has authority to modify specific fields
 * in Firestore documents, ensuring data consistency and preventing conflicts.
 */
enum class SsotOwnership {
    /**
     * Field is managed exclusively by the client application.
     * Cloud functions should not modify these fields.
     */
    CLIENT_OWNED,
    
    /**
     * Field is managed exclusively by cloud functions.
     * Client applications should not modify these fields directly.
     */
    CLOUD_OWNED,
    
    /**
     * Field may be updated by both client and cloud with conflict resolution.
     * Requires timestamp-based or version-based conflict resolution.
     */
    SHARED,
    
    /**
     * Field should not be modified after initial creation.
     * System-managed fields like createdAt, document IDs, etc.
     */
    READ_ONLY
} 