# Debug Protocol Documentation

## Meta Perspective: LLM-to-LLM Debug Orchestration Protocol

This document describes the systematic debug protocol for complex software issues using dual-LLM orchestration with the Zen MCP debugger server. This protocol has been validated through successful resolution of critical validation and timestamp conversion issues.

## Protocol Overview

### Participants
- **Primary LLM (Orchestrator)**: Conducts initial analysis, gathers context, orchestrates debug session
- **Debug LLM (Specialist)**: Deep technical analysis via Zen MCP server with o4-mini-high model
- **User**: Initiates protocol with "initiate debug protocol" command
- **Zen MCP Server**: Provides specialized debugging capabilities with high-performance reasoning

### Protocol Phases

## Phase 1: Issue Identification & Context Gathering

### 1.1 Issue Selection
- **Input**: User identifies specific error category from categorized error analysis
- **Action**: Primary LLM selects target issue from errors markdown file
- **Criteria**: Prioritize by impact (Critical > High > Medium), frequency, and architectural significance

### 1.2 Pre-Context Surveillance
**CRITICAL**: Always gather comprehensive context BEFORE engaging debugger

```markdown
Required Context Gathering:
1. Codebase retrieval for relevant components
2. Architecture understanding from clarity.md
3. Log trace analysis from error documentation
4. Related file identification and content review
5. Historical context from previous fixes/implementations
```

**Tools Used:**
- `codebase-retrieval`: Find relevant code components
- `read_file_*`: Read specific implementation files
- `view`: Examine code sections with search patterns

### 1.3 Initial Analysis
- **Thinking Tools**: Use `code-reasoning` for preliminary analysis
- **Pattern Recognition**: Identify error patterns, architectural violations
- **Hypothesis Formation**: Develop initial theories about root cause

## Phase 2: Debug Orchestration

### 2.1 Debugger Engagement
**Tool**: `debug_zen` with specific parameters:
- **Model**: `o4-mini-high` (high-performance reasoning)
- **Thinking Mode**: `high` (67% of model capacity for complex issues)
- **Web Search**: `true` (for best practices and documentation)

### 2.2 Context Provision Strategy
**COMPREHENSIVE UPFRONT CONTEXT** - Provide maximum relevant information in initial prompt:

```markdown
Required Context Elements:
1. Primary Issue Description
2. Specific Log Traces (copy-paste actual logs)
3. Architecture Context (Clarity Architecture patterns)
4. Code Implementation Details (relevant source code)
5. Error Frequency and Impact Assessment
6. Business Logic Context
7. Expected vs Actual Behavior
8. Related System Components
```

### 2.3 Iterative Refinement
- **Follow-up Questions**: Address debugger clarification requests
- **Code Provision**: Share specific implementation details as requested
- **Validation**: Confirm understanding and approach before implementation

## Phase 3: Implementation & Documentation

### 3.1 Solution Implementation
- **Code Changes**: Apply debugger-recommended fixes
- **Diagnostic Enhancement**: Add logging and error handling as suggested
- **Architecture Compliance**: Ensure changes follow Clarity Architecture principles

### 3.2 Documentation Updates
**MANDATORY**: Update error categorization file with:

```markdown
Documentation Requirements:
1. Root Cause Analysis (completed)
2. Original Log Traces (evidence)
3. Implementation Details (exact code changes)
4. Expected New Log Patterns (for future verification)
5. Next LLM Analysis Instructions (clear guidance)
6. Status Updates (✅ IMPLEMENTED vs 🔍 REQUIRES INVESTIGATION)
```

### 3.3 Memory Updates
- **Protocol Reinforcement**: Update LLM memories with debug protocol
- **Issue-Specific Learning**: Document specific technical insights
- **Process Improvements**: Note any protocol refinements

## Phase 4: Future LLM Handoff

### 4.1 Context Preservation
**For Subsequent LLMs:**
- **Clear Status Indicators**: ✅ IMPLEMENTED, 🔍 REQUIRES INVESTIGATION
- **Diagnostic Log Patterns**: What to look for in new logs
- **Verification Steps**: How to confirm fix effectiveness
- **Continuation Instructions**: Next steps if issues persist

### 4.2 Breadcrumb System
**Log Trace Integration**: Embed actual log traces as evidence for:
- **Problem Validation**: Proof of original issue
- **Solution Verification**: Expected new log patterns
- **Regression Detection**: Patterns that indicate problems

## Validated Protocol Examples

### Example 1: Delivery Validation Failure
**Issue**: "Domain Delivery validation failed: [Delivery details are required]"
**Process**:
1. **Context Gathering**: Retrieved ValidationEngine.kt, DeliveryMapper.kt, domain models
2. **Initial Analysis**: Used code-reasoning to identify faulty safe-call chain
3. **Debug Orchestration**: Engaged debug_zen with comprehensive context
4. **Root Cause**: Validation logic conflated missing details with missing nested properties
5. **Implementation**: Enhanced diagnostic logging in ValidationEngine.kt and DeliveryMapper.kt
6. **Documentation**: Updated errors6-13.md with complete analysis and expected log patterns

### Example 2: Firestore Timestamp Conversion
**Issue**: "HashMap does not contain valid Firestore Timestamp data"
**Process**:
1. **Context Gathering**: Retrieved DtoUtils.kt, timestamp handling code, backend implementations
2. **Initial Analysis**: Identified missing OffsetDateTime structure handling
3. **Debug Orchestration**: Provided comprehensive timestamp format examples
4. **Root Cause**: parseUniversalTimestamp() only handled simple Firestore format
5. **Implementation**: Enhanced with dual-format timestamp parsing
6. **Documentation**: Updated with implementation details and diagnostic patterns

## Protocol Commands

### User Initiation
```
"initiate debug protocol"
```

### LLM Response Pattern
```markdown
1. "I'll initiate the debug protocol for [ISSUE_CATEGORY]"
2. Conduct comprehensive context gathering
3. Perform initial analysis with thinking tools
4. Engage debug_zen with full context
5. Implement recommended solutions
6. Update documentation with complete analysis
7. Provide future LLM guidance
```

## Success Metrics

### Protocol Effectiveness
- **Root Cause Identification**: Clear technical explanation
- **Solution Implementation**: Working code changes
- **Documentation Quality**: Comprehensive future LLM guidance
- **Verification Path**: Clear steps to confirm resolution

### Quality Indicators
- **Diagnostic Logging**: Enhanced error reporting for future debugging
- **Architecture Compliance**: Solutions follow established patterns
- **Backward Compatibility**: No regression in existing functionality
- **Knowledge Transfer**: Future LLMs can continue from documented state

## Protocol Refinements

### Lessons Learned
1. **Context First**: Always gather comprehensive context before debugger engagement
2. **Specific Evidence**: Use actual log traces, not generic descriptions
3. **Implementation Focus**: Debugger provides actionable solutions
4. **Documentation Completeness**: Future LLMs need full context for continuation
5. **Status Clarity**: Clear indicators prevent duplicate work

### Future Improvements
- **Automated Context Gathering**: Streamline codebase surveillance
- **Template Standardization**: Consistent documentation formats
- **Verification Protocols**: Systematic solution validation
- **Knowledge Base Integration**: Link to architectural documentation

---

**Protocol Status**: ✅ VALIDATED - Successfully applied to 2 critical issues
**Next Evolution**: Expand to cover performance optimization and architecture compliance debugging
