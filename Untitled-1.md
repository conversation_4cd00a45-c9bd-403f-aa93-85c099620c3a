           D  User profile cache warming successful in 358ms
   I  Cache warming completed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: user_profile:success:358ms, subscription:success:296ms, addresses:success:257ms, total:360ms
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  ✅ MODERN: Job completed successfully
 D  🚀 MAP DATA LOADED: 1 addresses in 924ms
 D  🚀 INSTANT: Map data loaded, hiding skeleton immediately
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved 0 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getDeliveriesByUserId (SSoT) - Deliveries not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Remote:307ms Map:2ms Store:26ms Hit:false ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 335ms [OK]
 D  Retrieved 0 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Completed waiting for 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 448ms, result: true
 D  User profile cache warming successful in 454ms
 D  Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  observeCurrentUser: Cache warming initiated for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeWithProfiling: Completed getDefaultAddressSummary in 363ms
 D  getDeliveriesByUserId (SSoT) - Deliveries not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  [cache_system.UserRepository] cache_breakdown Check:2ms Remote:312ms Map:24ms Store:4ms Hit:false ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 342ms [OK]
 I  Cache warming completed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: user_profile:success:454ms, subscription:success:383ms, addresses:success:347ms, total:456ms
 D  [data.UserRepository] getDefaultAddressSummary(User) ID:current_user User:current_user Count:1 Size:0bytes Source:repository Strategy:cache-first [CACHE_MISS]: 363ms [OK]
 D  🗺️ MAP-CRITICAL WARMING COMPLETE: 458ms - addresses: Success(cachedAt=1749796145505, durationMs=347), user: Success(cachedAt=1749796145609, durationMs=454)
 D  Task getDefaultAddressSummary_current_user completed in 367ms
 D  🎯 SIMPLE GEOCODING: Testing native geocoding capability
 D  getAddresses (SSoT) - Fetching all addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [data.UserRepository] getUserById(User) ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1455bytes Source:remote Strategy:cache-first [CACHE_MISS]: 358ms [OK]
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 completed in 367ms
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  preferredRenderer: null
 D  preferredRenderer: null
 I  Making Creator dynamically
 I  Considering local module com.google.android.gms.maps_core_dynamite:0 and remote module com.google.android.gms.maps_core_dynamite:251625202
 I  Selected remote version of com.google.android.gms.maps_core_dynamite, version >= 251625202
 V  Dynamite loader version >= 2, using loadModule2NoCrashUtils
 D  Compat change id reported: 312399441; UID 10209; state: ENABLED
 W  Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.dm': No such file or directory
 W  Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.dm': No such file or directory
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=nbKRDYvJ1hYj72b8JvOj, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, dto_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), dto_tags_count=0, dto_isDefault=null}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=nbKRDYvJ1hYj72b8JvOj, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, dto_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=nbKRDYvJ1hYj72b8JvOj, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, domain_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Output Domain State: {domain_id=nbKRDYvJ1hYj72b8JvOj, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, domain_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  [AddressMapper] toSsot(Address) ID:nbKRDYvJ1hYj72b8JvOj User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1210bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 I  CACHE WARMING: Addresses loaded in 201ms
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [AddressMapper] toSsot(Address) ID:nbKRDYvJ1hYj72b8JvOj User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1210bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  🗺️ INSTANT MAP DATA: Loaded 1 addresses from warmed cache
 D  Complete cache warming: 633ms total (warming: 458ms)
 D  Batch normalization completed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 0 documents updated
 D  Batch normalization completed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 0 documents updated
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, hour=9, dayOfMonth=9, dayOfYear=160, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, dayOfYear=160, dayOfMonth=9, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 D  tagSocket(162) with statsTag=0xffffffff, statsUid=-1
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, dayOfYear=160, year=2025, dayOfMonth=9, monthValue=6, nano=919314000, minute=29, second=26}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, year=2025, dayOfYear=160, dayOfMonth=9, monthValue=6, nano=919422000, minute=29, second=26}
 D  early loading native code
 W  Loading native library 'gmm-jni' on thread main
 D  Configuring clns-12 for other apk . target_sdk_version=35, uses_libraries=ALL, library_path=/data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.apk!/lib/x86_64:/data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, hour=9, dayOfMonth=9, dayOfYear=160, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, dayOfYear=160, dayOfMonth=9, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, hour=9, dayOfMonth=9, dayOfYear=160, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, dayOfYear=160, year=2025, dayOfMonth=9, monthValue=6, nano=919314000, minute=29, second=26}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, dayOfYear=160, dayOfMonth=9, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, year=2025, dayOfYear=160, dayOfMonth=9, monthValue=6, nano=919422000, minute=29, second=26}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, dayOfYear=160, year=2025, dayOfMonth=9, monthValue=6, nano=919314000, minute=29, second=26}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, year=2025, dayOfYear=160, dayOfMonth=9, monthValue=6, nano=919422000, minute=29, second=26}
 D  Load /data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.apk!/lib/x86_64/libgmm-jni.so using isolated ns clns-12 (caller=/data/user_de/0/com.google.android.gms/app_chimera/m/0000000d/dl-MapsCoreDynamite.integ_251625202100800.apk): ok
 I  I0000 00:00:1749796145.858648    6079 jni_init.cc:30] Initializing JNI...
 I  JNI initialized.
 I  Google Play services client version: 19020000
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, hour=9, dayOfMonth=9, dayOfYear=160, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, hour=9, dayOfMonth=9, dayOfYear=160, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, dayOfYear=160, dayOfMonth=9, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, dayOfYear=160, dayOfMonth=9, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, dayOfYear=160, year=2025, dayOfMonth=9, monthValue=6, nano=919314000, minute=29, second=26}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, dayOfYear=160, year=2025, dayOfMonth=9, monthValue=6, nano=919314000, minute=29, second=26}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, year=2025, dayOfYear=160, dayOfMonth=9, monthValue=6, nano=919422000, minute=29, second=26}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, hour=9, dayOfMonth=9, dayOfYear=160, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, dayOfYear=160, dayOfMonth=9, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 D  getAllDeliveries: Found 1 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (1 modern, 0 legacy)
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, dayOfYear=160, year=2025, dayOfMonth=9, monthValue=6, nano=919314000, minute=29, second=26}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, year=2025, dayOfYear=160, dayOfMonth=9, monthValue=6, nano=919422000, minute=29, second=26}
 D  getAllDeliveries: Found 1 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (1 modern, 0 legacy)
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, year=2025, dayOfYear=160, dayOfMonth=9, monthValue=6, nano=919422000, minute=29, second=26}
 D  getAllDeliveries: Found 1 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (1 modern, 0 legacy)
 D  Delivery validation complete: 1 errors, 0 warnings
 W  Domain Delivery validation failed in mapper for delivery O9cNSdkn1oCrTWuf3t7a: [Delivery details are required]
 D  Delivery validation complete: 1 errors, 0 warnings
 W  Domain Delivery validation failed in mapper for delivery O9cNSdkn1oCrTWuf3t7a: [Delivery details are required]
 D  === DELIVERY MAPPING TRANSFORMATION ===
 D  === DELIVERY MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=O9cNSdkn1oCrTWuf3t7a, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_orderId=65656565, dto_addressId=nbKRDYvJ1hYj72b8JvOj, dto_tipAmount=20.0, dto_status=COMPLETED, dto_notes=empty}
 D  Input DTO State: {dto_id=O9cNSdkn1oCrTWuf3t7a, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_orderId=65656565, dto_addressId=nbKRDYvJ1hYj72b8JvOj, dto_tipAmount=20.0, dto_status=COMPLETED, dto_notes=empty}
 D  Output Domain State: {domain_id=O9cNSdkn1oCrTWuf3t7a, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_orderId=65656565, domain_addressId=nbKRDYvJ1hYj72b8JvOj, domain_tipAmount=20.0, domain_status=COMPLETED, domain_notes=empty}
 D  Field Transformations: [notes: DECRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, timestamps: DATE_PARSING]
 D  Business Logic Applied: [notes_decryption, field_validation, address_plain_mapping, tip_calculation, status_mapping, timestamp_conversion]
 D  PII Fields Processed: 2
 D  Mapping Duration: 2ms
 D  Delivery validation complete: 1 errors, 0 warnings
 W  Domain Delivery validation failed in mapper for delivery O9cNSdkn1oCrTWuf3t7a: [Delivery details are required]
 D  === DELIVERY MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=O9cNSdkn1oCrTWuf3t7a, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_orderId=65656565, dto_addressId=nbKRDYvJ1hYj72b8JvOj, dto_tipAmount=20.0, dto_status=COMPLETED, dto_notes=empty}
 D  Output Domain State: {domain_id=O9cNSdkn1oCrTWuf3t7a, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_orderId=65656565, domain_addressId=nbKRDYvJ1hYj72b8JvOj, domain_tipAmount=20.0, domain_status=COMPLETED, domain_notes=empty}
 D  Cached delivery list: deliveries_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  [DeliveryMapper] toSsot(Delivery) ID:O9cNSdkn1oCrTWuf3t7a User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1039bytes Fields:7 PII:2 Decrypt:2ms Logic:[notes_decryption,field_validation,address_plain_mapping,tip_calculation,status_mapping,timestamp_conversion] Issues:1 Delivery: 2ms [WARN] (1 issues)
 W  SSOT/DTO Alignment Issue in DeliveryMapper for Delivery:
 W     Validation Errors: Domain validation: Delivery details are required
 D  Cached delivery 65656565 with atomic cache system
 D  Field Transformations: [notes: DECRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, timestamps: DATE_PARSING]
 D  Business Logic Applied: [notes_decryption, field_validation, address_plain_mapping, tip_calculation, status_mapping, timestamp_conversion]
 D  PII Fields Processed: 2
 D  Mapping Duration: 2ms
 D  Cached delivery list: deliveries_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  Output Domain State: {domain_id=O9cNSdkn1oCrTWuf3t7a, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_orderId=65656565, domain_addressId=nbKRDYvJ1hYj72b8JvOj, domain_tipAmount=20.0, domain_status=COMPLETED, domain_notes=empty}
 D  Field Transformations: [notes: DECRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, timestamps: DATE_PARSING]
 D  Business Logic Applied: [notes_decryption, field_validation, address_plain_mapping, tip_calculation, status_mapping, timestamp_conversion]
 D  PII Fields Processed: 2
 D  Mapping Duration: 1ms
 D  Cached delivery 65656565 with atomic cache system
 D  Saved 1 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Saved 1 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeGetDeliveriesByUserIdOperation - Fetched and cached 1 SSoT deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [DeliveryMapper] toSsot(Delivery) ID:O9cNSdkn1oCrTWuf3t7a User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1039bytes Fields:7 PII:2 Decrypt:2ms Logic:[notes_decryption,field_validation,address_plain_mapping,tip_calculation,status_mapping,timestamp_conversion] Issues:1 Delivery: 2ms [WARN] (1 issues)
 W  SSOT/DTO Alignment Issue in DeliveryMapper for Delivery:
 W     Validation Errors: Domain validation: Delivery details are required
 D  executeGetDeliveriesByUserIdOperation - Fetched and cached 1 SSoT deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Session PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858: Fetched 1 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  [DeliveryMapper] toSsot(Delivery) ID:O9cNSdkn1oCrTWuf3t7a User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1039bytes Fields:7 PII:2 Decrypt:1ms Logic:[notes_decryption,field_validation,address_plain_mapping,tip_calculation,status_mapping,timestamp_conversion] Issues:1 Delivery: 1ms [WARN] (1 issues)
 W  SSOT/DTO Alignment Issue in DeliveryMapper for Delivery:
 W     Validation Errors: Domain validation: Delivery details are required
 D  Cached delivery list: deliveries_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  Saved 1 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeGetRecentDeliveriesOperation: Cached 1 recent deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeGetRecentDeliveriesOperation: Remote fetch for recent deliveries (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858, user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1, limit: 10, count: 1)
 D  DEDUPLICATION: Cleaned up completed request for key 'getRecentDeliveries_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_10'
 D  DEDUPLICATION: Completed new request 'getRecentDeliveries_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_10' in 293ms, result: true
 I  CACHE WARMING: Deliveries loaded in 294ms
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 266ms, result: true
 D  observeDeliveriesByUserId: Emitting 1 deliveries from cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Cached delivery 65656565 with atomic cache system
 D  Session PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858: Fetched 1 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 269ms, result: true
 D  SDK type: 1, version: 251625202
 D  maps_core_dynamite module version in use (0 represents standalone library): 251625202
 D  Added event: 109
 D  Added event: 112
 D  loadedRenderer: LATEST
 D  preferredRenderer: null
 D  SDK type: 1, version: 251625202
 I  Google Play services package version: 252037038
 I  Google Play services maps renderer version(maps_core): 251625202
 D  SDK type: 1, version: 251625202
 D  about to start loading native library asynchronously
 W  Suppressed StrictMode policy violation: StrictModeDiskReadViolation
 I  Using GMM server: https://clients4.google.com/glm/mmap
 W  Suppressed StrictMode policy violation: StrictModeDiskReadViolation
 W  Suppressed StrictMode policy violation: StrictModeDiskWriteViolation
 W  Suppressed StrictMode policy violation: StrictModeDiskReadViolation
 D  Native geocoding success: 1600 Amphitheatre Parkway, Mountain View, CA -> 37.4207967, -122.0852688
 D  Compat change id reported: 63938206; UID 10209; state: ENABLED
 I  Compiler allocated 4219KB to compile void android.widget.TextView.<init>(android.content.Context, android.util.AttributeSet, int, int)
 I  getExecutor  CREATED  boc@d58b22a[main]
 I  getExecutor  CREATED  ScheduledThreadPoolExecutor@812f791[Scheduler]
 I  getExecutor  CREATED  bof@2a7c6f6[Background]
 I  getExecutor  CREATED  bof@49a44fc[Lite]
 I  marker files=[]
 I  getExecutor  CREATED  bof@d9c5785[Blocking]
 I  getExecutor  CREATED  bof@f342383[TilePrep]
 I  Loaded cached client parameters
 I  finishInitialization ClientParametersManager.
 I  start()  account=Account {name=signedout@, type=com.google.android.apps.maps}  locale=en_US
 I  P/H: Scheduling next update in 0.000000 seconds: initial refresh
 I  P/H: Running update task for account Account {name=signedout@, type=com.google.android.apps.maps} , locale en_US
 I  P/H: Running update task via GWS.
 I  Requested experimentIds= []
 I  Fetching new client parameters: Account {name=signedout@, type=com.google.android.apps.maps}  en_US
 I  getExecutor  CREATED  bof@fac98c2[Network]
 I  Using Paint server URL: https://www.google.com/maps/vt
 I  FpsProfiler MAIN created on main
 I  getExecutor  CREATED  bof@50061e6[Picker]
 I  getExecutor  CREATED  bof@b7499d4[LocFresh]
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 W  No current context - attempting to create off-screen context
 W  using the fallback Cronet Engine implementation. Performance will suffer and many HTTP client features, including caching, will not work.
 I  PaintParametersListeningServerChannelManager got a client parameters update event.
 I  Updating the server URL. Old URL: https://www.google.com/maps/vt New URL: https://www.google.com/maps/vt
 I  Creating a new server channel with URL: https://www.google.com/maps/vt, server channel factory class: class m.brz
 I  Created GlConstants: faa{gpuVendor=Google (NVIDIA Corporation), glVersion=OpenGL ES 3.1 (4.5.0 NVIDIA 571.96), glRenderer=Android Emulator OpenGL ES Translator (NVIDIA GeForce RTX 3060 Ti/PCIe/SSE2), maxTextureSize=32768, maxVertexTextureImageUnits=32, maxVertexUniformVectors=1024, maxSupportedLineWidth=10, maxVertexAttribs=16, nonPowerOfTwoTextureSupport=FULL}
 I  Map using legacy labeler
 I  Create or open database: /storage/emulated/0/Android/data/ /cache/diskcache; secure file path: /data/user/0/ /app_/testdata
 I  Network fetching: false
 I  Current epoch is now 735
 I  requestDrawingConfig for epoch 735 legend ROADMAP
 I  Model SDK_GPHONE64_X86_64, Product name SDK_GPHONE64_X86_64, Board name GOLDFISH_X86_64, Manufacturer GOOGLE
 W  Model is not recognized, and therefore using default settings.
 I  Database cache size: 3719168 bytes
 I  Network fetching: true
 I  styleTableCache inserted: 735 ROADMAP https://www.gstatic.com/maps/res/CompactLegend-Roadmap-822e4ff884c662f5b9d6beea818bbb85
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 I  requestDrawingConfig for epoch 735 legend ROADMAP
 E  Failed to delete expired resources: (Ask Gemini)
                                                 m.eth: m.ayj: Database lock unavailable {canonicalCode=UNAVAILABLE, loggedCode=0, posixErrno=0}
                                                 	at com.google.android.libraries.geo.mapcore.internal.store.diskcache.NativeSqliteDiskCacheImpl.flushWrites(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:10)
                                                 	at m.eto.i(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:29)
                                                 	at m.etl.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:46)
                                                 	at m.gxz.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:68)
                                                 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
                                                 	at m.bni$a.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:23)
                                                 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
                                                 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
                                                 	at m.bnx.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:40)
                                                 	at java.lang.Thread.run(Thread.java:1012)
                                                 Caused by: m.ayj: Database lock unavailable {canonicalCode=UNAVAILABLE, loggedCode=0, posixErrno=0}
                                                 	at com.google.android.libraries.geo.mapcore.internal.store.diskcache.NativeSqliteDiskCacheImpl.nativeSqliteDiskCacheFlushWrites(Native Method)
                                                 	at com.google.android.libraries.geo.mapcore.internal.store.diskcache.NativeSqliteDiskCacheImpl.flushWrites(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:3)
                                                 	at m.eto.i(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:29) 
                                                 	at m.etl.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:46) 
                                                 	at m.gxz.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:68) 
                                                 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                 	at m.bni$a.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:23) 
                                                 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
                                                 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                 	at m.bnx.run(:com.google.android.gms.policy_maps_core_dynamite@251625207@251625202032.748283936.748283936:40) 
                                                 	at java.lang.Thread.run(Thread.java:1012) 
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 W  JNI critical lock held for 27.255ms on Thread[109,tid=6399,Runnable,Thread*=0x758c23b1a510,peer=0x15f9c6d0,"androidmapsapi-TilePrep_1"]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:3 Size:1547bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeUserProfile Load:1763ms Steps:[repository_call,data_mapping,state_emission] UserDelay:1763ms [CACHE_HIT] [SLOW_UX]: 1763ms [OK]
 I  Network fetching: true
 I  Network fetching: true
 I  Network fetching: true
 I  Skipped 78 frames!  The application may be doing too much work on its main thread.
 D  app_time_stats: avg=103.32ms min=2.30ms max=1332.66ms count=17
 D  Task DashboardMapCacheWarming completed in 1788ms
 D  🚀 PRIORITY TASK SUCCESS: Cache warming and maps pre-fetch completed successfully
 D  Deduplication: Original 1 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 I  Davey! duration=1364ms; Flags=0, FrameTimelineVsyncId=8634090, IntendedVsync=135077713334394, Vsync=135077730001060, InputEventId=0, HandleInputStart=135077741176400, AnimationStart=135077741191600, PerformTraversalsStart=135079011619200, DrawStart=135079018618200, FrameDeadline=135077763334392, FrameInterval=135077741165100, FrameStartTime=16666666, SyncQueued=135079037499800, SyncStart=135079037638500, IssueDrawCommandsStart=135079037766900, SwapBuffers=135079073757200, FrameCompleted=135079077982200, DequeueBufferDuration=16900, QueueBufferDuration=292300, GpuCompleted=135079077982200, SwapBuffersCompleted=135079075169600, DisplayPresentTime=458529729374979675, CommandSubmissionCompleted=135079073757200, 
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  observeCurrentUser: Authentication ready for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, starting observation (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  observeUserById: Starting observation for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  observeUserById: Triggering cache refresh for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Scheduling task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 with priority CRITICAL
 D  Executing task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserById: Starting operation for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 for current user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: true
 D  getUserById: Found user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 in local cache (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  Task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 completed in 4ms
 I  Davey! duration=1342ms; Flags=0, FrameTimelineVsyncId=8634134, IntendedVsync=135077746667726, Vsync=135079046667674, InputEventId=0, HandleInputStart=135079058816100, AnimationStart=135079058832500, PerformTraversalsStart=135079064927900, DrawStart=135079065006600, FrameDeadline=135079096667672, FrameInterval=135079057854400, FrameStartTime=16666666, SyncQueued=135079067383900, SyncStart=135079075341800, IssueDrawCommandsStart=135079075426800, SwapBuffers=135079077322700, FrameCompleted=135079097238700, DequeueBufferDuration=5110700, QueueBufferDuration=179700, GpuCompleted=135079097238700, SwapBuffersCompleted=135079083305500, DisplayPresentTime=316385287049381564, CommandSubmissionCompleted=135079077322700, 
 I  Starting enhanced cache warming for user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [data.UserRepository] getUserById(User) ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1547bytes Source:cache Strategy:cache-first [CACHE_HIT]: 1ms [OK]
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  getUserSubscription: Verifying authentication for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserSubscription: Managing session for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Found existing request for key 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  getUserSubscription: Using session PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  getUserSubscription: Getting subscription for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserSubscription: Successfully retrieved subscription for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  getAddresses (SSoT) - Fetching all addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:0ms Hit:true ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 0ms [OK]
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  Scheduling task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 with priority CRITICAL
 D  Executing task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserById: Starting operation for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  DEDUPLICATION: Completed new request 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 25ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription' in 40ms, result: true
 D  Subscription cache warming successful in 41ms
 D  Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 for current user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: true
 D  getUserById: Found user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 in local cache (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  Task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 completed in 21ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 42ms, result: true
 D  User profile cache warming successful in 43ms
 D  [cache_system.UserRepository] cache_breakdown Check:19ms Hit:true ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 19ms [OK]
 D  [data.UserRepository] getUserById(User) ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1547bytes Source:cache Strategy:cache-first [CACHE_HIT]: 20ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=nbKRDYvJ1hYj72b8JvOj, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, dto_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=nbKRDYvJ1hYj72b8JvOj, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, domain_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  [AddressMapper] toSsot(Address) ID:nbKRDYvJ1hYj72b8JvOj User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1210bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 149ms, result: true
 D  Addresses cache warming successful in 150ms
 I  Cache warming completed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: user_profile:success:43ms, subscription:success:41ms, addresses:success:150ms, total:168ms
 D  observeCurrentUser: Cache warming initiated for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 W  observeCurrentUser: Authentication not available: Flow was aborted, no more elements needed (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:1087bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1996ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1996ms [CACHE_HIT] [SLOW_UX]: 1996ms [OK]
 D  🚀 BACKGROUND REFRESH: Skipping update, data already loaded
 D  [presentation.SettingsViewModel] loadUserProfile Deps:3 Repos:[UserRepository] Data:User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Name:gay Pro:true State:Success [STATEFLOW_UPDATED]: 1935ms [OK]
 W  SLOW ViewModel Operation: SettingsViewModel.loadUserProfile took 1935ms (threshold: 30ms)
 W     Repositories involved: UserRepository
 D  Profile state updated: Success(data=User(id=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, email=QUdUMDE6Z8+69hKACqwxv0uRh/uCKuQiK3Uc5YKFuhrp34cb/wXFoKLd9uLH14u8MAFh59jf, displayName=gay, defaultAddressId=null, photoUrl=null, authProviders=[], accountStatus=active, timezone=null, createdAt=2025-06-09T04:29:07.103564-05:00, lastLoginAt=2025-06-09T04:29:07.103564-05:00, privacyPolicyAccepted=null, termsAccepted=null, version=2, subscription=UserSubscription(status=free, level=free, isActive=true, startDate=2025-06-09T04:29:07.103630-05:00, expiryDate=null, isLifetime=false, provider=null, orderId=null, verification=null), preferences=UserPreferences(notificationsEnabled=true, theme=system, useLocation=true, dnd=null), permissions=UserPermissions(bypassLimits=false, maxUploads=50), usage=UserUsage(mappingCount=null, deliveryCount=1, addressCount=1, dndMarkingsUsed=null, maxDndMarkings=null, autoCapturedOrders=null, lastUsageUpdate=2025-06-09T04:29:28.093340-05:00), syncInfo=null, appSettings=UserAppSettings(dataCollectionOptIn=false, lastVersion=, onboardingCompleted=false), communication=UserCommunication(emailOptIn=false, marketingOptIn=false, pushNotificationsEnabled=true), usageStats=UserUsageStats(deliveryCount=1, addressCount=1, lastUsageDate=2025-06-09T04:29:28.093340-05:00, totalRuns=0, activeDaysCount=0, totalTips=20.0, featureUsage=null), metadata=Metadata(createdAt=2025-06-09T04:29:07.103564-05:00, updatedAt=2025-06-09T09:29:31.844Z, importedAt=null, source=app_creation, importId=null, captureId=null, version=1, customData=null)))
 W  (25.1.4) [WriteStream]: (56420bb) Stream closed with status: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}.
 D  🏠 HOME POSITION SET: lat/lng: (41.65067,-93.7411922) at zoom 15.0
 W  (25.1.4) [Firestore]: Write failed at system_config/notification_patterns_default: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
 E  Error saving Notification_patterns ('notification_patterns_default') (Ask Gemini)
                                                 com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
                                                 	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:155)
                                                 	at com.google.firebase.firestore.core.SyncEngine.notifyUser(SyncEngine.java:629)
                                                 	at com.google.firebase.firestore.core.SyncEngine.handleRejectedWrite(SyncEngine.java:511)
                                                 	at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedWrite(MemoryComponentProvider.java:135)
                                                 	at com.google.firebase.firestore.remote.RemoteStore.handleWriteError(RemoteStore.java:748)
                                                 	at com.google.firebase.firestore.remote.RemoteStore.handleWriteStreamClose(RemoteStore.java:704)
                                                 	at com.google.firebase.firestore.remote.RemoteStore.access$600(RemoteStore.java:60)
                                                 	at com.google.firebase.firestore.remote.RemoteStore$2.onClose(RemoteStore.java:218)
                                                 	at com.google.firebase.firestore.remote.AbstractStream.close(AbstractStream.java:365)
                                                 	at com.google.firebase.firestore.remote.AbstractStream.handleServerClose(AbstractStream.java:419)
                                                 	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onClose$3$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:160)
                                                 	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda3.run(D8$$SyntheticClass:0)
                                                 	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
                                                 	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onClose(AbstractStream.java:146)
                                                 	at com.google.firebase.firestore.remote.FirestoreChannel$1.onClose(FirestoreChannel.java:173)
                                                 	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574)
                                                 	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72)
                                                 	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742)
                                                 	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723)
                                                 	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
                                                 	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
                                                 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
                                                 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                 	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
                                                 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
                                                 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
                                                 	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
                                                 	at java.lang.Thread.run(Thread.java:1012)
                                                 Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
                                                 	at io.grpc.Status.asException(Status.java:541)
                                                 	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:153)
                                                 	at com.google.firebase.firestore.core.SyncEngine.notifyUser(SyncEngine.java:629) 
                                                 	at com.google.firebase.firestore.core.SyncEngine.handleRejectedWrite(SyncEngine.java:511) 
                                                 	at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedWrite(MemoryComponentProvider.java:135) 
                                                 	at com.google.firebase.firestore.remote.RemoteStore.handleWriteError(RemoteStore.java:748) 
                                                 	at com.google.firebase.firestore.remote.RemoteStore.handleWriteStreamClose(RemoteStore.java:704) 
                                                 	at com.google.firebase.firestore.remote.RemoteStore.access$600(RemoteStore.java:60) 
                                                 	at com.google.firebase.firestore.remote.RemoteStore$2.onClose(RemoteStore.java:218) 
                                                 	at com.google.firebase.firestore.remote.AbstractStream.close(AbstractStream.java:365) 
                                                 	at com.google.firebase.firestore.remote.AbstractStream.handleServerClose(AbstractStream.java:419) 
                                                 	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onClose$3$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:160) 
                                                 	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda3.run(D8$$SyntheticClass:0) 
                                                 	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
                                                 	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onClose(AbstractStream.java:146) 
                                                 	at com.google.firebase.firestore.remote.FirestoreChannel$1.onClose(FirestoreChannel.java:173) 
                                                 	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574) 
                                                 	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72) 
                                                 	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742) 
                                                 	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723) 
                                                 	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
                                                 	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
                                                 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                 	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                 	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
                                                 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
                                                 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                 	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                 	at java.lang.Thread.run(Thread.java:1012) 
 D  Cached configuration: notification_patterns (domain: app_config, version: 1)
 D  Saved NotificationPatterns to cache
 D  ✅ MODERN: Notification patterns loaded/updated
 I  Background concurrent mark compact GC freed 24MB AllocSpace bytes, 114(5616KB) LOS objects, 49% free, 19MB/39MB, paused 2.830ms,2.602ms total 138.337ms
 D  tagSocket(168) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(174) with statsTag=0x99e6744e, statsUid=-1
 D  tagSocket(164) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(211) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(161) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(159) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(215) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(214) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(172) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(175) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(171) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(5) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(205) with statsTag=0xffffffff, statsUid=-1
 D  🏠 HOME POSITION: Stored position lat/lng: (41.65067,-93.7411922) at zoom 15.0
 D  tagSocket(126) with statsTag=0xffffffff, statsUid=-1
 I  Fetch new client parameters: Success!
 I  Got a successful parameters response!
 I  P/H: Scheduling next update in 21600.000000 seconds: refresh
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 I  Scheduling save of client parameters.
 I  Save client parameters to cache at SavedClientParameters.data for account null and Locale en_US
 I  Compiler allocated 5627KB to compile void m.eph.o()
 I  Compiler allocated 4569KB to compile void m.ecn.c(m.djs, m.ebf, m.edi, m.ekf, boolean)
 I  Client parameters saved.
 D  tagSocket(98) with statsTag=0xffffffff, statsUid=-1
 D  app_time_stats: avg=14.36ms min=1.87ms max=186.76ms count=44
 I  Compiler allocated 4278KB to compile m.elu m.ely.l(m.azn, m.hzf, m.elm, byte[], boolean, m.cdf, m.dev, java.lang.Iterable)
 W  Local module descriptor class for com.google.android.gms.googlecertificates not found.
 I  Considering local module com.google.android.gms.googlecertificates:0 and remote module com.google.android.gms.googlecertificates:7
 I  Selected remote version of com.google.android.gms.googlecertificates, version >= 7
 W  ClassLoaderContext classpath element checksum mismatch. expected=**********, found=********** (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar**********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]} | DLC[];PCL[/data/app/~~Up1dII4jRpGbugcMnYMmcg==/ -Sy1XG925L-oH2f9rXWU3qg==/base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar**********]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]})
 D  app_time_stats: avg=34.39ms min=5.81ms max=158.82ms count=30
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 I  P/H: Scheduling next update in 0.000000 seconds: forced update from androidmapsapi-TilePrep_1 thread
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 I  P/H: Running update task for account Account {name=signedout@, type=com.google.android.apps.maps} , locale en_US
 I  P/H: Running update task via GWS.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 I  Requested experimentIds= []
 I  Fetching new client parameters: Account {name=signedout@, type=com.google.android.apps.maps}  en_US
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 D  tagSocket(163) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(174) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(216) with statsTag=0xffffffff, statsUid=-1
 I  Compiler allocated 5041KB to compile void m.eho.W(m.ely, m.fdw, android.content.res.Resources, m.ejt, m.eum, boolean, m.dje, java.util.Map, boolean, boolean, boolean, boolean)
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 I  Initial labeling completed.
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 W  Could not find url for epoch 736
 W  urls for epoch 736 not available.
 W  Missing style tables for epoch 736 and legend ROADMAP. currentEpoch 735
 I  Fetch new client parameters: Success!
 I  Got a successful parameters response!
 I  P/H: Scheduling next update in 21600.000000 seconds: refresh
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 I  Scheduling save of client parameters.
 I  Save client parameters to cache at SavedClientParameters.data for account null and Locale en_US
 D  🚀 MAP READY: Maps Compose initialized and ready
 D  🚀 MAP LOADED: 1 markers ready
 I  Client parameters saved.
 I  Background concurrent mark compact GC freed 15MB AllocSpace bytes, 150(10MB) LOS objects, 48% free, 25MB/49MB, paused 1.621ms,9.615ms total 114.458ms


gratuity                 D  app_time_stats: avg=662.09ms min=12.13ms max=9976.28ms count=16
 W  Destroying egl surface
 W  OnBackInvokedCallback is not enabled for the application.
                                                 Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  app_time_stats: avg=50.78ms min=2.81ms max=539.51ms count=19
 D  ✅ MODERN AddEditDeliveryViewModel initialized
 D  [presentation.AddEditDeliveryViewModel] init Deps:4 Data:new_delivery_created State:Initialized [STATEFLOW_UPDATED]: 0ms [OK]
 D  tagSocket(162) with statsTag=0xffffffff, statsUid=-1
 I  Compiler allocated 8032KB to compile void  .ui.delivery.compose.AddEditDeliveryScreenKt$AddEditDeliveryScreen$11.invoke(androidx.compose.foundation.layout.PaddingValues, androidx.compose.runtime.Composer, int)
 I  Compiler allocated 8000KB to compile void  .ui.delivery.compose.AddEditDeliveryScreenKt$AddEditDeliveryScreen$11.invoke(androidx.compose.foundation.layout.PaddingValues, androidx.compose.runtime.Composer, int)
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  LaunchedEffect triggered but selectedPlace is null
 D  tagSocket(127) with statsTag=0xffffffff, statsUid=-1
 I  Skipped 40 frames!  The application may be doing too much work on its main thread.
 D  getAddresses (SSoT) - Fetching all addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [reactive_performance.AddEditDeliveryViewModel] stateflow_emission Flow:delivery Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 I  Starting enhanced cache warming for user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Starting new request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  getAddresses (SSoT) - Fetching all addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Scheduling task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 with priority CRITICAL
 D  getUserSubscription: Verifying authentication for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserSubscription: Managing session for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Found existing request for key 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  getUserSubscription: Using session PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858
 D  Executing task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserById: Starting operation for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 for current user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: true
 D  getUserById: Found user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 in local cache (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 0ms [OK]
 D  [data.UserRepository] getUserById(User) ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1547bytes Source:cache Strategy:cache-first [CACHE_HIT]: 1ms [OK]
 D  DEDUPLICATION: Starting new request for key 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  getUserSubscription: Getting subscription for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserSubscription: Successfully retrieved subscription for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Cleaned up completed request for key 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  [cache_system.SubscriptionRepository] cache_breakdown Check:0ms Hit:true ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 1ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription' in 9ms, result: true
 D  Subscription cache warming successful in 10ms
 D  Task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 completed in 3ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 10ms, result: true
 D  User profile cache warming successful in 11ms
 D  For capability in capabilities, log:
                                                 "AdvancedMarkers: false: Capabilities unavailable without a Map ID."Data-driven styling: false
 W  Destroying egl context
 W  Shutting down renderer while it's not idle - phase is INVALID
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=nbKRDYvJ1hYj72b8JvOj, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, dto_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), dto_tags_count=0, dto_isDefault=null}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 I  Trimming OTHER: VertexBuilders's current size 1 to 0.00000, or 0
 D  Input DTO State: {dto_id=nbKRDYvJ1hYj72b8JvOj, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, dto_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=nbKRDYvJ1hYj72b8JvOj, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, domain_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), domain_tags_count=0, domain_isDefault=null}
 D  Output Domain State: {domain_id=nbKRDYvJ1hYj72b8JvOj, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, domain_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 I  Trimming OTHER: NativeTessellators's current size 1 to 0.00000, or 0
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 I  Trimming POINTS_LABELS's current size 3 to 0.00000, or 0
 D  Cached address list: addresses_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:nbKRDYvJ1hYj72b8JvOj User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1210bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 I  Trimming LINE_LABELS's current size 51 to 0.00000, or 0
 D  Cached address list: addresses_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 I  Trimming OTHER: LoggingOp's current size 25 to 0.00000, or 0
 I  Trimming OTHER: LabelSourceOp's current size 2 to 0.00000, or 0
 D  DEDUPLICATION: Completed new request 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 145ms, result: true
 D  Addresses cache warming successful in 147ms
 I  Cache warming completed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: user_profile:success:11ms, subscription:success:10ms, addresses:success:147ms, total:150ms
 D  [AddressMapper] toSsot(Address) ID:nbKRDYvJ1hYj72b8JvOj User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1210bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Starting new request for key 'getDelivery_O9cNSdkn1oCrTWuf3t7a'
 D  Retrieved delivery O9cNSdkn1oCrTWuf3t7a for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: false
 D  getDeliveryById: Delivery O9cNSdkn1oCrTWuf3t7a not in cache or local fetch failed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  getDeliveryById: Fetching delivery O9cNSdkn1oCrTWuf3t7a for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 from path: users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_deliveries/O9cNSdkn1oCrTWuf3t7a
 D  Loaded 1 addresses
 D  app_time_stats: avg=20.13ms min=8.09ms max=175.95ms count=50
 D  app_time_stats: avg=16.58ms min=14.06ms max=19.59ms count=61
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, hour=9, dayOfMonth=9, dayOfYear=160, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, dayOfYear=160, dayOfMonth=9, year=2025, monthValue=6, nano=497221000, second=26, minute=29}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, dayOfYear=160, year=2025, dayOfMonth=9, monthValue=6, nano=919314000, minute=29, second=26}
 W  HashMap does not contain valid Firestore Timestamp data: {dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, year=2025, dayOfYear=160, dayOfMonth=9, monthValue=6, nano=919422000, minute=29, second=26}
 D  getDeliveryById: Successfully fetched delivery for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D    Firestore Path: users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_deliveries/O9cNSdkn1oCrTWuf3t7a
 D    Document Size: 1856 bytes
 D    Firestore Duration: 1382ms
 D    Raw Document Data: {deliveryData={reference={orderId=65656565, externalId=null, platformOrderId=null, addressId=nbKRDYvJ1hYj72b8JvOj}, metadata={createdAt={dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, dayOfYear=160, year=2025, dayOfMonth=9, monthValue=6, nano=919314000, minute=29, second=26}, importedAt=null, importId=null, customData=null, captureId=null, source=manual, version=1, updatedAt={dayOfWeek=MONDAY, month=JUNE, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, year=2025, dayOfYear=160, dayOfMonth=9, monthValue=6, nano=919422000, minute=29, second=26}}, times={completedAt={dayOfWeek=MONDAY, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, hour=9, dayOfMonth=9, dayOfYear=160, year=2025, monthValue=6, nano=497221000, second=26, minute=29}, tippedAt={dayOfWeek=MONDAY, hour=9, offset={totalSeconds=0, rules={fixedOffset=true, transitionRules=[], transitions=[]}, id=Z}, month=JUNE, dayOfYear=160, dayOfMonth=9, year=2025, monthValue=6, nano=497221000, second=26, minute=29}, acceptedAt=null}, address={latitude=41.65067, placeId=ChIJRdJF0oed7ocR0sUwohNOHXY, fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, id=nbKRDYvJ1hYj72b8JvOj, longitude=-93.7411922}, notes=null, amounts={totalAmount=20.0, tipAmount=20.0, distanceMiles=null, finalPay=20.0, estimatedPay=null, basePay=0.0, tipPercentage=null, currencyCode=USD}, orderId=65656565, userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, items=null, platform={displayName=null, name=Shipt, iconUrl=null, source=null, type=null, version=null}, status={cancellationReason=null, dndReason=null, verified=false, tipped=true, doNotDeliver=false, completed=true, state=COMPLETED, verificationSource=null, verificationTimestamp=null}}}
 D    Parsed DeliveryDto: Delivery(id=O9cNSdkn1oCrTWuf3t7a, deliveryData=DeliveryData(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, orderId=65656565, notes=null, address=SimpleAddress(id=nbKRDYvJ1hYj72b8JvOj, fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, latitude=41.65067, longitude=-93.7411922, placeId=ChIJRdJF0oed7ocR0sUwohNOHXY), status=Status(state=COMPLETED, isTipped=false, isCompleted=null, isVerified=null, cancellationReason=null, verificationSource=null, verificationTimestamp=null, doNotDeliver=false, dndReason=null), times=Times(acceptedAt=null, completedAt=null, tippedAt=null), amounts=Amounts(basePay=0.0, tipAmount=20.0, tipPercentage=null, totalAmount=20.0, currencyCode=USD, estimatedPay=null, finalPay=20.0, distanceMiles=null), reference=Reference(addressId=nbKRDYvJ1hYj72b8JvOj, orderId=65656565, externalId=null, platformOrderId=null), platform=Platform(name=Shipt, type=null, version=null, source=null, displayName=null, iconUrl=null), items=null, metadata=Metadata(createdAt=null, updatedAt=null, importedAt=null, source=manual, importId=null, captureId=null, version=1, customData=null)))
 D  [Firestore] READ GET_DOCUMENT:users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_deliveries/O9cNSdkn1oCrTWuf3t7a User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Results:1 Size:1856bytes Source:SERVER Filters:[delivery_by_id]: 1382ms [OK]
 I  FIRESTORE READ: GET_DOCUMENT:users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_deliveries/O9cNSdkn1oCrTWuf3t7a User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Results:1 Size:1856bytes Source:SERVER Filters:[delivery_by_id] (1382ms)
 D  Delivery validation complete: 1 errors, 0 warnings
 W  SLOW FIRESTORE READ: users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_deliveries/O9cNSdkn1oCrTWuf3t7a took 1382ms
 W  Domain Delivery validation failed in mapper for delivery O9cNSdkn1oCrTWuf3t7a: [Delivery details are required]
 D  === DELIVERY MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=O9cNSdkn1oCrTWuf3t7a, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_orderId=65656565, dto_addressId=nbKRDYvJ1hYj72b8JvOj, dto_tipAmount=20.0, dto_status=COMPLETED, dto_notes=empty}
 D  Output Domain State: {domain_id=O9cNSdkn1oCrTWuf3t7a, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_orderId=65656565, domain_addressId=nbKRDYvJ1hYj72b8JvOj, domain_tipAmount=20.0, domain_status=COMPLETED, domain_notes=empty}
 D  Field Transformations: [notes: DECRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, timestamps: DATE_PARSING]
 D  Business Logic Applied: [notes_decryption, field_validation, address_plain_mapping, tip_calculation, status_mapping, timestamp_conversion]
 D  PII Fields Processed: 2
 D  Mapping Duration: 1ms
 D  Saved delivery O9cNSdkn1oCrTWuf3t7a for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getDeliveryById: Fetched delivery O9cNSdkn1oCrTWuf3t7a from remote and updated cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getDeliveryById: Remote fetch for delivery O9cNSdkn1oCrTWuf3t7a (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDelivery_O9cNSdkn1oCrTWuf3t7a'
 D  DEDUPLICATION: Completed new request 'getDelivery_O9cNSdkn1oCrTWuf3t7a' in 1408ms, result: true
 D  [DeliveryMapper] toSsot(Delivery) ID:O9cNSdkn1oCrTWuf3t7a User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1039bytes Fields:7 PII:2 Decrypt:1ms Logic:[notes_decryption,field_validation,address_plain_mapping,tip_calculation,status_mapping,timestamp_conversion] Issues:1 Delivery: 1ms [WARN] (1 issues)
 D  [data.DeliveryRepository] getDeliveryById(Delivery) ID:O9cNSdkn1oCrTWuf3t7a User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1087bytes Source:remote Strategy:cache-first [CACHE_MISS]: 1557ms [OK]
 W  SSOT/DTO Alignment Issue in DeliveryMapper for Delivery:
 W     Validation Errors: Domain validation: Delivery details are required
 D  observeDeliveriesByUserId: Emitting 2 deliveries from cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Cached delivery O9cNSdkn1oCrTWuf3t7a with atomic cache system
 D  Updated selected place from loaded delivery
 D  LaunchedEffect triggered with selectedPlace: 8811 Plum Dr, Urbandale, IA 50322, USA
 D  Address text updated to: 8811 Plum Dr, Urbandale, IA 50322, USA
 D  Deduplication: Original 2 deliveries, deduplicated to 1
 V  ThrottleFirst: emitted value
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:1087bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:16566ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:16566ms [CACHE_HIT] [POOR_UX]: 16566ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 16566ms (user-visible)
 D  app_time_stats: avg=277.35ms min=2.83ms max=5057.25ms count=19
 D  app_time_stats: avg=69.29ms min=1.54ms max=2685.03ms count=41
 D  Navigating to destination: deliveries
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 I  Compiler allocated 7814KB to compile void  .ui.delivery.compose.DeliveriesScreenKt.DeliveriesScreen(java.lang.String,  .ui.delivery.DeliveryViewModel, androidx.compose.ui.Modifier, kotlin.jvm.functions.Function0, kotlin.jvm.functions.Function1, kotlin.jvm.functions.Function1, androidx.compose.runtime.Composer, int, int)
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 I  Starting enhanced cache warming for user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  getUserSubscription: Verifying authentication for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserSubscription: Managing session for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Found existing request for key 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  getUserSubscription: Using session PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858
 D  getAddresses (SSoT) - Fetching all addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription' in 3ms, result: true
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  Scheduling task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 with priority CRITICAL
 D  Subscription cache warming successful in 5ms
 D  Executing task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (CRITICAL)
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserById: Starting operation for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 for current user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: true
 D  getUserById: Found user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 in local cache (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  Task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 completed in 7ms
 D  [cache_system.UserRepository] cache_breakdown Check:1ms Hit:true ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 1ms [OK]
 D  [data.UserRepository] getUserById(User) ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1547bytes Source:cache Strategy:cache-first [CACHE_HIT]: 5ms [OK]
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 15ms, result: true
 D  User profile cache warming successful in 16ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=nbKRDYvJ1hYj72b8JvOj, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, dto_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=nbKRDYvJ1hYj72b8JvOj, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, domain_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [AddressMapper] toSsot(Address) ID:nbKRDYvJ1hYj72b8JvOj User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1210bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Completed new request 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 111ms, result: true
 D  Addresses cache warming successful in 111ms
 I  Cache warming completed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: user_profile:success:16ms, subscription:success:5ms, addresses:success:111ms, total:114ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved 2 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeGetDeliveriesByUserIdOperation - Found 2 deliveries in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858, count: 2)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 1ms, result: true
 D  ✅ Deliveries loaded successfully: 1 (deduplicated from 2)
 D  app_time_stats: avg=27.84ms min=1.85ms max=682.42ms count=53
 D  ✅ MODERN DeliveryDialogViewModel initialized with domain models
 W  OnBackInvokedCallback is not enabled for the application.
                                                 Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  User session ready - initializing
 D  User session ready - dialog operations available
 D  Loading delivery details...
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 I  Starting enhanced cache warming for user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  getUserSubscription: Verifying authentication for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserSubscription: Managing session for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Found existing request for key 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  getAddresses (SSoT) - Fetching all addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserSubscription: Using session PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1', waiting for result
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription' in 2ms, result: true
 D  Subscription cache warming successful in 2ms
 D  Scheduling task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 with priority CRITICAL
 D  Executing task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserById: Starting operation for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 for current user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: true
 D  getUserById: Found user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 in local cache (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 0ms [OK]
 D  Task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 completed in 7ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  [data.UserRepository] getUserById(User) ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1547bytes Source:cache Strategy:cache-first [CACHE_HIT]: 6ms [OK]
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 17ms, result: true
 D  User profile cache warming successful in 19ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=nbKRDYvJ1hYj72b8JvOj, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, dto_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=nbKRDYvJ1hYj72b8JvOj, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, domain_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [AddressMapper] toSsot(Address) ID:nbKRDYvJ1hYj72b8JvOj User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1210bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Completed new request 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 97ms, result: true
 D  Addresses cache warming successful in 98ms
 I  Cache warming completed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: user_profile:success:19ms, subscription:success:2ms, addresses:success:98ms, total:99ms
 D  DEDUPLICATION: Starting new request for key 'getDelivery_O9cNSdkn1oCrTWuf3t7a'
 D  Cache hit for delivery O9cNSdkn1oCrTWuf3t7a
 D  Retrieved delivery O9cNSdkn1oCrTWuf3t7a for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: true
 D  getDeliveryById: Found delivery O9cNSdkn1oCrTWuf3t7a in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getDeliveryById: Cache hit for delivery O9cNSdkn1oCrTWuf3t7a (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDelivery_O9cNSdkn1oCrTWuf3t7a'
 D  DEDUPLICATION: Completed new request 'getDelivery_O9cNSdkn1oCrTWuf3t7a' in 1ms, result: true
 D  Delivery loaded successfully: O9cNSdkn1oCrTWuf3t7a
 D  [data.DeliveryRepository] getDeliveryById(Delivery) ID:O9cNSdkn1oCrTWuf3t7a User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1085bytes Source:cache Strategy:cache-first [CACHE_HIT]: 102ms [OK]
 D  Delivery details loaded successfully
 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@525ee84
 D  app_time_stats: avg=477.76ms min=1.66ms max=19117.64ms count=42
 D  app_time_stats: avg=14.90ms min=1.54ms max=269.62ms count=53
 D  ✅ MODERN AddEditDeliveryViewModel initialized
 D  [presentation.AddEditDeliveryViewModel] init Deps:4 Data:new_delivery_created State:Initialized [STATEFLOW_UPDATED]: 0ms [OK]
 D  tagSocket(100) with statsTag=0xffffffff, statsUid=-1
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  LaunchedEffect triggered but selectedPlace is null
 D  tagSocket(173) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Found 1 addresses in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=nbKRDYvJ1hYj72b8JvOj, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, dto_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=nbKRDYvJ1hYj72b8JvOj, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, domain_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 1 items
 D  getAddresses (SSoT) - Fetched and cached 1 SSoT addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [AddressMapper] toSsot(Address) ID:nbKRDYvJ1hYj72b8JvOj User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1210bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Loaded 1 addresses
 D  show(ime(), fromIme=false)
 I   :9782291b: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{16b1cfa VFED..... .F....ID 0,0-1080,2400 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
 D  app_time_stats: avg=32.76ms min=12.16ms max=824.84ms count=51
 I  Flattened final assist data: 900 bytes, containing 1 windows, 6 views
 I   :40e78e24: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{16b1cfa VFED..... .F...... 0,0-1080,2400 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
 D  show(ime(), fromIme=true)
 W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package= 
 D  show(ime(), fromIme=true)
 I   :40e78e24: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I   :9782291b: onShown
 D  validateOrderIdInput: input='2' -> filtered='2'
 D  getOrderIdErrorMessage: input='2', error='Order ID must be exactly 9 digits (currently 1)'
 D  app_time_stats: avg=17.75ms min=1.48ms max=234.22ms count=38
 D  validateOrderIdInput: input='22' -> filtered='22'
 D  getOrderIdErrorMessage: input='22', error='Order ID must be exactly 9 digits (currently 2)'
 D  validateOrderIdInput: input='225' -> filtered='225'
 D  getOrderIdErrorMessage: input='225', error='Order ID must be exactly 9 digits (currently 3)'
 D  validateOrderIdInput: input='2252' -> filtered='2252'
 D  getOrderIdErrorMessage: input='2252', error='Order ID must be exactly 9 digits (currently 4)'
 D  validateOrderIdInput: input='22525' -> filtered='22525'
 D  getOrderIdErrorMessage: input='22525', error='Order ID must be exactly 9 digits (currently 5)'
 D  validateOrderIdInput: input='225252' -> filtered='225252'
 D  getOrderIdErrorMessage: input='225252', error='Order ID must be exactly 9 digits (currently 6)'
 D  validateOrderIdInput: input='2252525' -> filtered='2252525'
 D  getOrderIdErrorMessage: input='2252525', error='Order ID must be exactly 9 digits (currently 7)'
 D  app_time_stats: avg=81.86ms min=1.75ms max=425.36ms count=15
 D  validateOrderIdInput: input='22525258' -> filtered='22525258'
 D  getOrderIdErrorMessage: input='22525258', error='Order ID must be exactly 9 digits (currently 8)'
 D  validateOrderIdInput: input='225252588' -> filtered='225252588'
 D  getOrderIdErrorMessage: input='225252588', error='null'
 D  app_time_stats: avg=236.61ms min=7.23ms max=499.15ms count=5
 D  app_time_stats: avg=77.36ms min=8.28ms max=499.90ms count=13
 D  show(ime(), fromIme=false)
 I   :8cad7224: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I   :8cad7224: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I   :a98fc86b: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{16b1cfa VFED..... .F....ID 0,0-1080,2400 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
 D  show(ime(), fromIme=true)
 I   :6a97c387: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I   :a98fc86b: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=69.72ms min=3.55ms max=405.76ms count=15
 D  🔍 GOOGLE_PLACES_AUTOCOMPLETE_START: Starting autocomplete search for query='665...'
 D  tagSocket(101) with statsTag=0xffffffff, statsUid=-1
 E  ❌ Failed to get App Check token for Places SDK: Error returned from API. code: 403 body: App attestation failed. (Ask Gemini)
                                                 com.google.firebase.FirebaseException: Error returned from API. code: 403 body: App attestation failed.
                                                 	at com.google.firebase.appcheck.internal.NetworkClient.makeNetworkRequest(NetworkClient.java:190)
                                                 	at com.google.firebase.appcheck.internal.NetworkClient.exchangeAttestationForAppCheckToken(NetworkClient.java:122)
                                                 	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider.lambda$getToken$1$com-google-firebase-appcheck-debug-internal-DebugAppCheckProvider(DebugAppCheckProvider.java:121)
                                                 	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider$$ExternalSyntheticLambda2.call(D8$$SyntheticClass:0)
                                                 	at com.google.android.gms.tasks.zzz.run(com.google.android.gms:play-services-tasks@@18.1.0:1)
                                                 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
                                                 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
                                                 	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
                                                 	at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
                                                 	at java.lang.Thread.run(Thread.java:1012)
 D  🔍 GOOGLE_PLACES_AUTOCOMPLETE_START: Starting autocomplete search for query='665 ...'
 E  ❌ Failed to get App Check token for Places SDK: Too many attempts. (Ask Gemini)
                                                 com.google.firebase.FirebaseException: Too many attempts.
                                                 	at com.google.firebase.appcheck.internal.NetworkClient.exchangeAttestationForAppCheckToken(NetworkClient.java:118)
                                                 	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider.lambda$getToken$1$com-google-firebase-appcheck-debug-internal-DebugAppCheckProvider(DebugAppCheckProvider.java:121)
                                                 	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider$$ExternalSyntheticLambda2.call(D8$$SyntheticClass:0)
                                                 	at com.google.android.gms.tasks.zzz.run(com.google.android.gms:play-services-tasks@@18.1.0:1)
                                                 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
                                                 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
                                                 	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
                                                 	at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
                                                 	at java.lang.Thread.run(Thread.java:1012)
 D  tagSocket(174) with statsTag=0xffffffff, statsUid=-1
 D  ✅ GOOGLE_PLACES_AUTOCOMPLETE_SUCCESS: Found 5 predictions for query='665 ...'
 D  ✅ GOOGLE_PLACES_AUTOCOMPLETE_SUCCESS: Found 5 predictions for query='665...'
 D  app_time_stats: avg=112.15ms min=10.48ms max=321.69ms count=11
 W  OnBackInvokedCallback is not enabled for the application.
                                                 Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
 D  app_time_stats: avg=161.54ms min=1.31ms max=1569.14ms count=10
 D  Fetching place details for: 6657 Compton Court, Johnston, IA, USA
 D  🔍 GOOGLE_PLACES_DETAILS_START: Fetching place details for placeId='ChIJYxA3Q16d7ocR46uT2HsIS8w', prediction='6657 Compton Court, Johnston, IA, USA'
 D  🔍 GOOGLE_PLACES_DETAILS_REQUEST: Created request with fields=ID, DISPLAY_NAME, FORMATTED_ADDRESS, ADDRESS_COMPONENTS, LOCATION, sessionToken present
 E  ❌ Failed to get App Check token for Places SDK: Too many attempts. (Ask Gemini)
                                                 com.google.firebase.FirebaseException: Too many attempts.
                                                 	at com.google.firebase.appcheck.internal.NetworkClient.exchangeAttestationForAppCheckToken(NetworkClient.java:118)
                                                 	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider.lambda$getToken$1$com-google-firebase-appcheck-debug-internal-DebugAppCheckProvider(DebugAppCheckProvider.java:121)
                                                 	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider$$ExternalSyntheticLambda2.call(D8$$SyntheticClass:0)
                                                 	at com.google.android.gms.tasks.zzz.run(com.google.android.gms:play-services-tasks@@18.1.0:1)
                                                 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
                                                 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
                                                 	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
                                                 	at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
                                                 	at java.lang.Thread.run(Thread.java:1012)
 D  Place details fetched successfully: 6657 Compton Ct, Johnston, IA 50131, USA
 I  ✅ GOOGLE_PLACES_DETAILS_SUCCESS: Place details fetched successfully for placeId='ChIJYxA3Q16d7ocR46uT2HsIS8w', address='6657 Compton Ct, Johnston, IA 50131, USA'
 D  Selected place set for display only: 6657 Compton Ct, Johnston, IA 50131, USA
 D  ✅ GOOGLE_PLACES_SELECTION_COMPLETE: Place selected and stored for validation at save time
 D  🔄 GOOGLE_PLACES_SESSION_RENEWED: Session token regenerated for next search
 D  app_time_stats: avg=661.99ms min=33.88ms max=1892.25ms count=3
 D  LaunchedEffect triggered with selectedPlace: 6657 Compton Ct, Johnston, IA 50131, USA
 D  Address text updated to: 6657 Compton Ct, Johnston, IA 50131, USA
 D  show(ime(), fromIme=true)
 I   :523313e1: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@b5b3136
 D  endAllActiveAnimators on 0x758d33ae3590 (UnprojectedRipple) with handle 0x758ca3ad64e0
 D  app_time_stats: avg=138.47ms min=5.34ms max=500.91ms count=8
 D  show(ime(), fromIme=false)
 I   :2c7d65a2: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I   :2c7d65a2: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I   :a50e6966: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{16b1cfa VFED..... .F....ID 0,0-1080,2400 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
 D  app_time_stats: avg=24.97ms min=11.55ms max=358.43ms count=40
 D  show(ime(), fromIme=true)
 I   :a50e6966: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=131.28ms min=14.02ms max=500.16ms count=11
 D  app_time_stats: avg=216.77ms min=11.60ms max=501.14ms count=6
 D  app_time_stats: avg=334.09ms min=86.30ms max=499.72ms count=3
 D  getOrderIdErrorMessage: input='225252588', error='null'
 D  isValidOrderId: input='225252588', valid=true
 D  getOrderIdErrorMessage: input='225252588', error='null'
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  findOrCreateAddressFromPlace (SSoT) - Looking up or creating address for Place ID: ChIJYxA3Q16d7ocR46uT2HsIS8w, User ID: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  No address found for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 with placeId ChIJYxA3Q16d7ocR46uT2HsIS8w.
 D  Adding new address for placeId ChIJYxA3Q16d7ocR46uT2HsIS8w via remoteDataSource
 D  addAddress: Creating new address YeD7m8Wagq1ZrzZ6Fap4 for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 at path: users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_addresses/YeD7m8Wagq1ZrzZ6Fap4
 D  addAddress: Writing address data:
 D    Document ID: YeD7m8Wagq1ZrzZ6Fap4
 D    Data Size: 1233 bytes
 D    Write Data: Address(id=YeD7m8Wagq1ZrzZ6Fap4, addressData=AddressData(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fullAddress=6657 Compton Ct, Johnston, IA 50131, USA, normalizedAddress=6657 compton ct johnston ia 50131 usa, placeId=ChIJYxA3Q16d7ocR46uT2HsIS8w, isDefault=false, notes=null, tags=[], orderIds=[], searchTerms=[6657 Compton Ct, 6657 Compton Ct, Johnston, IA 50131, USA], components=Components(streetNumber=6657, streetName=Compton Court, city=Johnston, state=IA, postalCode=50131, country=US), coordinates=Coordinates(latitude=41.681893599999995, longitude=-93.7399427), searchFields=SearchFields(searchTerms=[6657 Compton Ct, 6657 Compton Ct, Johnston, IA 50131, USA], normalizedKey=null), deliveryStats=Delivery_stats(deliveryCount=null, tipCount=null, totalTips=null, highestTip=null, pendingCount=null, averageTimeMinutes=null, lastDeliveryDate=null, averageTipAmount=null, lastDeliveryTimestamp=null), flags=Flags(isFavorite=null, isVerified=true, doNotDeliver=null, isApartment=null, isArchived=null, hasAccessIssues=null, manualDndState=null, dndSource=null), metadata=Metadata(createdAt=null, updatedAt=null, importedAt=null, source=google_places_api, importId=null, captureId=null, version=null, customData=null), platform=null))
 D  app_time_stats: avg=16.59ms min=14.10ms max=19.48ms count=61
 D  Successfully added new address YeD7m8Wagq1ZrzZ6Fap4 for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 in 235ms
 D  Fetching newly created address YeD7m8Wagq1ZrzZ6Fap4 via remoteDataSource
 D  getAddressById: Fetching address YeD7m8Wagq1ZrzZ6Fap4 for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 from path: users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_addresses/YeD7m8Wagq1ZrzZ6Fap4
 D  [Firestore] WRITE SET:users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_addresses/YeD7m8Wagq1ZrzZ6Fap4 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1233bytes Fields:[addressData,id] DataFields:2 [id,addressData]: 235ms [OK]
 I  FIRESTORE WRITE: SET:users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_addresses/YeD7m8Wagq1ZrzZ6Fap4 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1233bytes Fields:[addressData,id] DataFields:2 [id,addressData] (235ms)
 D  getAddressById: Successfully fetched address for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D    Firestore Path: users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_addresses/YeD7m8Wagq1ZrzZ6Fap4
 D    Document Size: 1145 bytes
 D    Firestore Duration: 106ms
 D  [Firestore] READ GET_DOCUMENT:users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_addresses/YeD7m8Wagq1ZrzZ6Fap4 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Results:1 Size:1145bytes Source:SERVER Filters:[address_by_id]: 106ms [OK]
 I  FIRESTORE READ: GET_DOCUMENT:users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/user_addresses/YeD7m8Wagq1ZrzZ6Fap4 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Results:1 Size:1145bytes Source:SERVER Filters:[address_by_id] (106ms)
 D    Raw Document Data: {id=YeD7m8Wagq1ZrzZ6Fap4, addressData={deliveryStats={averageTipAmount=null, averageTimeMinutes=null, pendingCount=null, lastDeliveryTimestamp=null, totalTips=null, highestTip=null, deliveryCount=null, tipCount=null, lastDeliveryDate=null}, components={country=US, streetName=Compton Court, city=Johnston, streetNumber=6657, postalCode=50131, state=IA}, metadata={createdAt=null, importId=null, importedAt=null, captureId=null, customData=null, source=google_places_api, version=null, updatedAt=null}, notes=null, searchTerms=[6657 Compton Ct, 6657 Compton Ct, Johnston, IA 50131, USA], coordinates={latitude=41.681893599999995, longitude=-93.7399427}, flags={archived=null, hasAccessIssues=null, dndSource=null, manualDndState=null, verified=true, doNotDeliver=null, favorite=null, apartment=null}, placeId=ChIJYxA3Q16d7ocR46uT2HsIS8w, userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, platform=null, tags=[], default=false, normalizedAddress=6657 compton ct johnston ia 50131 usa, fullAddress=6657 Compton Ct, Johnston, IA 50131, USA, searchFields={normalizedKey=null, searchTerms=[6657 Compton Ct, 6657 Compton Ct, Johnston, IA 50131, USA]}, orderIds=[]}}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=YeD7m8Wagq1ZrzZ6Fap4, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=6657 Compton Ct, Johnston, IA 50131, USA, dto_coordinates=Coordinates(latitude=41.681893599999995, longitude=-93.7399427), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=YeD7m8Wagq1ZrzZ6Fap4, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=6657 Compton Ct, Johnston, IA 50131, USA, domain_coordinates=Coordinates(latitude=41.681893599999995, longitude=-93.7399427), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  findOrCreateAddressFromPlace (SSoT) - Successfully found/created and mapped SSoT address: YeD7m8Wagq1ZrzZ6Fap4
 D  [AddressMapper] toSsot(Address) ID:YeD7m8Wagq1ZrzZ6Fap4 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1186bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Scheduling task addDelivery_1749796210735 with priority HIGH
 D  Executing task addDelivery_1749796210735 (HIGH)
 D  validateDelivery: Validating delivery 
 D  validateDelivery: Delivery  is valid
 D  CONVERSION: Delivery SSoT -> DTO | ID:  | User: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D    Input: orderId=225252588 | status=COMPLETED | tipAmount=12.0
 D    Converting address: 6657 Compton Ct, Johnston, IA 50131, USA... (ID: YeD7m8Wagq1ZrzZ6Fap4)
 D    Created missing reference with addressId: YeD7m8Wagq1ZrzZ6Fap4
 D  === DELIVERY SSoT -> DTO TRANSFORMATION ===
 D  Input SSoT State: {ssot_id=, ssot_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, ssot_orderId=225252588, ssot_tipAmount=12.0, ssot_status=COMPLETED, ssot_notes=empty, ssot_addressId=YeD7m8Wagq1ZrzZ6Fap4}
 D  Output DTO State: {dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_orderId=225252588, dto_tipAmount=12.0, dto_status=COMPLETED, dto_notes=empty, dto_addressId=YeD7m8Wagq1ZrzZ6Fap4}
 D  Field Transformations: [notes: ENCRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, times: TIMESTAMP_MAPPING]
 D  Business Logic Applied: [notes_encryption, field_validation, address_plain_mapping, status_mapping, amounts_calculation]
 D  PII Fields Encrypted: 0
 D  Mapping Duration: 1ms
 D  CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: 
 D    Output: encrypted_fields=0 | validation_errors=0 | duration=1ms
 D    DTO size: 1165 bytes
 D    Business logic: address_mapping, status_mapping, amounts_calculation
 D  executeAddDeliveryOperation: Adding delivery with transaction for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [DeliveryMapper] toDto Delivery ID: User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1165bytes Delivery: 1ms [OK]
 W  Cleared Reference was only reachable from finalizer (only reported once)
 D  Attempting to add new delivery (from map) and update stats for user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  isAssociateAddressIfNotFound: false, rawAddressDetails provided: false
 D  Delivery (from map) added to transaction with ID: zL55jhiEpXrulNP22GwJ
 D  First delivery to address YeD7m8Wagq1ZrzZ6Fap4 - incrementing address count for PvUDqhBngIPdmfzXuPdJ8Mr63pw1.
 D  Incrementing user profile delivery count (both usage and usageStats) for PvUDqhBngIPdmfzXuPdJ8Mr63pw1.
 D  Incremented address stats for YeD7m8Wagq1ZrzZ6Fap4.
 D  getOrderIdErrorMessage: input='225252588', error='null'
 D  isValidOrderId: input='225252588', valid=true
 D  getOrderIdErrorMessage: input='225252588', error='null'
 D  app_time_stats: avg=33.71ms min=13.92ms max=398.02ms count=37
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  findOrCreateAddressFromPlace (SSoT) - Looking up or creating address for Place ID: ChIJYxA3Q16d7ocR46uT2HsIS8w, User ID: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Existing address found for placeId ChIJYxA3Q16d7ocR46uT2HsIS8w via remoteDataSource
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=YeD7m8Wagq1ZrzZ6Fap4, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=6657 Compton Ct, Johnston, IA 50131, USA, dto_coordinates=Coordinates(latitude=41.681893599999995, longitude=-93.7399427), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=YeD7m8Wagq1ZrzZ6Fap4, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=6657 Compton Ct, Johnston, IA 50131, USA, domain_coordinates=Coordinates(latitude=41.681893599999995, longitude=-93.7399427), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  findOrCreateAddressFromPlace (SSoT) - Successfully found/created and mapped SSoT address: YeD7m8Wagq1ZrzZ6Fap4
 D  [AddressMapper] toSsot(Address) ID:YeD7m8Wagq1ZrzZ6Fap4 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1186bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Scheduling task addDelivery_1749796211958 with priority HIGH
 D  Executing task addDelivery_1749796211958 (HIGH)
 D  validateDelivery: Validating delivery 
 D  validateDelivery: Delivery  is valid
 D  CONVERSION: Delivery SSoT -> DTO | ID:  | User: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D    Input: orderId=225252588 | status=COMPLETED | tipAmount=12.0
 D    Converting address: 6657 Compton Ct, Johnston, IA 50131, USA... (ID: YeD7m8Wagq1ZrzZ6Fap4)
 D    Created missing reference with addressId: YeD7m8Wagq1ZrzZ6Fap4
 D  === DELIVERY SSoT -> DTO TRANSFORMATION ===
 D  Input SSoT State: {ssot_id=, ssot_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, ssot_orderId=225252588, ssot_tipAmount=12.0, ssot_status=COMPLETED, ssot_notes=empty, ssot_addressId=YeD7m8Wagq1ZrzZ6Fap4}
 D  Output DTO State: {dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_orderId=225252588, dto_tipAmount=12.0, dto_status=COMPLETED, dto_notes=empty, dto_addressId=YeD7m8Wagq1ZrzZ6Fap4}
 D  Field Transformations: [notes: ENCRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, times: TIMESTAMP_MAPPING]
 D  Business Logic Applied: [notes_encryption, field_validation, address_plain_mapping, status_mapping, amounts_calculation]
 D  PII Fields Encrypted: 0
 D  Mapping Duration: 0ms
 D  CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: 
 D    Output: encrypted_fields=0 | validation_errors=0 | duration=0ms
 D  [DeliveryMapper] toDto Delivery ID: User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1165bytes Delivery: 0ms [OK]
 D    DTO size: 1165 bytes
 D    Business logic: address_mapping, status_mapping, amounts_calculation
 D  executeAddDeliveryOperation: Adding delivery with transaction for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Attempting to add new delivery (from map) and update stats for user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  isAssociateAddressIfNotFound: false, rawAddressDetails provided: false
 I  Successfully added new delivery (from map) with ID: zL55jhiEpXrulNP22GwJ and updated all stats for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 I  executeAddDeliveryOperation: Delivery added with transaction, ID zL55jhiEpXrulNP22GwJ for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Saved delivery zL55jhiEpXrulNP22GwJ for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Cached delivery zL55jhiEpXrulNP22GwJ with atomic cache system
 D  executeAddDeliveryOperation: Delivery added zL55jhiEpXrulNP22GwJ (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858, user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  observeDeliveriesByUserId: Emitting 3 deliveries from cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Task addDelivery_1749796210735 completed in 1237ms
 D  Deduplication: Original 3 deliveries, deduplicated to 2
 D  [data.DeliveryRepository] addDelivery(Delivery) ID:zL55jhiEpXrulNP22GwJ User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1165bytes Source:transaction Strategy:write-through [CACHE_MISS]: 1235ms [OK]
 V  ThrottleFirst: emitted value
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:66836ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:66836ms [CACHE_HIT] [POOR_UX]: 66836ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 66836ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:2022bytes [EFFICIENT]: 0ms [OK]
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 I  Starting enhanced cache warming for user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  Scheduling task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 with priority CRITICAL
 D  Executing task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (CRITICAL)
 D  DEDUPLICATION: Starting new request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserById: Starting operation for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 for current user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: true
 D  getUserById: Found user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 in local cache (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  [cache_system.UserRepository] cache_breakdown Check:1ms Hit:true ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 1ms [OK]
 D  Task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 completed in 3ms
 D  getAddresses (SSoT) - Fetching all addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [data.UserRepository] getUserById(User) ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1547bytes Source:cache Strategy:cache-first [CACHE_HIT]: 1ms [OK]
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  getAddresses (SSoT) - Found 2 addresses in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  getUserSubscription: Verifying authentication for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 5ms, result: true
 D  getUserSubscription: Managing session for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Found existing request for key 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1', waiting for result
 D  User profile cache warming successful in 6ms
 D  DEDUPLICATION: Completed waiting for 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 1ms, result: true
 D  getUserSubscription: Using session PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription' in 8ms, result: true
 D  Subscription cache warming successful in 9ms
 D  Delivery (from map) added to transaction with ID: 0jpcuLLPExyNYNzQSWqX
 D  Address YeD7m8Wagq1ZrzZ6Fap4 already has 1 deliveries - not incrementing address count.
 D  Incrementing user profile delivery count (both usage and usageStats) for PvUDqhBngIPdmfzXuPdJ8Mr63pw1.
 D  Incremented address stats for YeD7m8Wagq1ZrzZ6Fap4.
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=YeD7m8Wagq1ZrzZ6Fap4, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=6657 Compton Ct, Johnston, IA 50131, USA, dto_coordinates=Coordinates(latitude=41.681893599999995, longitude=-93.7399427), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=YeD7m8Wagq1ZrzZ6Fap4, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=6657 Compton Ct, Johnston, IA 50131, USA, domain_coordinates=Coordinates(latitude=41.681893599999995, longitude=-93.7399427), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=nbKRDYvJ1hYj72b8JvOj, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, dto_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=nbKRDYvJ1hYj72b8JvOj, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, domain_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:YeD7m8Wagq1ZrzZ6Fap4 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1236bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 2 items
 D  getAddresses (SSoT) - Fetched and cached 2 SSoT addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  [AddressMapper] toSsot(Address) ID:nbKRDYvJ1hYj72b8JvOj User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1210bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 110ms, result: true
 D  Addresses cache warming successful in 111ms
 I  Cache warming completed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: user_profile:success:6ms, subscription:success:9ms, addresses:success:111ms, total:111ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved 3 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeGetDeliveriesByUserIdOperation - Found 3 deliveries in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858, count: 3)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  ✅ Deliveries loaded successfully: 2 (deduplicated from 3)
 I  Compiler allocated 5354KB to compile java.lang.Object  .data.mapper.AddressMapper.mapToDomain(java.lang.String,  .data.model.generated_kt.Address$AddressData, kotlin.coroutines.Continuation)
 I  Successfully added new delivery (from map) with ID: 0jpcuLLPExyNYNzQSWqX and updated all stats for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 I  executeAddDeliveryOperation: Delivery added with transaction, ID 0jpcuLLPExyNYNzQSWqX for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Saved delivery 0jpcuLLPExyNYNzQSWqX for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeAddDeliveryOperation: Delivery added 0jpcuLLPExyNYNzQSWqX (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858, user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  Task addDelivery_1749796211958 completed in 317ms
 D  [data.DeliveryRepository] addDelivery(Delivery) ID:0jpcuLLPExyNYNzQSWqX User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1165bytes Source:transaction Strategy:write-through [CACHE_MISS]: 318ms [OK]
 D  observeDeliveriesByUserId: Emitting 4 deliveries from cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Cached delivery 0jpcuLLPExyNYNzQSWqX with atomic cache system
 D  Deduplication: Original 4 deliveries, deduplicated to 3
 V  ThrottleFirst: emitted value
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:2957bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:67142ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:67142ms [CACHE_HIT] [POOR_UX]: 67142ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 67142ms (user-visible)
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 I  Starting enhanced cache warming for user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  Scheduling task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 with priority CRITICAL
 D  getUserSubscription: Verifying authentication for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Fetching all addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserSubscription: Managing session for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Found existing request for key 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  getUserSubscription: Using session PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858
 D  getAddresses (SSoT) - Found 2 addresses in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  Executing task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (CRITICAL)
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription' in 2ms, result: true
 D  Subscription cache warming successful in 3ms
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getUserById: Starting operation for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 for current user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: true
 D  getUserById: Found user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 in local cache (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858)
 D  Task getUserById_PvUDqhBngIPdmfzXuPdJ8Mr63pw1 completed in 6ms
 D  [cache_system.UserRepository] cache_breakdown Check:1ms Hit:true ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1: 1ms [OK]
 D  DEDUPLICATION: Cleaned up completed request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  [data.UserRepository] getUserById(User) ID:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Count:1 Size:1547bytes Source:cache Strategy:cache-first [CACHE_HIT]: 3ms [OK]
 D  DEDUPLICATION: Completed new request 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 8ms, result: true
 D  User profile cache warming successful in 9ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=YeD7m8Wagq1ZrzZ6Fap4, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=6657 Compton Ct, Johnston, IA 50131, USA, dto_coordinates=Coordinates(latitude=41.681893599999995, longitude=-93.7399427), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=YeD7m8Wagq1ZrzZ6Fap4, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=6657 Compton Ct, Johnston, IA 50131, USA, domain_coordinates=Coordinates(latitude=41.681893599999995, longitude=-93.7399427), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=nbKRDYvJ1hYj72b8JvOj, dto_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, dto_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, dto_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=nbKRDYvJ1hYj72b8JvOj, domain_userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1, domain_fullAddress=8811 Plum Dr, Urbandale, IA 50322, USA, domain_coordinates=Coordinates(latitude=41.65067, longitude=-93.7411922), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:YeD7m8Wagq1ZrzZ6Fap4 User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1236bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_PvUDqhBngIPdmfzXuPdJ8Mr63pw1_all with 2 items
 D  getAddresses (SSoT) - Fetched and cached 2 SSoT addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Completed new request 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 95ms, result: true
 D  Addresses cache warming successful in 96ms
 I  Cache warming completed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1: user_profile:success:9ms, subscription:success:3ms, addresses:success:96ms, total:97ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  Retrieved 4 deliveries for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeGetDeliveriesByUserIdOperation - Found 4 deliveries in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1 (session: PvUDqhBngIPdmfzXuPdJ8Mr63pw1_1749796142858, count: 4)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  [AddressMapper] toSsot(Address) ID:nbKRDYvJ1hYj72b8JvOj User:PvUDqhBngIPdmfzXuPdJ8Mr63pw1 Size:1210bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_PvUDqhBngIPdmfzXuPdJ8Mr63pw1' in 0ms, result: true
 D  ✅ Deliveries loaded successfully: 3 (deduplicated from 4)
 D  app_time_stats: avg=17.43ms min=11.12ms max=38.73ms count=58
 D  hide(ime(), fromIme=false)
 I   :b72c3095: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_BY_INSETS_API fromUser false
 I   :eec08c4c: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_REQUEST_HIDE_WITH_CONTROL fromUser false
 W  requestCursorUpdates on inactive InputConnection
 D  hide(ime(), fromIme=true)
 I   :eec08c4c: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I   :8dcd7f76: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
 I   :b72c3095: onHidden
 D  app_time_stats: avg=72.66ms min=1.46ms max=1999.88ms count=31
 D  app_time_stats: avg=20618.16ms min=20618.16ms max=20618.16ms count=1
 D  Navigating to destination: dashboard
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  Updated selected place from loaded delivery
 D  LaunchedEffect triggered with selectedPlace: 8811 Plum Dr, Urbandale, IA 50322, USA
 D  Address text updated to: 8811 Plum Dr, Urbandale, IA 50322, USA
 D  tagSocket(168) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getCurrentUserIdSuspend: Checking authentication state
 D  Waiting for authentication readiness...
 D  Authentication state immediately available: Authenticated(userId=PvUDqhBngIPdmfzXuPdJ8Mr63pw1)
 D  getAddresses (SSoT) - Found 2 addresses in local cache for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1, fetching from remote.
 D  getCurrentUserIdSuspend: Authentication confirmed for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 I  Starting enhanced cache warming for user: PvUDqhBngIPdmfzXuPdJ8Mr63pw1
 D  [reactive_performance.AddEditDeliveryViewModel] stateflow_emission Flow:delivery Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  DEDUPLICATION: Starting new request for key 'addresses/user/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1'
 D  DEDUPLICATION: Starting new request for key 'users/PvUDqhBngIPdmfzXuPdJ8Mr63pw1/subscription'
 D  getUserSubscription: Verifying authentication for user PvUDqhBngIPdmfzXuPdJ8Mr63pw1