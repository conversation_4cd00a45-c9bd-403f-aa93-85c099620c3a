package com.autogratuity.data.repository.preference

import android.util.Log
import com.autogratuity.data.datasource.local.PreferenceLocalDataSource
import com.autogratuity.data.datasource.remote.PreferenceRemoteDataSource
import com.autogratuity.data.mapper.PreferenceMapper
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.generated_kt.User_profile
// FirestoreRepository and FirestoreRepositoryDependencies removed - no longer needed

import com.autogratuity.data.repository.core.RepositoryErrorHandler
import com.autogratuity.data.security.AuthenticationManager
import com.autogratuity.debug.ClarityArchitectureMonitor
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.withContext
import kotlin.time.TimeSource
import com.autogratuity.domain.model.UserPreferences
import com.autogratuity.domain.model.DndDetails
import com.autogratuity.domain.repository.PreferenceRepository


/**
 * PreferenceRepository implementation following AddressRepositoryImpl reference pattern.
 *
 * Uses FirestoreRepository inheritance with RemoteDataSource + LocalDataSource + Mapper pattern.
 * Extends FirestoreRepository like AddressRepositoryImpl for consistency.
 *
 * Key features:
 * - FirestoreRepository inheritance pattern like AddressRepositoryImpl
 * - Cache-first strategy: local → remote → cache SSoT results
 * - Result<T> pattern for all operations
 * - SSoT User models throughout
 * - Pure orchestration between components
 *
 * ✅ MODERNIZED: Simplified constructor for Constructor DSL compatibility
 */
@OptIn(ExperimentalCoroutinesApi::class)
class PreferenceRepositoryImpl(
    private val remoteDataSource: PreferenceRemoteDataSource,
    private val localDataSource: PreferenceLocalDataSource,
    private val preferenceMapper: PreferenceMapper,
    // Temporary dependencies - will be moved to DataSources in next iteration
    private val authManager: AuthenticationManager,
    private val ioDispatcher: CoroutineDispatcher,
    // Performance infrastructure
    private val repositoryErrorHandler: RepositoryErrorHandler,
    // JSON serialization with proper JSR310 support
    private val objectMapper: com.fasterxml.jackson.databind.ObjectMapper,
    // Config repository for notification patterns creation
    private val configRepository: com.autogratuity.domain.repository.ConfigRepository
) : PreferenceRepository, PreferenceDataRepository {

    private val TAG = "PreferenceRepositoryImpl"

    private fun getAuthenticatedUserId(): String {
        return authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")
    }

    // ===== DOMAIN PREFERENCE REPOSITORY IMPLEMENTATION =====

    // ===== INTERNAL HELPER METHODS =====

    private suspend fun updateUserProfileFields(fields: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            
            // Delegate to remote data source for field updates
            remoteDataSource.updateUserProfileFieldsDto(userId, fields)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    // ===== INTERNAL HELPER METHODS =====

    private suspend fun setDisplayName(displayName: String) {
        try {
            getAuthenticatedUserId()
            val result = updateUserProfileFields(mapOf("displayName" to displayName))
            if (result is Result.Error) {
                throw result.exception
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting display name", e)
        }
    }

    private suspend fun <T : Any> getPreferenceSetting(key: String, defaultValue: T): T {
        return try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> {
                    val user = userResult.data
                    // Extract preference value based on key
                    when (key) {
                        "theme" -> {
                            val theme = user?.preferences?.theme
                            if (theme != null && theme::class == defaultValue::class) {
                                @Suppress("UNCHECKED_CAST")
                                theme as T
                            } else defaultValue
                        }
                        "notifications_enabled" -> {
                            val enabled = user?.preferences?.notificationsEnabled
                            if (enabled != null && enabled::class == defaultValue::class) {
                                @Suppress("UNCHECKED_CAST")
                                enabled as T
                            } else defaultValue
                        }
                        else -> defaultValue
                    }
                }
                else -> defaultValue
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting preference setting $key", e)
            defaultValue
        }
    }

    private suspend fun <T : Any> setPreferenceSetting(key: String, value: T) {
        try {
            val userId = getAuthenticatedUserId()
            val currentUserResult = localDataSource.getUserById(userId)
            when (currentUserResult) {
                is Result.Success -> {
                    val currentUser = currentUserResult.data ?: return
                    
                    // ✅ FIXED: Delegate business logic to mapper instead of doing it in repository
                    val updateResult = preferenceMapper.updatePreferenceSetting(currentUser, key, value)
                    when (updateResult) {
                        is Result.Success -> {
                            val updatedUser = updateResult.data
                            // Save updated user
                            val dtoResult = preferenceMapper.mapToDto(updatedUser)
                            when (dtoResult) {
                                is Result.Success -> {
                                    val remoteResult = remoteDataSource.updateUserProfileDto(userId, dtoResult.data)
                                    if (remoteResult is Result.Success) {
                                        localDataSource.saveUser(userId, updatedUser)
                                    } else if (remoteResult is Result.Error) {
                                        Log.e(TAG, "Error updating remote user preferences", remoteResult.exception)
                                    }
                                }
                                is Result.Error -> {
                                    Log.e(TAG, "Error mapping user to DTO", dtoResult.exception)
                                }
                                is Result.Loading -> {
                                    Log.w(TAG, "Unexpected Loading state from mapper.mapToDto")
                                }
                            }
                        }
                        is Result.Error -> {
                            Log.e(TAG, "Error updating preference setting '$key'", updateResult.exception)
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "Unexpected Loading state from mapper.updatePreferenceSetting")
                        }
                    }
                }
                else -> Log.e(TAG, "Error getting current user for preference setting")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting preference $key", e)
        }
    }

    // ===== REMAINING PREFERENCE REPOSITORY METHODS =====

    suspend fun getDefaultAddressIdLegacy(): String {
        return try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> userResult.data?.defaultAddressId ?: ""
                else -> ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting default address ID", e)
            ""
        }
    }

    suspend fun setDefaultAddressIdLegacy(addressId: String) {
        try {
            getAuthenticatedUserId()
            val result = setDefaultAddressId(addressId)
            if (result is Result.Error) {
                throw result.exception
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting default address ID", e)
        }
    }

    suspend fun isDataCollectionOptedInLegacy(): Boolean {
        return try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> userResult.data?.preferences?.useLocation == true
                else -> false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting data collection opt-in", e)
            false
        }
    }

    // ===== DND PREFERENCE METHODS =====

    override fun observeDndSettings(): Flow<DndDetails?> {
        return try {
            val userId = getAuthenticatedUserId()
            localDataSource.observeById(userId).map { user ->
                user?.preferences?.dnd
            }
        } catch (_: Exception) {
            flowOf(null)
        }
    }

    override fun isProUser(): Flow<Result<Boolean>> {
        return try {
            val userId = getAuthenticatedUserId()
            localDataSource.observeById(userId).map { user ->
                val isProUser = preferenceMapper.isProUser(user?.subscription)
                Result.Success(isProUser) as Result<Boolean>
            }.catch { exception ->
                emit(Result.Error(Exception(exception)))
            }
        } catch (e: Exception) {
            flowOf(Result.Error(e))
        }
    }

    // ===== INTERNAL HELPER METHODS =====

    private suspend fun getHomeAddressSummary(): String {
        return try {
            val defaultIdResult = getDefaultAddressId()
            val defaultId = when (defaultIdResult) {
                is Result.Success -> defaultIdResult.data
                else -> ""
            }
            if (defaultId.isBlank()) {
                "No home address set"
            } else {
                // Return formatted summary with address ID
                // Note: Full address details would require AddressRepository integration
                // For now, provide a meaningful summary with the available information
                "Default Address (ID: $defaultId)"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting home address summary", e)
            "Error fetching address"
        }
    }

    private suspend fun clearAppCache() {
        try {
            val userId = getAuthenticatedUserId()
            localDataSource.deleteUser(userId)
            Log.i(TAG, "App cache cleared for user ID: $userId")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing app cache", e)
        }
    }

    private fun <T : Any> getPreferenceSettingFlow(key: String, defaultValue: T): Flow<T> {
        return try {
            val userId = getAuthenticatedUserId()
            localDataSource.observeById(userId).map { user ->
                when (key) {
                    "theme" -> {
                        val theme = user?.preferences?.theme
                        if (theme != null && theme::class == defaultValue::class) {
                            @Suppress("UNCHECKED_CAST")
                            theme as T
                        } else defaultValue
                    }
                    "notifications_enabled" -> {
                        val enabled = user?.preferences?.notificationsEnabled
                        if (enabled != null && enabled::class == defaultValue::class) {
                            @Suppress("UNCHECKED_CAST")
                            enabled as T
                        } else defaultValue
                    }
                    else -> defaultValue
                }
            }
        } catch (_: Exception) {
            flowOf(defaultValue)
        }
    }

    // ===== DOMAIN PREFERENCE REPOSITORY IMPLEMENTATION =====

    override suspend fun getCurrentUserPreferences(): Result<UserPreferences?> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            getUserPreferences(userId)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun getUserPreferences(userId: String): Result<UserPreferences?> = withContext(ioDispatcher) {
        val startTime = TimeSource.Monotonic.markNow()
        var success = true
        
        try {
            // ENHANCED: Add session correlation for preference access
            ClarityArchitectureMonitor.addSessionEvent("preferences_fetch:$userId")
            
            val userResult = localDataSource.getUserById(userId)
            val result = when (userResult) {
                is Result.Success -> Result.Success(userResult.data?.preferences)
                is Result.Error -> {
                    success = false
                    Result.Success(null) // Return null on error
                }
                is Result.Loading -> Result.Success(null) // Return null on loading
            }
            
            // ENHANCED: Monitor preference repository operation
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "PreferenceRepository",
                operation = "getUserPreferences",
                duration = startTime.elapsedNow(),
                success = success,
                cacheHit = userResult is Result.Success,
                dataType = "UserPreferences",
                entityId = userId,
                userId = userId,
                dataSource = "local_cache"
            )
            
            result
        } catch (e: Exception) {
            success = false
            
            // ENHANCED: Monitor preference repository error
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "PreferenceRepository",
                operation = "getUserPreferences",
                duration = startTime.elapsedNow(),
                success = false,
                error = e,
                dataType = "UserPreferences",
                entityId = userId,
                userId = userId
            )
            
            Result.Error(e)
        }
    }

    override suspend fun updateUserPreferences(userId: String, preferences: UserPreferences): Result<Unit> = withContext(ioDispatcher) {
        val totalStartTime = TimeSource.Monotonic.markNow()
        var cacheCheckDuration: kotlin.time.Duration? = null
        var remoteFetchDuration: kotlin.time.Duration? = null
        var mappingDuration: kotlin.time.Duration? = null
        var cacheStoreDuration: kotlin.time.Duration? = null
        var success = true
        
        try {
            // ENHANCED: Add session correlation for preference update
            ClarityArchitectureMonitor.addSessionEvent("preferences_update:$userId")
            
            // 1. CACHE CHECK PHASE
            val cacheCheckStart = TimeSource.Monotonic.markNow()
            val currentUserResult = localDataSource.getUserById(userId)
            cacheCheckDuration = cacheCheckStart.elapsedNow()
            
            val result = when (currentUserResult) {
                is Result.Success -> {
                    val currentUser = currentUserResult.data ?: return@withContext Result.Error(IllegalStateException("User not found"))
                    val updatedUser = currentUser.copy(preferences = preferences)
                    
                    // 2. MAPPING PHASE
                    val mappingStart = TimeSource.Monotonic.markNow()
                    val dtoResult = preferenceMapper.mapToDto(updatedUser)
                    mappingDuration = mappingStart.elapsedNow()
                    
                    when (dtoResult) {
                        is Result.Success -> {
                            // 3. REMOTE UPDATE PHASE
                            val remoteStart = TimeSource.Monotonic.markNow()
                            val remoteResult = remoteDataSource.updateUserProfileDto(userId, dtoResult.data)
                            remoteFetchDuration = remoteStart.elapsedNow()
                            
                            when (remoteResult) {
                                is Result.Success -> {
                                    // 4. CACHE UPDATE PHASE
                                    val cacheStoreStart = TimeSource.Monotonic.markNow()
                                    localDataSource.saveUser(userId, updatedUser)
                                    cacheStoreDuration = cacheStoreStart.elapsedNow()
                                    
                                    Result.Success(Unit)
                                }
                                is Result.Error -> {
                                    success = false
                                    remoteResult
                                }
                                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                            }
                        }
                        is Result.Error -> {
                            success = false
                            dtoResult
                        }
                        is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                    }
                }
                is Result.Error -> {
                    success = false
                    currentUserResult
                }
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
            
            // ENHANCED: Monitor cache system performance breakdown
            ClarityArchitectureMonitor.monitorCacheSystemPerformance(
                repositoryClass = "PreferenceRepository",
                operation = "updateUserPreferences",
                cacheCheckDuration = cacheCheckDuration,
                remoteFetchDuration = remoteFetchDuration,
                mappingDuration = mappingDuration,
                cacheStoreDuration = cacheStoreDuration,
                cacheHit = currentUserResult is Result.Success,
                entityType = "UserPreferences",
                entityId = userId,
                userId = userId,
                cacheMetrics = mapOf(
                    "operation" to "preference_update",
                    "source" to "cache_and_remote",
                    "preferences_count" to preferences.toString().length
                )
            )
            
            // ENHANCED: Monitor repository operation
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "PreferenceRepository",
                operation = "updateUserPreferences",
                duration = totalStartTime.elapsedNow(),
                success = success,
                cacheHit = currentUserResult is Result.Success,
                dataType = "UserPreferences",
                entityId = userId,
                userId = userId,
                dataSource = "cache_and_remote",
                dataSize = preferences.toString().length,
                cacheStrategy = "cache-first-update"
            )
            
            result
        } catch (e: Exception) {
            success = false
            
            // ENHANCED: Monitor failed operation
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "PreferenceRepository",
                operation = "updateUserPreferences",
                duration = totalStartTime.elapsedNow(),
                success = false,
                error = e,
                dataType = "UserPreferences",
                entityId = userId,
                userId = userId
            )
            
            Result.Error(e)
        }
    }

    override suspend fun createDefaultPreferences(userId: String): Result<UserPreferences> = withContext(ioDispatcher) {
        try {
            val defaultPreferences = preferenceMapper.calculateDefaultPreferences()
            Result.Success(defaultPreferences)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override fun observeCurrentUserPreferences(): Flow<Result<UserPreferences?>> {
        return try {
            val userId = getAuthenticatedUserId()
            observeUserPreferences(userId)
        } catch (e: Exception) {
            flowOf(Result.Error(e))
        }
    }

    override fun observeUserPreferences(userId: String): Flow<Result<UserPreferences?>> {
        return localDataSource.observeById(userId)
            .map { user ->
                Result.Success(user?.preferences) as Result<UserPreferences?>
            }
            // Backpressure handling removed - was inherited from FirestoreRepository
            .catch { exception ->
                emit(Result.Error(Exception(exception)))
            }
    }

    override suspend fun getThemePreference(): Result<String> = withContext(ioDispatcher) {
        try {
            val theme = getPreferenceSetting("theme", "system")
            Result.Success(theme)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun setThemePreference(theme: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            setPreferenceSetting("theme", theme)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override fun observeThemePreference(): Flow<String> {
        return getPreferenceSettingFlow("theme", "system")
    }

    override suspend fun getNotificationsEnabled(): Result<Boolean> = withContext(ioDispatcher) {
        try {
            val enabled = getPreferenceSetting("notifications_enabled", false) // ✅ FIX: Default false - user must manually enable
            Result.Success(enabled)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun setNotificationsEnabled(enabled: Boolean): Result<Unit> = withContext(ioDispatcher) {
        try {
            setPreferenceSetting("notifications_enabled", enabled)

            // If enabling notifications for the first time, create default notification patterns
            if (enabled) {
                createNotificationPatternsIfNeeded()
            }

            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    /**
     * Create notification patterns if they don't exist (when user enables tip capture)
     */
    private suspend fun createNotificationPatternsIfNeeded() {
        try {
            // Check if patterns already exist
            val existingPatterns = configRepository.getNotificationPatterns()
            when (existingPatterns) {
                is Result.Success -> {
                    if (existingPatterns.data == null) {
                        Log.d(TAG, "Creating notification patterns for first-time tip capture enablement")
                        val defaultResult = configRepository.createDefaultNotificationPatterns()
                        when (defaultResult) {
                            is Result.Success -> {
                                // Save to Firestore now that user has explicitly enabled tip capture
                                configRepository.updateNotificationPatterns(defaultResult.data)
                                Log.d(TAG, "✅ Default notification patterns created and saved")
                            }
                            is Result.Error -> {
                                Log.e(TAG, "Failed to create default notification patterns", defaultResult.exception)
                            }
                            is Result.Loading -> {
                                Log.w(TAG, "Unexpected Loading state creating notification patterns")
                            }
                        }
                    } else {
                        Log.d(TAG, "Notification patterns already exist, no need to create")
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "Error checking existing notification patterns", existingPatterns.exception)
                }
                is Result.Loading -> {
                    Log.w(TAG, "Unexpected Loading state checking notification patterns")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception creating notification patterns", e)
        }
    }

    override fun observeNotificationsEnabled(): Flow<Boolean> {
        return getPreferenceSettingFlow("notifications_enabled", false) // ✅ FIX: Default false - user must manually enable
    }

    override suspend fun isDataCollectionOptedIn(): Result<Boolean> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> Result.Success(userResult.data?.preferences?.useLocation == true)
                is Result.Error -> Result.Success(false) // Return default value on error
                is Result.Loading -> Result.Success(false) // Return default value on loading
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun setDataCollectionOptedIn(optedIn: Boolean): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            val currentUserResult = localDataSource.getUserById(userId)
            when (currentUserResult) {
                is Result.Success -> {
                    val currentUser = currentUserResult.data ?: return@withContext Result.Error(IllegalStateException("User not found"))
                    val currentPrefs = currentUser.preferences ?: preferenceMapper.calculateDefaultPreferences()
                    val updatedPrefs = currentPrefs.copy(useLocation = optedIn)
                    val updatedUser = currentUser.copy(preferences = updatedPrefs)
                    
                    // Save updated user
                    val dtoResult = preferenceMapper.mapToDto(updatedUser)
                    when (dtoResult) {
                        is Result.Success -> {
                            val remoteResult = remoteDataSource.updateUserProfileDto(userId, dtoResult.data)
                            when (remoteResult) {
                                is Result.Success -> {
                                    localDataSource.saveUser(userId, updatedUser)
                                    Result.Success(Unit)
                                }
                                is Result.Error -> remoteResult
                                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                            }
                        }
                        is Result.Error -> dtoResult
                        is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                    }
                }
                is Result.Error -> currentUserResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override fun observeDataCollectionOptedIn(): Flow<Boolean> {
        return try {
            val userId = getAuthenticatedUserId()
            localDataSource.observeById(userId).map { user ->
                user?.preferences?.useLocation == true
            }
        } catch (_: Exception) {
            flowOf(false)
        }
    }

    override suspend fun getDndSettings(): Result<DndDetails?> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> Result.Success(userResult.data?.preferences?.dnd)
                is Result.Error -> userResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun updateDndSettings(dndDetails: DndDetails): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            val currentUserResult = localDataSource.getUserById(userId)
            when (currentUserResult) {
                is Result.Success -> {
                    val currentUser = currentUserResult.data ?: return@withContext Result.Error(IllegalStateException("User not found"))
                    val currentPrefs = currentUser.preferences ?: preferenceMapper.calculateDefaultPreferences()
                    val updatedPrefs = currentPrefs.copy(dnd = dndDetails)
                    val updatedUser = currentUser.copy(preferences = updatedPrefs)
                    
                    // Save updated user
                    val dtoResult = preferenceMapper.mapToDto(updatedUser)
                    when (dtoResult) {
                        is Result.Success -> {
                            val remoteResult = remoteDataSource.updateUserProfileDto(userId, dtoResult.data)
                            when (remoteResult) {
                                is Result.Success -> {
                                    localDataSource.saveUser(userId, updatedUser)
                                    Result.Success(Unit)
                                }
                                is Result.Error -> remoteResult
                                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                            }
                        }
                        is Result.Error -> dtoResult
                        is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                    }
                }
                is Result.Error -> currentUserResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun isOnboardingCompleted(): Result<Boolean> = withContext(ioDispatcher) {
        try {
            val completed = getPreferenceSetting("onboarding_completed", false)
            Result.Success(completed)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun setOnboardingCompleted(completed: Boolean): Result<Unit> = withContext(ioDispatcher) {
        try {
            setPreferenceSetting("onboarding_completed", completed)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun getDefaultAddressId(): Result<String> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> Result.Success(userResult.data?.defaultAddressId ?: "")
                is Result.Error -> userResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun setDefaultAddressId(addressId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            val currentUserResult = localDataSource.getUserById(userId)
            when (currentUserResult) {
                is Result.Success -> {
                    val currentUser = currentUserResult.data ?: return@withContext Result.Error(IllegalStateException("User not found"))
                    val updatedUser = currentUser.copy(defaultAddressId = addressId)
                    
                    // Save updated user
                    val dtoResult = preferenceMapper.mapToDto(updatedUser)
                    when (dtoResult) {
                        is Result.Success -> {
                            val remoteResult = remoteDataSource.updateUserProfileDto(userId, dtoResult.data)
                            when (remoteResult) {
                                is Result.Success -> {
                                    localDataSource.saveUser(userId, updatedUser)
                                    Result.Success(Unit)
                                }
                                is Result.Error -> remoteResult
                                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                            }
                        }
                        is Result.Error -> dtoResult
                        is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                    }
                }
                is Result.Error -> currentUserResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override fun observeDefaultAddressId(): Flow<String> {
        return try {
            val userId = getAuthenticatedUserId()
            localDataSource.observeById(userId).map { user ->
                user?.defaultAddressId ?: ""
            }
        } catch (_: Exception) {
            flowOf("")
        }
    }

    override suspend fun getDndMarkingsUsedCount(): Result<Long> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> Result.Success(userResult.data?.usage?.dndMarkingsUsed ?: 0L)
                is Result.Error -> userResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun getMaxDndMarkings(): Result<Long> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> Result.Success(userResult.data?.usage?.maxDndMarkings ?: 0L)
                is Result.Error -> userResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun getAutoCapturedOrdersCount(): Result<Long> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> Result.Success(userResult.data?.usage?.autoCapturedOrders ?: 0L)
                is Result.Error -> userResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun <T : Any> getPreferenceSettingWithResult(key: String, defaultValue: T): Result<T> = withContext(ioDispatcher) {
        try {
            val value = getPreferenceSetting(key, defaultValue)
            Result.Success(value)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun <T : Any> setPreferenceSettingWithResult(key: String, value: T): Result<Unit> = withContext(ioDispatcher) {
        try {
            setPreferenceSetting(key, value)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override fun <T : Any> observePreferenceSetting(key: String, defaultValue: T): Flow<T> {
        return getPreferenceSettingFlow(key, defaultValue)
    }

    override suspend fun setPreferenceSettings(settings: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            remoteDataSource.updateUserProfileFieldsDto(userId, settings)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun validatePreferences(preferences: UserPreferences): Result<Unit> = withContext(ioDispatcher) {
        try {
            preferenceMapper.validateUserPreferences(preferences)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun resetToDefaults(): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            val defaultPreferences = preferenceMapper.calculateDefaultPreferences()
            updateUserPreferences(userId, defaultPreferences)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun clearCache(): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            clearCache(userId)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun clearCache(userId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            localDataSource.deleteUser(userId)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun prefetchCriticalData(): Result<Unit> = withContext(ioDispatcher) {
        try {
            getAuthenticatedUserId()
            // Prefetch current user preferences
            getCurrentUserPreferences()
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun initialize(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.i(TAG, "initialize: Initializing PreferenceRepository")
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun cleanup(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.i(TAG, "cleanup: Cleaning up PreferenceRepository resources")
            clearCache()
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun setDndEnabled(enabled: Boolean): Result<Unit> = withContext(ioDispatcher) {
        try {
            getAuthenticatedUserId()
            val result = updateUserProfileFields(mapOf("preferences.dnd.customRule.isEnabled" to enabled))
            result
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun setDndTipThreshold(threshold: Double): Result<Unit> = withContext(ioDispatcher) {
        try {
            getAuthenticatedUserId()
            val result = updateUserProfileFields(mapOf("preferences.dnd.customRule.tipAmountThreshold" to threshold))
            result
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun setDndComparisonType(comparisonType: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            getAuthenticatedUserId()
            val result = updateUserProfileFields(mapOf("preferences.dnd.customRule.comparisonType" to comparisonType))
            result
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    // ===== DATA INTERFACE CORE DTO OPERATIONS =====

    override suspend fun updateUserProfileDto(profile: User_profile): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            remoteDataSource.updateUserProfileDto(userId, profile)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun updateUserProfileFieldsDto(fields: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = getAuthenticatedUserId()
            remoteDataSource.updateUserProfileFieldsDto(userId, fields)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override fun observeUserProfileDto(): Flow<Result<User_profile?>> {
        return try {
            val userId = getAuthenticatedUserId()
            localDataSource.observeById(userId).map { user ->
                if (user != null) {
                    val dtoResult = preferenceMapper.mapToDto(user)
                    when (dtoResult) {
                        is Result.Success -> Result.Success(dtoResult.data)
                        is Result.Error -> Result.Error(dtoResult.exception)
                        is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                    }
                } else {
                    Result.Success(null)
                }
            }.catch { exception ->
                emit(Result.Error(Exception(exception)))
            }
        } catch (e: Exception) {
            flowOf(Result.Error(e))
        }
    }

    // ===== DATA INTERFACE INTERNAL INFRASTRUCTURE METHODS =====

    override suspend fun setDisplayNameInternal(displayName: String) {
        setDisplayName(displayName)
    }

    override suspend fun <T : Any> getPreferenceSettingInternal(
        key: String,
        defaultValue: T
    ): T {
        return getPreferenceSetting(key, defaultValue)
    }

    override suspend fun <T : Any> setPreferenceSettingInternal(
        key: String,
        value: T
    ) {
        setPreferenceSetting(key, value)
    }

    override suspend fun getThemePreferenceInternal(): String {
        return getPreferenceSetting("theme", "system")
    }

    override suspend fun setThemePreferenceInternal(theme: String) {
        setPreferenceSetting("theme", theme)
    }

    override suspend fun getNotificationsEnabledInternal(): Boolean {
        return getPreferenceSetting("notifications_enabled", false) // ✅ FIX: Default false - user must manually enable
    }

    override suspend fun setNotificationsEnabledInternal(enabled: Boolean) {
        setPreferenceSetting("notifications_enabled", enabled)
    }

    override suspend fun getDefaultAddressIdInternal(): String {
        return try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> userResult.data?.defaultAddressId ?: ""
                else -> ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting default address ID internal", e)
            ""
        }
    }

    override suspend fun setDefaultAddressIdInternal(addressId: String) {
        try {
            val result = setDefaultAddressId(addressId)
            if (result is Result.Error) {
                Log.e(TAG, "Error setting default address ID internal", result.exception)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting default address ID internal", e)
        }
    }

    override suspend fun isOnboardingCompletedInternal(): Boolean {
        return getPreferenceSetting("onboarding_completed", false)
    }

    override suspend fun setOnboardingCompletedInternal(completed: Boolean) {
        setPreferenceSetting("onboarding_completed", completed)
    }

    override suspend fun isDataCollectionOptedInInternal(): Boolean {
        return try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> userResult.data?.preferences?.useLocation == true
                else -> false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting data collection opt-in internal", e)
            false
        }
    }

    override suspend fun setDataCollectionOptedInInternal(optedIn: Boolean) {
        try {
            val result = setDataCollectionOptedIn(optedIn)
            if (result is Result.Error) {
                Log.e(TAG, "Error setting data collection opt-in internal", result.exception)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting data collection opt-in internal", e)
        }
    }

    override suspend fun getDndSettingsInternal(): User_profile.Dnd? {
        return try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> {
                    val user = userResult.data
                    if (user?.preferences?.dnd != null) {
                        // Convert SSoT DndDetails to DTO User_profile.Dnd
                        val dndDetails = user.preferences.dnd
                        val dtoResult = preferenceMapper.mapToDto(user)
                        when (dtoResult) {
                            is Result.Success -> dtoResult.data.preferences?.dnd
                            else -> null
                        }
                    } else null
                }
                else -> null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting DND settings internal", e)
            null
        }
    }

    override fun observeDndSettingsInternal(): Flow<User_profile.Dnd?> {
        return try {
            val userId = getAuthenticatedUserId()
            localDataSource.observeById(userId).map { user ->
                if (user?.preferences?.dnd != null) {
                    // Convert SSoT DndDetails to DTO User_profile.Dnd
                    val dtoResult = preferenceMapper.mapToDto(user)
                    when (dtoResult) {
                        is Result.Success -> dtoResult.data.preferences?.dnd
                        else -> null
                    }
                } else null
            }.catch { exception ->
                Log.e(TAG, "Error observing DND settings internal", exception)
                emit(null)
            }
        } catch (e: Exception) {
            flowOf(null)
        }
    }

    override suspend fun setDndEnabledInternal(enabled: Boolean) {
        try {
            val result = setDndEnabled(enabled)
            if (result is Result.Error) {
                Log.e(TAG, "Error setting DND enabled internal", result.exception)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting DND enabled internal", e)
        }
    }

    override suspend fun setDndTipThresholdInternal(threshold: Double) {
        try {
            val result = setDndTipThreshold(threshold)
            if (result is Result.Error) {
                Log.e(TAG, "Error setting DND tip threshold internal", result.exception)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting DND tip threshold internal", e)
        }
    }

    override suspend fun setDndComparisonTypeInternal(comparisonType: String) {
        try {
            val result = setDndComparisonType(comparisonType)
            if (result is Result.Error) {
                Log.e(TAG, "Error setting DND comparison type internal", result.exception)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting DND comparison type internal", e)
        }
    }

    override fun isProUserInternal(): Flow<Result<Boolean>> {
        return isProUser()
    }

    override suspend fun getHomeAddressSummaryInternal(): String {
        return getHomeAddressSummary()
    }

    override suspend fun getDndMarkingsUsedCountInternal(): Long {
        return try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> userResult.data?.usage?.dndMarkingsUsed ?: 0L
                else -> 0L
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting DND markings used count internal", e)
            0L
        }
    }

    override suspend fun getMaxDndMarkingsInternal(): Long {
        return try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> userResult.data?.usage?.maxDndMarkings ?: 0L
                else -> 0L
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting max DND markings internal", e)
            0L
        }
    }

    override suspend fun getAutoCapturedOrdersCountInternal(): Long {
        return try {
            val userId = getAuthenticatedUserId()
            val userResult = localDataSource.getUserById(userId)
            when (userResult) {
                is Result.Success -> userResult.data?.usage?.autoCapturedOrders ?: 0L
                else -> 0L
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting auto captured orders count internal", e)
            0L
        }
    }

    override suspend fun clearAppCacheInternal() {
        clearAppCache()
    }

    override suspend fun setPreferenceSettingsInternal(settings: Map<String, Any>): Result<Unit> {
        return setPreferenceSettings(settings)
    }

    override fun <T : Any> getPreferenceSettingFlowInternal(
        key: String,
        defaultValue: T
    ): Flow<T> {
        return getPreferenceSettingFlow(key, defaultValue)
    }

}