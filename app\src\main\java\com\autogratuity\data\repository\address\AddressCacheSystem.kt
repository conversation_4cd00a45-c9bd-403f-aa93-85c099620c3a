package com.autogratuity.data.repository.address

import android.util.Log
import com.autogratuity.domain.model.Address
import com.autogratuity.data.repository.core.AtomicCacheSystem
import com.autogratuity.data.repository.core.CacheSource
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes
import kotlin.time.TimeSource

/**
 * Specialized cache system for Address entities following 2025 Kotlin standards.
 * Provides domain-specific operations for address caching with SSOT awareness and
 * integration with cloud function updates.
 *
 * Key Features:
 * - User-specific cache invalidation
 * - DND (Do Not Deliver) flag aware caching
 * - Cloud function update tracking
 * - Address statistics metadata
 * - SSOT field ownership respect
 */
@Singleton
class AddressCacheSystem @Inject constructor(
    timeSource: TimeSource,
    ioDispatcher: kotlinx.coroutines.CoroutineDispatcher,
    private val applicationScope: kotlinx.coroutines.CoroutineScope
) : AtomicCacheSystem<String, Address>(timeSource) {
    private val TAG = "AddressCacheSystem"
    private val coroutineScope = CoroutineScope(ioDispatcher)

    companion object {
        private const val CACHE_ADDRESSES = "addresses" // Base key for list of addresses
        private const val CACHE_ADDRESS = "address"     // Base key for a single address
    }

    // Domain-specific configuration
    override val defaultTtl: Duration = 2.hours  // Longer TTL for address data
    override val maxCacheSize: Int = 500         // Addresses are fewer but larger objects

    // ✅ SMART CACHE TTL: DND-aware cache configuration
    private val dndDataTtl: Duration = 5.minutes     // Short TTL for DND-sensitive data
    private val flagsDataTtl: Duration = 15.minutes  // Medium TTL for flags data
    private val deliveryDataTtl: Duration = 15.minutes // Medium TTL for delivery-related data

    /**
     * ✅ SMART CACHE TTL: Determine appropriate TTL based on data type
     * DND-related data gets shorter TTL for better consistency with cloud updates
     */
    private fun getSmartTtl(key: String, metadata: Map<String, Any> = emptyMap()): Duration {
        val keyLower = key.lowercase()
        val metadataString = metadata.toString().lowercase()

        return when {
            // DND-specific data gets shortest TTL (5 minutes)
            keyLower.contains("dnd") ||
            keyLower.contains("donotdeliver") ||
            metadataString.contains("dnd") ||
            metadataString.contains("donotdeliver") -> dndDataTtl

            // Flags data gets medium TTL (15 minutes)
            keyLower.contains("flags") ||
            keyLower.contains("flag") ||
            metadataString.contains("flags") -> flagsDataTtl

            // Delivery-related data gets medium TTL (15 minutes)
            keyLower.contains("delivery") ||
            keyLower.contains("deliveries") ||
            metadataString.contains("delivery") -> deliveryDataTtl

            // Everything else uses default TTL (2 hours)
            else -> defaultTtl
        }
    }

    /**
     * Cache an address with specialized metadata for domain-specific operations (suspend version)
     *
     * @param addressId The address ID (used as cache key)
     * @param address The Address object
     * @param userId The user ID this address belongs to
     * @param source The source of this cache entry (default: FIRESTORE)
     */
    suspend fun cacheAddressSuspend(
        addressId: String,
        address: Address,
        userId: String,
        source: CacheSource = CacheSource.FIRESTORE
    ) {
        // Extract domain-specific metadata using domain model method
        val hasDnd = address.isDndEnforced()
        val hasDeliveryStats = address.deliveryStats != null
        val tipCount = address.deliveryStats?.tipCount ?: 0
        val totalTips = address.deliveryStats?.totalTips?.toDouble() ?: 0.0

        val metadata = buildMap<String, Any> {
            put("userId", userId)
            put("hasDndFlag", hasDnd)
            put("hasDeliveryStats", hasDeliveryStats)
            put("tipCount", tipCount)
            put("totalTips", totalTips)
            put("lastCached", Clock.System.now().toEpochMilliseconds())

            // Track SSOT field ownership for conflict resolution
            put("ssotFields", listOf(
                "deliveryStats",      // Cloud-owned (direct property in SSoT)
                "flags.doNotDeliver", // Cloud-owned (within flags object, direct property in SSoT)
                "flags.isVerified",   // Cloud-owned (within flags object, direct property in SSoT)
                "flags.dndSource"     // Cloud-owned (within flags object, direct property in SSoT)
            ))

            address.flags?.dndSource?.let { put("dndSource", it) }
            address.deliveryStats?.lastDeliveryTimestamp?.let {
                put("lastDeliveryTime", it)
            }
        }

        // ✅ SMART CACHE TTL: Use DND-aware TTL based on address content
        val smartTtl = getSmartTtl(addressId, metadata)
        put(addressId, address, smartTtl, metadata, source)
    }

    /**
     * ✅ MODERNIZED: Cache address with comprehensive SSOT-aware metadata (non-suspend version)
     */
    fun cacheAddress(
        addressId: String,
        address: Address,
        userId: String,
        source: CacheSource = CacheSource.FIRESTORE
    ) {
        if (addressId.isBlank()) {
            Log.w(TAG, "Cannot cache address with blank addressId")
            return
        }

        coroutineScope.launch {
            cacheAddressSuspend(addressId, address, userId, source)
        }
    }

    /**
     * Invalidate all addresses associated with a specific user.
     * Useful when user-level changes affect all addresses.
     */
    suspend fun invalidateUserAddresses(userId: String) {
        invalidateByPredicate { key: String, metadata: Map<String, Any> ->
            metadata["userId"] == userId
        }
    }

    /**
     * Invalidate addresses based on DND status updates.
     * Called when cloud functions update DND flags.
     */
    suspend fun invalidateDndAddresses() {
        invalidateByPredicate { key: String, metadata: Map<String, Any> ->
            metadata.containsKey("hasDndFlag") && metadata["hasDndFlag"] == true
        }
    }

    /**
     * Invalidate addresses that have delivery statistics, typically called
     * when address stats are updated by cloud functions.
     */
    suspend fun invalidateAddressesWithStats() {
        invalidateByPredicate { key: String, metadata: Map<String, Any> ->
            metadata["hasDeliveryStats"] == true
        }
    }

    /**
     * Invalidate addresses with statistics above a certain tip threshold.
     * Useful for high-value address cache management.
     */
    suspend fun invalidateHighValueAddresses(tipThreshold: Double) {
        invalidateByPredicate { key: String, metadata: Map<String, Any> ->
            val totalTips = metadata["totalTips"] as? Double ?: 0.0
            totalTips > tipThreshold
        }
    }

    /**
     * Get addresses cached for a specific user with optional filtering
     */
    suspend fun getUserAddresses(userId: String): List<Address> {
        val allAddresses = getCurrentCacheSnapshot()
        return allAddresses.values.filter { address ->
            val addressKey = getAddressKey(address)
            val metadata = getWithMetadata(addressKey)?.second ?: return@filter false
            metadata["userId"] == userId
        }
    }

    /**
     * Get addresses with DND flags set, useful for UI filtering
     */
    suspend fun getDndAddresses(): List<Address> {
        val allAddresses = getCurrentCacheSnapshot()
        return allAddresses.values.filter { address ->
            address.isDndEnforced()
        }
    }

    /**
     * Get top tipping addresses from cache for quick access
     */
    suspend fun getTopTippingAddresses(limit: Int = 10): List<Pair<Address, Double>> {
        val allAddresses = getCurrentCacheSnapshot()
        return allAddresses.values
            .mapNotNull { address ->
                val totalTips = address.deliveryStats?.totalTips?.toDouble()
                if (totalTips != null && totalTips > 0) {
                    Pair(address, totalTips)
                } else null
            }
            .sortedByDescending { it.second }
            .take(limit)
    }

    /**
     * Update an address with cloud function data while preserving client-managed fields.
     * Implements SSOT field ownership rules.
     */
    suspend fun updateFromCloudFunction(
        addressId: String,
        updatedSsotAddress: Address,
        userId: String
    ) {
        val existingEntry = getWithMetadata(addressId)

        if (existingEntry != null) {
            val (existingSsotAddress, metadata) = existingEntry

            // Merge cloud-managed fields with existing client-managed fields
            // The 'updatedSsotAddress' might already be merged by the caller,
            // or this method performs a final merge.
            // For now, assume updatedSsotAddress is the new state to cache.
            // If merge rules are still needed here, mergeAddressWithSsotRules_Ssot needs to be robust.

            // For simplicity, let's assume updatedSsotAddress is the desired new state.
            // A more complex merge might be needed if updatedSsotAddress only contains partial cloud updates.
            // This part requires careful thought based on how `AddressRepositoryImpl` will use it.
            // Let's assume the caller (AddressRepositoryImpl using AddressMapper) provides a fully resolved SSoT.

            cacheAddressSuspend(addressId, updatedSsotAddress, userId, CacheSource.CLOUD_FUNCTION)
        } else {
            // No existing entry, cache the new SSoT address
            Log.i(TAG, "updateFromCloudFunction: New SSoT address from cloud for $addressId. Caching.")
            cacheAddressSuspend(addressId, updatedSsotAddress, userId, CacheSource.CLOUD_FUNCTION)
        }
    }

    /**
     * ✅ MODERNIZED: Cache address list operations
     */
    fun cacheAddressList(
        cacheKey: String,
        addresses: List<Address>,
        userId: String
    ) {
        // Cache individual addresses
        addresses.forEach { address ->
            val addressKey = getAddressKey(address)
            cacheAddress(addressKey, address, userId, CacheSource.PREFETCH)
        }

        Log.d(TAG, "Cached address list: $cacheKey with ${addresses.size} items")
    }

    /**
     * ✅ MODERNIZED: Get address using atomic cache with access tracking
     */
    suspend fun getAddress(userId: String, addressId: String): Address? {
        val key = getAddressCacheKey(userId, addressId)
        val result = get(key)
        if (result != null) {
            Log.d(TAG, "Cache hit for address $addressId")
        }
        return result
    }

    /**
     * Helper method to get current cache snapshot
     */
    private suspend fun getCurrentCacheSnapshot(): Map<String, Address> {
        return observeAll().map { it.entries.associate { entry -> entry.key.toString() to entry.value } }.first()
    }

    /**
     * Helper to get a consistent key for an address object
     */
    private fun getAddressKey(address: Address): String {
        return address.id
    }

    /**
     * Get cache performance metrics specific to address operations
     */
    suspend fun getAddressCacheMetrics(): Map<String, Any> {
        val baseMetrics = getDetailedStats()
        val allAddresses = getCurrentCacheSnapshot()

        val userAddressCount = allAddresses.values
            .mapNotNull { address ->
                val addressKey = getAddressKey(address)
                getWithMetadata(addressKey)?.second?.get("userId") as? String
            }
            .distinct()
            .size

        val dndAddressCount = getDndAddressesInternal().size

        val addressesWithStats = allAddresses.values.count { address ->
            address.deliveryStats != null
        }

        val avgTips = allAddresses.values
            .mapNotNull { it.deliveryStats?.totalTips?.toDouble() }
            .average()
            .takeIf { !it.isNaN() } ?: 0.0

        return baseMetrics + mapOf(
            "uniqueUsers" to userAddressCount,
            "dndAddresses" to dndAddressCount,
            "addressesWithStats" to addressesWithStats,
            "avgTipsPerAddress" to avgTips
        )
    }

    /**
     * Generates a cache key for a single address.
     *
     * @param userId The ID of the user who owns the address.
     * @param addressId The ID of the address.
     * @return The cache key string.
     */
    fun getAddressCacheKey(userId: String, addressId: String): String {
        return "${CACHE_ADDRESS}_${userId}_$addressId"
    }

    /**
     * Generates a cache key for a list of addresses for a user.
     *
     * @param userId The ID of the user.
     * @return The cache key string.
     */
    fun getAddressesCacheKey(userId: String): String {
        return "${CACHE_ADDRESSES}_$userId"
    }

    /**
     * Retrieves an address by its ID from the cache.
     *
     * @param userId The ID of the user who owns the address.
     * @param addressId The ID of the address to retrieve.
     * @return The cached Address, or null if not found.
     */
    suspend fun getAddressFromCache(userId: String, addressId: String): Address? {
        val key = getAddressCacheKey(userId, addressId)
        val cachedAddress = super.get(key)
        if (cachedAddress != null) {
            Log.d(TAG, "Cache hit for address $addressId (user: $userId)")
        } else {
            Log.d(TAG, "Cache miss for address $addressId (user: $userId)")
        }
        return cachedAddress
    }

    /**
     * Retrieves the list of addresses for a user from the cache.
     *
     * @param userId The ID of the user.
     * @return The cached list of addresses, or null if not found.
     */
    suspend fun getAddressesFromCache(userId: String): List<Address> {
        Log.d(TAG, "Fetching addresses from cache for user $userId")
        return getUserAddresses(userId)
    }

    /**
     * Puts an address into the cache.
     *
     * @param userId The ID of the user who owns the address.
     * @param addressId The ID of the address.
     * @param address The address to cache.
     */
    suspend fun putAddressInCache(userId: String, addressId: String, address: Address) {
        val key = getAddressCacheKey(userId, addressId) // addressId should be preferred for consistency
        val metadata = mapOf("userId" to userId) // Simplified metadata for this example
        // ✅ SMART CACHE TTL: Use DND-aware TTL
        val smartTtl = getSmartTtl(key, metadata)
        super.put(key, address, smartTtl, metadata, CacheSource.MANUAL)
        Log.d(TAG, "Cached address $addressId for user $userId via AtomicCacheSystem")
    }

    /**
     * Puts a list of addresses into the cache.
     *
     * @param userId The ID of the user who owns the addresses.
     * @param addresses The list of addresses to cache.
     */
    suspend fun putAddressesInCache(userId: String, addresses: List<Address>) {
        // TODO: If input is List<Address> (schema), convert to List<AddressFirestore>.
        // This method will now cache each address individually using the inherited system.
        Log.d(TAG, "Caching ${addresses.size} addresses for user $userId via AtomicCacheSystem")
        val userMetadata = mapOf("userId" to userId) // Common metadata for this batch
        addresses.forEach { address ->
            val addressId = address.id
            if (true) {
                val key = getAddressCacheKey(userId, addressId)
                // ✅ SMART CACHE TTL: Use DND-aware TTL
                val smartTtl = getSmartTtl(key, userMetadata)
                super.put(key, address, smartTtl, userMetadata, CacheSource.MANUAL)
            } else {
                Log.w(TAG, "Address with no ID found in batch for user $userId, skipping.")
            }
        }
    }

    /**
     * Invalidates (removes) an address from the cache.
     *
     * @param userId The ID of the user who owns the address.
     * @param addressId The ID of the address to invalidate.
     */
    suspend fun invalidateAddressCache(userId: String, addressId: String) {
        val key = getAddressCacheKey(userId, addressId)
        super.remove(key)
        Log.d(TAG, "Invalidated cache for address $addressId (user: $userId) via AtomicCacheSystem")
    }

    /**
     * Invalidates (removes) the list of addresses for a user from the cache.
     *
     * @param userId The ID of the user.
     */
    suspend fun invalidateAddressesCache(userId: String) {
        // This will use the invalidateByPredicate logic to remove all entries with matching userId metadata.
        Log.d(TAG, "Invalidating all cached addresses for user $userId via AtomicCacheSystem")
        invalidateUserAddresses(userId)
    }

    /**
     * Gets a StateFlow for observing a specific address.
     * Will create a new flow if one doesn't exist.
     *
     * @param userId The ID of the user who owns the address.
     * @param addressId The ID of the address to observe.
     * @return A StateFlow emitting the address as it changes.
     */
    fun getAddressFlow(userId: String, addressId: String): Flow<Address?> {
        val key = getAddressCacheKey(userId, addressId)
        // TODO: If public API needs Flow<Address?> (schema), map the Flow<AddressFirestore?> here.
        // This requires a CoroutineScope to launch the collection of the flow from super.observe if it's to be converted to StateFlow.
        // For now, directly returning the Flow from AtomicCacheSystem.
        Log.d(TAG, "Providing Flow for address $addressId (user: $userId) from AtomicCacheSystem")
        return super.observe(key)
    }

    /**
     * Gets a StateFlow for observing the list of addresses for a user.
     * Will create a new flow if one doesn't exist.
     *
     * @param userId The ID of the user.
     * @return A StateFlow emitting the list of addresses as it changes.
     */
    fun getAddressesFlow(userId: String): Flow<List<Address>> {
        Log.d(TAG, "Providing Flow for addresses for user $userId from AtomicCacheSystem")
        return observeAll().map { allAddresses ->
            allAddresses.values.filter { address ->
                val addressKey = getAddressKey(address)
                val metadata = getWithMetadata(addressKey)?.second
                metadata?.get("userId") == userId
            }
        }
    }

    /**
     * Invalidates all address data from the cache for a specific user.
     *
     * @param userId The ID of the user whose cache to clear.
     */
    suspend fun clearUserCache(userId: String) {
        Log.d(TAG, "Clearing all address cache data for user $userId via AtomicCacheSystem")
        invalidateUserAddresses(userId) // Leverages invalidateByPredicate
    }

    /**
     * Invalidates all address data from the cache.
     * Typically called when a user logs out or major data sync occurs.
     */
    suspend fun clearAllCache() {
        Log.d(TAG, "Cleared all address cache data via AtomicCacheSystem")
        super.clear()
    }

    /**
     * Generic method to get any type from cache using a string key.
     * Used by the repository to abstract cache access.
     *
     * @param cacheKey The key to retrieve from cache.
     * @return The cached value of type T, or null if not found.
     */
    @Suppress("UNCHECKED_CAST")
    suspend fun <T> getFromCache(cacheKey: String): T? {
        return when {
            cacheKey.startsWith(CACHE_ADDRESS) -> {
                super.get(cacheKey) as? T // super.get(key) returns V? which is Address?
            }
            cacheKey.startsWith(CACHE_ADDRESSES) -> {
                // This key "addresses_USERID" is not directly used to store a List<Address> in AtomicCacheSystem.
                // AtomicCacheSystem stores individual Address objects by "address_USERID_ADDRESSID".
                // This branch needs to extract USERID and then call a method that filters all addresses for that USERID.
                val userId = cacheKey.removePrefix(CACHE_ADDRESSES + "_")
                if (userId.isNotBlank()) {
                    // Call the method that correctly fetches all addresses for a user and filters
                    getAddressesFromCache(userId) as? T // This returns List<Address>
                } else {
                    null
                }
            }
            else -> null
        }
    }

    /**
     * Generic method to put any value into cache using a string key.
     * Used by the repository to abstract cache access.
     *
     * @param cacheKey The key to use for caching.
     * @param value The value to cache.
     */
    suspend fun putInCache(cacheKey: String, value: Any, userIdHint: String? = null) {
        // This generic putter is also problematic. Assuming value is Address for single items.
        // TODO: This method might need significant rework or removal.
        when {
            cacheKey.startsWith(CACHE_ADDRESS) && value is Address -> {
                val metadata = userIdHint?.let { mapOf("userId" to it) } ?: emptyMap()
                // ✅ SMART CACHE TTL: Use DND-aware TTL
                val smartTtl = getSmartTtl(cacheKey, metadata)
                super.put(cacheKey, value, smartTtl, metadata, CacheSource.MANUAL)
            }
            cacheKey.startsWith(CACHE_ADDRESSES) && value is List<*> -> {
                // Attempt to cast and process as List<Address>
                try {
                    @Suppress("UNCHECKED_CAST")
                    val addresses = value as List<Address>
                    val uId = userIdHint ?: cacheKey.split("_").getOrNull(1) ?: "unknown_user"
                    putAddressesInCache(uId, addresses)
                } catch (e: ClassCastException) {
                    Log.w(TAG, "Invalid value type for addresses cache in putInCache (expected List<Address>): $cacheKey", e)
                }
            }
            else -> {
                Log.w(TAG, "Unknown cache key format or incompatible value type in putInCache: $cacheKey")
            }
        }
    }

    /**
     * Generic method to invalidate a cache entry using a string key.
     * Used by the repository to abstract cache invalidation.
     *
     * @param cacheKey The key to invalidate.
     */
    suspend fun invalidateCache(cacheKey: String) {
        // This generic invalidator is also simplified.
        // TODO: This method might need significant rework or removal.
        when {
            cacheKey.startsWith(CACHE_ADDRESS) -> {
                super.remove(cacheKey)
            }
            cacheKey.startsWith(CACHE_ADDRESSES) -> {
                // This is a broad stroke. Invalidating all addresses for a user based on a list key.
                val userId = cacheKey.split("_").getOrNull(1)
                if (userId != null) {
                    invalidateUserAddresses(userId)
                } else {
                    Log.w(TAG, "Could not extract userId from addresses cache key for invalidation: $cacheKey")
                }
            }
            else -> {
                Log.w(TAG, "Unknown cache key format in invalidateCache: $cacheKey")
            }
        }
    }

    /**
     * Clears all caches. Used when the repository needs to reset all cached data.
     */
    suspend fun clearCache() { // Renamed to avoid conflict with inherited clear, now suspend
        clearAllCache()
    }

    /**
     * ✅ LIFECYCLE: Cleanup resources when cache system is destroyed
     */
    fun cleanup() {
        try {
            coroutineScope.cancel("AddressCacheSystem cleanup")
            Log.d(TAG, "AddressCacheSystem cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during AddressCacheSystem cleanup", e)
        }
    }

    // Renamed to avoid conflict if there's a public one with different sig
    private suspend fun getDndAddressesInternal(): List<Address> {
        val allAddresses = getCurrentCacheSnapshot()
        return allAddresses.values.filter { address ->
            val addressKey = getAddressKey(address)
            val metadata = getWithMetadata(addressKey)?.second
            (metadata?.get("hasDndFlag") as? Boolean) == true || address.isDndEnforced()
        }
    }
}