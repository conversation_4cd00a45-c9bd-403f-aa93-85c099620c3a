 D  D  app_time_stats: avg=53.70ms min=1.46ms max=595.55ms count=17
 D  Place details fetched successfully: 222 5th Ave, Des Moines, IA 50309, USA
 I  ✅ GOOGLE_PLACES_DETAILS_SUCCESS: Place details fetched successfully for placeId='ChIJDe9qRgCZ7ocRBzLIDSN1RMg', address='222 5th Ave, Des Moines, IA 50309, USA'
 D  Selected place set for display only: 222 5th Ave, Des Moines, IA 50309, USA
 D  ✅ GOOGLE_PLACES_SELECTION_COMPLETE: Place selected and stored for validation at save time
 D  🔄 GOOGLE_PLACES_SESSION_RENEWED: Session token regenerated for next search
 D  LaunchedEffect triggered with selectedPlace: 222 5th Ave, Des Moines, IA 50309, USA
 D  Address text updated to: 222 5th Ave, Des Moines, IA 50309, USA
 D  app_time_stats: avg=250.01ms min=8.84ms max=905.54ms count=4
 D  show(ime(), fromIme=true)
 I  com.autogratuity:3e17da8f: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 W  sendCancelIfRunning: isInProgress=false callback=androidx.compose.ui.window.Api33Impl$$ExternalSyntheticLambda0@fef67bf
 D  endAllActiveAnimators on 0x758d33acace0 (UnprojectedRipple) with handle 0x758ca3acd960
 W  sendCancelIfRunning: isInProgress=false callback=ImeCallback=ImeOnBackInvokedCallback@255721015 Callback=android.window.IOnBackInvokedCallback$Stub$Proxy@4f8c8e9
 D  app_time_stats: avg=188.01ms min=11.95ms max=500.27ms count=6
 D  app_time_stats: avg=77.00ms min=14.98ms max=502.37ms count=13
 D  show(ime(), fromIme=false)
 I  com.autogratuity:35cad12c: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:35cad12c: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:13df279d: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  show(ime(), fromIme=true)
 I  com.autogratuity:5024d0e: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:13df279d: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=83.19ms min=11.45ms max=500.89ms count=15
 D  app_time_stats: avg=500.45ms min=498.97ms max=501.92ms count=2
 D  app_time_stats: avg=499.28ms min=497.71ms max=500.59ms count=3
 D  app_time_stats: avg=101.57ms min=15.07ms max=516.58ms count=10
 D  app_time_stats: avg=124.88ms min=13.49ms max=499.65ms count=12
 D  app_time_stats: avg=283.35ms min=11.83ms max=500.18ms count=4
 D  app_time_stats: avg=62.51ms min=12.51ms max=499.34ms count=16
 D  show(ime(), fromIme=false)
 I  com.autogratuity:e66cb35e: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:e66cb35e: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:a5de7251: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F...... 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  app_time_stats: avg=23.07ms min=12.91ms max=216.36ms count=49
 D  show(ime(), fromIme=true)
 I  com.autogratuity:a5de7251: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 W  sendCancelIfRunning: isInProgress=false callback=androidx.compose.ui.window.Api33Impl$$ExternalSyntheticLambda0@6a00daf
 D  app_time_stats: avg=201.67ms min=22.89ms max=500.45ms count=5
 D  app_time_stats: avg=335.40ms min=7.31ms max=501.93ms count=3
 D  app_time_stats: avg=262.83ms min=15.12ms max=496.56ms count=5
 D  app_time_stats: avg=500.18ms min=500.16ms max=500.21ms count=2
 D  show(ime(), fromIme=false)
 I  com.autogratuity:4195abcb: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:4195abcb: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:cf209b86: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F...... 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  show(ime(), fromIme=true)
 I  com.autogratuity:cf209b86: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=false)
 I  com.autogratuity:514eda8b: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:514eda8b: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:af7a81f8: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F...... 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  show(ime(), fromIme=true)
 I  com.autogratuity:af7a81f8: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 W  sendCancelIfRunning: isInProgress=false callback=androidx.compose.ui.window.Api33Impl$$ExternalSyntheticLambda0@c29a668
 D  app_time_stats: avg=212.05ms min=60.98ms max=499.72ms count=5
 D  app_time_stats: avg=201.61ms min=6.47ms max=504.58ms count=6
 D  app_time_stats: avg=249.03ms min=12.73ms max=499.62ms count=5
 D  app_time_stats: avg=100.00ms min=15.09ms max=465.79ms count=10
 D  app_time_stats: avg=22.65ms min=13.56ms max=217.98ms count=44
 D  getOrderIdErrorMessage: input='*********', error='null'
 D  isValidOrderId: input='*********', valid=true
 D  getOrderIdErrorMessage: input='*********', error='null'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  findOrCreateAddressFromPlace (SSoT) - Looking up or creating address for Place ID: ChIJDe9qRgCZ7ocRBzLIDSN1RMg, User ID: myuivBnSjJe686W71qJTTuZsQet1
 D  No address found for user myuivBnSjJe686W71qJTTuZsQet1 with placeId ChIJDe9qRgCZ7ocRBzLIDSN1RMg.
 D  Adding new address for placeId ChIJDe9qRgCZ7ocRBzLIDSN1RMg via remoteDataSource
 D  addAddress: Creating new address OMzXiC4kUENI4sydIr5P for user myuivBnSjJe686W71qJTTuZsQet1 at path: users/myuivBnSjJe686W71qJTTuZsQet1/user_addresses/OMzXiC4kUENI4sydIr5P
 D  addAddress: Writing address data:
 D    Document ID: OMzXiC4kUENI4sydIr5P
 D    Data Size: 1214 bytes
 D    Write Data: Address(id=OMzXiC4kUENI4sydIr5P, addressData=AddressData(userId=myuivBnSjJe686W71qJTTuZsQet1, fullAddress=222 5th Ave, Des Moines, IA 50309, USA, normalizedAddress=222 5th ave des moines ia 50309 usa, placeId=ChIJDe9qRgCZ7ocRBzLIDSN1RMg, isDefault=false, notes=null, tags=[], orderIds=[], searchTerms=[222 5th Ave, 222 5th Ave, Des Moines, IA 50309, USA], components=Components(streetNumber=222, streetName=5th Avenue, city=Des Moines, state=IA, postalCode=50309, country=US), coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), searchFields=SearchFields(searchTerms=[222 5th Ave, 222 5th Ave, Des Moines, IA 50309, USA], normalizedKey=null), deliveryStats=Delivery_stats(deliveryCount=null, tipCount=null, totalTips=null, highestTip=null, pendingCount=null, averageTimeMinutes=null, lastDeliveryDate=null, averageTipAmount=null, lastDeliveryTimestamp=null), flags=Flags(isFavorite=null, isVerified=true, doNotDeliver=null, isApartment=null, isArchived=null, hasAccessIssues=null, manualDndState=null, dndSource=null), metadata=Metadata(createdAt=null, updatedAt=null, importedAt=null, source=google_places_api, importId=null, captureId=null, version=null, customData=null), platform=null))
 D  Successfully added new address OMzXiC4kUENI4sydIr5P for user myuivBnSjJe686W71qJTTuZsQet1 in 268ms
 D  Fetching newly created address OMzXiC4kUENI4sydIr5P via remoteDataSource
 D  getAddressById: Fetching address OMzXiC4kUENI4sydIr5P for user myuivBnSjJe686W71qJTTuZsQet1 from path: users/myuivBnSjJe686W71qJTTuZsQet1/user_addresses/OMzXiC4kUENI4sydIr5P
 D  [Firestore] WRITE SET:users/myuivBnSjJe686W71qJTTuZsQet1/user_addresses/OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1214bytes Fields:[addressData,id] DataFields:2 [id,addressData]: 268ms [OK]
 I  FIRESTORE WRITE: SET:users/myuivBnSjJe686W71qJTTuZsQet1/user_addresses/OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1214bytes Fields:[addressData,id] DataFields:2 [id,addressData] (268ms)
 D  getAddressById: Successfully fetched address for user myuivBnSjJe686W71qJTTuZsQet1
 D    Firestore Path: users/myuivBnSjJe686W71qJTTuZsQet1/user_addresses/OMzXiC4kUENI4sydIr5P
 D    Document Size: 1126 bytes
 D    Firestore Duration: 91ms
 D  [Firestore] READ GET_DOCUMENT:users/myuivBnSjJe686W71qJTTuZsQet1/user_addresses/OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Results:1 Size:1126bytes Source:SERVER Filters:[address_by_id]: 91ms [OK]
 I  FIRESTORE READ: GET_DOCUMENT:users/myuivBnSjJe686W71qJTTuZsQet1/user_addresses/OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Results:1 Size:1126bytes Source:SERVER Filters:[address_by_id] (91ms)
 D    Raw Document Data: {id=OMzXiC4kUENI4sydIr5P, addressData={deliveryStats={averageTipAmount=null, averageTimeMinutes=null, pendingCount=null, lastDeliveryTimestamp=null, totalTips=null, highestTip=null, deliveryCount=null, tipCount=null, lastDeliveryDate=null}, components={country=US, streetName=5th Avenue, city=Des Moines, streetNumber=222, postalCode=50309, state=IA}, metadata={createdAt=null, importId=null, importedAt=null, captureId=null, customData=null, source=google_places_api, version=null, updatedAt=null}, notes=null, searchTerms=[222 5th Ave, 222 5th Ave, Des Moines, IA 50309, USA], coordinates={latitude=41.5856411, longitude=-93.62360079999999}, flags={archived=null, hasAccessIssues=null, dndSource=null, manualDndState=null, verified=true, doNotDeliver=null, favorite=null, apartment=null}, placeId=ChIJDe9qRgCZ7ocRBzLIDSN1RMg, userId=myuivBnSjJe686W71qJTTuZsQet1, platform=null, tags=[], default=false, normalizedAddress=222 5th ave des moines ia 50309 usa, fullAddress=222 5th Ave, Des Moines, IA 50309, USA, searchFields={normalizedKey=null, searchTerms=[222 5th Ave, 222 5th Ave, Des Moines, IA 50309, USA]}, orderIds=[]}}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  findOrCreateAddressFromPlace (SSoT) - Successfully found/created and mapped SSoT address: OMzXiC4kUENI4sydIr5P
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1167bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  Scheduling task addDelivery_1749825868682 with priority HIGH
 D  Executing task addDelivery_1749825868682 (HIGH)
 D  validateDelivery: Validating delivery 
 D  validateDelivery: Delivery  is valid
 D  CONVERSION: Delivery SSoT -> DTO | ID:  | User: myuivBnSjJe686W71qJTTuZsQet1
 D    Input: orderId=********* | status=COMPLETED | tipAmount=69.0
 D    Converting address: 222 5th Ave, Des Moines, IA 50309, USA... (ID: OMzXiC4kUENI4sydIr5P)
 D    Created missing reference with addressId: OMzXiC4kUENI4sydIr5P
 D  === DELIVERY SSoT -> DTO TRANSFORMATION ===
 D  Input SSoT State: {ssot_id=, ssot_userId=myuivBnSjJe686W71qJTTuZsQet1, ssot_orderId=*********, ssot_tipAmount=69.0, ssot_status=COMPLETED, ssot_notes=empty, ssot_addressId=OMzXiC4kUENI4sydIr5P}
 D  Output DTO State: {dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_orderId=*********, dto_tipAmount=69.0, dto_status=COMPLETED, dto_notes=empty, dto_addressId=OMzXiC4kUENI4sydIr5P}
 D  Field Transformations: [notes: ENCRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, times: TIMESTAMP_MAPPING]
 D  Business Logic Applied: [notes_encryption, field_validation, address_plain_mapping, status_mapping, amounts_calculation]
 D  PII Fields Encrypted: 0
 D  Mapping Duration: 2ms
 D  CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: 
 D    Output: encrypted_fields=0 | validation_errors=0 | duration=2ms
 D  [DeliveryMapper] toDto Delivery ID: User:myuivBnSjJe686W71qJTTuZsQet1 Size:1162bytes Delivery: 2ms [OK]
 D    DTO size: 1162 bytes
 D    Business logic: address_mapping, status_mapping, amounts_calculation
 D  executeAddDeliveryOperation: Adding delivery with transaction for user myuivBnSjJe686W71qJTTuZsQet1
 D  Attempting to add new delivery (from map) and update stats for user: myuivBnSjJe686W71qJTTuZsQet1
 D  isAssociateAddressIfNotFound: false, rawAddressDetails provided: false
 D  app_time_stats: avg=22.07ms min=11.85ms max=267.96ms count=46
 D  Delivery (from map) added to transaction with ID: QdqJBMTjl5fqAumWVrJk
 D  First delivery to address OMzXiC4kUENI4sydIr5P - incrementing address count for myuivBnSjJe686W71qJTTuZsQet1.
 D  Incrementing user profile delivery count (both usage and usageStats) for myuivBnSjJe686W71qJTTuZsQet1.
 D  Incremented address stats for OMzXiC4kUENI4sydIr5P.
 I  Successfully added new delivery (from map) with ID: QdqJBMTjl5fqAumWVrJk and updated all stats for user myuivBnSjJe686W71qJTTuZsQet1
 I  executeAddDeliveryOperation: Delivery added with transaction, ID QdqJBMTjl5fqAumWVrJk for user myuivBnSjJe686W71qJTTuZsQet1
 D  Saved delivery QdqJBMTjl5fqAumWVrJk for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeAddDeliveryOperation: Delivery added QdqJBMTjl5fqAumWVrJk (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, user: myuivBnSjJe686W71qJTTuZsQet1)
 D  Task addDelivery_1749825868682 completed in 326ms
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [data.DeliveryRepository] addDelivery(Delivery) ID:QdqJBMTjl5fqAumWVrJk User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1162bytes Source:transaction Strategy:write-through [CACHE_MISS]: 326ms [OK]
 D  Cached delivery QdqJBMTjl5fqAumWVrJk with atomic cache system
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4257bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:400733ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:400733ms [CACHE_HIT] [POOR_UX]: 400733ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 400733ms (user-visible)
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1251852ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1251852ms [CACHE_HIT] [POOR_UX]: 1251852ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1251852ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4257bytes [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4257bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1193092ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1193092ms [CACHE_HIT] [POOR_UX]: 1193092ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1193092ms (user-visible)
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1716110ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1716110ms [CACHE_HIT] [POOR_UX]: 1716110ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1716110ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4257bytes [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4257bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1684056ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1684056ms [CACHE_HIT] [POOR_UX]: 1684056ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1684056ms (user-visible)
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription'
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  getUserSubscription: Verifying authentication for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserSubscription: Managing session for user myuivBnSjJe686W71qJTTuZsQet1
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  DEDUPLICATION: Found existing request for key 'session_creation_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Completed waiting for 'session_creation_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  getUserSubscription: Using session myuivBnSjJe686W71qJTTuZsQet1_1749824675933
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 1ms
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription'
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 2ms, result: true
 D  User profile cache warming successful in 3ms
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 3ms, result: true
 D  Subscription cache warming successful in 4ms
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 1ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1217bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 106ms, result: true
 D  Addresses cache warming successful in 107ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:3ms, subscription:success:4ms, addresses:success:107ms, total:108ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved 7 deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Found 7 deliveries in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, count: 7)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 2ms, result: true
 D  ✅ Deliveries loaded successfully: 5 (deduplicated from 7)
 D  hide(ime(), fromIme=false)
 I  com.autogratuity:800ed44d: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:a879d5d: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_REQUEST_HIDE_WITH_CONTROL fromUser false
 W  requestCursorUpdates on inactive InputConnection
 D  hide(ime(), fromIme=true)
 I  com.autogratuity:a879d5d: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 W  sendCancelIfRunning: isInProgress=false callback=ImeCallback=ImeOnBackInvokedCallback@255721015 Callback=android.window.IOnBackInvokedCallback$Stub$Proxy@4f8c8e9
 D  app_time_stats: avg=8.91ms min=1.37ms max=43.15ms count=60
 I  com.autogratuity:9a804e4b: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
 I  com.autogratuity:800ed44d: onHidden
 D  ✅ MODERN: Notification listener service is enabled
 D  ✅ MODERN: Health check completed successfully
 D  ✅ MODERN: Saved notification listener state: true
 D  app_time_stats: avg=11687.41ms min=15.40ms max=186747.11ms count=16
 D  ✅ MODERN DeliveryDialogViewModel initialized with domain models
 D  User session ended - clearing state
 D  User session ended - clearing dialog state
 D  Loading delivery details...
 D  app_time_stats: avg=128.96ms min=8.28ms max=1814.87ms count=16
 W  sendCancelIfRunning: isInProgress=false callback=androidx.activity.OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1@29e9416
 D  endAllActiveAnimators on 0x758d33afaaf0 (UnprojectedRipple) with handle 0x758ca3ad6750
 D  app_time_stats: avg=59.86ms min=1.96ms max=2703.14ms count=51
 D  User session ended - clearing state
 D  User session ended - clearing dialog state
 D  Loading delivery details...
 D  app_time_stats: avg=89.12ms min=8.04ms max=1183.77ms count=16
 W  sendCancelIfRunning: isInProgress=false callback=androidx.activity.OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1@381302e
 D  endAllActiveAnimators on 0x758d33b0e660 (UnprojectedRipple) with handle 0x758ca3aa2fd0
 D  app_time_stats: avg=50.23ms min=7.64ms max=1702.58ms count=51
 D  Navigating to destination: dashboard
 W  sendCancelIfRunning: isInProgress=false callback=androidx.activity.OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1@52d5a85
 D  🚀 HERO DASHBOARD: Starting render at 1749826063187
 D  User session ready - initializing dashboard data
 I  SYSTEM HEALTH CHECK: Starting comprehensive monitoring
 I  SYSTEM STATE:
 I  - Repository Initialized: true
 I  - User Session Ready: false
 I  - Core Data Ready: true
 I  SYSTEM COMPONENTS:
 I  - AuthManager: AuthenticationManagerImpl
 I  - EncryptionUtils: EncryptionUtils
 I  - FirebaseAuth: zzad
 I  - CacheLifecycleManager: CacheLifecycleManager
 D  🚀 MAP DATA LOADED: 3 addresses in 33ms
 D  🔍 STATE CHANGED: showAddressDetails = false, selectedAddressId = null
 D  🚀 INSTANT: Map data loaded, hiding skeleton immediately
 I  CACHE METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=7, completedDeliveries=3, pendingTips=0, totalCachedTips=215.64, uniqueUsers=1, uniqueAddresses=2, avgTipPerDelivery=30.805714285714284}, address={size=5, maxSize=500, metrics=CacheMetrics(hits=88, misses=1, puts=66, removes=0, expiredCleanups=0, totalOperations=89), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=2, addressesWithStats=5, avgTipsPerAddress=38.16}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=38, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=44), defaultTtl=1d, enableMetrics=true, hitRate=0.8636363636363636, missRate=0.13636363636363635, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=2, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=4), defaultTtl=1h, enableMetrics=true}, config={size=1, maxSize=500, metrics=CacheMetrics(hits=0, misses=1, puts=1, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=false, onboarding=false, dataCollection=false}}}
 D  📊 ENHANCED CACHE BREAKDOWN:
 D     🚚 DELIVERY CACHE:
 D        💰 Tips: $215.64 total, $30.81 avg
 D        📦 Deliveries: 3 completed, 7 total entries
 D        🎯 Performance: 100.0% hit rate
 D     👤 USER PROFILE CACHE:
 D        💰 Tips: $0.00 total, $0.00 per user
 D        👥 Users: 1 total entries
 D        🎯 Performance: 86.4% hit rate
 W        🚨 AGGREGATION ISSUE: Users cached but no aggregated tips
 D     🏠 ADDRESS CACHE:
 D        💰 Tips: $38.16 avg per address
 D        📍 Addresses: 5 with stats, 5 total
 D     📈 OVERALL CACHE HEALTH: 93.2% avg hit rate [EXCELLENT]
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 I  UI PERFORMANCE MONITORING: Started/Verified
 D  [SYSTEM_HEALTH] Repository:true UserSession:false CoreData:true
 D  [SYSTEM_HEALTH] Cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=7, completedDeliveries=3, pendingTips=0, totalCachedTips=215.64, uniqueUsers=1, uniqueAddresses=2, avgTipPerDelivery=30.805714285714284}, address={size=5, maxSize=500, metrics=CacheMetrics(hits=88, misses=1, puts=66, removes=0, expiredCleanups=0, totalOperations=89), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=2, addressesWithStats=5, avgTipsPerAddress=38.16}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=38, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=44), defaultTtl=1d, enableMetrics=true, hitRate=0.8636363636363636, missRate=0.13636363636363635, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=2, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=4), defaultTtl=1h, enableMetrics=true}, config={size=1, maxSize=500, metrics=CacheMetrics(hits=0, misses=1, puts=1, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=false, onboarding=false, dataCollection=false}}}
 I  SYSTEM HEALTH CHECK: Completed successfully
 D  preferredRenderer: null
 D  preferredRenderer: null
 I  Google Play services package version: 252037038
 I  Google Play services maps renderer version(maps_core): 251625202
 I  FpsProfiler MAIN created on main
 I  Map using legacy labeler
 I  Network fetching: false
 I  requestDrawingConfig for epoch 736 legend ROADMAP
 I  Network fetching: true
 I  Network fetching: true
 I  Network fetching: true
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 D  tagSocket(152) with statsTag=0x99e6744e, statsUid=-1
 D  tagSocket(160) with statsTag=0x99e6744e, statsUid=-1
 D  DEDUPLICATION: Found existing request for key 'session_creation_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  ✅ SESSION MANAGER: Created/retrieved session myuivBnSjJe686W71qJTTuZsQet1_1749824675933
 D  Repository initialization state: true
 D  UI frame monitoring started
 D  🔗 Auth state: User authenticated (myuivBnSjJe686W71qJTTuZsQet1)
 D  🚀 HERO PERFORMANCE: Starting priority cache warming for instant map
 D  Scheduling task DashboardMapCacheWarming with priority CRITICAL
 D  Executing task DashboardMapCacheWarming (CRITICAL)
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  🗺️ Testing simple geocoding for user: myuivBnSjJe686W71qJTTuZsQet1
 D  🧪 Testing native geocoding with: 1600 Amphitheatre Parkway, Mountain View, CA
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  🗺️ Loading addresses from repository for map display
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getCurrentUser: Authentication ready for user myuivBnSjJe686W71qJTTuZsQet1
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 7ms [OK]
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  loadPage: Loading address page for user myuivBnSjJe686W71qJTTuZsQet1 with size: 50, key: null
 D  loadAddressPage: Loading address page with size: 50, key: null
 D  loadAddressPage: Executing Firestore query for addresses
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription'
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription'
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:2 [EFFICIENT]: 0ms [OK]
 D  getUserSubscription: Verifying authentication for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserSubscription: Managing session for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Found existing request for key 'session_creation_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  getUserSubscription: Using session myuivBnSjJe686W71qJTTuZsQet1_1749824675933
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription'
 D  getUserSubscription: Verifying authentication for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserSubscription: Managing session for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Found existing request for key 'session_creation_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  getUserSubscription: Using session myuivBnSjJe686W71qJTTuZsQet1_1749824675933
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:myuivBnSjJe686W71qJTTuZsQet1' in 2ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription'
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 4ms, result: true
 D  Subscription cache warming successful in 5ms
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 19ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription'
 D  getUserSubscription: Verifying authentication for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserSubscription: Managing session for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Found existing request for key 'session_creation_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  getUserSubscription: Using session myuivBnSjJe686W71qJTTuZsQet1_1749824675933
 D  DEDUPLICATION: Found existing request for key 'getUserSubscription:myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getUserSubscription:myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription'
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 1ms, result: true
 D  Subscription cache warming successful in 1ms
 D  DEDUPLICATION: Found existing request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 19ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 31ms, result: true
 D  User profile cache warming successful in 31ms
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 24ms
 D  [cache_system.UserRepository] cache_breakdown Check:7ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 7ms [OK]
 D  [data.UserRepository] getCurrentUser(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 31ms [OK]
 D  Starting backpressure handling for dashboard_delivery_observation with THROTTLE_FIRST
 D  observeDeliveriesByUserId: Starting observation for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 32ms, result: true
 D  Subscription cache warming successful in 32ms
 D  DEDUPLICATION: Found existing request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1' in 19ms, result: true
 D  User profile cache warming successful in 19ms
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1' in 17ms, result: true
 D  User profile cache warming successful in 18ms
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:3 Size:1311bytes [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4257bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeUserProfile Load:93ms Steps:[repository_call,data_mapping,state_emission] UserDelay:93ms [CACHE_HIT] [FAST_UX]: 93ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:100ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:101ms [CACHE_HIT] [FAST_UX]: 100ms [OK]
 I  Initial labeling completed.
 D  🚀 MAP READY: Maps Compose initialized and ready
 D  🚀 MAP LOADED: 3 markers ready
 I  IncrementDisableThreadFlip blocked for 5.000ms
 D  🏠 HOME POSITION SET: lat/lng: (41.5871262,-93.8359044) at zoom 12.0
 D  🏠 HOME POSITION: Stored position lat/lng: (41.5871262,-93.8359044) at zoom 12.0
 D  [Firestore] QUERY user_addresses: 328ms [OK]
 D  loadAddressPage: Successfully loaded 5 DTOs (336ms total, query: 328ms)
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1237bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 I  Background concurrent mark compact GC freed 21MB AllocSpace bytes, 120(3404KB) LOS objects, 42% free, 32MB/56MB, paused 1.823ms,9.695ms total 153.117ms
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1237bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1237bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed waiting for 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 388ms, result: true
 D  Addresses cache warming successful in 389ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  DEDUPLICATION: Completed waiting for 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 379ms, result: true
 D  Addresses cache warming successful in 379ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:19ms, subscription:success:1ms, addresses:success:389ms, total:408ms
 D  getCurrentUser: Cache warming initiated for user myuivBnSjJe686W71qJTTuZsQet1
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 2ms
 D  loadPage: Successfully loaded 5 addresses
 D  [data.AddressPageLoader] loadPage(Address) User:myuivBnSjJe686W71qJTTuZsQet1 Count:5 Source:RemoteDataSource Strategy:none [CACHE_MISS]: 412ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:2ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 2ms [OK]
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 415ms, result: true
 D  Addresses cache warming successful in 421ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:31ms, subscription:success:32ms, addresses:success:421ms, total:426ms
 D  🗺️ MAP-CRITICAL WARMING COMPLETE: 432ms - addresses: Success(cachedAt=1749826063722, durationMs=421), user: Success(cachedAt=1749826063334, durationMs=31)
 D  🎯 SIMPLE GEOCODING: Testing native geocoding capability
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:18ms, subscription:success:5ms, addresses:success:379ms, total:421ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  Native geocoding success: 1600 Amphitheatre Parkway, Mountain View, CA -> 37.4220094, -122.0847525
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved 7 deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Found 7 deliveries in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, count: 7)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 7ms, result: true
 D  Fallback: Loaded 5 addresses from repository
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:TODAY User:stats_calculation Size:207bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:MONTH User:stats_calculation Size:207bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:WEEK User:stats_calculation Size:206bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  🚀 MAP DATA LOADED: 5 addresses in 573ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1237bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  🗺️ INSTANT MAP DATA: Loaded 5 addresses from warmed cache
 D  Complete cache warming: 504ms total (warming: 432ms)
 D  Task DashboardMapCacheWarming completed in 525ms
 D  🚀 PRIORITY TASK SUCCESS: Cache warming and maps pre-fetch completed successfully
 D  🚀 BACKGROUND REFRESH: Skipping update, data already loaded
 D  app_time_stats: avg=10.52ms min=1.82ms max=52.91ms count=56
 D  🏠 HOME POSITION SET: lat/lng: (41.5871262,-93.75568379999999) at zoom 12.0
 D  tagSocket(193) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(205) with statsTag=0xffffffff, statsUid=-1
 D  tagSocket(204) with statsTag=0xffffffff, statsUid=-1
 D  🏠 HOME POSITION: Stored position lat/lng: (41.5871262,-93.75568379999999) at zoom 12.0
 D  app_time_stats: avg=29.38ms min=1.74ms max=250.66ms count=34
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  loadPage: Loading address page for user myuivBnSjJe686W71qJTTuZsQet1 with size: 50, key: null
 D  loadAddressPage: Loading address page with size: 50, key: null
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  loadAddressPage: Executing Firestore query for addresses
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  Subscription cache warming successful in 1ms
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  [cache_system.UserRepository] cache_breakdown Check:1ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 1ms [OK]
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 4ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 2ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 8ms, result: true
 D  User profile cache warming successful in 9ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  loadAddressPage: Successfully loaded 5 DTOs (110ms total, query: 105ms)
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [Firestore] QUERY user_addresses: 105ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1237bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1237bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  PII Fields Processed: 10
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 122ms, result: true
 D  Addresses cache warming successful in 123ms
 D  Mapping Duration: 0ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:9ms, subscription:success:1ms, addresses:success:123ms, total:124ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Retrieved 7 deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Found 7 deliveries in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, count: 7)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [data.AddressPageLoader] loadPage(Address) User:myuivBnSjJe686W71qJTTuZsQet1 Count:5 Source:RemoteDataSource Strategy:none [CACHE_MISS]: 127ms [OK]
 D  loadPage: Successfully loaded 5 addresses
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:TODAY User:stats_calculation Size:207bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:WEEK User:stats_calculation Size:206bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:MONTH User:stats_calculation Size:207bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  🚀 MAP DATA LOADED: 5 addresses in 1394ms
 D  🏠 HOME POSITION SET: lat/lng: (41.5871262,-93.75568379999999) at zoom 12.0
 D  🏠 HOME POSITION: Stored position lat/lng: (41.5871262,-93.75568379999999) at zoom 12.0
 D  app_time_stats: avg=37.56ms min=8.54ms max=1169.09ms count=55
 D  app_time_stats: avg=253.11ms min=13.62ms max=5934.97ms count=25
 
   D  ✅ MODERN AddEditDeliveryViewModel initialized
 D  [presentation.AddEditDeliveryViewModel] init Deps:4 Data:new_delivery_created State:Initialized [STATEFLOW_UPDATED]: 0ms [OK]
 D  tagSocket(148) with statsTag=0xffffffff, statsUid=-1
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  LaunchedEffect triggered but selectedPlace is null
 D  tagSocket(225) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.AddEditDeliveryViewModel] stateflow_emission Flow:delivery Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  Subscription cache warming successful in 0ms
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 0ms [OK]
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 5ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 12ms, result: true
 D  User profile cache warming successful in 13ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1237bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1237bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Loaded 5 addresses
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 127ms, result: true
 D  Addresses cache warming successful in 128ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:13ms, subscription:success:0ms, addresses:success:128ms, total:129ms
 D  DEDUPLICATION: Starting new request for key 'getDelivery_QdqJBMTjl5fqAumWVrJk'
 D  Cache hit for delivery QdqJBMTjl5fqAumWVrJk
 D  Retrieved delivery QdqJBMTjl5fqAumWVrJk for user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getDeliveryById: Found delivery QdqJBMTjl5fqAumWVrJk in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getDeliveryById: Cache hit for delivery QdqJBMTjl5fqAumWVrJk (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDelivery_QdqJBMTjl5fqAumWVrJk'
 D  [data.DeliveryRepository] getDeliveryById(Delivery) ID:QdqJBMTjl5fqAumWVrJk User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:930bytes Source:cache Strategy:cache-first [CACHE_HIT]: 130ms [OK]
 D  DEDUPLICATION: Completed new request 'getDelivery_QdqJBMTjl5fqAumWVrJk' in 1ms, result: true
 D  Updated selected place from loaded delivery
 D  LaunchedEffect triggered with selectedPlace: 222 5th Ave, Des Moines, IA 50309, USA
 D  Address text updated to: 222 5th Ave, Des Moines, IA 50309, USA
 D  For capability in capabilities, log:
"AdvancedMarkers: false: Capabilities unavailable without a Map ID."Data-driven styling: false
 W  Destroying egl context
 I  Trimming OTHER: SingleLabelPickEntityPool's current size 3 to 0.00000, or 0
 W  Shutting down renderer while it's not idle - phase is INVALID
 I  Trimming OTHER: LoggingOp's current size 33 to 0.00000, or 0
 I  Trimming LINE_LABELS's current size 2 to 0.00000, or 0
 I  Trimming OTHER: NativeTessellators's current size 1 to 0.00000, or 0
 I  Trimming POINTS_LABELS's current size 39 to 0.00000, or 0
 I  Trimming OTHER: VertexBuilders's current size 1 to 0.00000, or 0
 I  Trimming OTHER: LabelSourceOp's current size 3 to 0.00000, or 0
 D  show(ime(), fromIme=false)
 I  com.autogratuity:abc8ac0f: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
 D  app_time_stats: avg=64.64ms min=2.68ms max=2087.55ms count=45
 I  Flattened final assist data: 848 bytes, containing 1 windows, 6 views
 I  com.autogratuity:cf0d52b2: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  Autofill popup isn't shown because autofill is not available.

Did you set up autofill?
1. Go to Settings > System > Languages&input > Advanced > Autofill Service
2. Pick a service

Did you add an account?
1. Go to Settings > System > Languages&input > Advanced
2. Click on the settings icon next to the Autofill Service
3. Add your account
 D  show(ime(), fromIme=true)
 I  com.autogratuity:cf0d52b2: onShown
 D  app_time_stats: avg=21.33ms min=1.21ms max=484.15ms count=31
 W  sendCancelIfRunning: isInProgress=false callback=androidx.compose.ui.window.Api33Impl$$ExternalSyntheticLambda0@a77b088
 D  app_time_stats: avg=66.85ms min=3.84ms max=491.11ms count=15
 D  app_time_stats: avg=454.00ms min=363.43ms max=499.33ms count=3
 D  app_time_stats: avg=500.01ms min=499.50ms max=500.52ms count=2
 D  app_time_stats: avg=500.35ms min=500.14ms max=500.57ms count=2
 D  app_time_stats: avg=507.61ms min=499.31ms max=515.92ms count=2
 D  app_time_stats: avg=500.56ms min=500.49ms max=500.62ms count=2
 D  app_time_stats: avg=499.80ms min=498.26ms max=501.14ms count=3
 D  app_time_stats: avg=267.09ms min=12.05ms max=501.11ms count=4
 D  app_time_stats: avg=44.04ms min=12.63ms max=497.45ms count=23
 D  getOrderIdErrorMessage: input='*********', error='null'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  🔧 PRESERVING EXISTING ADDRESS: 222 5th Ave, Des Moines, IA 50309, USA... (ID: OMzXiC4kUENI4sydIr5P)
 D  🔒 PRESERVING IMMUTABLE ORDER ID: *********
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'updateDelivery_QdqJBMTjl5fqAumWVrJk'
 D  validateDelivery: Validating delivery QdqJBMTjl5fqAumWVrJk
 D  validateDelivery: Delivery QdqJBMTjl5fqAumWVrJk is valid
 D  CONVERSION: Delivery SSoT -> DTO | ID: QdqJBMTjl5fqAumWVrJk | User: myuivBnSjJe686W71qJTTuZsQet1
 D    Input: orderId=********* | status=CREATED | tipAmount=null
 W    ⚠️ CLOUD FUNCTION WARNING: Delivery QdqJBMTjl5fqAumWVrJk missing tipAmount - will be skipped for tip counting
 W    ⚠️ CLOUD FUNCTION WARNING: Delivery QdqJBMTjl5fqAumWVrJk missing completedAt - may have sorting issues
 D    Converting address: 222 5th Ave, Des Moines, IA 50309, USA... (ID: OMzXiC4kUENI4sydIr5P)
 D    Created missing reference with addressId: OMzXiC4kUENI4sydIr5P
 D  === DELIVERY SSoT -> DTO TRANSFORMATION ===
 D  Input SSoT State: {ssot_id=QdqJBMTjl5fqAumWVrJk, ssot_userId=myuivBnSjJe686W71qJTTuZsQet1, ssot_orderId=*********, ssot_tipAmount=null, ssot_status=CREATED, ssot_notes=empty, ssot_addressId=OMzXiC4kUENI4sydIr5P}
 D  Output DTO State: {dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_orderId=*********, dto_tipAmount=0.0, dto_status=CREATED, dto_notes=empty, dto_addressId=OMzXiC4kUENI4sydIr5P}
 D  Field Transformations: [notes: ENCRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, times: TIMESTAMP_MAPPING]
 D  Business Logic Applied: [notes_encryption, field_validation, address_plain_mapping, status_mapping, amounts_calculation]
 D  PII Fields Encrypted: 0
 D  Mapping Duration: 0ms
 D  CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: QdqJBMTjl5fqAumWVrJk
 D    Output: encrypted_fields=0 | validation_errors=0 | duration=0ms
 D    DTO size: 1103 bytes
 D    Business logic: address_mapping, status_mapping, amounts_calculation
 D  executeUpdateDeliveryOperation: Updating delivery QdqJBMTjl5fqAumWVrJk with transaction for user myuivBnSjJe686W71qJTTuZsQet1
 D  [DeliveryMapper] toDto Delivery ID:QdqJBMTjl5fqAumWVrJk User:myuivBnSjJe686W71qJTTuZsQet1 Size:1103bytes Delivery: 0ms [OK]
 D  Transaction: Updated delivery QdqJBMTjl5fqAumWVrJk with new deliveryData
 I  executeUpdateDeliveryOperation: Delivery QdqJBMTjl5fqAumWVrJk updated with transaction for user myuivBnSjJe686W71qJTTuZsQet1
 D  Saved delivery QdqJBMTjl5fqAumWVrJk for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'updateDelivery_QdqJBMTjl5fqAumWVrJk'
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Completed new request 'updateDelivery_QdqJBMTjl5fqAumWVrJk' in 218ms, result: true
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  Cached delivery QdqJBMTjl5fqAumWVrJk with atomic cache system
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [data.DeliveryRepository] updateDelivery(Delivery) ID:QdqJBMTjl5fqAumWVrJk User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1151bytes Source:firestore_transaction Strategy:write-through [CACHE_MISS]: 218ms [OK]
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:23995ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:23995ms [CACHE_HIT] [POOR_UX]: 23995ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 23995ms (user-visible)
 I  Ignoring popBackStack to route deliveries as it was not found on the current back stack
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4059bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1934383ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1934383ms [CACHE_HIT] [POOR_UX]: 1934383ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1934383ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4059bytes [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4059bytes [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4059bytes [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4059bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:619012ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:619012ms [CACHE_HIT] [POOR_UX]: 619012ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4059bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1902328ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1902328ms [CACHE_HIT] [POOR_UX]: 1902328ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 619012ms (user-visible)
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1411367ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1411367ms [CACHE_HIT] [POOR_UX]: 1411367ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1411367ms (user-visible)
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1902328ms (user-visible)
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1470131ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1470131ms [CACHE_HIT] [POOR_UX]: 1470131ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1470131ms (user-visible)
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  Subscription cache warming successful in 0ms
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 2ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 2ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 4ms, result: true
 D  User profile cache warming successful in 5ms
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  app_time_stats: avg=39.10ms min=12.02ms max=382.54ms count=26
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1237bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 114ms, result: true
 D  Addresses cache warming successful in 115ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:5ms, subscription:success:0ms, addresses:success:115ms, total:120ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved 7 deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Found 7 deliveries in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, count: 7)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 1ms, result: true
 D  ✅ Deliveries loaded successfully: 5 (deduplicated from 7)
 D  hide(ime(), fromIme=false)
 I  com.autogratuity:9c5d32a8: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:754e493f: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_REQUEST_HIDE_WITH_CONTROL fromUser false
 W  requestCursorUpdates on inactive InputConnection
 D  hide(ime(), fromIme=true)
 I  com.autogratuity:754e493f: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 W  sendCancelIfRunning: isInProgress=false callback=ImeCallback=ImeOnBackInvokedCallback@255721015 Callback=android.window.IOnBackInvokedCallback$Stub$Proxy@7c6c820
 I  com.autogratuity:4277aace: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
 I  com.autogratuity:9c5d32a8: onHidden
 D  app_time_stats: avg=17.17ms min=10.51ms max=38.00ms count=58
 D  app_time_stats: avg=73364.24ms min=73364.24ms max=73364.24ms count=1
 D  Navigating to destination: dashboard
 D  app_time_stats: avg=22.26ms min=12.79ms max=315.35ms count=53
 D  Navigating to destination: dashboard
 D  Navigating to destination: deliveries
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  Subscription cache warming successful in 0ms
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 4ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 6ms, result: true
 D  User profile cache warming successful in 9ms
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 1ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 250ms, result: true
 D  Addresses cache warming successful in 250ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:9ms, subscription:success:0ms, addresses:success:250ms, total:252ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved 7 deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Found 7 deliveries in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, count: 7)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 1ms, result: true
 D  ✅ Deliveries loaded successfully: 5 (deduplicated from 7)
 D  app_time_stats: avg=16.60ms min=8.39ms max=22.64ms count=61
 D  Navigating to destination: deliveries
 D  app_time_stats: avg=20.41ms min=13.91ms max=200.38ms count=49
 D  Navigating to destination: dashboard
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  Subscription cache warming successful in 0ms
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 6ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 5ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 7ms, result: true
 D  User profile cache warming successful in 8ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 156ms, result: true
 D  Addresses cache warming successful in 156ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:8ms, subscription:success:0ms, addresses:success:156ms, total:157ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved 7 deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Found 7 deliveries in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, count: 7)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 1ms, result: true
 D  ✅ Deliveries loaded successfully: 5 (deduplicated from 7)
 D  app_time_stats: avg=22.70ms min=13.58ms max=367.25ms count=57
 D  Navigating to destination: deliveries
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 1ms, result: true
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  Subscription cache warming successful in 2ms
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 6ms
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 2ms [OK]
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 7ms, result: true
 D  User profile cache warming successful in 7ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 106ms, result: true
 D  Addresses cache warming successful in 106ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:7ms, subscription:success:2ms, addresses:success:106ms, total:108ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved 7 deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Found 7 deliveries in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, count: 7)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 2ms, result: true
 D  ✅ Deliveries loaded successfully: 5 (deduplicated from 7)
 W  sendCancelIfRunning: isInProgress=false callback=androidx.activity.OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1@52d5a85
 D  🚀 HERO DASHBOARD: Starting render at 1749826167653
 D  User session ready - initializing dashboard data
 I  SYSTEM HEALTH CHECK: Starting comprehensive monitoring
 I  SYSTEM STATE:
 I  - Repository Initialized: true
 I  - User Session Ready: false
 I  - Core Data Ready: true
 D  🚀 MAP DATA LOADED: 5 addresses in 21ms
 I  SYSTEM COMPONENTS:
 I  - AuthManager: AuthenticationManagerImpl
 I  - EncryptionUtils: EncryptionUtils
 D  🔍 STATE CHANGED: showAddressDetails = false, selectedAddressId = null
 I  - FirebaseAuth: zzad
 I  - CacheLifecycleManager: CacheLifecycleManager
 D  🚀 INSTANT: Map data loaded, hiding skeleton immediately
 D  app_time_stats: avg=27.00ms min=10.84ms max=570.07ms count=53
 I  CACHE METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=7, completedDeliveries=2, pendingTips=0, totalCachedTips=146.64, uniqueUsers=1, uniqueAddresses=2, avgTipPerDelivery=20.948571428571427}, address={size=5, maxSize=500, metrics=CacheMetrics(hits=148, misses=1, puts=116, removes=0, expiredCleanups=0, totalOperations=149), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=3, addressesWithStats=5, avgTipsPerAddress=7.32}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=46, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=52), defaultTtl=1d, enableMetrics=true, hitRate=0.8846153846153846, missRate=0.11538461538461539, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=2, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=4), defaultTtl=1h, enableMetrics=true}, config={size=1, maxSize=500, metrics=CacheMetrics(hits=0, misses=1, puts=1, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=false, onboarding=false, dataCollection=false}}}
 D  📊 ENHANCED CACHE BREAKDOWN:
 D     🚚 DELIVERY CACHE:
 D        💰 Tips: $146.64 total, $20.95 avg
 D        📦 Deliveries: 2 completed, 7 total entries
 D        🎯 Performance: 100.0% hit rate
 D     👤 USER PROFILE CACHE:
 D        💰 Tips: $0.00 total, $0.00 per user
 D        👥 Users: 1 total entries
 D        🎯 Performance: 88.5% hit rate
 W        🚨 AGGREGATION ISSUE: Users cached but no aggregated tips
 D     🏠 ADDRESS CACHE:
 D        💰 Tips: $7.32 avg per address
 D        📍 Addresses: 5 with stats, 5 total
 D     📈 OVERALL CACHE HEALTH: 94.2% avg hit rate [EXCELLENT]
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 I  UI PERFORMANCE MONITORING: Started/Verified
 D  preferredRenderer: null
 D  [SYSTEM_HEALTH] Repository:true UserSession:false CoreData:true
 D  preferredRenderer: null
 D  [SYSTEM_HEALTH] Cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=7, completedDeliveries=2, pendingTips=0, totalCachedTips=146.64, uniqueUsers=1, uniqueAddresses=2, avgTipPerDelivery=20.948571428571427}, address={size=5, maxSize=500, metrics=CacheMetrics(hits=148, misses=1, puts=116, removes=0, expiredCleanups=0, totalOperations=149), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=3, addressesWithStats=5, avgTipsPerAddress=7.32}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=46, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=52), defaultTtl=1d, enableMetrics=true, hitRate=0.8846153846153846, missRate=0.11538461538461539, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=2, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=4), defaultTtl=1h, enableMetrics=true}, config={size=1, maxSize=500, metrics=CacheMetrics(hits=0, misses=1, puts=1, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=false, onboarding=false, dataCollection=false}}}
 I  SYSTEM HEALTH CHECK: Completed successfully
 I  Google Play services package version: 252037038
 I  Google Play services maps renderer version(maps_core): 251625202
 I  FpsProfiler MAIN created on main
 I  Map using legacy labeler
 I  Network fetching: false
 I  requestDrawingConfig for epoch 736 legend ROADMAP
 I  Network fetching: true
 I  Network fetching: true
 I  Network fetching: true
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 D  DEDUPLICATION: Found existing request for key 'session_creation_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  ✅ SESSION MANAGER: Created/retrieved session myuivBnSjJe686W71qJTTuZsQet1_1749824675933
 D  Repository initialization state: true
 D  UI frame monitoring started
 D  🔗 Auth state: User authenticated (myuivBnSjJe686W71qJTTuZsQet1)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:2 [EFFICIENT]: 0ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getCurrentUser: Authentication ready for user myuivBnSjJe686W71qJTTuZsQet1
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  🚀 HERO PERFORMANCE: Starting priority cache warming for instant map
 D  Scheduling task DashboardMapCacheWarming with priority CRITICAL
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  Executing task DashboardMapCacheWarming (CRITICAL)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  loadPage: Loading address page for user myuivBnSjJe686W71qJTTuZsQet1 with size: 50, key: null
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  loadAddressPage: Loading address page with size: 50, key: null
 D  loadAddressPage: Executing Firestore query for addresses
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 14ms, result: true
 D  Subscription cache warming successful in 15ms
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 10ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 24ms, result: true
 D  User profile cache warming successful in 25ms
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1' in 15ms, result: true
 D  User profile cache warming successful in 16ms
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1' in 22ms, result: true
 D  User profile cache warming successful in 23ms
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 18ms [OK]
 D  DEDUPLICATION: Found existing request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 7ms [OK]
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  🗺️ Testing simple geocoding for user: myuivBnSjJe686W71qJTTuZsQet1
 D  🧪 Testing native geocoding with: 1600 Amphitheatre Parkway, Mountain View, CA
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 29ms, result: true
 D  Subscription cache warming successful in 29ms
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 25ms
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  🗺️ Loading addresses from repository for map display
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 21ms, result: true
 D  Subscription cache warming successful in 21ms
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  [data.UserRepository] getCurrentUser(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 36ms [OK]
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  Starting backpressure handling for dashboard_delivery_observation with THROTTLE_FIRST
 D  observeDeliveriesByUserId: Starting observation for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeUserProfile Load:55ms Steps:[repository_call,data_mapping,state_emission] UserDelay:55ms [CACHE_HIT] [FAST_UX]: 55ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:3 Size:1311bytes [EFFICIENT]: 0ms [OK]
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:96ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:96ms [CACHE_HIT] [FAST_UX]: 96ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4059bytes [EFFICIENT]: 0ms [OK]
 D  [Firestore] QUERY user_addresses: 158ms [OK]
 D  loadAddressPage: Successfully loaded 5 DTOs (166ms total, query: 158ms)
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 9ms
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:9ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 9ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  loadPage: Successfully loaded 5 addresses
 D  [data.AddressPageLoader] loadPage(Address) User:myuivBnSjJe686W71qJTTuZsQet1 Count:5 Source:RemoteDataSource Strategy:none [CACHE_MISS]: 230ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  Fallback: Loaded 5 addresses from repository
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 235ms, result: true
 D  Addresses cache warming successful in 236ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:23ms, subscription:success:15ms, addresses:success:236ms, total:240ms
 D  getCurrentUser: Cache warming initiated for user myuivBnSjJe686W71qJTTuZsQet1
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 243ms, result: true
 D  Addresses cache warming successful in 245ms
 D  DEDUPLICATION: Completed waiting for 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 233ms, result: true
 D  🚀 MAP DATA LOADED: 5 addresses in 330ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:25ms, subscription:success:29ms, addresses:success:245ms, total:249ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Addresses cache warming successful in 240ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:16ms, subscription:success:21ms, addresses:success:240ms, total:256ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved 7 deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Found 7 deliveries in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, count: 7)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  🗺️ MAP-CRITICAL WARMING COMPLETE: 260ms - addresses: Success(cachedAt=1749826167981, durationMs=245), user: Success(cachedAt=1749826167758, durationMs=25)
 D  🎯 SIMPLE GEOCODING: Testing native geocoding capability
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:WEEK User:stats_calculation Size:200bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:TODAY User:stats_calculation Size:201bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:MONTH User:stats_calculation Size:201bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 I  Initial labeling completed.
 D  🚀 MAP DATA LOADED: 5 addresses in 362ms
 D  🚀 MAP READY: Maps Compose initialized and ready
 D  🚀 MAP LOADED: 5 markers ready
 D  Native geocoding success: 1600 Amphitheatre Parkway, Mountain View, CA -> 37.4220094, -122.0847525
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 1ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:1ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 1ms [OK]
 D  🗺️ INSTANT MAP DATA: Loaded 5 addresses from warmed cache
 D  Complete cache warming: 385ms total (warming: 260ms)
 D  Task DashboardMapCacheWarming completed in 392ms
 D  🚀 PRIORITY TASK SUCCESS: Cache warming and maps pre-fetch completed successfully
 D  🚀 MAP DATA LOADED: 5 addresses in 472ms
 D  🚀 BACKGROUND REFRESH: Skipping update, data already loaded
 D  🏠 HOME POSITION SET: lat/lng: (41.5871262,-93.75568379999999) at zoom 12.0
 D  🏠 HOME POSITION: Stored position lat/lng: (41.5871262,-93.75568379999999) at zoom 12.0
 D  app_time_stats: avg=21.98ms min=1.35ms max=464.74ms count=42
 D  app_time_stats: avg=1934.00ms min=1934.00ms max=1934.00ms count=1
 D  app_time_stats: avg=253.11ms min=12.85ms max=2380.57ms count=10
 W  Destroying egl surface
 D  ✅ MODERN AddEditDeliveryViewModel initialized
 D  [presentation.AddEditDeliveryViewModel] init Deps:4 Data:new_delivery_created State:Initialized [STATEFLOW_UPDATED]: 0ms [OK]
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  LaunchedEffect triggered but selectedPlace is null
 D  tagSocket(122) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  [reactive_performance.AddEditDeliveryViewModel] stateflow_emission Flow:delivery Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 1ms, result: true
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Subscription cache warming successful in 12ms
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 9ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 8ms [OK]
 D  tagSocket(163) with statsTag=0xffffffff, statsUid=-1
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 17ms, result: true
 D  User profile cache warming successful in 17ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 96ms, result: true
 D  Addresses cache warming successful in 97ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:17ms, subscription:success:12ms, addresses:success:97ms, total:100ms
 D  DEDUPLICATION: Starting new request for key 'getDelivery_QdqJBMTjl5fqAumWVrJk'
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Cache hit for delivery QdqJBMTjl5fqAumWVrJk
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Retrieved delivery QdqJBMTjl5fqAumWVrJk for user myuivBnSjJe686W71qJTTuZsQet1: true
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getDeliveryById: Found delivery QdqJBMTjl5fqAumWVrJk in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getDeliveryById: Cache hit for delivery QdqJBMTjl5fqAumWVrJk (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDelivery_QdqJBMTjl5fqAumWVrJk'
 D  DEDUPLICATION: Completed new request 'getDelivery_QdqJBMTjl5fqAumWVrJk' in 11ms, result: true
 D  [data.DeliveryRepository] getDeliveryById(Delivery) ID:QdqJBMTjl5fqAumWVrJk User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:732bytes Source:cache Strategy:cache-first [CACHE_HIT]: 110ms [OK]
 D  Loaded 5 addresses
 D  Updated selected place from loaded delivery
 D  LaunchedEffect triggered with selectedPlace: 222 5th Ave, Des Moines, IA 50309, USA
 D  Address text updated to: 222 5th Ave, Des Moines, IA 50309, USA
 D  For capability in capabilities, log:
"AdvancedMarkers: false: Capabilities unavailable without a Map ID."Data-driven styling: false
 W  Destroying egl context
 I  Trimming OTHER: SingleLabelPickEntityPool's current size 1 to 0.00000, or 0
 W  Shutting down renderer while it's not idle - phase is INVALID
 I  Trimming OTHER: NativeTessellators's current size 1 to 0.00000, or 0
 I  Trimming OTHER: LabelSourceOp's current size 4 to 0.00000, or 0
 I  Trimming LINE_LABELS's current size 1 to 0.00000, or 0
 I  Trimming OTHER: LoggingOp's current size 42 to 0.00000, or 0
 I  Trimming OTHER: VertexBuilders's current size 1 to 0.00000, or 0
 I  Trimming POINTS_LABELS's current size 27 to 0.00000, or 0
 D  app_time_stats: avg=81.77ms min=1.97ms max=3301.61ms count=45
 D  app_time_stats: avg=38.45ms min=13.70ms max=518.69ms count=26
 D  app_time_stats: avg=24.63ms min=13.50ms max=500.45ms count=60
 D  getOrderIdErrorMessage: input='*********', error='null'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  🔧 PRESERVING EXISTING ADDRESS: 222 5th Ave, Des Moines, IA 50309, USA... (ID: OMzXiC4kUENI4sydIr5P)
 D  🔒 PRESERVING IMMUTABLE ORDER ID: *********
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'updateDelivery_QdqJBMTjl5fqAumWVrJk'
 D  validateDelivery: Validating delivery QdqJBMTjl5fqAumWVrJk
 D  validateDelivery: Delivery QdqJBMTjl5fqAumWVrJk is valid
 D  CONVERSION: Delivery SSoT -> DTO | ID: QdqJBMTjl5fqAumWVrJk | User: myuivBnSjJe686W71qJTTuZsQet1
 D    Input: orderId=********* | status=CREATED | tipAmount=null
 W    ⚠️ CLOUD FUNCTION WARNING: Delivery QdqJBMTjl5fqAumWVrJk missing tipAmount - will be skipped for tip counting
 W    ⚠️ CLOUD FUNCTION WARNING: Delivery QdqJBMTjl5fqAumWVrJk missing completedAt - may have sorting issues
 D    Converting address: 222 5th Ave, Des Moines, IA 50309, USA... (ID: OMzXiC4kUENI4sydIr5P)
 D    Created missing reference with addressId: OMzXiC4kUENI4sydIr5P
 D  === DELIVERY SSoT -> DTO TRANSFORMATION ===
 D  Input SSoT State: {ssot_id=QdqJBMTjl5fqAumWVrJk, ssot_userId=myuivBnSjJe686W71qJTTuZsQet1, ssot_orderId=*********, ssot_tipAmount=null, ssot_status=CREATED, ssot_notes=empty, ssot_addressId=OMzXiC4kUENI4sydIr5P}
 D  Output DTO State: {dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_orderId=*********, dto_tipAmount=0.0, dto_status=CREATED, dto_notes=empty, dto_addressId=OMzXiC4kUENI4sydIr5P}
 D  Field Transformations: [notes: ENCRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, times: TIMESTAMP_MAPPING]
 D  Business Logic Applied: [notes_encryption, field_validation, address_plain_mapping, status_mapping, amounts_calculation]
 D  PII Fields Encrypted: 0
 D  Mapping Duration: 0ms
 D  CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: QdqJBMTjl5fqAumWVrJk
 D    Output: encrypted_fields=0 | validation_errors=0 | duration=0ms
 D    DTO size: 1103 bytes
 D    Business logic: address_mapping, status_mapping, amounts_calculation
 D  executeUpdateDeliveryOperation: Updating delivery QdqJBMTjl5fqAumWVrJk with transaction for user myuivBnSjJe686W71qJTTuZsQet1
 D  [DeliveryMapper] toDto Delivery ID:QdqJBMTjl5fqAumWVrJk User:myuivBnSjJe686W71qJTTuZsQet1 Size:1103bytes Delivery: 0ms [OK]
 D  Transaction: Updated delivery QdqJBMTjl5fqAumWVrJk with new deliveryData
 I  executeUpdateDeliveryOperation: Delivery QdqJBMTjl5fqAumWVrJk updated with transaction for user myuivBnSjJe686W71qJTTuZsQet1
 D  Saved delivery QdqJBMTjl5fqAumWVrJk for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'updateDelivery_QdqJBMTjl5fqAumWVrJk'
 D  [data.DeliveryRepository] updateDelivery(Delivery) ID:QdqJBMTjl5fqAumWVrJk User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1151bytes Source:firestore_transaction Strategy:write-through [CACHE_MISS]: 100ms [OK]
 D  DEDUPLICATION: Completed new request 'updateDelivery_QdqJBMTjl5fqAumWVrJk' in 101ms, result: true
 I  Ignoring popBackStack to route deliveries as it was not found on the current back stack
 D  Cached delivery QdqJBMTjl5fqAumWVrJk with atomic cache system
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  Subscription cache warming successful in 0ms
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 5ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  [cache_system.UserRepository] cache_breakdown Check:3ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 3ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 6ms, result: true
 D  User profile cache warming successful in 6ms
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 4ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 105ms, result: true
 D  Addresses cache warming successful in 106ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:6ms, subscription:success:0ms, addresses:success:106ms, total:107ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved 7 deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Found 7 deliveries in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, count: 7)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 2ms, result: true
 D  ✅ Deliveries loaded successfully: 5 (deduplicated from 7)
 D  app_time_stats: avg=29.23ms min=8.91ms max=664.67ms count=54
 D  ✅ MODERN DeliveryDialogViewModel initialized with domain models
 D  User session ended - clearing state
 D  User session ended - clearing dialog state
 D  Loading delivery details...
 
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  Updated selected place from loaded delivery
 D  LaunchedEffect triggered with selectedPlace: 222 5th Ave, Des Moines, IA 50309, USA
 D  Address text updated to: 222 5th Ave, Des Moines, IA 50309, USA
 D  tagSocket(172) with statsTag=0xffffffff, statsUid=-1
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.AddEditDeliveryViewModel] stateflow_emission Flow:delivery Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 2ms, result: true
 D  Subscription cache warming successful in 2ms
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  [cache_system.UserRepository] cache_breakdown Check:1ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 1ms [OK]
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 7ms [OK]
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 11ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 17ms, result: true
 D  User profile cache warming successful in 19ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 129ms, result: true
 D  Addresses cache warming successful in 130ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:19ms, subscription:success:2ms, addresses:success:130ms, total:132ms
 D  PII Fields Processed: 10
 D  Mapping Duration: 5ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:5ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 5ms [OK]
 D  Loaded 5 addresses
 D  DEDUPLICATION: Starting new request for key 'getDelivery_QdqJBMTjl5fqAumWVrJk'
 D  Cache hit for delivery QdqJBMTjl5fqAumWVrJk
 D  Retrieved delivery QdqJBMTjl5fqAumWVrJk for user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getDeliveryById: Found delivery QdqJBMTjl5fqAumWVrJk in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getDeliveryById: Cache hit for delivery QdqJBMTjl5fqAumWVrJk (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDelivery_QdqJBMTjl5fqAumWVrJk'
 D  [data.DeliveryRepository] getDeliveryById(Delivery) ID:QdqJBMTjl5fqAumWVrJk User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:732bytes Source:cache Strategy:cache-first [CACHE_HIT]: 135ms [OK]
 D  DEDUPLICATION: Completed new request 'getDelivery_QdqJBMTjl5fqAumWVrJk' in 2ms, result: true
 D  Updated selected place from loaded delivery
 W  sendCancelIfRunning: isInProgress=false callback=androidx.activity.OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1@52d5a85
 D  🚀 HERO DASHBOARD: Starting render at 1749826185002
 D  User session ready - initializing dashboard data
 D  🚀 MAP DATA LOADED: 5 addresses in 22ms
 D  🔍 STATE CHANGED: showAddressDetails = false, selectedAddressId = null
 I  SYSTEM HEALTH CHECK: Starting comprehensive monitoring
 I  SYSTEM STATE:
 I  - Repository Initialized: true
 I  - User Session Ready: false
 I  - Core Data Ready: true
 I  SYSTEM COMPONENTS:
 I  - AuthManager: AuthenticationManagerImpl
 I  - EncryptionUtils: EncryptionUtils
 I  - FirebaseAuth: zzad
 D  🚀 INSTANT: Map data loaded, hiding skeleton immediately
 I  - CacheLifecycleManager: CacheLifecycleManager
 D  app_time_stats: avg=22.79ms min=1.38ms max=786.50ms count=43
 I  CACHE METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=7, completedDeliveries=2, pendingTips=0, totalCachedTips=146.64, uniqueUsers=1, uniqueAddresses=2, avgTipPerDelivery=20.948571428571427}, address={size=5, maxSize=500, metrics=CacheMetrics(hits=203, misses=1, puts=161, removes=0, expiredCleanups=0, totalOperations=204), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=3, addressesWithStats=5, avgTipsPerAddress=7.32}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=51, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=57), defaultTtl=1d, enableMetrics=true, hitRate=0.8947368421052632, missRate=0.10526315789473684, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=2, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=4), defaultTtl=1h, enableMetrics=true}, config={size=1, maxSize=500, metrics=CacheMetrics(hits=0, misses=1, puts=1, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=false, onboarding=false, dataCollection=false}}}
 D  📊 ENHANCED CACHE BREAKDOWN:
 D     🚚 DELIVERY CACHE:
 D        💰 Tips: $146.64 total, $20.95 avg
 D        📦 Deliveries: 2 completed, 7 total entries
 D        🎯 Performance: 100.0% hit rate
 D     👤 USER PROFILE CACHE:
 D        💰 Tips: $0.00 total, $0.00 per user
 D        👥 Users: 1 total entries
 D        🎯 Performance: 89.5% hit rate
 W        🚨 AGGREGATION ISSUE: Users cached but no aggregated tips
 D     🏠 ADDRESS CACHE:
 D        💰 Tips: $7.32 avg per address
 D        📍 Addresses: 5 with stats, 5 total
 D     📈 OVERALL CACHE HEALTH: 94.7% avg hit rate [EXCELLENT]
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 I  UI PERFORMANCE MONITORING: Started/Verified
 D  [SYSTEM_HEALTH] Repository:true UserSession:false CoreData:true
 D  [SYSTEM_HEALTH] Cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=7, completedDeliveries=2, pendingTips=0, totalCachedTips=146.64, uniqueUsers=1, uniqueAddresses=2, avgTipPerDelivery=20.948571428571427}, address={size=5, maxSize=500, metrics=CacheMetrics(hits=203, misses=1, puts=161, removes=0, expiredCleanups=0, totalOperations=204), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=3, addressesWithStats=5, avgTipsPerAddress=7.32}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=51, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=57), defaultTtl=1d, enableMetrics=true, hitRate=0.8947368421052632, missRate=0.10526315789473684, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=2, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=4), defaultTtl=1h, enableMetrics=true}, config={size=1, maxSize=500, metrics=CacheMetrics(hits=0, misses=1, puts=1, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=false, onboarding=false, dataCollection=false}}}
 I  SYSTEM HEALTH CHECK: Completed successfully
 D  preferredRenderer: null
 D  preferredRenderer: null
 I  Google Play services package version: 252037038
 I  Google Play services maps renderer version(maps_core): 251625202
 I  FpsProfiler MAIN created on main
 I  Map using legacy labeler
 I  Network fetching: false
 I  requestDrawingConfig for epoch 736 legend ROADMAP
 I  Network fetching: true
 I  Network fetching: true
 I  Found 45 zoom mappings
 I  Network fetching: true
 I  Zoom tables loaded
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 D  DEDUPLICATION: Found existing request for key 'session_creation_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  ✅ SESSION MANAGER: Created/retrieved session myuivBnSjJe686W71qJTTuZsQet1_1749824675933
 D  Repository initialization state: true
 D  UI frame monitoring started
 D  🔗 Auth state: User authenticated (myuivBnSjJe686W71qJTTuZsQet1)
 D  🚀 HERO PERFORMANCE: Starting priority cache warming for instant map
 D  Scheduling task DashboardMapCacheWarming with priority CRITICAL
 D  Executing task DashboardMapCacheWarming (CRITICAL)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:2 [EFFICIENT]: 0ms [OK]
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  🗺️ Testing simple geocoding for user: myuivBnSjJe686W71qJTTuZsQet1
 D  🧪 Testing native geocoding with: 1600 Amphitheatre Parkway, Mountain View, CA
 D  getCurrentUserIdSuspend: Checking authentication state
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  🗺️ Loading addresses from repository for map display
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getCurrentUser: Authentication ready for user myuivBnSjJe686W71qJTTuZsQet1
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  loadPage: Loading address page for user myuivBnSjJe686W71qJTTuZsQet1 with size: 50, key: null
 D  loadAddressPage: Loading address page with size: 50, key: null
 D  loadAddressPage: Executing Firestore query for addresses
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  Subscription cache warming successful in 1ms
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  DEDUPLICATION: Found existing request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 0ms [OK]
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  DEDUPLICATION: Found existing request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 1ms, result: true
 D  Subscription cache warming successful in 1ms
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 7ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 3ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 13ms, result: true
 D  User profile cache warming successful in 14ms
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1' in 10ms, result: true
 D  User profile cache warming successful in 10ms
 D  Subscription cache warming successful in 1ms
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 9ms
 D  [data.UserRepository] getCurrentUser(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 33ms [OK]
 D  Starting backpressure handling for dashboard_delivery_observation with THROTTLE_FIRST
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1' in 9ms, result: true
 D  User profile cache warming successful in 9ms
 D  observeDeliveriesByUserId: Starting observation for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeUserProfile Load:55ms Steps:[repository_call,data_mapping,state_emission] UserDelay:55ms [CACHE_HIT] [FAST_UX]: 55ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4059bytes [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:3 Size:1311bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:61ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:61ms [CACHE_HIT] [FAST_UX]: 61ms [OK]
 D  Native geocoding success: 1600 Amphitheatre Parkway, Mountain View, CA -> 37.4220094, -122.0847525
 D  [Firestore] QUERY user_addresses: 148ms [OK]
 D  loadAddressPage: Successfully loaded 5 DTOs (160ms total, query: 148ms)
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  loadPage: Successfully loaded 5 addresses
 D  [data.AddressPageLoader] loadPage(Address) User:myuivBnSjJe686W71qJTTuZsQet1 Count:5 Source:RemoteDataSource Strategy:none [CACHE_MISS]: 180ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed waiting for 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 191ms, result: true
 D  Addresses cache warming successful in 193ms
 D  DEDUPLICATION: Completed waiting for 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 189ms, result: true
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Fallback: Loaded 5 addresses from repository
 D  Addresses cache warming successful in 193ms
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 215ms, result: true
 D  Addresses cache warming successful in 221ms
 I  Initial labeling completed.
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:14ms, subscription:success:1ms, addresses:success:221ms, total:226ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:9ms, subscription:success:1ms, addresses:success:193ms, total:207ms
 D  getCurrentUser: Cache warming initiated for user myuivBnSjJe686W71qJTTuZsQet1
 D  🚀 MAP DATA LOADED: 5 addresses in 305ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:10ms, subscription:success:1ms, addresses:success:193ms, total:227ms
 D  DEDUPLICATION: Starting new request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  executeGetDeliveriesByUserIdOperation - Fetching all deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved 7 deliveries for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation - Found 7 deliveries in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  executeGetDeliveriesByUserIdOperation: Cache hit for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, count: 7)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 3ms, result: true
 D  🗺️ MAP-CRITICAL WARMING COMPLETE: 236ms - addresses: Success(cachedAt=1749826185299, durationMs=221), user: Success(cachedAt=1749826185108, durationMs=14)
 D  🎯 SIMPLE GEOCODING: Testing native geocoding capability
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:TODAY User:stats_calculation Size:201bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:WEEK User:stats_calculation Size:200bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:MONTH User:stats_calculation Size:201bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  🚀 MAP DATA LOADED: 5 addresses in 324ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  🚀 MAP READY: Maps Compose initialized and ready
 D  🚀 MAP LOADED: 5 markers ready
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  🗺️ INSTANT MAP DATA: Loaded 5 addresses from warmed cache
 D  Complete cache warming: 320ms total (warming: 236ms)
 D  Task DashboardMapCacheWarming completed in 321ms
 D  🚀 PRIORITY TASK SUCCESS: Cache warming and maps pre-fetch completed successfully
 D  🚀 MAP DATA LOADED: 5 addresses in 419ms
 I  Background concurrent mark compact GC freed 18MB AllocSpace bytes, 144(4356KB) LOS objects, 43% free, 31MB/55MB, paused 2.925ms,4.192ms total 101.179ms
 D  🚀 BACKGROUND REFRESH: Skipping update, data already loaded
 D  🏠 HOME POSITION SET: lat/lng: (41.5871262,-93.75568379999999) at zoom 12.0
 D  🏠 HOME POSITION: Stored position lat/lng: (41.5871262,-93.75568379999999) at zoom 12.0
 D  app_time_stats: avg=15.76ms min=1.34ms max=285.72ms count=43
 W  Destroying egl surface
 D  ✅ MODERN AddEditDeliveryViewModel initialized
 D  [presentation.AddEditDeliveryViewModel] init Deps:4 Data:new_delivery_created State:Initialized [STATEFLOW_UPDATED]: 0ms [OK]
 D  tagSocket(191) with statsTag=0xffffffff, statsUid=-1
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  LaunchedEffect triggered but selectedPlace is null
 D  tagSocket(164) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  [reactive_performance.AddEditDeliveryViewModel] stateflow_emission Flow:delivery Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  Subscription cache warming successful in 1ms
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  [cache_system.UserRepository] cache_breakdown Check:6ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 6ms [OK]
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 10ms [OK]
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 11ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 14ms, result: true
 D  User profile cache warming successful in 15ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 89ms, result: true
 D  Addresses cache warming successful in 91ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:15ms, subscription:success:1ms, addresses:success:91ms, total:99ms
 D  DEDUPLICATION: Starting new request for key 'getDelivery_QdqJBMTjl5fqAumWVrJk'
 D  Cache hit for delivery QdqJBMTjl5fqAumWVrJk
 D  Retrieved delivery QdqJBMTjl5fqAumWVrJk for user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getDeliveryById: Found delivery QdqJBMTjl5fqAumWVrJk in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getDeliveryById: Cache hit for delivery QdqJBMTjl5fqAumWVrJk (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDelivery_QdqJBMTjl5fqAumWVrJk'
 D  DEDUPLICATION: Completed new request 'getDelivery_QdqJBMTjl5fqAumWVrJk' in 1ms, result: true
 D  [data.DeliveryRepository] getDeliveryById(Delivery) ID:QdqJBMTjl5fqAumWVrJk User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:732bytes Source:cache Strategy:cache-first [CACHE_HIT]: 101ms [OK]
 D  Loaded 5 addresses
 D  Updated selected place from loaded delivery
 D  LaunchedEffect triggered with selectedPlace: 222 5th Ave, Des Moines, IA 50309, USA
 D  Address text updated to: 222 5th Ave, Des Moines, IA 50309, USA
 D  app_time_stats: avg=53.47ms min=3.10ms max=646.22ms count=17
 D  For capability in capabilities, log:
"AdvancedMarkers: false: Capabilities unavailable without a Map ID."Data-driven styling: false
 W  Destroying egl context
 W  Shutting down renderer while it's not idle - phase is INVALID
 I  Trimming OTHER: LoggingOp's current size 20 to 0.00000, or 0
 I  Trimming OTHER: NativeTessellators's current size 1 to 0.00000, or 0
 I  Trimming OTHER: LabelSourceOp's current size 2 to 0.00000, or 0
 I  Trimming LINE_LABELS's current size 1 to 0.00000, or 0
 I  Trimming OTHER: VertexBuilders's current size 1 to 0.00000, or 0
 I  Trimming POINTS_LABELS's current size 26 to 0.00000, or 0
 D  app_time_stats: avg=3051.58ms min=1.68ms max=91419.58ms count=30
 D  Navigating to destination: dashboard
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  Subscription cache warming successful in 1ms
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  [cache_system.UserRepository] cache_breakdown Check:1ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 1ms [OK]
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 4ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 2ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 10ms, result: true
 D  User profile cache warming successful in 11ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 226ms, result: true
 D  Addresses cache warming successful in 227ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:11ms, subscription:success:1ms, addresses:success:227ms, total:227ms
 D  DEDUPLICATION: Found existing request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  ✅ Deliveries loaded successfully: 5 (deduplicated from 7)
 D  app_time_stats: avg=38.24ms min=13.05ms max=1128.28ms count=52
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  Updated selected place from loaded delivery
 D  LaunchedEffect triggered with selectedPlace: 222 5th Ave, Des Moines, IA 50309, USA
 D  Address text updated to: 222 5th Ave, Des Moines, IA 50309, USA
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  [reactive_performance.AddEditDeliveryViewModel] stateflow_emission Flow:delivery Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 1ms [OK]
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 7ms, result: true
 D  Subscription cache warming successful in 9ms
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  tagSocket(149) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  [cache_system.UserRepository] cache_breakdown Check:0ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 0ms [OK]
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 14ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 15ms, result: true
 D  User profile cache warming successful in 16ms
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Loaded 5 addresses
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 121ms, result: true
 D  Addresses cache warming successful in 122ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:16ms, subscription:success:9ms, addresses:success:122ms, total:123ms
 D  DEDUPLICATION: Starting new request for key 'getDelivery_QdqJBMTjl5fqAumWVrJk'
 D  Cache hit for delivery QdqJBMTjl5fqAumWVrJk
 D  Retrieved delivery QdqJBMTjl5fqAumWVrJk for user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getDeliveryById: Found delivery QdqJBMTjl5fqAumWVrJk in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getDeliveryById: Cache hit for delivery QdqJBMTjl5fqAumWVrJk (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Cleaned up completed request for key 'getDelivery_QdqJBMTjl5fqAumWVrJk'
 D  DEDUPLICATION: Completed new request 'getDelivery_QdqJBMTjl5fqAumWVrJk' in 5ms, result: true
 D  [data.DeliveryRepository] getDeliveryById(Delivery) ID:QdqJBMTjl5fqAumWVrJk User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:732bytes Source:cache Strategy:cache-first [CACHE_HIT]: 128ms [OK]
 D  Updated selected place from loaded delivery
 W  sendCancelIfRunning: isInProgress=false callback=androidx.activity.OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1@52d5a85
 D  🚀 HERO DASHBOARD: Starting render at 1749826282769
 D  User session ready - initializing dashboard data
 I  SYSTEM HEALTH CHECK: Starting comprehensive monitoring
 I  SYSTEM STATE:
 I  - Repository Initialized: true
 I  - User Session Ready: false
 I  - Core Data Ready: true
 I  SYSTEM COMPONENTS:
 I  - AuthManager: AuthenticationManagerImpl
 D  🚀 MAP DATA LOADED: 5 addresses in 21ms
 I  - EncryptionUtils: EncryptionUtils
 D  🔍 STATE CHANGED: showAddressDetails = false, selectedAddressId = null
 D  app_time_stats: avg=29.92ms min=1.33ms max=1069.37ms count=43
 I  - FirebaseAuth: zzad
 I  - CacheLifecycleManager: CacheLifecycleManager
 D  🚀 INSTANT: Map data loaded, hiding skeleton immediately
 I  CACHE METRICS: {delivery={hitRate=1.0, missRate=0.0, totalEntries=7, completedDeliveries=2, pendingTips=0, totalCachedTips=146.64, uniqueUsers=1, uniqueAddresses=2, avgTipPerDelivery=20.948571428571427}, address={size=5, maxSize=500, metrics=CacheMetrics(hits=253, misses=1, puts=201, removes=0, expiredCleanups=0, totalOperations=254), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=3, addressesWithStats=5, avgTipsPerAddress=7.32}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=56, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=62), defaultTtl=1d, enableMetrics=true, hitRate=0.9032258064516129, missRate=0.0967741935483871, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=2, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=4), defaultTtl=1h, enableMetrics=true}, config={size=1, maxSize=500, metrics=CacheMetrics(hits=0, misses=1, puts=1, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=false, onboarding=false, dataCollection=false}}}
 D  📊 ENHANCED CACHE BREAKDOWN:
 D     🚚 DELIVERY CACHE:
 D        💰 Tips: $146.64 total, $20.95 avg
 D        📦 Deliveries: 2 completed, 7 total entries
 D        🎯 Performance: 100.0% hit rate
 D     👤 USER PROFILE CACHE:
 D        💰 Tips: $0.00 total, $0.00 per user
 D        👥 Users: 1 total entries
 D        🎯 Performance: 90.3% hit rate
 W        🚨 AGGREGATION ISSUE: Users cached but no aggregated tips
 D     🏠 ADDRESS CACHE:
 D        💰 Tips: $7.32 avg per address
 D        📍 Addresses: 5 with stats, 5 total
 D     📈 OVERALL CACHE HEALTH: 95.2% avg hit rate [EXCELLENT]
 W  Failed to start UI performance monitoring: The current thread must have a looper!
 I  UI PERFORMANCE MONITORING: Started/Verified
 D  [SYSTEM_HEALTH] Repository:true UserSession:false CoreData:true
 D  [SYSTEM_HEALTH] Cache metrics: {delivery={hitRate=1.0, missRate=0.0, totalEntries=7, completedDeliveries=2, pendingTips=0, totalCachedTips=146.64, uniqueUsers=1, uniqueAddresses=2, avgTipPerDelivery=20.948571428571427}, address={size=5, maxSize=500, metrics=CacheMetrics(hits=253, misses=1, puts=201, removes=0, expiredCleanups=0, totalOperations=254), defaultTtl=2h, enableMetrics=true, uniqueUsers=1, dndAddresses=3, addressesWithStats=5, avgTipsPerAddress=7.32}, userProfile={size=1, maxSize=50, metrics=CacheMetrics(hits=56, misses=6, puts=6, removes=0, expiredCleanups=0, totalOperations=62), defaultTtl=1d, enableMetrics=true, hitRate=0.9032258064516129, missRate=0.0967741935483871, totalEntries=1, activeSubscriptions=1, premiumUsers=0, totalCachedDeliveries=0, totalCachedTips=0.0, avgProfileCompleteness=0.6666666666666666, avgDeliveriesPerUser=0.0, avgTipsPerUser=0.0}, subscription={size=1, maxSize=20, metrics=CacheMetrics(hits=2, misses=2, puts=1, removes=0, expiredCleanups=0, totalOperations=4), defaultTtl=1h, enableMetrics=true}, config={size=1, maxSize=500, metrics=CacheMetrics(hits=0, misses=1, puts=1, removes=0, expiredCleanups=0, totalOperations=1), defaultTtl=6h, enableMetrics=true}, preference={size=0, maxSize=100, metrics=CacheMetrics(hits=0, misses=0, puts=0, removes=0, expiredCleanups=0, totalOperations=0), defaultTtl=1d, enableMetrics=true, cloudManagedPrefs=0, clientManagedPrefs=0, sharedPrefs=0, totalPreferences=0, activeFlows={userProfile=false, userProfileDto=false, theme=system, notifications=false, onboarding=false, dataCollection=false}}}
 I  SYSTEM HEALTH CHECK: Completed successfully
 D  preferredRenderer: null
 D  preferredRenderer: null
 I  Google Play services package version: 252037038
 I  Google Play services maps renderer version(maps_core): 251625202
 I  FpsProfiler MAIN created on main
 I  Map using legacy labeler
 I  Network fetching: false
 I  requestDrawingConfig for epoch 736 legend ROADMAP
 I  Network fetching: true
 I  Network fetching: true
 I  Network fetching: true
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 I  Found 45 zoom mappings
 I  Zoom tables loaded
 D  DEDUPLICATION: Found existing request for key 'session_creation_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'session_creation_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  ✅ SESSION MANAGER: Created/retrieved session myuivBnSjJe686W71qJTTuZsQet1_1749824675933
 D  Repository initialization state: true
 D  UI frame monitoring started
 D  🔗 Auth state: User authenticated (myuivBnSjJe686W71qJTTuZsQet1)
 D  🚀 HERO PERFORMANCE: Starting priority cache warming for instant map
 D  Scheduling task DashboardMapCacheWarming with priority CRITICAL
 D  Executing task DashboardMapCacheWarming (CRITICAL)
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  🗺️ Testing simple geocoding for user: myuivBnSjJe686W71qJTTuZsQet1
 D  🧪 Testing native geocoding with: 1600 Amphitheatre Parkway, Mountain View, CA
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getCurrentUser: Authentication ready for user myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:1 [EFFICIENT]: 0ms [OK]
 D  🗺️ Loading addresses from repository for map display
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:2 [EFFICIENT]: 0ms [OK]
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  loadPage: Loading address page for user myuivBnSjJe686W71qJTTuZsQet1 with size: 50, key: null
 D  loadAddressPage: Loading address page with size: 50, key: null
 D  loadAddressPage: Executing Firestore query for addresses
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 2ms, result: true
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  Subscription cache warming successful in 4ms
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 16ms
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 8ms, result: true
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Subscription cache warming successful in 9ms
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 9ms [OK]
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  [cache_system.UserRepository] cache_breakdown Check:11ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 11ms [OK]
 D  [data.UserRepository] getCurrentUser(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 28ms [OK]
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 14ms [OK]
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 5ms, result: true
 D  Subscription cache warming successful in 12ms
 D  Starting backpressure handling for dashboard_delivery_observation with THROTTLE_FIRST
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 12ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  observeDeliveriesByUserId: Starting observation for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 7 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [cache_system.UserRepository] cache_breakdown Check:3ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 3ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 21ms, result: true
 D  User profile cache warming successful in 21ms
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1' in 14ms, result: true
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  User profile cache warming successful in 21ms
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1' in 21ms, result: true
 D  User profile cache warming successful in 26ms
 D  Deduplication: Original 7 deliveries, deduplicated to 5
 V  ThrottleFirst: emitted value
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeUserProfile Load:78ms Steps:[repository_call,data_mapping,state_emission] UserDelay:78ms [CACHE_HIT] [FAST_UX]: 78ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:userProfile Emit:0ms Subs:1 Chain:3 Size:1311bytes [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4059bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:109ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:109ms [CACHE_HIT] [FAST_UX]: 109ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [Firestore] QUERY user_addresses: 196ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 244ms, result: true
 D  Addresses cache warming successful in 247ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:26ms, subscription:success:4ms, addresses:success:247ms, total:263ms
 D  DEDUPLICATION: Found existing request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 I  Initial labeling completed.
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  loadAddressPage: Successfully loaded 5 DTOs (218ms total, query: 196ms)
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 6ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 4ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:4ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 4ms [OK]
 D  loadPage: Successfully loaded 5 addresses
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [data.AddressPageLoader] loadPage(Address) User:myuivBnSjJe686W71qJTTuZsQet1 Count:5 Source:RemoteDataSource Strategy:none [CACHE_MISS]: 283ms [OK]
 D  Fallback: Loaded 5 addresses from repository
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:6ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 6ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:WEEK User:stats_calculation Size:200bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:MONTH User:stats_calculation Size:201bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  🚀 MAP DATA LOADED: 5 addresses in 400ms
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [DeliveryStatsMapper] toSsot(DeliveryStats) ID:TODAY User:stats_calculation Size:201bytes Fields:8 Logic:[period_filtering,stats_calculation,time_averaging] DeliveryStats: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 312ms, result: true
 D  Addresses cache warming successful in 322ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:21ms, subscription:success:9ms, addresses:success:322ms, total:330ms
 D  getCurrentUser: Cache warming initiated for user myuivBnSjJe686W71qJTTuZsQet1
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  🚀 MAP READY: Maps Compose initialized and ready
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  🚀 MAP LOADED: 5 markers ready
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 332ms, result: true
 D  Addresses cache warming successful in 332ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:21ms, subscription:success:12ms, addresses:success:332ms, total:354ms
 D  🗺️ MAP-CRITICAL WARMING COMPLETE: 355ms - addresses: Success(cachedAt=1749826283196, durationMs=332), user: Success(cachedAt=1749826282876, durationMs=21)
 D  🎯 SIMPLE GEOCODING: Testing native geocoding capability
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  Native geocoding success: 1600 Amphitheatre Parkway, Mountain View, CA -> 37.4220094, -122.0847525
 I  Background concurrent mark compact GC freed 23MB AllocSpace bytes, 132(3660KB) LOS objects, 42% free, 32MB/56MB, paused 4.879ms,4.426ms total 143.928ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  🗺️ INSTANT MAP DATA: Loaded 5 addresses from warmed cache
 D  Complete cache warming: 470ms total (warming: 355ms)
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Task DashboardMapCacheWarming completed in 472ms
 D  🚀 PRIORITY TASK SUCCESS: Cache warming and maps pre-fetch completed successfully
 D  🚀 MAP DATA LOADED: 5 addresses in 553ms
 D  🚀 BACKGROUND REFRESH: Skipping update, data already loaded
 D  🏠 HOME POSITION SET: lat/lng: (41.5871262,-93.75568379999999) at zoom 12.0
 D  🏠 HOME POSITION: Stored position lat/lng: (41.5871262,-93.75568379999999) at zoom 12.0
 D  app_time_stats: avg=47.95ms min=1.28ms max=1334.36ms count=41
 D  app_time_stats: avg=149.80ms min=14.95ms max=1732.80ms count=13
 D  Navigating to destination: deliveries
 W  Destroying egl surface
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 0ms, result: true
 D  Subscription cache warming successful in 0ms
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  [cache_system.UserRepository] cache_breakdown Check:2ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 2ms [OK]
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 5ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 4ms [OK]
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 8ms, result: true
 D  User profile cache warming successful in 8ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 80ms, result: true
 D  Addresses cache warming successful in 80ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:8ms, subscription:success:0ms, addresses:success:80ms, total:82ms
 D  DEDUPLICATION: Found existing request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  ✅ Deliveries loaded successfully: 5 (deduplicated from 7)
 D  For capability in capabilities, log:
"AdvancedMarkers: false: Capabilities unavailable without a Map ID."Data-driven styling: false
 ✅ MODERN AddEditDeliveryViewModel initialized
 D  tagSocket(147) with statsTag=0xffffffff, statsUid=-1
 D  [presentation.AddEditDeliveryViewModel] init Deps:4 Data:new_delivery_created State:Initialized [STATEFLOW_UPDATED]: 0ms [OK]
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  LaunchedEffect triggered but selectedPlace is null
 D  tagSocket(216) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Loaded 5 addresses
 D  show(ime(), fromIme=false)
 I  com.autogratuity:2f08a5c4: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
 I  Flattened final assist data: 900 bytes, containing 1 windows, 6 views
 I  com.autogratuity:88c299f2: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  app_time_stats: avg=25.73ms min=12.06ms max=457.94ms count=50
 D  show(ime(), fromIme=true)
 I  com.autogratuity:88c299f2: onShown
 D  app_time_stats: avg=34.60ms min=8.24ms max=500.52ms count=29
 D  validateOrderIdInput: input='784 W Hickman Rd, Waukee, IA 50263, USA' -> filtered='78450263'
 D  getOrderIdErrorMessage: input='78450263', error='Order ID must be exactly 9 digits (currently 8)'
 D  show(ime(), fromIme=true)
 I  com.autogratuity:b80ffee6: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=75.31ms min=1.59ms max=518.30ms count=14
 D  show(ime(), fromIme=false)
 I  com.autogratuity:ae97137f: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:ae97137f: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:6f8d6266: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  app_time_stats: avg=462.20ms min=387.83ms max=499.76ms count=3
 D  show(ime(), fromIme=true)
 I  com.autogratuity:a77ea8db: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:6f8d6266: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:3b5f7c26: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  🔍 GOOGLE_PLACES_AUTOCOMPLETE_START: Starting autocomplete search for query='784 W Hickman Rd, Waukee, IA 50263, USA...'
 E  ❌ Failed to get App Check token for Places SDK: Too many attempts. (Ask Gemini)
com.google.firebase.FirebaseException: Too many attempts.
	at com.google.firebase.appcheck.internal.NetworkClient.exchangeAttestationForAppCheckToken(NetworkClient.java:118)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider.lambda$getToken$1$com-google-firebase-appcheck-debug-internal-DebugAppCheckProvider(DebugAppCheckProvider.java:121)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider$$ExternalSyntheticLambda2.call(D8$$SyntheticClass:0)
	at com.google.android.gms.tasks.zzz.run(com.google.android.gms:play-services-tasks@@18.1.0:1)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
	at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
	at java.lang.Thread.run(Thread.java:1012)
 D  tagSocket(218) with statsTag=0xffffffff, statsUid=-1
 D  ✅ GOOGLE_PLACES_AUTOCOMPLETE_SUCCESS: Found 2 predictions for query='784 W Hickman Rd, Waukee, IA 50263, USA...'
 D  app_time_stats: avg=58.03ms min=7.31ms max=458.09ms count=18
 D  app_time_stats: avg=409.67ms min=1.19ms max=4054.81ms count=10
 D  Fetching place details for: 784 W Hickman Rd, Waukee, IA 50263, USA
 D  🔍 GOOGLE_PLACES_DETAILS_START: Fetching place details for placeId='ChIJb4RSl18j7IcR72v3JxSeaL4', prediction='784 W Hickman Rd, Waukee, IA 50263, USA'
 D  🔍 GOOGLE_PLACES_DETAILS_REQUEST: Created request with fields=ID, DISPLAY_NAME, FORMATTED_ADDRESS, ADDRESS_COMPONENTS, LOCATION, sessionToken present
 E  ❌ Failed to get App Check token for Places SDK: Too many attempts. (Ask Gemini)
com.google.firebase.FirebaseException: Too many attempts.
	at com.google.firebase.appcheck.internal.NetworkClient.exchangeAttestationForAppCheckToken(NetworkClient.java:118)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider.lambda$getToken$1$com-google-firebase-appcheck-debug-internal-DebugAppCheckProvider(DebugAppCheckProvider.java:121)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider$$ExternalSyntheticLambda2.call(D8$$SyntheticClass:0)
	at com.google.android.gms.tasks.zzz.run(com.google.android.gms:play-services-tasks@@18.1.0:1)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
	at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
	at java.lang.Thread.run(Thread.java:1012)
 D  Place details fetched successfully: 784 W Hickman Rd, Waukee, IA 50263, USA
 I  ✅ GOOGLE_PLACES_DETAILS_SUCCESS: Place details fetched successfully for placeId='ChIJb4RSl18j7IcR72v3JxSeaL4', address='784 W Hickman Rd, Waukee, IA 50263, USA'
 D  Selected place set for display only: 784 W Hickman Rd, Waukee, IA 50263, USA
 D  ✅ GOOGLE_PLACES_SELECTION_COMPLETE: Place selected and stored for validation at save time
 D  🔄 GOOGLE_PLACES_SESSION_RENEWED: Session token regenerated for next search
 D  app_time_stats: avg=4268.86ms min=4268.86ms max=4268.86ms count=1
 D  LaunchedEffect triggered with selectedPlace: 784 W Hickman Rd, Waukee, IA 50263, USA
 D  Address text updated to: 784 W Hickman Rd, Waukee, IA 50263, USA
 W  sendCancelIfRunning: isInProgress=false callback=androidx.compose.ui.window.Api33Impl$$ExternalSyntheticLambda0@4321526
 D  endAllActiveAnimators on 0x758d33aec9a0 (UnprojectedRipple) with handle 0x758ca3af38e0
 W  sendCancelIfRunning: isInProgress=false callback=ImeCallback=ImeOnBackInvokedCallback@255721015 Callback=android.window.IOnBackInvokedCallback$Stub$Proxy@55fe083
 D  app_time_stats: avg=232.39ms min=11.87ms max=500.67ms count=5
 D  show(ime(), fromIme=false)
 I  com.autogratuity:5bb63fa7: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:5bb63fa7: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:b65fb822: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  show(ime(), fromIme=true)
 I  com.autogratuity:dcba967f: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:b65fb822: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=77.68ms min=13.19ms max=500.95ms count=15
 D  app_time_stats: avg=462.41ms min=387.49ms max=499.88ms count=3
 D  app_time_stats: avg=337.84ms min=12.51ms max=502.26ms count=3
 D  app_time_stats: avg=461.47ms min=386.91ms max=499.21ms count=3
 D  app_time_stats: avg=338.04ms min=13.89ms max=500.52ms count=3
 D  app_time_stats: avg=125.09ms min=12.65ms max=500.64ms count=8
 D  show(ime(), fromIme=false)
 I  com.autogratuity:7daf8c01: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:7daf8c01: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:54461add: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
 D  app_time_stats: avg=23.52ms min=14.50ms max=241.01ms count=50
 D  show(ime(), fromIme=true)
 I  com.autogratuity:37a12649: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:54461add: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  validateOrderIdInput: input='784502632' -> filtered='784502632'
 D  getOrderIdErrorMessage: input='784502632', error='null'
 W  sendCancelIfRunning: isInProgress=false callback=androidx.compose.ui.window.Api33Impl$$ExternalSyntheticLambda0@ad6cb80
 D  validateOrderIdInput: input='7845026322' -> filtered='784502632'
 D  getOrderIdErrorMessage: input='784502632', error='null'
 D  show(ime(), fromIme=true)
 I  com.autogratuity:510da523: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=64.65ms min=7.29ms max=313.61ms count=16
 D  app_time_stats: avg=333.82ms min=17.32ms max=499.71ms count=3
 D  app_time_stats: avg=32.49ms min=10.80ms max=479.29ms count=31
 D  app_time_stats: avg=499.95ms min=499.90ms max=500.03ms count=3
 D  app_time_stats: avg=500.34ms min=498.94ms max=501.73ms count=2
 D  getOrderIdErrorMessage: input='784502632', error='null'
 D  isValidOrderId: input='784502632', valid=true
 D  getOrderIdErrorMessage: input='784502632', error='null'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  findOrCreateAddressFromPlace (SSoT) - Looking up or creating address for Place ID: ChIJb4RSl18j7IcR72v3JxSeaL4, User ID: myuivBnSjJe686W71qJTTuZsQet1
 D  app_time_stats: avg=361.63ms min=85.62ms max=500.14ms count=3
 D  Existing address found for placeId ChIJb4RSl18j7IcR72v3JxSeaL4 via remoteDataSource
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  findOrCreateAddressFromPlace (SSoT) - Successfully found/created and mapped SSoT address: upOCQJkNCZX6C6vRJa1P
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  Scheduling task addDelivery_1749826315727 with priority HIGH
 D  Executing task addDelivery_1749826315727 (HIGH)
 D  validateDelivery: Validating delivery 
 D  validateDelivery: Delivery  is valid
 D  CONVERSION: Delivery SSoT -> DTO | ID:  | User: myuivBnSjJe686W71qJTTuZsQet1
 D    Input: orderId=784502632 | status=COMPLETED | tipAmount=67.0
 D    Converting address: 784 W Hickman Rd, Waukee, IA 50263, USA... (ID: upOCQJkNCZX6C6vRJa1P)
 D    Created missing reference with addressId: upOCQJkNCZX6C6vRJa1P
 D  === DELIVERY SSoT -> DTO TRANSFORMATION ===
 D  Input SSoT State: {ssot_id=, ssot_userId=myuivBnSjJe686W71qJTTuZsQet1, ssot_orderId=784502632, ssot_tipAmount=67.0, ssot_status=COMPLETED, ssot_notes=empty, ssot_addressId=upOCQJkNCZX6C6vRJa1P}
 D  Output DTO State: {dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_orderId=784502632, dto_tipAmount=67.0, dto_status=COMPLETED, dto_notes=empty, dto_addressId=upOCQJkNCZX6C6vRJa1P}
 D  Field Transformations: [notes: ENCRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, times: TIMESTAMP_MAPPING]
 D  Business Logic Applied: [notes_encryption, field_validation, address_plain_mapping, status_mapping, amounts_calculation]
 D  PII Fields Encrypted: 0
 D  Mapping Duration: 0ms
 D  CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: 
 D    Output: encrypted_fields=0 | validation_errors=0 | duration=0ms
 D    DTO size: 1156 bytes
 D    Business logic: address_mapping, status_mapping, amounts_calculation
 D  executeAddDeliveryOperation: Adding delivery with transaction for user myuivBnSjJe686W71qJTTuZsQet1
 D  [DeliveryMapper] toDto Delivery ID: User:myuivBnSjJe686W71qJTTuZsQet1 Size:1156bytes Delivery: 0ms [OK]
 D  Attempting to add new delivery (from map) and update stats for user: myuivBnSjJe686W71qJTTuZsQet1
 D  isAssociateAddressIfNotFound: false, rawAddressDetails provided: false
 D  Delivery (from map) added to transaction with ID: Kt0pEd3N8W8vXrB1U1Aj
 D  Address upOCQJkNCZX6C6vRJa1P already has 1 deliveries - not incrementing address count.
 D  Incrementing user profile delivery count (both usage and usageStats) for myuivBnSjJe686W71qJTTuZsQet1.
 D  Incremented address stats for upOCQJkNCZX6C6vRJa1P.
 I  Successfully added new delivery (from map) with ID: Kt0pEd3N8W8vXrB1U1Aj and updated all stats for user myuivBnSjJe686W71qJTTuZsQet1
 I  executeAddDeliveryOperation: Delivery added with transaction, ID Kt0pEd3N8W8vXrB1U1Aj for user myuivBnSjJe686W71qJTTuZsQet1
 D  Saved delivery Kt0pEd3N8W8vXrB1U1Aj for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 D  executeAddDeliveryOperation: Delivery added Kt0pEd3N8W8vXrB1U1Aj (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, user: myuivBnSjJe686W71qJTTuZsQet1)
 D  [data.DeliveryRepository] addDelivery(Delivery) ID:Kt0pEd3N8W8vXrB1U1Aj User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1156bytes Source:transaction Strategy:write-through [CACHE_MISS]: 300ms [OK]
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  Task addDelivery_1749826315727 completed in 301ms
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  Cached delivery Kt0pEd3N8W8vXrB1U1Aj with atomic cache system
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:2131065ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:2131065ms [CACHE_HIT] [POOR_UX]: 2131065ms [OK]
 V  ThrottleFirst: emitted value
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 2131065ms (user-visible)
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:33195ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:33196ms [CACHE_HIT] [POOR_UX]: 33195ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 33196ms (user-visible)
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:2163121ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:2163121ms [CACHE_HIT] [POOR_UX]: 2163121ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 2163121ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:130964ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:130964ms [CACHE_HIT] [POOR_UX]: 130964ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 130964ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:252745ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:252745ms [CACHE_HIT] [POOR_UX]: 252745ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 252745ms (user-visible)
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:847757ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:847757ms [CACHE_HIT] [POOR_UX]: 847757ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 847757ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1698873ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1698873ms [CACHE_HIT] [POOR_UX]: 1698873ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1698873ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:148318ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:148318ms [CACHE_HIT] [POOR_UX]: 148318ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 148318ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1640118ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1640118ms [CACHE_HIT] [POOR_UX]: 1640118ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1640118ms (user-visible)
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 3ms, result: true
 D  Subscription cache warming successful in 3ms
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 4ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  [cache_system.UserRepository] cache_breakdown Check:2ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 2ms [OK]
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 3ms [OK]
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 8ms, result: true
 D  User profile cache warming successful in 9ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1256bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 100ms, result: true
 D  Addresses cache warming successful in 101ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:9ms, subscription:success:3ms, addresses:success:101ms, total:102ms
 D  DEDUPLICATION: Found existing request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  ✅ Deliveries loaded successfully: 5 (deduplicated from 7)
 D  app_time_stats: avg=16.98ms min=9.07ms max=38.12ms count=59
 D  validateOrderIdInput: input='784502632' -> filtered='784502632'
 D  getOrderIdErrorMessage: input='784502632', error='null'
 D  hide(ime(), fromIme=false)