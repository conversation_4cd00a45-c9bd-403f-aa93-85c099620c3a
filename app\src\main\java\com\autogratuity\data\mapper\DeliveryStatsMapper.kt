package com.autogratuity.data.mapper

import android.util.Log
import com.autogratuity.domain.model.Delivery
import com.autogratuity.domain.model.DeliveryStats
import com.autogratuity.domain.model.StatisticsPeriod
import kotlinx.coroutines.ExperimentalCoroutinesApi

/**
 * ✅ MAPPER: App-level delivery statistics calculations
 * Handles conversion from delivery lists to statistical aggregations
 * Following Clarity Architecture: Mappers contain domain business logic
 */
@ExperimentalCoroutinesApi
class DeliveryStatsMapper {

    companion object {
        private const val TAG = "DeliveryStatsMapper"

        /**
         * ✅ STATIC TRIGGER: Easy way to trigger enhanced stats logging from anywhere
         * This can be called from ViewModels, repositories, or debug tools
         */
        @Suppress("unused") // Utility function for debugging - may be called dynamically
        fun triggerEnhancedStatsLogging(deliveries: List<Delivery>, context: String = "MANUAL_TRIGGER") {
            val mapper = DeliveryStatsMapper()
            mapper.calculateAndLogCurrentStats(deliveries, context)
        }
    }
    

    
    /**
     * Combine multiple period statistics into a summary
     */
    fun combineStats(statsList: List<DeliveryStats>): DeliveryStats {
        if (statsList.isEmpty()) {
            Log.w(TAG, "🚨 STATS WARNING: Combining empty stats list - returning empty stats")
            return DeliveryStats.empty(StatisticsPeriod.ALL_TIME)
        }

        val totalDeliveryCount = statsList.sumOf { it.deliveryCount }
        val totalTipCount = statsList.sumOf { it.tipCount }
        val totalTips = statsList.sumOf { it.totalTips }
        val averageTipAmount = if (totalTipCount > 0) totalTips / totalTipCount else 0.0

        // ✅ ENHANCED LOGGING: Log combined stats details
        Log.d(TAG, "📊 COMBINING ${statsList.size} STATS PERIODS:")
        statsList.forEach { stats ->
            Log.d(TAG, "   ${stats.period}: ${stats.deliveryCount} deliveries, \$${String.format("%.2f", stats.totalTips)} tips")
        }
        Log.d(TAG, "   📈 COMBINED RESULT: $totalDeliveryCount deliveries, \$${String.format("%.2f", totalTips)} total tips")

        return DeliveryStats(
            deliveryCount = totalDeliveryCount,
            tipCount = totalTipCount,
            totalTips = totalTips,
            averageTipAmount = averageTipAmount,
            highestTip = statsList.maxOfOrNull { it.highestTip } ?: 0.0,
            pendingCount = statsList.sumOf { it.pendingCount },
            averageTimeMinutes = statsList.map { it.averageTimeMinutes }.average(),
            lastDeliveryDate = statsList.mapNotNull { it.lastDeliveryDate }.maxOrNull(),
            period = StatisticsPeriod.ALL_TIME
        )
    }

    /**
     * ✅ CRITICAL ISSUES ONLY: Log only stats problems, not normal breakdowns
     */
    fun logStatsBreakdown(stats: DeliveryStats, context: String = "") {
        val prefix = if (context.isNotEmpty()) "[$context] " else ""

        // Only log critical issues, not normal stats
        if (!stats.hasData) {
            Log.w(TAG, "${prefix}🚨 STATS ISSUE: No data found for ${stats.period}")
        }
        if (stats.deliveryCount > 0 && stats.tipCount == 0L) {
            Log.w(TAG, "${prefix}🚨 STATS ISSUE: ${stats.deliveryCount} deliveries but no tips recorded")
        }
        if (stats.totalTips > 0 && stats.averageTipAmount == 0.0) {
            Log.w(TAG, "${prefix}🚨 STATS ISSUE: Total tips > 0 but average is 0 - calculation error?")
        }
    }

    /**
     * ✅ CRITICAL CONVERSION ONLY: Log stats only during critical mapping operations
     * Reduced verbosity - only logs errors and critical conversion events
     */
    fun calculateAndLogCurrentStats(deliveries: List<Delivery>, context: String = "DYNAMIC_REQUEST") {
        // Only log during critical conversion operations, not routine cache saves
        if (context.startsWith("CACHE_SAVE")) {
            return // Skip logging for routine cache operations
        }

        if (deliveries.isEmpty()) {
            Log.w(TAG, "⚠️ No deliveries provided for stats calculation in $context")
            return
        }

        // ✅ SSOT COMPLIANCE: Stats now calculated by address-stats-updater cloud function
        Log.d(TAG, "✅ Stats available from server-side for $context: ${deliveries.size} deliveries")
    }
}