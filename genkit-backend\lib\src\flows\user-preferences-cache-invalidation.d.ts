/**
 * ✅ FIRESTORE-ONLY: User Preferences Change Trigger
 *
 * Monitors user DND preferences and subscription changes to maintain data consistency.
 * No longer uses Redis cache - just logs changes for monitoring and debugging.
 */
export declare const onUserPreferencesChange: import("firebase-functions/core").CloudFunction<import("firebase-functions/v2/firestore").FirestoreEvent<import("firebase-functions/v2/firestore").Change<import("firebase-functions/v2/firestore").QueryDocumentSnapshot> | undefined, {
    userId: string;
}>>;
