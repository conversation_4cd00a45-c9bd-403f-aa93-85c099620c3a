// Auto-generated from User.kt
// TypeScript equivalent of Android domain.model.User

/**
 * Domain model generated from Kotlin User
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface User {
  id: string;
  userId?: string | null;
  email?: string | null;
  displayName?: string | null;
  defaultAddressId?: string | null;
  photoUrl?: string | null;
  authProviders?: string[] | null;
  accountStatus?: string | null;
  timezone?: string | null;
  createdAt?: Date | null;
  lastLoginAt?: Date | null;
  privacyPolicyAccepted?: Date | null;
  termsAccepted?: Date | null;
  version?: number | null;
  subscription?: UserSubscription | null;
  preferences?: UserPreferences | null;
  permissions?: UserPermissions | null;
  usage?: UserUsage | null;
  syncInfo?: UserSyncInfo | null;
  appSettings?: User_profile.UserAppSettings | null;
  communication?: UserCommunication | null;
  usageStats?: UserUsageStats | null;
  metadata?: Metadata | null;
}