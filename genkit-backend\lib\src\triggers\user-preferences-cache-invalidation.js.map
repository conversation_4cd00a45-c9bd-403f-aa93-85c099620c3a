{"version": 3, "file": "user-preferences-cache-invalidation.js", "sourceRoot": "", "sources": ["../../../src/triggers/user-preferences-cache-invalidation.ts"], "names": [], "mappings": ";;;AAAA,+DAAoE;AACpE,wDAAsD;AACtD,0EAA0F;AAE1F;;;;;GAKG;AACU,QAAA,uBAAuB,GAAG,IAAA,6BAAiB,EACtD,gBAAgB,EAChB,KAAK,EAAE,KAAK,EAAE,EAAE;IACd,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;IACnC,MAAM,SAAS,GAAG,6CAA6C,MAAM,KAAK,CAAC;IAE3E,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC;QACzC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;QAEvC,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,0DAA0D,CAAC,CAAC;YACpF,OAAO;QACT,CAAC;QAED,mDAAmD;QACnD,MAAM,kBAAkB,GAAG,2CAAmB,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAEpF,IAAI,kBAAkB,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,8DAA8D,CAAC,CAAC;YAExF,uCAAuC;YACvC,MAAM,2CAAmB,CAAC,mBAAmB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAEjE,0DAA0D;YAC1D,IAAI,CAAC;gBACH,4DAA4D;gBAC5D,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;oBAC3B,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,6BAA6B,EAAE,sBAAU,CAAC,SAAS,CAAC,CAAC,CAAC;wBACtD,4CAA4C,EAAE,sBAAU,CAAC,eAAe,EAAE;qBAC3E,CAAC,CAAC;oBACH,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,mCAAmC,MAAM,EAAE,CAAC,CAAC;gBACvE,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,2DAA2D,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBAClE,kDAAkD;YACpD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,4CAA4C,MAAM,EAAE,CAAC,CAAC;QAChF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,kEAAkE,CAAC,CAAC;QAC9F,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,GAAG,SAAS,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC1E,4EAA4E;IAC9E,CAAC;AACH,CAAC,CACF,CAAC"}