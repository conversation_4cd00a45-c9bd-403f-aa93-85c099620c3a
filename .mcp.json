{"mcpServers": {"fetch": {"command": "npx", "args": ["-y", "mcp-server-fetch"], "description": "Web fetching capabilities"}, "tavily-mcp": {"command": "npx", "args": ["-y", "tavily-mcp@0.1.2"], "env": {"TAVILY_API_KEY": "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"}, "description": "Web search and research capabilities"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "Sequential thinking and reasoning tools"}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}, "description": "GitHub integration for repository operations"}, "atom-of-thoughts": {"autoApprove": ["AoT-light", "atomcommands", "AoT"], "timeout": 300, "command": "node", "args": ["/mnt/c/Users/<USER>/AppData/Roaming/Claude/MCP_Atom_of_Thoughts/build/index.js"], "transportType": "stdio", "description": "Advanced reasoning and thought processes"}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>"], "description": "File system access for WSL environment"}, "perplexity": {"autoApprove": ["reason", "deep_research"], "timeout": 300, "command": "npx", "args": ["-y", "perplexity-mcp"], "env": {"PERPLEXITY_API_KEY": "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"}, "transportType": "stdio", "description": "Perplexity AI search and research capabilities"}, "n8n-mcp-server": {"autoApprove": [], "timeout": 600, "command": "node", "args": ["/mnt/c/Users/<USER>/Documents/Cline/MCP/n8n-mcp-server/dist/index.js"], "env": {"N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJiMzY4MTQwYS1iYjEwLTRlMWEtYTZhZC1mZTE4ZDQ5NTY1NjUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ0NTgxOTg5fQ.60oNGecIPC0IyrWl9wt0tV1BNh7uf3M23IONDpk88_M"}, "transportType": "stdio", "description": "n8n workflow automation server"}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "description": "Context7 MCP server for enhanced context management"}, "just-prompt-server": {"timeout": 300, "command": "uv", "args": ["--directory", "/mnt/c/Users/<USER>/AppData/Roaming/just-prompt", "run", "just-prompt", "--default-models", "openai:anthropic/claude-3.7-sonnet:thinking,openai:openai/o3-mini-high,openai:openai/o4-mini-high,openai:openai/gpt-4.1"], "transportType": "stdio", "cwd": "/mnt/c/Users/<USER>/AppData/Roaming/just-prompt", "description": "Just Prompt server for model interactions"}, "@21st-dev/magic": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest", "API_KEY=\"f679e7afa4182f9575c9825b046046f9edf299cf4a588a1c98fa9ccd09b1a461\""], "description": "21st Century Magic development tools"}, "mcp-sequentialthinking-tools": {"command": "npx", "args": ["-y", "mcp-sequentialthinking-tools"], "description": "Sequential thinking tools for enhanced reasoning"}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"], "description": "Desktop command and automation tools"}, "zen": {"command": "docker.exe", "args": ["exec", "-i", "zen-mcp-server", "python", "server.py"], "description": "Zen MCP server for mindful development"}, "think-tool": {"command": "npx", "args": ["-y", "@cgize/mcp-think-tool"], "transportType": "stdio", "pollingInterval": 30000, "startupTimeout": 30000, "restartOnFailure": true, "description": "Advanced thinking and reasoning tool"}}}