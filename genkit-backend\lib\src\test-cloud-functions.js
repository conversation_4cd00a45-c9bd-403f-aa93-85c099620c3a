"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CloudFunctionTester = void 0;
const app_1 = require("firebase-admin/app");
const flow_1 = require("@genkit-ai/flow");
const firestore_1 = require("firebase-admin/firestore");
const admin = __importStar(require("firebase-admin"));
// Import all the flows we want to test
// ❌ REMOVED: Legacy pending tips import - collections eliminated
const address_stats_updater_1 = require("./flows/address-stats-updater");
const process_uploaded_geojson_1 = require("./flows/process-uploaded-geojson");
const import_parsing_1 = require("./flows/import-parsing");
const set_manual_address_dnd_override_1 = require("./flows/set-manual-address-dnd-override");
// Import schemas for validation
// ❌ REMOVED: Legacy pending tips schema import - collections eliminated
const address_stats_updater_2 = require("./flows/address-stats-updater");
const process_uploaded_geojson_2 = require("./flows/process-uploaded-geojson");
const set_manual_address_dnd_override_2 = require("./flows/set-manual-address-dnd-override");
// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
    (0, app_1.initializeApp)();
}
class CloudFunctionTester {
    constructor() {
        this.results = [];
        this.firestore = (0, firestore_1.getFirestore)();
    }
    async runAllTests() {
        console.log('🚀 Starting Cloud Function Testing Simulation...\n');
        // Test each function category
        // ❌ REMOVED: testPendingTipsFlow() - legacy collections eliminated
        await this.testAddressStatsFlow();
        await this.testGeoJsonProcessing();
        await this.testImportParsing();
        await this.testManualDndOverride();
        await this.testFirestoreReadWrite();
        this.printSummary();
        return this.results;
    }
    // ❌ REMOVED: testPendingTipsFlow method - legacy collections eliminated
    // The proper domain model uses tipAmount=null for pending deliveries
    // Address Stats Updater already handles this correctly
    async testAddressStatsFlow() {
        console.log('📊 Testing Address Stats Update...');
        const testInput = {
            userId: 'test-user-123',
            addressId: 'test-address-456'
        };
        await this.runTest('onDeliveryWrittenUpdateAddressStats', 'Update Address Delivery Stats', async () => {
            const validation = address_stats_updater_2.AddressStatsUpdateInputSchema.safeParse(testInput);
            if (!validation.success) {
                throw new Error(`Invalid test data: ${validation.error.message}`);
            }
            return await (0, flow_1.runFlow)(address_stats_updater_1.updateAddressDeliveryStatsFlow, validation.data);
        });
    }
    async testGeoJsonProcessing() {
        console.log('🗺️ Testing GeoJSON File Processing...');
        const testInput = {
            bucket: 'autogratuity-me.firebasestorage.app',
            file: 'user-uploads/test-user-123/test-geojson.json'
        };
        await this.runTest('onGeoJsonFileFinalized', 'Process Uploaded GeoJSON File', async () => {
            const validation = process_uploaded_geojson_2.GcsEventDataInputSchema.safeParse(testInput);
            if (!validation.success) {
                throw new Error(`Invalid test data: ${validation.error.message}`);
            }
            return await (0, flow_1.runFlow)(process_uploaded_geojson_1.processUploadedGeoJsonFile, validation.data);
        });
    }
    async testImportParsing() {
        console.log('📥 Testing Import Data Parsing...');
        const testText = `
        Order #12345
        Delivery to: 123 Main St, City, State 12345
        Tip: $5.50
        Platform: Shipt
        Date: 2024-01-15
        `;
        await this.runTest('testParseImportFlow', 'Parse Import Data with Logging', async () => {
            return await (0, flow_1.runFlow)(import_parsing_1.parseImportDataWithLogging, testText.trim());
        });
    }
    // Google API testing removed - unused functionality
    // Dashboard batch testing removed - unused functionality
    async testManualDndOverride() {
        console.log('🚫 Testing Manual DND Override...');
        const testInput = {
            userId: 'test-user-123',
            addressId: 'test-address-456',
            desiredState: 'FORCE_DND'
        };
        await this.runTest('setManualAddressDndOverride', 'Set Manual Address DND Override', async () => {
            const validation = set_manual_address_dnd_override_2.SetManualAddressDndInputSchema.safeParse(testInput);
            if (!validation.success) {
                throw new Error(`Invalid test data: ${validation.error.message}`);
            }
            return await (0, flow_1.runFlow)(set_manual_address_dnd_override_1.setManualAddressDndOverrideFlow, validation.data);
        });
    }
    async testFirestoreReadWrite() {
        console.log('🔥 Testing Firestore Read/Write Operations...');
        const testDocId = `test-doc-${Date.now()}`;
        const testData = {
            testField: 'test-value',
            timestamp: new Date(),
            userId: 'test-user-123'
        };
        // Test Write
        await this.runTest('Firestore', 'Write Test Document', async () => {
            await this.firestore.collection('test-collection').doc(testDocId).set(testData);
            return { success: true, docId: testDocId };
        });
        // Test Read
        await this.runTest('Firestore', 'Read Test Document', async () => {
            const doc = await this.firestore.collection('test-collection').doc(testDocId).get();
            if (!doc.exists) {
                throw new Error('Document not found');
            }
            return doc.data();
        });
        // Test Delete (cleanup)
        await this.runTest('Firestore', 'Delete Test Document', async () => {
            await this.firestore.collection('test-collection').doc(testDocId).delete();
            return { success: true, deleted: testDocId };
        });
    }
    async runTest(functionName, testName, testFunction) {
        const startTime = Date.now();
        try {
            console.log(`  ⏳ Running: ${testName}...`);
            const result = await testFunction();
            const duration = Date.now() - startTime;
            this.results.push({
                functionName,
                testName,
                success: true,
                result,
                duration
            });
            console.log(`  ✅ ${testName} - Success (${duration}ms)`);
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.results.push({
                functionName,
                testName,
                success: false,
                error: error.message,
                duration
            });
            console.log(`  ❌ ${testName} - Failed: ${error.message} (${duration}ms)`);
        }
        console.log(''); // Add spacing
    }
    printSummary() {
        console.log('📋 Test Summary:');
        console.log('================');
        const successful = this.results.filter(r => r.success).length;
        const failed = this.results.filter(r => !r.success).length;
        const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
        console.log(`Total Tests: ${this.results.length}`);
        console.log(`Successful: ${successful}`);
        console.log(`Failed: ${failed}`);
        console.log(`Total Duration: ${totalDuration}ms`);
        console.log('');
        // Group by function
        const byFunction = this.results.reduce((acc, result) => {
            if (!acc[result.functionName]) {
                acc[result.functionName] = [];
            }
            acc[result.functionName].push(result);
            return acc;
        }, {});
        Object.entries(byFunction).forEach(([functionName, results]) => {
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            console.log(`${functionName}: ${successCount}/${totalCount} tests passed`);
            results.forEach(result => {
                const status = result.success ? '✅' : '❌';
                console.log(`  ${status} ${result.testName} (${result.duration}ms)`);
                if (!result.success && result.error) {
                    console.log(`    Error: ${result.error}`);
                }
            });
            console.log('');
        });
    }
}
exports.CloudFunctionTester = CloudFunctionTester;
// Main execution function
async function main() {
    const tester = new CloudFunctionTester();
    const results = await tester.runAllTests();
    // Exit with error code if any tests failed
    const hasFailures = results.some(r => !r.success);
    process.exit(hasFailures ? 1 : 0);
}
// Run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=test-cloud-functions.js.map