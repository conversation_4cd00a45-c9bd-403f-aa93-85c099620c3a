package com.autogratuity.data.repository.config

import android.util.Log
import com.autogratuity.data.repository.core.AtomicCacheSystem
import com.autogratuity.data.repository.core.CacheSource
import com.autogratuity.data.repository.core.DomainCacheSystem
import com.autogratuity.data.repository.core.SsotOwnership
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.datetime.Clock
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration.Companion.hours
import kotlin.time.TimeSource

/**
 * A+ Configuration Cache System for atomic configuration data management.
 * 
 * Provides specialized caching functionality for application configuration data
 * with atomic operations, reactive programming, and comprehensive analytics.
 * 
 * Features:
 * - Configuration versioning and validation
 * - Feature flag management and A/B testing support
 * - Cloud function integration for dynamic configuration updates
 * - Rich metadata for configuration analytics and audit trails
 * - Reactive programming for real-time configuration updates
 * - SSOT awareness for configuration field ownership
 */
@Singleton
class ConfigCacheSystem @Inject constructor(
    timeSource: TimeSource,
    applicationScope: CoroutineScope,
    private val ioDispatcher: kotlinx.coroutines.CoroutineDispatcher
) :
    AtomicCacheSystem<String, Map<String, Any>>(timeSource),
    DomainCacheSystem<String, Map<String, Any>> {
    
    // Domain-specific configuration
    override val defaultTtl: kotlin.time.Duration = 6.hours  // Configuration data TTL
    override val maxCacheSize: Int = 500                     // Configuration entries
    
    companion object {
        private const val TAG = "ConfigCacheSystem"
        
        // Configuration domains for organized caching
        private const val DOMAIN_APP_CONFIG = "app_config"
        private const val DOMAIN_FEATURE_FLAGS = "feature_flags"
        private const val DOMAIN_UI_CONFIG = "ui_config"
        private const val DOMAIN_API_CONFIG = "api_config"
        private const val DOMAIN_ANALYTICS_CONFIG = "analytics_config"
        
        // SSOT field ownership for configuration data
        private val CONFIG_FIELD_OWNERSHIP = mapOf(
            "version" to SsotOwnership.CLOUD_OWNED,
            "last_updated" to SsotOwnership.CLOUD_OWNED,
            "feature_flags" to SsotOwnership.CLOUD_OWNED,
            "api_endpoints" to SsotOwnership.CLOUD_OWNED,
            "user_preferences" to SsotOwnership.CLIENT_OWNED,
            "ui_customization" to SsotOwnership.SHARED,
            "cache_settings" to SsotOwnership.SHARED
        )
    }
    
    // Modern scope management: use centralized ApplicationScope
    // Configuration should persist across activity lifecycle
    private val cacheScope = applicationScope
    
    // Reactive state for current configuration
    private val _currentAppConfig = MutableStateFlow<Map<String, Any>?>(null)
    private val _currentFeatureFlags = MutableStateFlow<Map<String, Any>?>(null)
    private val _configurationVersion = MutableStateFlow<String?>(null)
    
    
    
    // --- Configuration-specific Operations ---
    
    /**
     * Cache configuration data with domain-specific metadata
     */
    suspend fun cacheConfiguration(
        configKey: String,
        configData: Map<String, Any>,
        source: CacheSource,
        domain: String = DOMAIN_APP_CONFIG,
        version: String? = null
    ) {
        val metadata = buildMap<String, Any> {
            put("domain", domain)
            put("config_type", getConfigurationType(configKey))
            put("fields_count", configData.size)
            put("last_accessed", Clock.System.now().toEpochMilliseconds())
            version?.let { put("version", it) }
            
            // Add configuration-specific metadata
            when (domain) {
                DOMAIN_FEATURE_FLAGS -> {
                    put("enabled_flags", configData.values.count { it == true })
                    put("total_flags", configData.size)
                }
                DOMAIN_API_CONFIG -> {
                    put("endpoints_count", configData.keys.count { it.contains("endpoint") })
                    put("timeout_settings", configData.keys.count { it.contains("timeout") })
                }
                DOMAIN_UI_CONFIG -> {
                    put("theme_settings", configData.keys.count { it.contains("theme") })
                    put("layout_settings", configData.keys.count { it.contains("layout") })
                }
            }
        }
        
        put(configKey, configData, defaultTtl, metadata, source)
        
        // Update reactive state for specific domains
        when (domain) {
            DOMAIN_APP_CONFIG -> _currentAppConfig.value = configData
            DOMAIN_FEATURE_FLAGS -> _currentFeatureFlags.value = configData
        }
        
        version?.let { _configurationVersion.value = it }
        
        Log.d(TAG, "Cached configuration: $configKey (domain: $domain, version: $version)")
    }
    
    /**
     * Update configuration with SSOT field ownership validation
     */
    suspend fun updateConfigurationWithSsot(
        configKey: String,
        updates: Map<String, Any>,
        source: CacheSource
    ) {
        val current = get(configKey)
        if (current == null) {
            Log.w(TAG, "Cannot update non-existent configuration: $configKey")
            return
        }
        
        // Validate SSOT field ownership
        val invalidFields = updates.keys.filter { fieldKey ->
            CONFIG_FIELD_OWNERSHIP[fieldKey] == SsotOwnership.CLOUD_OWNED && source != CacheSource.CLOUD_FUNCTION ||
            CONFIG_FIELD_OWNERSHIP[fieldKey] == SsotOwnership.READ_ONLY
        }
        
        if (invalidFields.isNotEmpty()) {
            Log.w(TAG, "Attempted to update cloud-owned/read-only fields: $invalidFields for config: $configKey")
            return
        }
        
        // Apply updates
        val updatedConfig = current.toMutableMap().apply {
            putAll(updates)
            put("last_modified", Clock.System.now().toEpochMilliseconds())
        }
        
        cacheConfiguration(
            configKey = configKey,
            configData = updatedConfig,
            source = source
        )
        
        Log.d(TAG, "Updated configuration with SSOT validation: $configKey")
    }
    
    /**
     * Handle cloud function configuration updates
     */
    suspend fun updateFromCloudFunction(
        configKey: String,
        cloudConfig: Map<String, Any>
    ) {
        val enhancedConfig = cloudConfig.toMutableMap().apply {
            put("cloud_updated_at", Clock.System.now().toEpochMilliseconds())
            put("source", "cloud_function")
        }
        
        cacheConfiguration(
            configKey = configKey,
            configData = enhancedConfig,
            source = CacheSource.CLOUD_FUNCTION,
            version = cloudConfig["version"] as? String
        )
        
        Log.d(TAG, "Updated configuration from cloud function: $configKey")
    }
    
    /**
     * Get feature flag status with fallback
     */
    suspend fun getFeatureFlag(flagName: String, defaultValue: Boolean = false): Boolean {
        val featureFlags = get("feature_flags")
        return featureFlags?.get(flagName) as? Boolean ?: defaultValue
    }
    
    /**
     * Update feature flag status
     */
    suspend fun updateFeatureFlag(
        flagName: String, 
        enabled: Boolean,
        source: CacheSource = CacheSource.MANUAL
    ) {
        val currentFlags = get("feature_flags")?.toMutableMap() ?: mutableMapOf()
        currentFlags[flagName] = enabled
        currentFlags["last_updated"] = Clock.System.now().toEpochMilliseconds()
        
        cacheConfiguration(
            configKey = "feature_flags",
            configData = currentFlags,
            source = source,
            domain = DOMAIN_FEATURE_FLAGS
        )
        
        Log.d(TAG, "Updated feature flag: $flagName = $enabled")
    }
    
    /**
     * Invalidate configuration cache by domain
     */
    suspend fun invalidateConfigurationDomain(domain: String) {
        invalidateByPredicate { key, metadata ->
            metadata["domain"] == domain
        }
        
        Log.d(TAG, "Invalidated configurations in domain: $domain")
    }
    
    // --- Reactive Configuration Observables ---
    
    /**
     * Observe current app configuration
     */
    fun observeAppConfiguration(): StateFlow<Map<String, Any>?> = _currentAppConfig.asStateFlow()
    
    /**
     * Observe current feature flags
     */
    fun observeFeatureFlags(): StateFlow<Map<String, Any>?> = _currentFeatureFlags.asStateFlow()
    
    /**
     * Observe configuration version
     */
    fun observeConfigurationVersion(): StateFlow<String?> = _configurationVersion.asStateFlow()
    
    /**
     * Observe specific feature flag
     */
    fun observeFeatureFlag(flagName: String, defaultValue: Boolean = false): Flow<Boolean> {
        return observeFeatureFlags().map { flags ->
            flags?.get(flagName) as? Boolean ?: defaultValue
        }
    }
    
    // --- Configuration Analytics ---
    
    /**
     * Get configuration cache metrics
     */
    suspend fun getConfigurationCacheMetrics(): Map<String, Any> {
        val baseMetrics = getMetrics()
        val allEntries = observeAll().map { it }.first()
        
        // Add configuration-specific metrics
        val configsByDomain = mutableMapOf<String, Int>()
        for (entry in allEntries.entries) {
            val key = entry.key
            val metadata = getWithMetadata(key)?.second
            val domain = metadata?.get("domain") as? String ?: "unknown"
            configsByDomain[domain] = (configsByDomain[domain] ?: 0) + 1
        }
        
        return mapOf(
            "hitRate" to baseMetrics.hitRate,
            "missRate" to baseMetrics.missRate,
            "totalEntries" to allEntries.size,
            "configurations_by_domain" to configsByDomain,
            "feature_flags_count" to (get("feature_flags")?.size ?: 0),
            "current_version" to (_configurationVersion.value ?: "unknown")
        )
    }
    
    // --- Private Helper Methods ---
    
    private fun getConfigurationType(configKey: String): String {
        return when {
            configKey.contains("feature") -> "feature_flag"
            configKey.contains("api") -> "api_config"
            configKey.contains("ui") -> "ui_config"
            configKey.contains("analytics") -> "analytics_config"
            else -> "general_config"
        }
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        cacheScope.cancel()
        Log.d(TAG, "ConfigCacheSystem cleanup completed")
    }
} 