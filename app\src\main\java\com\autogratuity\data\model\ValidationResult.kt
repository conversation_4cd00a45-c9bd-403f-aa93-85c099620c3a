package com.autogratuity.data.model

/**
 * Result of validating a single item (delivery, address, etc.)
 */
data class SingleValidationResult(
    val isValid: Boolean,
    val warnings: List<String>,
    val errors: List<String>
)

/**
 * Result of validating multiple items in bulk
 */
data class BulkValidationResult(
    val validDeliveries: List<com.autogratuity.domain.model.Delivery>,
    val totalProcessed: Int,
    val warningCount: Int,
    val errorCount: Int,
    val issues: List<String>
)

/**
 * Legacy validation result class for backward compatibility
 * TODO: Migrate usages to BulkValidationResult
 */
data class ValidationResult(
    val validDeliveries: List<com.autogratuity.domain.model.Delivery>,
    val totalProcessed: Int,
    val warningCount: Int,
    val errorCount: Int,
    val issues: List<String>
)