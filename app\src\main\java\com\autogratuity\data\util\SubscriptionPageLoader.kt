package com.autogratuity.data.util

import android.util.Log
import com.autogratuity.data.datasource.remote.SubscriptionRemoteDataSource
import com.autogratuity.data.mapper.SubscriptionMapper
import com.autogratuity.data.model.Result as DataResult
import com.autogratuity.domain.model.UserSubscription
import com.google.firebase.firestore.DocumentSnapshot
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Page loader for UserSubscription data using Firestore pagination.
 * Provides memory-efficient loading of subscription data with proper error handling.
 * 
 * REFACTORED: Now delegates ALL Firestore operations to SubscriptionRemoteDataSource
 * following clarity.md architectural guidelines.
 * 
 * Uses existing FlowPageLoader interface and PageResult from PaginatedDataSource.kt
 */
@Singleton
class SubscriptionPageLoader @Inject constructor(
    private val remoteDataSource: SubscriptionRemoteDataSource,
    private val subscriptionMapper: SubscriptionMapper
) : FlowPageLoader<UserSubscription, DocumentSnapshot> {

    companion object {
        private const val TAG = "SubscriptionPageLoader"
    }

    override suspend fun loadPage(
        pageSize: Int,
        pageKey: DocumentSnapshot?
    ): Result<PageResult<UserSubscription, DocumentSnapshot>> {
        return try {
            Log.d(TAG, "loadPage: Loading subscription page with size: $pageSize, key: ${pageKey?.id}")

            // Delegate to RemoteDataSource for ALL Firestore operations per clarity.md
            val pageResult = remoteDataSource.loadSubscriptionPage(pageSize, pageKey)
            
            when (pageResult) {
                is DataResult.Success -> {
                    val subscriptionDtos = pageResult.data.items
                    val nextPageKey = pageResult.data.nextPageKey
                    
                    // Convert DTOs to domain models using mapper
                    val subscriptions = subscriptionDtos.mapNotNull { dto ->
                        val mapperResult = subscriptionMapper.dtoToSsot(dto)
                        when (mapperResult) {
                            is DataResult.Success -> mapperResult.data
                            is DataResult.Error -> {
                                Log.w(TAG, "Error mapping subscription DTO to domain model", mapperResult.exception)
                                null
                            }
                            is DataResult.Loading -> {
                                Log.w(TAG, "Unexpected Loading state from mapper")
                                null
                            }
                        }
                    }

                    Log.d(TAG, "loadPage: Successfully loaded ${subscriptions.size} subscriptions")
                    Result.success(PageResult(subscriptions, nextPageKey))
                }
                is DataResult.Error -> {
                    Log.e(TAG, "loadPage: Error loading page from RemoteDataSource", pageResult.exception)
                    Result.failure(pageResult.exception)
                }
                is DataResult.Loading -> {
                    Log.w(TAG, "loadPage: Unexpected Loading state from RemoteDataSource")
                    Result.failure(IllegalStateException("RemoteDataSource returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "loadPage: Unexpected error in SubscriptionPageLoader", e)
            Result.failure(e)
        }
    }
}

/**
 * Factory for creating subscription-specific page loaders
 */
@Singleton
class SubscriptionPageLoaderFactory @Inject constructor(
    private val remoteDataSource: SubscriptionRemoteDataSource,
    private val subscriptionMapper: SubscriptionMapper
) {
    
    /**
     * Create a page loader for user subscriptions
     */
    fun createSubscriptionPageLoader(): SubscriptionPageLoader {
        return SubscriptionPageLoader(remoteDataSource, subscriptionMapper)
    }
    
    /**
     * Create a page loader for active subscriptions only
     * Uses RemoteDataSource's loadActiveSubscriptionPage method
     */
    fun createActiveSubscriptionPageLoader(): ActiveSubscriptionPageLoader {
        return ActiveSubscriptionPageLoader(remoteDataSource, subscriptionMapper)
    }
}

/**
 * Specialized page loader for active subscriptions only
 */
@Singleton
class ActiveSubscriptionPageLoader @Inject constructor(
    private val remoteDataSource: SubscriptionRemoteDataSource,
    private val subscriptionMapper: SubscriptionMapper
) : FlowPageLoader<UserSubscription, DocumentSnapshot> {

    companion object {
        private const val TAG = "ActiveSubscriptionPageLoader"
    }

    override suspend fun loadPage(
        pageSize: Int,
        pageKey: DocumentSnapshot?
    ): Result<PageResult<UserSubscription, DocumentSnapshot>> {
        return try {
            Log.d(TAG, "loadPage: Loading active subscription page with size: $pageSize, key: ${pageKey?.id}")

            // Delegate to RemoteDataSource for active subscription queries per clarity.md
            val pageResult = remoteDataSource.loadActiveSubscriptionPage(pageSize, pageKey)
            
            when (pageResult) {
                is DataResult.Success -> {
                    val subscriptionDtos = pageResult.data.items
                    val nextPageKey = pageResult.data.nextPageKey
                    
                    // Convert DTOs to domain models using mapper
                    val subscriptions = subscriptionDtos.mapNotNull { dto ->
                        val mapperResult = subscriptionMapper.dtoToSsot(dto)
                        when (mapperResult) {
                            is DataResult.Success -> mapperResult.data
                            is DataResult.Error -> {
                                Log.w(TAG, "Error mapping subscription DTO to domain model", mapperResult.exception)
                                null
                            }
                            is DataResult.Loading -> {
                                Log.w(TAG, "Unexpected Loading state from mapper")
                                null
                            }
                        }
                    }

                    Log.d(TAG, "loadPage: Successfully loaded ${subscriptions.size} active subscriptions")
                    Result.success(PageResult(subscriptions, nextPageKey))
                }
                is DataResult.Error -> {
                    Log.e(TAG, "loadPage: Error loading active page from RemoteDataSource", pageResult.exception)
                    Result.failure(pageResult.exception)
                }
                is DataResult.Loading -> {
                    Log.w(TAG, "loadPage: Unexpected Loading state from RemoteDataSource")
                    Result.failure(IllegalStateException("RemoteDataSource returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "loadPage: Unexpected error in ActiveSubscriptionPageLoader", e)
            Result.failure(e)
        }
    }
}
