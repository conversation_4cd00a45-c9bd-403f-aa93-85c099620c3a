package com.autogratuity.data.repository.utils

import androidx.annotation.NonNull

/**
 * Standard constants for repository implementations.
 * Provides consistent naming conventions, cache key formats, and other constants
 * to enforce standardization across repository implementations.
 */
object RepositoryConstants {

    // Cache expiry constants
    const val CACHE_MAX_AGE_DELIVERY_DETAIL_MS = 300000L // 5 minutes

    // Default document IDs
    const val DEFAULT_ADDRESS_ID = "default_address" // Added: For default address creation

    /**
     * Cache key prefixes for different entity types.
     * These prefixes should be used when generating cache keys to ensure consistency.
     */
    object CachePrefix {
        const val USER_PROFILE = "user_profile"
        const val APP_CONFIG = "app_config"
        const val NOTIFICATION_PATTERNS = "notification_patterns" // Added for NotificationPatternsSchema
        const val ADDRESS = "address"
        const val ADDRESSES = "addresses"
        const val DELIVERY = "delivery"
        const val DELIVERIES = "deliveries"
        const val SUBSCRIPTION = "subscription"
    }

    /**
     * Entity type identifiers for error reporting and logging.
     * These identifiers should be used when calling error handling methods.
     */
    object EntityType {
        const val USER_PROFILE = "user_profile"
        const val APP_CONFIG = "app_config"
        const val NOTIFICATION_PATTERNS = "notification_patterns" // Added for NotificationPatternsSchema
        const val ADDRESS = "address"
        const val DELIVERY = "delivery"
        const val SUBSCRIPTION = "subscription"
        const val DEVICE = "device"
        const val COUNTER = "counter"
    }

    /**
     * Operation name templates for error reporting and logging.
     * These should be used when calling error handling methods.
     */
    object OperationName {
        const val GET = "get %s"
        const val GET_ALL = "get all %ss"
        const val GET_BY_ID = "get %s by id"
        const val FIND_BY = "find %s by %s"
        const val UPDATE = "update %s"
        const val UPDATE_FIELDS = "update %s fields"
        const val DELETE = "delete %s"
        const val OBSERVE = "observe %s"
        const val OBSERVE_ALL = "observe all %ss"
        const val SYNC = "sync %s"
    }

    /**
     * Generates a standardized cache key for a single entity.
     *
     * @param prefix The entity type prefix
     * @param id The entity ID
     * @param userId The user ID (optional, can be null)
     * @return A standardized cache key
     */
    @JvmStatic
    fun singleEntityCacheKey(prefix: String, id: String, userId: String?): String {
        return userId?.let {
            "${prefix}_${it}_${id}"
        } ?: "${prefix}_${id}"
    }

    /**
     * Generates a standardized operation name for error reporting.
     *
     * @param template The operation name template
     * @param entityType The entity type
     * @param criteria The search criteria (optional, can be null)
     * @return A standardized operation name
     */
    @JvmStatic
    fun operationName(template: String, entityType: String, criteria: String?): String {
        return criteria?.let {
            String.format(template, entityType, it)
        } ?: String.format(template, entityType)
    }

    // Collection Names (Top Level)
    const val USERS = "users"
    const val SUBSCRIPTION_RECORDS = "subscription_records"
    const val SYNC_OPERATIONS = "sync_operations"

    // ADDED Inner classes for Firestore collection and document names
    object FirestoreCollection {
        const val SYSTEM_CONFIG = "system_config"
        const val USERS = "users" // Centralizing this constant
        const val COMPONENTS_SUBCOLLECTION = "components" // For /system_config/app_config/components
        const val USER_CONFIG_SUBCOLLECTION = "user_config"
        const val USER_ADDRESSES_SUBCOLLECTION = "user_addresses"
        const val USER_DELIVERIES_SUBCOLLECTION = "user_deliveries"
        // Add other top-level collection names here as needed
    }

    object FirestoreDocument {
        const val GLOBAL_APP_CONFIG = "app_config"
        const val NOTIFICATION_PATTERNS = "notification_patterns"
        const val USER_SPECIFIC_PREFERENCES = "user_specific_preferences"
        // Add other common document IDs here as needed
    }

    // ADDED: Inner class for common Firestore field paths used in queries
    object FirestoreFieldPath {
        const val ADDRESS_FLAGS_MANUAL_DND_STATE = "addressData.flags.manualDndState"
        const val DELIVERY_METADATA_SOURCE = "deliveryData.metadata.source"
        // Add other common field paths here as needed
    }

    // ADDED: Inner class for common Firestore field values
    object FirestoreFieldValue {
        const val DND_STATE_FORCE_DND = "FORCE_DND"
        const val DELIVERY_SOURCE_SHIPT_NOTIFICATION = "shipt_notification_capture"
        const val USER_PERMISSION_LEVEL_PRO = "pro"
        // Add other common field values here as needed
    }

    // ADDED: Specific constants for repository configurations
    const val RECENT_DELIVERIES_LISTENER_LIMIT = 50
    const val DELIVERY_CACHE_TTL_MINUTES = 30

    // ADDED: Application-specific limits and constants
    object AppLimits {
        const val MAX_DND_MARKINGS_FREE_TIER = 15
        const val MAX_DND_MARKINGS_PRO_TIER = Integer.MAX_VALUE // Effectively unlimited
    }

    // Paging Constants
    const val DEFAULT_PAGE_SIZE = 20 // Default page size for Paging 3
} 