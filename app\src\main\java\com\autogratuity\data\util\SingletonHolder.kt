package com.autogratuity.data.util

import java.util.concurrent.atomic.AtomicReference
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.reflect.KClass

/**
 * Generic singleton holder for creating and managing singleton instances.
 * Ensures thread-safe, lazy initialization of singletons.
 *
 * @param T The type of the singleton instance.
 * @param A The type of the argument required by the creator function.
 */
abstract class SingletonHolder<T, A> {
    @Volatile
    private var instance: T? = null

    protected abstract fun create(argument: A): T

    fun getInstance(argument: A): T {
        val i = instance // Read volatile instance once
        if (i != null) {
            return i
        }
        return synchronized(this) {
            val i2 = instance // Double-check locking pattern
            if (i2 != null) {
                i2
            } else {
                val created = create(argument)
                instance = created
                created
            }
        }
    }

    /**
     * Clears the singleton instance.
     * Subsequent calls to getInstance will recreate the instance.
     */
    fun clearInstance() {
        instance = null // Volatile write ensures visibility
    }
}

/**
 * Modern thread-safe singleton holder using standard Java atomic operations.
 * This replaces the legacy double-checked locking pattern with atomic references
 * following 2025 Kotlin standards.
 * 
 * @param T The type of the singleton instance
 * @param A The type of the argument required by the creator function
 * 
 * @deprecated Prefer dependency injection with @Singleton annotation instead of manual singleton management
 */
@Deprecated(
    message = "Use dependency injection with @Singleton annotation instead",
    replaceWith = ReplaceWith("@Singleton class with @Inject constructor"),
    level = DeprecationLevel.WARNING
)
abstract class ModernSingletonHolder<T : Any, A> {
    private val instance = AtomicReference<T?>(null)

    protected abstract fun create(argument: A): T

    /**
     * Get or create the singleton instance using atomic operations
     */
    fun getInstance(argument: A): T {
        return instance.updateAndGet { current ->
            current ?: create(argument)
        }!!
    }

    /**
     * Clear the singleton instance atomically
     */
    fun clearInstance() {
        instance.set(null)
    }
    
    /**
     * Check if instance is currently initialized without creating it
     */
    val isInitialized: Boolean
        get() = instance.get() != null
}

/**
 * Modern atomic singleton holder for instances that don't require arguments
 * 
 * @param T The type of the singleton instance
 * 
 * @deprecated Prefer dependency injection with @Singleton annotation instead
 */
@Deprecated(
    message = "Use dependency injection with @Singleton annotation instead",
    replaceWith = ReplaceWith("@Singleton class with @Inject constructor"),
    level = DeprecationLevel.WARNING
)
abstract class AtomicSingletonHolder<T : Any> {
    private val instance = AtomicReference<T?>(null)

    protected abstract fun create(): T

    /**
     * Get or create the singleton instance using atomic operations
     */
    fun getInstance(): T {
        return instance.updateAndGet { current ->
            current ?: create()
        }!!
    }

    /**
     * Clear the singleton instance atomically
     */
    fun clearInstance() {
        instance.set(null)
    }
    
    /**
     * Check if instance is currently initialized
     */
    val isInitialized: Boolean
        get() = instance.get() != null
}

/**
 * Modern factory-based singleton holder using standard Java atomic operations
 * This provides a more functional approach to singleton management
 * 
 * @param T The type of the singleton instance
 * @param A The type of the argument required by the factory function
 * 
 * @deprecated Prefer dependency injection with @Singleton annotation instead
 */
@Deprecated(
    message = "Use dependency injection with @Singleton annotation instead",
    level = DeprecationLevel.WARNING
)
class AtomicFactorySingleton<T : Any, A>(
    private val factory: (A) -> T
) {
    private val instance = AtomicReference<T?>(null)

    /**
     * Get or create the singleton instance using the provided factory
     */
    fun getInstance(argument: A): T {
        return instance.updateAndGet { current ->
            current ?: factory(argument)
        }!!
    }

    /**
     * Clear the singleton instance atomically
     */
    fun clearInstance() {
        instance.set(null)
    }
    
    /**
     * Check if instance is currently initialized
     */
    val isInitialized: Boolean
        get() = instance.get() != null
}

/**
 * Thread-safe lazy singleton using Kotlin's built-in lazy delegation
 * This is the recommended approach for simple singletons in modern Kotlin
 * 
 * Usage:
 * ```
 * val myService by threadSafeLazy { MyService() }
 * ```
 */
fun <T> threadSafeLazy(initializer: () -> T): Lazy<T> = lazy(LazyThreadSafetyMode.SYNCHRONIZED, initializer)

/**
 * Atomic lazy singleton with custom synchronization
 * Provides more control over the initialization process
 */
class AtomicLazy<T : Any>(private val initializer: () -> T) {
    private val instance = AtomicReference<T?>(null)
    private val isInitializing = AtomicBoolean(false)

    val value: T
        get() {
            // Fast path: already initialized
            instance.get()?.let { return it }
            
            // Atomic initialization check
            if (isInitializing.compareAndSet(false, true)) {
                try {
                    // Double-check pattern with atomic operations
                    return instance.updateAndGet { current ->
                        current ?: initializer()
                    }!!
                } finally {
                    isInitializing.set(false)
                }
            } else {
                // Another thread is initializing, wait for completion
                while (isInitializing.get()) {
                    Thread.yield()
                }
                return instance.get() ?: throw IllegalStateException("Initialization failed")
            }
        }

    val isInitialized: Boolean
        get() = instance.get() != null
} 