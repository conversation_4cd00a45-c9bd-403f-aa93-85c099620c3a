 D  ✅ MODERN AddEditDeliveryViewModel initialized
 D  tagSocket(147) with statsTag=0xffffffff, statsUid=-1
 D  [presentation.AddEditDeliveryViewModel] init Deps:4 Data:new_delivery_created State:Initialized [STATEFLOW_UPDATED]: 0ms [OK]
 D  ✅ PlacesClient created successfully
 D  User session ready - initializing delivery form data
 D  LaunchedEffect triggered but selectedPlace is null
 D  tagSocket(216) with statsTag=0xffffffff, statsUid=-1
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  Loaded 5 addresses
 D  show(ime(), fromIme=false)
 I  com.autogratuity:2f08a5c4: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
 I  Flattened final assist data: 900 bytes, containing 1 windows, 6 views
 I  com.autogratuity:88c299f2: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
 D  app_time_stats: avg=25.73ms min=12.06ms max=457.94ms count=50
 D  show(ime(), fromIme=true)
 I  com.autogratuity:88c299f2: onShown
 D  app_time_stats: avg=34.60ms min=8.24ms max=500.52ms count=29
 D  validateOrderIdInput: input='784 W Hickman Rd, Waukee, IA 50263, USA' -> filtered='78450263'
 D  getOrderIdErrorMessage: input='78450263', error='Order ID must be exactly 9 digits (currently 8)'
 D  show(ime(), fromIme=true)
 I  com.autogratuity:b80ffee6: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=75.31ms min=1.59ms max=518.30ms count=14
 D  show(ime(), fromIme=false)
 I  com.autogratuity:ae97137f: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:ae97137f: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:6f8d6266: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
 D  app_time_stats: avg=462.20ms min=387.83ms max=499.76ms count=3
 D  show(ime(), fromIme=true)
 I  com.autogratuity:a77ea8db: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:6f8d6266: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:3b5f7c26: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  🔍 GOOGLE_PLACES_AUTOCOMPLETE_START: Starting autocomplete search for query='784 W Hickman Rd, Waukee, IA 50263, USA...'
 E  ❌ Failed to get App Check token for Places SDK: Too many attempts. (Ask Gemini)
com.google.firebase.FirebaseException: Too many attempts.
	at com.google.firebase.appcheck.internal.NetworkClient.exchangeAttestationForAppCheckToken(NetworkClient.java:118)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider.lambda$getToken$1$com-google-firebase-appcheck-debug-internal-DebugAppCheckProvider(DebugAppCheckProvider.java:121)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider$$ExternalSyntheticLambda2.call(D8$$SyntheticClass:0)
	at com.google.android.gms.tasks.zzz.run(com.google.android.gms:play-services-tasks@@18.1.0:1)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
	at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
	at java.lang.Thread.run(Thread.java:1012)
 D  tagSocket(218) with statsTag=0xffffffff, statsUid=-1
 D  ✅ GOOGLE_PLACES_AUTOCOMPLETE_SUCCESS: Found 2 predictions for query='784 W Hickman Rd, Waukee, IA 50263, USA...'
 D  app_time_stats: avg=58.03ms min=7.31ms max=458.09ms count=18
 D  app_time_stats: avg=409.67ms min=1.19ms max=4054.81ms count=10
 D  Fetching place details for: 784 W Hickman Rd, Waukee, IA 50263, USA
 D  🔍 GOOGLE_PLACES_DETAILS_START: Fetching place details for placeId='ChIJb4RSl18j7IcR72v3JxSeaL4', prediction='784 W Hickman Rd, Waukee, IA 50263, USA'
 D  🔍 GOOGLE_PLACES_DETAILS_REQUEST: Created request with fields=ID, DISPLAY_NAME, FORMATTED_ADDRESS, ADDRESS_COMPONENTS, LOCATION, sessionToken present
 E  ❌ Failed to get App Check token for Places SDK: Too many attempts. (Ask Gemini)
com.google.firebase.FirebaseException: Too many attempts.
	at com.google.firebase.appcheck.internal.NetworkClient.exchangeAttestationForAppCheckToken(NetworkClient.java:118)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider.lambda$getToken$1$com-google-firebase-appcheck-debug-internal-DebugAppCheckProvider(DebugAppCheckProvider.java:121)
	at com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider$$ExternalSyntheticLambda2.call(D8$$SyntheticClass:0)
	at com.google.android.gms.tasks.zzz.run(com.google.android.gms:play-services-tasks@@18.1.0:1)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
	at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
	at java.lang.Thread.run(Thread.java:1012)
 D  Place details fetched successfully: 784 W Hickman Rd, Waukee, IA 50263, USA
 I  ✅ GOOGLE_PLACES_DETAILS_SUCCESS: Place details fetched successfully for placeId='ChIJb4RSl18j7IcR72v3JxSeaL4', address='784 W Hickman Rd, Waukee, IA 50263, USA'
 D  Selected place set for display only: 784 W Hickman Rd, Waukee, IA 50263, USA
 D  ✅ GOOGLE_PLACES_SELECTION_COMPLETE: Place selected and stored for validation at save time
 D  🔄 GOOGLE_PLACES_SESSION_RENEWED: Session token regenerated for next search
 D  app_time_stats: avg=4268.86ms min=4268.86ms max=4268.86ms count=1
 D  LaunchedEffect triggered with selectedPlace: 784 W Hickman Rd, Waukee, IA 50263, USA
 D  Address text updated to: 784 W Hickman Rd, Waukee, IA 50263, USA
 W  sendCancelIfRunning: isInProgress=false callback=androidx.compose.ui.window.Api33Impl$$ExternalSyntheticLambda0@4321526
 D  endAllActiveAnimators on 0x758d33aec9a0 (UnprojectedRipple) with handle 0x758ca3af38e0
 W  sendCancelIfRunning: isInProgress=false callback=ImeCallback=ImeOnBackInvokedCallback@255721015 Callback=android.window.IOnBackInvokedCallback$Stub$Proxy@55fe083
 D  app_time_stats: avg=232.39ms min=11.87ms max=500.67ms count=5
 D  show(ime(), fromIme=false)
 I  com.autogratuity:5bb63fa7: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:5bb63fa7: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:b65fb822: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
 D  show(ime(), fromIme=true)
 I  com.autogratuity:dcba967f: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:b65fb822: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=77.68ms min=13.19ms max=500.95ms count=15
 D  app_time_stats: avg=462.41ms min=387.49ms max=499.88ms count=3
 D  app_time_stats: avg=337.84ms min=12.51ms max=502.26ms count=3
 D  app_time_stats: avg=461.47ms min=386.91ms max=499.21ms count=3
 D  app_time_stats: avg=338.04ms min=13.89ms max=500.52ms count=3
 D  app_time_stats: avg=125.09ms min=12.65ms max=500.64ms count=8
 D  show(ime(), fromIme=false)
 I  com.autogratuity:7daf8c01: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
 I  com.autogratuity:7daf8c01: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 I  com.autogratuity:54461add: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
 D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{57c477d VFED..... .F....ID 0,0-1080,2400 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
 D  app_time_stats: avg=23.52ms min=14.50ms max=241.01ms count=50
 D  show(ime(), fromIme=true)
 I  com.autogratuity:37a12649: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  show(ime(), fromIme=true)
 I  com.autogratuity:54461add: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  validateOrderIdInput: input='784502632' -> filtered='784502632'
 D  getOrderIdErrorMessage: input='784502632', error='null'
 W  sendCancelIfRunning: isInProgress=false callback=androidx.compose.ui.window.Api33Impl$$ExternalSyntheticLambda0@ad6cb80
 D  validateOrderIdInput: input='7845026322' -> filtered='784502632'
 D  getOrderIdErrorMessage: input='784502632', error='null'
 D  show(ime(), fromIme=true)
 I  com.autogratuity:510da523: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
 D  app_time_stats: avg=64.65ms min=7.29ms max=313.61ms count=16
 D  app_time_stats: avg=333.82ms min=17.32ms max=499.71ms count=3
 D  app_time_stats: avg=32.49ms min=10.80ms max=479.29ms count=31
 D  app_time_stats: avg=499.95ms min=499.90ms max=500.03ms count=3
 D  app_time_stats: avg=500.34ms min=498.94ms max=501.73ms count=2
 D  getOrderIdErrorMessage: input='784502632', error='null'
 D  isValidOrderId: input='784502632', valid=true
 D  getOrderIdErrorMessage: input='784502632', error='null'
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  findOrCreateAddressFromPlace (SSoT) - Looking up or creating address for Place ID: ChIJb4RSl18j7IcR72v3JxSeaL4, User ID: myuivBnSjJe686W71qJTTuZsQet1
 D  app_time_stats: avg=361.63ms min=85.62ms max=500.14ms count=3
 D  Existing address found for placeId ChIJb4RSl18j7IcR72v3JxSeaL4 via remoteDataSource
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  findOrCreateAddressFromPlace (SSoT) - Successfully found/created and mapped SSoT address: upOCQJkNCZX6C6vRJa1P
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1247bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  Scheduling task addDelivery_1749826315727 with priority HIGH
 D  Executing task addDelivery_1749826315727 (HIGH)
 D  validateDelivery: Validating delivery 
 D  validateDelivery: Delivery  is valid
 D  CONVERSION: Delivery SSoT -> DTO | ID:  | User: myuivBnSjJe686W71qJTTuZsQet1
 D    Input: orderId=784502632 | status=COMPLETED | tipAmount=67.0
 D    Converting address: 784 W Hickman Rd, Waukee, IA 50263, USA... (ID: upOCQJkNCZX6C6vRJa1P)
 D    Created missing reference with addressId: upOCQJkNCZX6C6vRJa1P
 D  === DELIVERY SSoT -> DTO TRANSFORMATION ===
 D  Input SSoT State: {ssot_id=, ssot_userId=myuivBnSjJe686W71qJTTuZsQet1, ssot_orderId=784502632, ssot_tipAmount=67.0, ssot_status=COMPLETED, ssot_notes=empty, ssot_addressId=upOCQJkNCZX6C6vRJa1P}
 D  Output DTO State: {dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_orderId=784502632, dto_tipAmount=67.0, dto_status=COMPLETED, dto_notes=empty, dto_addressId=upOCQJkNCZX6C6vRJa1P}
 D  Field Transformations: [notes: ENCRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, times: TIMESTAMP_MAPPING]
 D  Business Logic Applied: [notes_encryption, field_validation, address_plain_mapping, status_mapping, amounts_calculation]
 D  PII Fields Encrypted: 0
 D  Mapping Duration: 0ms
 D  CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: 
 D    Output: encrypted_fields=0 | validation_errors=0 | duration=0ms
 D    DTO size: 1156 bytes
 D    Business logic: address_mapping, status_mapping, amounts_calculation
 D  executeAddDeliveryOperation: Adding delivery with transaction for user myuivBnSjJe686W71qJTTuZsQet1
 D  [DeliveryMapper] toDto Delivery ID: User:myuivBnSjJe686W71qJTTuZsQet1 Size:1156bytes Delivery: 0ms [OK]
 D  Attempting to add new delivery (from map) and update stats for user: myuivBnSjJe686W71qJTTuZsQet1
 D  isAssociateAddressIfNotFound: false, rawAddressDetails provided: false
 D  Delivery (from map) added to transaction with ID: Kt0pEd3N8W8vXrB1U1Aj
 D  Address upOCQJkNCZX6C6vRJa1P already has 1 deliveries - not incrementing address count.
 D  Incrementing user profile delivery count (both usage and usageStats) for myuivBnSjJe686W71qJTTuZsQet1.
 D  Incremented address stats for upOCQJkNCZX6C6vRJa1P.
 I  Successfully added new delivery (from map) with ID: Kt0pEd3N8W8vXrB1U1Aj and updated all stats for user myuivBnSjJe686W71qJTTuZsQet1
 I  executeAddDeliveryOperation: Delivery added with transaction, ID Kt0pEd3N8W8vXrB1U1Aj for user myuivBnSjJe686W71qJTTuZsQet1
 D  Saved delivery Kt0pEd3N8W8vXrB1U1Aj for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 D  executeAddDeliveryOperation: Delivery added Kt0pEd3N8W8vXrB1U1Aj (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933, user: myuivBnSjJe686W71qJTTuZsQet1)
 D  [data.DeliveryRepository] addDelivery(Delivery) ID:Kt0pEd3N8W8vXrB1U1Aj User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1156bytes Source:transaction Strategy:write-through [CACHE_MISS]: 300ms [OK]
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  Task addDelivery_1749826315727 completed in 301ms
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  Cached delivery Kt0pEd3N8W8vXrB1U1Aj with atomic cache system
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  observeDeliveriesByUserId: Emitting 8 deliveries from cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:2131065ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:2131065ms [CACHE_HIT] [POOR_UX]: 2131065ms [OK]
 V  ThrottleFirst: emitted value
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 2131065ms (user-visible)
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:33195ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:33196ms [CACHE_HIT] [POOR_UX]: 33195ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 33196ms (user-visible)
 D  Deduplication: Original 8 deliveries, deduplicated to 6
 V  ThrottleFirst: emitted value
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:2163121ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:2163121ms [CACHE_HIT] [POOR_UX]: 2163121ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 2163121ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:130964ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:130964ms [CACHE_HIT] [POOR_UX]: 130964ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 130964ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:252745ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:252745ms [CACHE_HIT] [POOR_UX]: 252745ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 252745ms (user-visible)
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:847757ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:847757ms [CACHE_HIT] [POOR_UX]: 847757ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 847757ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1698873ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1698873ms [CACHE_HIT] [POOR_UX]: 1698873ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1698873ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:148318ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:148318ms [CACHE_HIT] [POOR_UX]: 148318ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 148318ms (user-visible)
 D  [reactive_performance.DashboardViewModel] stateflow_emission Flow:recentDeliveriesState Emit:0ms Subs:1 Chain:5 Size:4985bytes [EFFICIENT]: 0ms [OK]
 D  [loading_performance.DashboardViewModel] loading_performance Op:observeRecentDeliveries Load:1640118ms Steps:[auth_check,repository_flow,data_processing,state_emission] UserDelay:1640118ms [CACHE_HIT] [POOR_UX]: 1640118ms [OK]
 W  POOR LOADING UX: DashboardViewModel.observeRecentDeliveries took 1640118ms (user-visible)
 D  DeliveryViewModel initializing with 3 repositories
 D  [presentation.DeliveryViewModel] init Deps:3 Data:initialization_complete State:Initialized: 0ms [OK]
 D  waitForAuthentication: Starting authentication readiness check (timeout: 3000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 I  Starting enhanced cache warming for user: myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Starting new request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  Scheduling task getUserById_myuivBnSjJe686W71qJTTuZsQet1 with priority CRITICAL
 D  Executing task getUserById_myuivBnSjJe686W71qJTTuZsQet1 (CRITICAL)
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=myuivBnSjJe686W71qJTTuZsQet1)
 D  getCurrentUserIdSuspend: Authentication confirmed for user myuivBnSjJe686W71qJTTuZsQet1
 D  getUserById: Starting operation for user myuivBnSjJe686W71qJTTuZsQet1 (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  DEDUPLICATION: Starting new request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  DEDUPLICATION: Found existing request for key 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription', waiting for result
 D  DEDUPLICATION: Completed waiting for 'users/myuivBnSjJe686W71qJTTuZsQet1/subscription' in 3ms, result: true
 D  Subscription cache warming successful in 3ms
 D  Cache hit for user myuivBnSjJe686W71qJTTuZsQet1
 D  Retrieved user myuivBnSjJe686W71qJTTuZsQet1 for current user myuivBnSjJe686W71qJTTuZsQet1: true
 D  getUserById: Found user myuivBnSjJe686W71qJTTuZsQet1 in local cache (session: myuivBnSjJe686W71qJTTuZsQet1_1749824675933)
 D  Task getUserById_myuivBnSjJe686W71qJTTuZsQet1 completed in 4ms
 D  DEDUPLICATION: Cleaned up completed request for key 'users/myuivBnSjJe686W71qJTTuZsQet1'
 D  [cache_system.UserRepository] cache_breakdown Check:2ms Hit:true ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1: 2ms [OK]
 D  [data.UserRepository] getUserById(User) ID:myuivBnSjJe686W71qJTTuZsQet1 User:myuivBnSjJe686W71qJTTuZsQet1 Count:1 Size:1311bytes Source:cache Strategy:cache-first [CACHE_HIT]: 3ms [OK]
 D  getAddresses (SSoT) - Fetching all addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Found 5 addresses in local cache for user myuivBnSjJe686W71qJTTuZsQet1
 D  getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user myuivBnSjJe686W71qJTTuZsQet1, fetching from remote.
 D  DEDUPLICATION: Completed new request 'users/myuivBnSjJe686W71qJTTuZsQet1' in 8ms, result: true
 D  User profile cache warming successful in 9ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=02N5j2GsgIeJkVuHoCNJ, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, dto_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=02N5j2GsgIeJkVuHoCNJ, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=2180 NW 156th St, Clive, IA 50325, USA, domain_coordinates=Coordinates(latitude=41.6136941, longitude=-93.8347823), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=OMzXiC4kUENI4sydIr5P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=OMzXiC4kUENI4sydIr5P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=222 5th Ave, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.5856411, longitude=-93.62360079999999), domain_tags_count=0, domain_isDefault=null}
 D  [AddressMapper] toSsot(Address) ID:02N5j2GsgIeJkVuHoCNJ User:myuivBnSjJe686W71qJTTuZsQet1 Size:1249bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=gpELpZKxALYy3nXLG2Fe, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=369 S 91st St, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=gpELpZKxALYy3nXLG2Fe, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=369 S 91st St, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.5617163, longitude=-93.8316153), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:OMzXiC4kUENI4sydIr5P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1238bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=p2HpUSPSuRnEVOhgGGR3, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, dto_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=p2HpUSPSuRnEVOhgGGR3, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=5585 Mills Civic Pkwy, West Des Moines, IA 50266, USA, domain_coordinates=Coordinates(latitude=41.559772099999996, longitude=-93.784042), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:gpELpZKxALYy3nXLG2Fe User:myuivBnSjJe686W71qJTTuZsQet1 Size:1230bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=upOCQJkNCZX6C6vRJa1P, dto_userId=myuivBnSjJe686W71qJTTuZsQet1, dto_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, dto_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=upOCQJkNCZX6C6vRJa1P, domain_userId=myuivBnSjJe686W71qJTTuZsQet1, domain_fullAddress=784 W Hickman Rd, Waukee, IA 50263, USA, domain_coordinates=Coordinates(latitude=41.6144803, longitude=-93.8877668), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:p2HpUSPSuRnEVOhgGGR3 User:myuivBnSjJe686W71qJTTuZsQet1 Size:1332bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  Cached address list: addresses_myuivBnSjJe686W71qJTTuZsQet1_all with 5 items
 D  getAddresses (SSoT) - Fetched and cached 5 SSoT addresses for user myuivBnSjJe686W71qJTTuZsQet1
 D  DEDUPLICATION: Cleaned up completed request for key 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1'
 D  [AddressMapper] toSsot(Address) ID:upOCQJkNCZX6C6vRJa1P User:myuivBnSjJe686W71qJTTuZsQet1 Size:1256bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  DEDUPLICATION: Completed new request 'addresses/user/myuivBnSjJe686W71qJTTuZsQet1' in 100ms, result: true
 D  Addresses cache warming successful in 101ms
 I  Cache warming completed for user myuivBnSjJe686W71qJTTuZsQet1: user_profile:success:9ms, subscription:success:3ms, addresses:success:101ms, total:102ms
 D  DEDUPLICATION: Found existing request for key 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1', waiting for result
 D  DEDUPLICATION: Completed waiting for 'getDeliveriesByUser_myuivBnSjJe686W71qJTTuZsQet1' in 0ms, result: true
 D  ✅ Deliveries loaded successfully: 5 (deduplicated from 7)
 D  app_time_stats: avg=16.98ms min=9.07ms max=38.12ms count=59
 D  validateOrderIdInput: input='784502632' -> filtered='784502632'
 D  getOrderIdErrorMessage: input='784502632', error='null'
 D  hide(ime(), fromIme=false)