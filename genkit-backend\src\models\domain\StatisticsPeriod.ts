// Auto-generated from StatisticsPeriod.kt
// TypeScript equivalent of Android domain.model.StatisticsPeriod

export enum StatisticsPeriod {
  Statistics = 'Statistics',
  TODAY = 'TODAY',
  WEEK = 'WEEK',
  Statistics = 'Statistics',
  MONTH = 'MONTH',
  ALL_TIME = 'ALL_TIME',
  Get = 'Get',
  val = 'val',
  TODAY = 'TODAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  ALL_TIME = 'ALL_TIME'
}

/**
 * Domain model generated from Kotlin StatisticsPeriod
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface StatisticsPeriod {

}