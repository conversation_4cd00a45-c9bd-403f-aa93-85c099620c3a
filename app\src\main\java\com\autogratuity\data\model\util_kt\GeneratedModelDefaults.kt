package com.autogratuity.data.model.util_kt

import com.autogratuity.data.model.generated_kt.Amounts
import com.autogratuity.data.model.generated_kt.Delivery
import com.autogratuity.data.model.generated_kt.Delivery_stats
import com.autogratuity.data.model.generated_kt.Metadata
import com.autogratuity.data.model.generated_kt.Platform
import com.autogratuity.data.model.generated_kt.Reference
import com.autogratuity.data.model.generated_kt.Status
import com.autogratuity.data.model.generated_kt.Times
import com.autogratuity.data.model.generated_kt.User_profile
import com.autogratuity.data.model.generated_kt.App_config
import com.autogratuity.data.model.generated_kt.Notification_patterns
import java.time.OffsetDateTime // Ensure OffsetDateTime is imported
// Potentially java.time.OffsetDateTime if your generated models use that for date-time fields
// For Firestore server timestamps, actual ServerTimestamp objects are used at the repository level.
// These defaults are for in-memory object creation.
import com.autogratuity.data.model.generated_kt.Address // Add import if not present
import com.autogratuity.data.model.generated_kt.Coordinates // Add import if not present
import com.autogratuity.data.model.generated_kt.Flags // Add import if not present
import java.time.ZoneOffset

// --- Default Creation Functions for Nested Objects ---

fun createDefaultStatus(): Status = Status(
    state = "CREATED",
    isTipped = false,
    isCompleted = false,
    isVerified = false,
    doNotDeliver = false,
    cancellationReason = null,
    verificationSource = null,
    verificationTimestamp = null,
    dndReason = null
)

fun createDefaultTimes(): Times = Times(
    acceptedAt = null,
    completedAt = null,
    tippedAt = null
)

fun createDefaultAmounts(): Amounts = Amounts(
    basePay = 0.0,
    tipAmount = 0.0,
    tipPercentage = 0.0,
    totalAmount = 0.0,
    currencyCode = "USD",
    estimatedPay = 0.0,
    finalPay = 0.0,
    distanceMiles = 0.0
)

fun createDefaultReference(): Reference = Reference(
    addressId = null,
    orderId = null,
    externalId = null,
    platformOrderId = null
)

fun createDefaultPlatform(): Platform = Platform(
    name = "UNKNOWN",
    type = null,
    version = null,
    source = null,
    displayName = null,
    iconUrl = null
)

// RENAMED to avoid conflict and clarify purpose
// ✅ CRITICAL FIX: Always set proper timestamps - null timestamps break Firestore queries and tip tracking
fun createDefaultGeneratedKtMetadata(now: OffsetDateTime?): Metadata {
    val timestamp = now ?: OffsetDateTime.now() // ✅ FIX: Always provide a timestamp
    return Metadata(
        createdAt = timestamp, // ✅ FIX: Never null - breaks Firestore queries
        updatedAt = timestamp, // ✅ FIX: Never null - breaks Firestore queries
        importedAt = null,
        source = "app_creation",
        importId = null,
        captureId = null,
        version = 1L,
        customData = null // Fix: Don't instantiate empty CustomData class
    )
}

fun createDefaultKtVerification(): User_profile.Verification {
    return User_profile.Verification(
        lastVerified = null,
        status = "unknown",
        error = null
    )
}

fun createDefaultKtUserSubscription(): User_profile.UserSubscription {
    return User_profile.UserSubscription(
        status = "free",
        level = "free",
        isActive = true,
        isLifetime = false,
        startDate = null,
        expiryDate = null,
        provider = null,
        orderId = null,
        verification = createDefaultKtVerification()
    )
}

fun createDefaultKtUserPreferences(): User_profile.UserPreferences {
    return User_profile.UserPreferences(
        notificationsEnabled = false, // ✅ FIX: Default false - user must manually enable tip capture
        theme = "system",
        useLocation = true,
        dnd = null
    )
}

fun createDefaultKtUserPermissions(): User_profile.UserPermissions {
    return User_profile.UserPermissions(
        bypassLimits = false,
        maxUploads = 50L
    )
}

fun createDefaultKtUserUsage(now: OffsetDateTime?): User_profile.UserUsage {
    val timestamp = now ?: OffsetDateTime.now() // ✅ FIX: Always provide a timestamp
    return User_profile.UserUsage(
        mappingCount = 0L,
        deliveryCount = 0L,
        addressCount = 0L,
        lastUsageUpdate = timestamp // ✅ FIX: Never null - breaks usage tracking
    )
}

fun createDefaultKtUserSyncInfo(): User_profile.UserSyncInfo {
    return User_profile.UserSyncInfo(
        lastSyncTime = null,
        deviceIds = emptyList<String>(),
        version = 1L
    )
}

fun createDefaultKtUserAppSettings(): User_profile.UserAppSettings {
    return User_profile.UserAppSettings(
        dataCollectionOptIn = false,
        lastVersion = null,
        onboardingCompleted = false
    )
}

fun createDefaultKtUserCommunication(): User_profile.UserCommunication {
    return User_profile.UserCommunication(
        emailOptIn = true,
        marketingOptIn = false,
        pushNotificationsEnabled = true
    )
}

fun createDefaultKtUserUsageStats(): User_profile.UserUsageStats {
    return User_profile.UserUsageStats(
        deliveryCount = 0L,
        addressCount = 0L,
        lastUsageDate = null,
        totalRuns = 0L,
        activeDaysCount = 0L,
        totalTips = null,
        featureUsage = null
    )
}

fun createDefaultKtUserProfile(
    userId: String,
    email: String?,
    photoUrl: String?,
    authProviders: List<String>?
): User_profile {
    // Use null timestamps to avoid Jackson serialization issues - timestamps will be set by Firestore
    return User_profile(
        userId = userId,
        email = email,
        displayName = (email?.substringBefore('@') ?: "New User"),
        photoUrl = photoUrl,
        authProviders = authProviders ?: emptyList(),
        accountStatus = "active",
        subscription = createDefaultKtUserSubscription(),
        preferences = createDefaultKtUserPreferences(),
        permissions = createDefaultKtUserPermissions(),
        usage = createDefaultKtUserUsage(OffsetDateTime.now()), // ✅ FIX: Always provide timestamp
        syncInfo = createDefaultKtUserSyncInfo(),
        appSettings = createDefaultKtUserAppSettings(),
        communication = createDefaultKtUserCommunication(),
        usageStats = createDefaultKtUserUsageStats(),
        metadata = createDefaultGeneratedKtMetadata(OffsetDateTime.now()), // ✅ FIX: Always provide timestamp
        version = 1L,
        defaultAddressId = null,
        timezone = null,
        createdAt = OffsetDateTime.now(), // ✅ FIX: Always provide timestamp
        lastLoginAt = OffsetDateTime.now(), // ✅ FIX: Always provide timestamp
        privacyPolicyAccepted = null,
        termsAccepted = null
    )
}

// --- Default Creation Functions for AppConfig and its nested objects ---

fun createDefaultKtAppConfigVersions(): App_config.Versions {
    return App_config.Versions(
        minimum = "1.0.0",
        recommended = "1.0.0",
        latest = "1.0.0"
    )
}

fun createDefaultKtAppConfigFeatures(): App_config.Features {
    // App_config.Features is a data class with default values for its properties
    return App_config.Features() 
}

fun createDefaultKtAppConfigTierLimits(mapping: Long, importLimit: Long, exportLimit: Long): App_config.TierLimits {
    return App_config.TierLimits(
        mappingLimit = mapping,
        importLimit = importLimit,
        exportLimit = exportLimit
    )
}

fun createDefaultKtAppConfigLimits(): App_config.Limits {
    return App_config.Limits(
        freeTier = createDefaultKtAppConfigTierLimits(50L, 10L, 10L),
        proTier = createDefaultKtAppConfigTierLimits(10000L, 1000L, 1000L)
    )
}

fun createDefaultKtAppConfigSync(): App_config.Sync {
    return App_config.Sync(
        interval = 300L,
        backgroundInterval = 3600L,
        maxBatchSize = 100L,
        conflictStrategy = "server_wins"
    )
}

fun createDefaultKtAppConfigMaintenance(): App_config.Maintenance {
    return App_config.Maintenance(
        isInMaintenance = false, // Default provided by data class, but explicit is fine
        maintenanceMessage = "The app is currently undergoing scheduled maintenance. Please try again later.",
        estimatedEndTime = null
    )
}

fun createDefaultKtAppConfig(): App_config {
    return App_config(
        versions = createDefaultKtAppConfigVersions(),
        features = createDefaultKtAppConfigFeatures(),
        limits = createDefaultKtAppConfigLimits(),
        sync = createDefaultKtAppConfigSync(),
        maintenance = createDefaultKtAppConfigMaintenance(),
        updatedAt = OffsetDateTime.now(), // ✅ FIX: Always provide timestamp
        version = 1L,
        customData = null // Fix: Don't instantiate empty CustomData class
    )
}

// --- Default Creation function for NotificationPatterns ---
fun createDefaultKtNotificationPatternsShiptExtractor(): Notification_patterns.ShiptExtractor {
    return Notification_patterns.ShiptExtractor(
        orderId = "DEFAULT_SHIPIT_ORDER_ID_REGEX", // Placeholder
        tipAmount = "DEFAULT_SHIPIT_TIP_REGEX" // Placeholder
    )
}

fun createDefaultKtNotificationPatternsExtractors(): Notification_patterns.Extractors {
    return Notification_patterns.Extractors(
        shipt = createDefaultKtNotificationPatternsShiptExtractor()
    )
}

fun createDefaultKtNotificationPatternsPatterns(): Notification_patterns.Patterns {
    return Notification_patterns.Patterns(
        doordash = listOf("DEFAULT_DD_PATTERN"), // Placeholder
        shipt = listOf("DEFAULT_SHIPT_PATTERN"), // Placeholder
        ubereats = listOf("DEFAULT_U E_PATTERN") // Placeholder
    )
}

fun createDefaultKtNotificationPatterns(): Notification_patterns {
    return Notification_patterns(
       extractors = createDefaultKtNotificationPatternsExtractors(),
       patterns = createDefaultKtNotificationPatternsPatterns(),
       updatedAt = OffsetDateTime.now(), // ✅ FIX: Always provide timestamp
       version = 1L
    )
}

// --- Default Creation Functions for Top-Level Delivery Model ---

/**
 * Creates a Delivery.DeliveryData object with sensible defaults.
 * Requires essential identifiers to be passed.
 */
fun createDefaultDeliveryData(
    userId: String, 
    orderId: String,  
    address: Delivery.SimpleAddress, 
    notes: String? = null,
    status: Status = createDefaultStatus(),
    times: Times = createDefaultTimes(),
    amounts: Amounts = createDefaultAmounts(),
    reference: Reference = createDefaultReference(),
    platform: Platform = createDefaultPlatform(),
    items: List<Delivery.Item> = emptyList(), 
    metadata: Metadata = createDefaultGeneratedKtMetadata(OffsetDateTime.now()) // ✅ FIX: Always provide timestamp
): Delivery.DeliveryData {
    // Assuming Delivery.DeliveryData is a data class with named arguments
    return Delivery.DeliveryData(
        userId = userId,
        orderId = orderId,
        notes = notes,
        address = address,
        status = status,
        times = times,
        amounts = amounts,
        reference = reference,
        platform = platform,
        items = items,
        metadata = metadata
    )
}


/**
 * Creates a top-level Delivery object, typically by wrapping a DeliveryData object.
 * Requires the document ID to be provided.
 */
fun createDefaultDelivery(
    id: String,
    deliveryData: Delivery.DeliveryData
): Delivery {
    // CRITICAL -- NEEDS MANUAL UPDATE AFTER REGENERATION (if DTO constructor changes)
    return Delivery(
        id = id,
        deliveryData = deliveryData
    )
}

/**
 * Convenience function to create a new Delivery object with all defaults populated,
 * requiring only the absolute essential contextual information.
 */
fun createNewDeliveryWithDefaults(
    userId: String,
    orderId: String,
    documentId: String,
    address: Delivery.SimpleAddress
): Delivery {
    val defaultDeliveryData = createDefaultDeliveryData(
        userId = userId,
        orderId = orderId,
        address = address
    )
    // CRITICAL -- NEEDS MANUAL UPDATE AFTER REGENERATION (if DTO constructor changes)
    return createDefaultDelivery(
        id = documentId,
        deliveryData = defaultDeliveryData
    )
}

fun createDefaultSimpleAddress(
    id: String, 
    fullAddress: String,
    latitude: Double,
    longitude: Double,
    placeId: String? = null
): Delivery.SimpleAddress {
    // Assuming Delivery.SimpleAddress is a data class with named arguments
    return Delivery.SimpleAddress(
        id = id,
        fullAddress = fullAddress,
        latitude = latitude,
        longitude = longitude,
        placeId = placeId
        // Add any other required fields with default values or null
    )
}

fun createDefaultDeliveryStats(): Delivery_stats {
    return Delivery_stats(
        deliveryCount = 0L,
        tipCount = 0L,
        totalTips = 0.0,
        highestTip = 0.0,
        averageTipAmount = 0.0,
        pendingCount = 0L,
        averageTimeMinutes = 0.0,
        lastDeliveryDate = null,
        lastDeliveryTimestamp = null
    )
}

/**
 * Creates a default Address with the specified userId and addressId.
 * This is useful for creating an initial default address for a new user.
 *
 * @param userId The user ID to associate with the address.
 * @param addressId The ID to use for the address document.
 * @param isDefault Whether this address should be marked as the default address.
 * @param notes Optional notes to add to the address.
 * @return A new Address instance with default values.
 */
fun createDefaultAddress(
    addressId: String,
    userId: String,
    isDefault: Boolean = false,
    notes: String? = null
    // Potentially other parameters if needed for a 'default'
): Address {
    val now = OffsetDateTime.now(ZoneOffset.UTC) // Use UTC for consistency

    // Prepare individual components for AddressData constructor
    val defaultCoordinates = Coordinates(
        latitude = 0.0,
        longitude = 0.0
    )
    val defaultFlags = Flags(
        isVerified = false
        // Other flags default to null/false as per data class definition
    )
    val defaultMetadata = createDefaultGeneratedKtMetadata(now ?: OffsetDateTime.now()) // ✅ FIX: Always provide timestamp

    // Construct AddressData directly using named arguments
    val addressData = Address.AddressData(
        userId = userId,
        fullAddress = "", // Default empty string
        normalizedAddress = "", // Default empty string
        placeId = "", // Default empty string or null if preferred for a new default
        isDefault = isDefault,
        notes = notes ?: "Default address",
        tags = emptyList<String>(),
        orderIds = emptyList<String>(),
        searchTerms = emptyList<String>(),
        components = null, // Or createDefaultComponents() if you have one
        coordinates = defaultCoordinates,
        searchFields = null, // Or createDefaultSearchFields()
        deliveryStats = null, // Or createDefaultDeliveryStats()
        flags = defaultFlags,
        metadata = defaultMetadata,
        platform = null // Or createDefaultPlatform() if one makes sense for a default address
    )
    
    // Construct Address using named arguments
    return Address(
        id = addressId,
        addressData = addressData
    )
}

// --- Default Creation Functions for Generated Top-Level Models ---
// ... existing code ... 