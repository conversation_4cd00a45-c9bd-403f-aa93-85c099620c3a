package com.autogratuity.domain.model

/**
 * Enum representing the manual DND override state for an address.
 * This provides type safety and consistency across the codebase.
 */
enum class ManualDndState(val value: String) {
    /**
     * User has explicitly forced this address to be marked as "Do Not Deliver"
     */
    FORCE_DND("FORCE_DND"),
    
    /**
     * User has explicitly allowed deliveries to this address, overriding automatic rules
     */
    FORCE_ALLOW("FORCE_ALLOW"),
    
    /**
     * Address should use automatic DND evaluation based on rules and delivery history
     */
    AUTOMATIC("AUTOMATIC");
    
    companion object {
        /**
         * Convert string value to enum, handling null and unknown values
         */
        fun fromString(value: String?): ManualDndState? {
            return when (value) {
                "FORCE_DND" -> FORCE_DND
                "FORCE_ALLOW" -> FORCE_ALLOW
                "AUTOMATIC" -> AUTOMATIC
                null -> null // null means automatic (no manual override)
                else -> null // unknown values default to automatic
            }
        }
        
        /**
         * Convert enum to string value for storage/transmission
         */
        fun toString(state: ManualDndState?): String? {
            return state?.value
        }
        
        /**
         * Check if a string represents a manual override (not automatic)
         */
        fun isManualOverride(value: String?): Bo<PERSON>an {
            return value == FORCE_DND.value || value == FORCE_ALLOW.value
        }
        
        /**
         * Check if state represents DND enforcement
         */
        fun isDndEnforced(state: ManualDndState?): Boolean {
            return state == FORCE_DND
        }
        
        /**
         * Check if state represents DND override (allow deliveries)
         */
        fun isDndOverridden(state: ManualDndState?): Boolean {
            return state == FORCE_ALLOW
        }
    }
}
