package com.autogratuity.domain.model

/**
 * SSoT model for DND quota information display.
 * Used by UI components to show quota usage and limits.
 */
data class DndQuotaInfo(
    val used: Int,
    val limit: Int,
    val isUnlimited: Boolean
) {
    /**
     * Get display text for quota usage.
     * Examples: "3 of 15 used", "Unlimited", "15 of 15 used"
     */
    fun getDisplayText(): String {
        return if (isUnlimited) {
            "Unlimited"
        } else {
            "$used of $limit used"
        }
    }

    /**
     * Get short display text for compact UI.
     * Examples: "3/15", "∞", "15/15"
     */
    fun getShortDisplayText(): String {
        return if (isUnlimited) {
            "∞"
        } else {
            "$used/$limit"
        }
    }

    /**
     * Check if user is near their quota limit (80% threshold).
     * Always returns false for unlimited users.
     */
    fun isNearLimit(): Boolean {
        return !isUnlimited && used >= (limit * 0.8)
    }

    /**
     * Check if user has reached their quota limit.
     * Always returns false for unlimited users.
     */
    fun isAtLimit(): Boolean {
        return !isUnlimited && used >= limit
    }

    /**
     * Get remaining quota count.
     * Returns Int.MAX_VALUE for unlimited users.
     */
    fun getRemainingQuota(): Int {
        return if (isUnlimited) {
            Int.MAX_VALUE
        } else {
            kotlin.math.max(0, limit - used)
        }
    }

    /**
     * Get quota usage percentage (0.0 to 1.0).
     * Returns 0.0 for unlimited users.
     */
    fun getUsagePercentage(): Float {
        return if (isUnlimited || limit == 0) {
            0.0f
        } else {
            (used.toFloat() / limit.toFloat()).coerceIn(0.0f, 1.0f)
        }
    }
}
