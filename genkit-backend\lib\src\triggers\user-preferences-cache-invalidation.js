"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.onUserPreferencesChange = void 0;
const firestore_1 = require("firebase-functions/v2/firestore");
const firestore_2 = require("firebase-admin/firestore");
const dnd_preferences_cache_1 = require("../utils/dnd-preferences-cache");
/**
 * Cache Invalidation Trigger for User Preferences
 *
 * Automatically invalidates Redis cache when user DND preferences or subscription status changes.
 * This ensures cache consistency across all cloud functions.
 */
exports.onUserPreferencesChange = (0, firestore_1.onDocumentUpdated)('users/{userId}', async (event) => {
    const userId = event.params.userId;
    const logPrefix = `[UserPreferencesCacheInvalidation UserId: ${userId}] -`;
    try {
        const before = event.data?.before.data();
        const after = event.data?.after.data();
        if (!before || !after) {
            console.log(`${logPrefix} Skipping cache invalidation - missing before/after data`);
            return;
        }
        // Check if DND preferences or subscription changed
        const preferencesChanged = dnd_preferences_cache_1.DndPreferencesCache.dndPreferencesChanged(before, after);
        if (preferencesChanged) {
            console.log(`${logPrefix} DND preferences or subscription changed, invalidating cache`);
            // Invalidate Redis cache for this user
            await dnd_preferences_cache_1.dndPreferencesCache.invalidateUserCache(userId, logPrefix);
            // Optional: Bump cache version for versioned invalidation
            try {
                // ✅ TYPESCRIPT FIX: Add proper null checking for event.data
                if (event.data?.after?.ref) {
                    await event.data.after.ref.update({
                        'profileData.dndCacheVersion': firestore_2.FieldValue.increment(1),
                        'profileData.metadata.lastCacheInvalidation': firestore_2.FieldValue.serverTimestamp()
                    });
                    console.log(`${logPrefix} Updated cache version for user ${userId}`);
                }
                else {
                    console.warn(`${logPrefix} Cannot update cache version - missing document reference`);
                }
            }
            catch (error) {
                console.warn(`${logPrefix} Error updating cache version:`, error);
                // Continue - cache version update is not critical
            }
            console.log(`${logPrefix} Successfully invalidated cache for user ${userId}`);
        }
        else {
            console.log(`${logPrefix} No DND preference changes detected, skipping cache invalidation`);
        }
    }
    catch (error) {
        console.error(`${logPrefix} Error in cache invalidation trigger:`, error);
        // Don't throw - cache invalidation failure should not break user operations
    }
});
//# sourceMappingURL=user-preferences-cache-invalidation.js.map