package com.autogratuity.data.repository.delivery

import com.autogratuity.data.model.Result
import com.autogratuity.data.model.generated_kt.Status
import com.autogratuity.domain.model.Delivery
import com.autogratuity.domain.model.DeliveryStats
import kotlinx.coroutines.flow.Flow
import java.time.OffsetDateTime

/**
 * Data layer interface for Delivery repository operations.
 * Follows UserProfileRepository pattern: comprehensive data layer interface with all operations.
 * All operations use SSoT Delivery models, never DTOs.
 *
 * ✅ DUAL INTERFACE ALIGNMENT: This interface includes ALL domain interface methods
 * plus comprehensive infrastructure operations for proper dual interface orchestration.
 *
 * Used by: CacheLifecycleManager, CacheWarmingManager, system maintenance components
 * Domain interface (DeliveryRepository) used by: ViewModels, UseCases, business logic
 */
interface DeliveryRepository {

    // ===== CORE CRUD OPERATIONS (SHARED WITH DOMAIN) =====
    suspend fun getDeliveryById(id: String): Result<Delivery?>
    suspend fun getDeliveriesByUserId(userId: String): Result<List<Delivery>>
    suspend fun getDeliveriesByOrderId(orderId: String): Result<List<Delivery>>
    suspend fun addDelivery(delivery: Delivery): Result<String>
    suspend fun updateDelivery(delivery: Delivery): Result<Unit>
    suspend fun deleteDelivery(id: String): Result<Unit>

    // ===== BATCH OPERATIONS (DATA LAYER SPECIFIC) =====
    suspend fun batchDeleteDeliveries(deliveryIds: List<String>): Result<Unit>

    // ===== REACTIVE FLOW OPERATIONS (SHARED WITH DOMAIN) =====
    fun observeDeliveryById(id: String): Flow<Result<Delivery?>>
    fun observeDeliveriesByUserId(userId: String): Flow<Result<List<Delivery>>>
    fun observeDeliveriesByOrderId(orderId: String): Flow<Result<List<Delivery>>>

    // ===== QUERY OPERATIONS (SHARED WITH DOMAIN) =====
    suspend fun getDeliveriesByDateRange(userId: String, startDate: OffsetDateTime, endDate: OffsetDateTime): Result<List<Delivery>>
    suspend fun getDeliveriesByStatus(userId: String, status: Status): Result<List<Delivery>>
    suspend fun getRecentDeliveries(userId: String, limit: Int): Result<List<Delivery>>
    suspend fun getTippedDeliveries(userId: String, limit: Int): Result<List<Delivery>>
    suspend fun getUntippedDeliveries(userId: String, limit: Int): Result<List<Delivery>>
    suspend fun getDeliveriesByAddress(userId: String, addressId: String): Result<List<Delivery>>
    suspend fun findDeliveryByMetadataOrderId(orderId: String): Result<Delivery?>

    // ===== BUSINESS OPERATIONS (SHARED WITH DOMAIN) =====
    suspend fun updateDeliveryTip(deliveryId: String, tipAmount: Double, tipPercentage: Double?, timestamp: OffsetDateTime?): Result<Unit>
    suspend fun updateDeliveryStatus(deliveryId: String, status: Status, timestamp: OffsetDateTime?): Result<Unit>
    suspend fun markDeliveryAsCompleted(deliveryId: String, completionTime: OffsetDateTime): Result<Unit>
    suspend fun reassociateDeliveryAddress(userId: String, deliveryId: String, newAddressId: String): Result<Unit>

    // ===== STATISTICS AND ANALYTICS (ALIGNED WITH DOMAIN) =====
    suspend fun getOldestDeliveryTimestamp(userId: String): Result<OffsetDateTime?>
    suspend fun createDefaultDelivery(): Result<Delivery>

    // ===== DOMAIN INTERFACE METHODS (MISSING FROM ORIGINAL DATA INTERFACE) =====

    // Additional validation methods from domain interface
    suspend fun validateDeliveryWithReferences(delivery: Delivery): Result<com.autogratuity.data.model.SingleValidationResult>
    suspend fun validateDeliveries(deliveries: List<Delivery>): Result<com.autogratuity.data.model.BulkValidationResult>

    // Prefetch method with proper signature from domain interface
    suspend fun prefetch(userId: String, limit: Int): Result<Map<String, Any>>

    // ===== CACHE MANAGEMENT (INFRASTRUCTURE FOCUS) =====
    suspend fun prefetchCriticalData(userId: String): Result<Unit>
    suspend fun clearCache(userId: String): Result<Unit>
    suspend fun clearAllCache(): Result<Unit>
    suspend fun invalidateCache(deliveryId: String): Result<Unit>
    suspend fun invalidateUserCache(userId: String): Result<Unit>
    suspend fun refreshFromRemote(userId: String): Result<Unit>

    // ===== REPOSITORY LIFECYCLE (INFRASTRUCTURE FOCUS) =====
    suspend fun initialize(): Result<Unit>
    suspend fun cleanup(): Result<Unit>

    // ===== IMPORT/EXPORT OPERATIONS (INFRASTRUCTURE FOCUS) =====
    suspend fun importDeliveries(userId: String, deliveries: List<Map<String, Any>>): Result<Int>
    suspend fun exportDeliveries(userId: String, startDate: OffsetDateTime?, endDate: OffsetDateTime?, format: String): Result<String>

    // ===== PAGINATION SUPPORT (SHARED WITH DOMAIN) =====
    suspend fun getDeliveriesPaginated(userId: String, limit: Int, offset: Int): Result<List<Delivery>>
    suspend fun getDeliveryCountByDateRange(userId: String, startDate: OffsetDateTime, endDate: OffsetDateTime): Result<Int>

    // ===== VALIDATION AND UTILITY (SHARED WITH DOMAIN) =====
    suspend fun validateDelivery(delivery: Delivery): Result<Unit>
    suspend fun normalizeDelivery(delivery: Delivery): Delivery
    suspend fun deliveryExistsByOrderId(userId: String, orderId: String): Result<Boolean>
    suspend fun getDeliveryCompletionRate(userId: String, startDate: OffsetDateTime, endDate: OffsetDateTime): Result<Double>
    suspend fun getAverageTipAmount(userId: String, startDate: OffsetDateTime, endDate: OffsetDateTime): Result<Double>

    // ===== BACKUP AND RECOVERY (INFRASTRUCTURE FOCUS) =====
    suspend fun createDeliveryBackup(userId: String): Result<Map<String, Any>>
    suspend fun restoreDeliveryBackup(userId: String, backup: Map<String, Any>): Result<Unit>
    suspend fun migrateDeliveryData(userId: String, fromVersion: Long, toVersion: Long): Result<Unit>
}