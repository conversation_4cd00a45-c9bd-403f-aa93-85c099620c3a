package com.autogratuity.data.repository.core

import android.util.Log
import com.autogratuity.data.repository.delivery.DeliveryCacheSystem
import com.autogratuity.data.repository.address.AddressCacheSystem
import com.autogratuity.data.repository.user.UserProfileCacheSystem
import com.autogratuity.data.repository.subscription.SubscriptionCacheSystem
import com.autogratuity.data.repository.config.ConfigCacheSystem
import com.autogratuity.data.repository.preference.PreferenceCacheSystem
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Centralized lifecycle management for all cache systems
 * Ensures proper cleanup and resource management
 */
@Singleton
class CacheLifecycleManager @Inject constructor(
    private val deliveryCacheSystem: DeliveryCacheSystem,
    private val addressCacheSystem: AddressCacheSystem,
    private val userProfileCacheSystem: UserProfileCacheSystem,
    private val subscriptionCacheSystem: SubscriptionCacheSystem,
    private val configCacheSystem: ConfigCacheSystem,
    private val preferenceCacheSystem: PreferenceCacheSystem
) {

    private val TAG = "CacheLifecycleManager"

    /**
     * Cleanup all cache systems when application is destroyed
     */
    suspend fun cleanupAllCaches() {
        try {
            Log.i(TAG, "Starting cleanup of all cache systems")

            // Cleanup delivery cache system
            runCatching {
                deliveryCacheSystem.cleanup()
                Log.d(TAG, "DeliveryCacheSystem cleanup completed")
            }.onFailure { e ->
                Log.e(TAG, "Error cleaning up DeliveryCacheSystem", e)
            }

            // Cleanup address cache system
            runCatching {
                addressCacheSystem.cleanup()
                Log.d(TAG, "AddressCacheSystem cleanup completed")
            }.onFailure { e ->
                Log.e(TAG, "Error cleaning up AddressCacheSystem", e)
            }

            // Cleanup user profile cache system
            runCatching {
                userProfileCacheSystem.cleanup()
                Log.d(TAG, "UserProfileCacheSystem cleanup completed")
            }.onFailure { e ->
                Log.e(TAG, "Error cleaning up UserProfileCacheSystem", e)
            }

            // Cleanup subscription cache system
            runCatching {
                subscriptionCacheSystem.clear()
                Log.d(TAG, "SubscriptionCacheSystem cleanup completed")
            }.onFailure { e ->
                Log.e(TAG, "Error cleaning up SubscriptionCacheSystem", e)
            }

            // Cleanup config cache system
            runCatching {
                configCacheSystem.cleanup()
                Log.d(TAG, "ConfigCacheSystem cleanup completed")
            }.onFailure { e ->
                Log.e(TAG, "Error cleaning up ConfigCacheSystem", e)
            }

            // Cleanup preference cache system
            runCatching {
                preferenceCacheSystem.cleanup()
                Log.d(TAG, "PreferenceCacheSystem cleanup completed")
            }.onFailure { e ->
                Log.e(TAG, "Error cleaning up PreferenceCacheSystem", e)
            }

            Log.i(TAG, "All cache systems cleanup completed")

        } catch (e: Exception) {
            Log.e(TAG, "Error during cache cleanup", e)
        }
    }

    /**
     * Clear all caches without destroying resources
     */
    suspend fun clearAllCaches() {
        try {
            Log.i(TAG, "Clearing all cache data")

            deliveryCacheSystem.clear()
            addressCacheSystem.clearCache()
            userProfileCacheSystem.clearCache()
            subscriptionCacheSystem.clear()
            configCacheSystem.clear()
            preferenceCacheSystem.clear()

            Log.i(TAG, "All cache data cleared")

        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all caches", e)
        }
    }

    /**
     * Get cache metrics for all systems
     */
    suspend fun getAllCacheMetrics(): Map<String, Any> {
        return try {
            mapOf(
                "delivery" to deliveryCacheSystem.getDeliveryCacheMetrics(),
                "address" to addressCacheSystem.getAddressCacheMetrics(),
                "userProfile" to userProfileCacheSystem.getUserProfileDetailedStats(),
                "subscription" to subscriptionCacheSystem.getDetailedStats(),
                "config" to configCacheSystem.getDetailedStats(),
                "preference" to preferenceCacheSystem.getPreferenceCacheMetrics()
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting cache metrics", e)
            mapOf("error" to (e.message ?: "Unknown error"))
        }
    }
}
