package com.autogratuity.data.mapper

// ===== OPTIMIZED IMPORTS =====
// DTO Models with aliases

// Domain Models (SSoT) with aliases

// Data & Result Types

// Security & Encryption

// Standard Libraries
import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.data.repository.core.RepositoryException
import com.autogratuity.data.security.CryptoResult
import com.autogratuity.data.security.EncryptionUtils
import com.autogratuity.debug.ClarityArchitectureMonitor
import com.autogratuity.domain.model.ComparisonType
import com.autogratuity.domain.model.CustomDndRule
import com.autogratuity.domain.model.DndDetails
import com.autogratuity.domain.model.User
import com.autogratuity.domain.model.UserCommunication
import com.autogratuity.domain.model.UserPermissions
import com.autogratuity.domain.model.UserPreferences
import com.autogratuity.domain.model.UserSubscription
import com.autogratuity.domain.model.UserSyncInfo
import com.autogratuity.domain.model.UserUsage
import com.autogratuity.domain.model.UserUsageStats
import kotlinx.coroutines.ExperimentalCoroutinesApi
import javax.inject.Inject
import com.autogratuity.data.model.generated_kt.User_profile as UserProfileDto

@ExperimentalCoroutinesApi
class PreferenceMapper @Inject constructor(private val encryptionUtils: EncryptionUtils) {
    
    companion object {
        private const val TAG = "PreferenceMapper"
    }

    // Helper to handle CryptoResult for a single field decryption
    private suspend fun decryptField(encrypted: String?, fieldName: String): String? {
        return encrypted?.let {
            if (encryptionUtils.isEncrypted(it)) {
                when (val cryptoOutcome = encryptionUtils.decrypt(it)) {
                    is CryptoResult.Success -> cryptoOutcome.data
                    is CryptoResult.DecryptionFailure -> throw RepositoryException.CryptographicError("Failed to decrypt $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "decryption_failure")
                    is CryptoResult.AuthenticationFailure -> throw RepositoryException.CryptographicError("Authentication failure during decryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "authentication_failure")
                    is CryptoResult.InitializationFailure -> throw RepositoryException.CryptographicError("Crypto init failure during decryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "initialization_failure")
                    is CryptoResult.InputValidationFailure -> throw RepositoryException.CryptographicError("Input validation failure during decryption of $fieldName: ${cryptoOutcome.message}", null, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "input_validation_failure")
                    is CryptoResult.KeyFailure -> throw RepositoryException.CryptographicError("Key failure during decryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "key_failure")
                    is CryptoResult.TimeoutFailure -> throw RepositoryException.CryptographicError("Timeout during decryption of $fieldName: ${cryptoOutcome.message}", null, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "timeout_failure")
                    is CryptoResult.Failure -> throw RepositoryException.CryptographicError("Generic crypto failure during decryption of $fieldName: ${cryptoOutcome.message}", null, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "generic_failure")
                }
            } else {
                // If not prefixed, assume it's plaintext (legacy data)
                it
            }
        }
    }

    // Helper to handle CryptoResult for a single field encryption
    private suspend fun encryptField(plaintext: String?, fieldName: String): String? {
        return plaintext?.let {
            when (val cryptoOutcome = encryptionUtils.encrypt(it)) {
                is CryptoResult.Success -> cryptoOutcome.data
                is CryptoResult.EncryptionFailure -> throw RepositoryException.CryptographicError("Failed to encrypt $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "encryption_failure")
                is CryptoResult.InitializationFailure -> throw RepositoryException.CryptographicError("Crypto init failure during encryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "initialization_failure")
                is CryptoResult.InputValidationFailure -> throw RepositoryException.CryptographicError("Input validation failure during encryption of $fieldName: ${cryptoOutcome.message}", null, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "input_validation_failure")
                is CryptoResult.KeyFailure -> throw RepositoryException.CryptographicError("Key failure during encryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "key_failure")
                is CryptoResult.TimeoutFailure -> throw RepositoryException.CryptographicError("Timeout during encryption of $fieldName: ${cryptoOutcome.message}", null, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "timeout_failure")
                is CryptoResult.Failure -> throw RepositoryException.CryptographicError("Generic crypto failure during encryption of $fieldName: ${cryptoOutcome.message}", null, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "generic_failure")
            }
        }
    }

    suspend fun mapToDomain(dto: UserProfileDto): Result<User> {
        val mappingStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        var piiFieldsProcessed = mutableListOf<String>()
        var validationErrors = mutableListOf<String>()
        
        return try {
            Log.d(TAG, "CONVERSION: UserProfile DTO -> Domain | ID: ${dto.userId}")
            Log.d(TAG, "  Input: email=${dto.email?.take(10)}... | subscription=${dto.subscription?.level} | preferences=${dto.preferences != null}")
            
            // Track PII field processing
            val email = decryptField(dto.email, "email").also { piiFieldsProcessed.add("email") }
            val displayName = decryptField(dto.displayName, "displayName").also { piiFieldsProcessed.add("displayName") }
            val defaultAddressId = decryptField(dto.defaultAddressId, "defaultAddressId").also { piiFieldsProcessed.add("defaultAddressId") }
            val photoUrl = decryptField(dto.photoUrl, "photoUrl").also { piiFieldsProcessed.add("photoUrl") }
            
            // Validate critical fields
            if (dto.userId.isNullOrBlank()) {
                validationErrors.add("userId is null or blank")
            }
            if (email.isNullOrBlank()) {
                validationErrors.add("email is null or blank after decryption")
            }
            
            Log.d(TAG, "  Converting subscription: ${dto.subscription?.level}")
            Log.d(TAG, "  Converting preferences: theme=${dto.preferences?.theme}")
            
            val domainUser = User(
                id = dto.userId ?: "",
                userId = dto.userId,
                email = email,
                displayName = displayName,
                defaultAddressId = defaultAddressId,
                photoUrl = photoUrl,
                authProviders = dto.authProviders,
                accountStatus = dto.accountStatus,
                timezone = dto.timezone,
                createdAt = dto.createdAt,
                lastLoginAt = dto.lastLoginAt,
                privacyPolicyAccepted = dto.privacyPolicyAccepted,
                termsAccepted = dto.termsAccepted,
                version = dto.version,
                subscription = mapSubscriptionToDomain(dto.subscription),
                preferences = mapPreferencesToDomain(dto.preferences),
                permissions = mapPermissionsToDomain(dto.permissions),
                usage = mapUsageToDomain(dto.usage),
                syncInfo = mapSyncInfoToDomain(dto.syncInfo),
                appSettings = dto.appSettings,
                communication = mapCommunicationToDomain(dto.communication),
                usageStats = mapUsageStatsToDomain(dto.usageStats),
                metadata = dto.metadata
            )
            
            val mappingDuration = mappingStartTime.elapsedNow()
            
            // 📊 COMPREHENSIVE BEFORE/AFTER STATE LOGGING
            val inputState = mapOf(
                "dto_userId" to dto.userId,
                "dto_email" to (if (dto.email.isNullOrEmpty()) "empty" else "encrypted"),
                "dto_displayName" to (if (dto.displayName.isNullOrEmpty()) "empty" else "encrypted"),
                "dto_defaultAddressId" to dto.defaultAddressId,
                "dto_accountStatus" to dto.accountStatus,
                "dto_subscription_active" to dto.subscription?.isActive,
                "dto_preferences_size" to dto.preferences?.toString()?.length
            )
            
            val outputState = mapOf(
                "domain_userId" to domainUser.userId,
                "domain_email" to (if (domainUser.email.isNullOrEmpty()) "empty" else "decrypted"),
                "domain_displayName" to (if (domainUser.displayName.isNullOrEmpty()) "empty" else "decrypted"),
                "domain_defaultAddressId" to domainUser.defaultAddressId,
                "domain_accountStatus" to domainUser.accountStatus,
                "domain_subscription_active" to domainUser.subscription?.isActive,
                "domain_preferences_size" to domainUser.preferences?.toString()?.length
            )
            
            val fieldTransformations = listOf(
                "email: DECRYPT",
                "displayName: DECRYPT",
                "defaultAddressId: DECRYPT",
                "photoUrl: DECRYPT",
                "subscription: NESTED_MAPPING",
                "preferences: COMPLEX_MAPPING",
                "permissions: NESTED_MAPPING"
            )
            
            val businessLogicApplied = listOf(
                "pii_decryption",
                "field_validation",
                "subscription_mapping",
                "preferences_calculation",
                "usage_stats_processing"
            )

            Log.d(TAG, "=== PREFERENCE MAPPING TRANSFORMATION ===")
            Log.d(TAG, "Input DTO State: $inputState")
            Log.d(TAG, "Output Domain State: $outputState")
            Log.d(TAG, "Field Transformations: $fieldTransformations")
            Log.d(TAG, "Business Logic Applied: $businessLogicApplied")
            Log.d(TAG, "PII Fields Processed: ${piiFieldsProcessed.size}")
            Log.d(TAG, "Mapping Duration: ${mappingDuration.inWholeMilliseconds}ms")
            
            // 🔍 SESSION CORRELATION: Add mapping success to session
            ClarityArchitectureMonitor.addSessionEvent("preference_mapping_success:${dto.userId}")
            
            // ENHANCED: Monitor successful DTO to Domain conversion
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "PreferenceMapper",
                entityType = "User",
                duration = mappingDuration,
                success = true,
                piiFieldsProcessed = piiFieldsProcessed.size,
                validationErrors = validationErrors,
                fieldMismatches = emptyList(),
                entityId = dto.userId ?: "unknown",
                userId = dto.userId,
                dataSize = dto.toString().length,
                fieldsTransformed = fieldTransformations.size,
                businessLogicApplied = businessLogicApplied,
                cacheUpdated = false
            )
            
            Log.d(TAG, "CONVERSION SUCCESS: UserProfile DTO -> Domain | ID: ${dto.userId}")
            Log.d(TAG, "  Output: pii_fields=${piiFieldsProcessed.size} | validation_errors=${validationErrors.size} | duration=${mappingDuration.inWholeMilliseconds}ms")
            Log.d(TAG, "  Domain size: ${domainUser.toString().length} bytes")
            Log.d(TAG, "  Business logic: decryption, validation, subscription_mapping, preferences_calculation")
            
            Result.Success(domainUser)
        } catch (e: Exception) {
            val mappingDuration = mappingStartTime.elapsedNow()
            
            // 🔍 SESSION CORRELATION: Add mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("preference_mapping_failure:${dto.userId}:exception")
            
            // ENHANCED: Monitor mapping error
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "PreferenceMapper",
                entityType = "User",
                duration = mappingDuration,
                success = false,
                piiFieldsProcessed = piiFieldsProcessed.size,
                validationErrors = validationErrors + "Mapping error: ${e.message}",
                fieldMismatches = emptyList(),
                error = e,
                entityId = dto.userId ?: "unknown",
                userId = dto.userId,
                dataSize = dto.toString().length,
                businessLogicApplied = listOf("mapping_failed")
            )
            
            Log.e(TAG, "CONVERSION FAILED: UserProfile DTO -> Domain | ID: ${dto.userId} | Mapping error", e)
            Log.e(TAG, "  PII fields processed: $piiFieldsProcessed")
            Log.e(TAG, "  Validation errors: $validationErrors")
            Log.e(TAG, "  Duration: ${mappingDuration.inWholeMilliseconds}ms")
            
            Result.Error(e)
        }
    }    suspend fun mapToDto(user: User): Result<UserProfileDto> {
        return try {
            val dtoUser = UserProfileDto(
                userId = user.userId,
                email = encryptField(user.email, "email"),
                displayName = encryptField(user.displayName, "displayName"),
                defaultAddressId = encryptField(user.defaultAddressId, "defaultAddressId"),
                photoUrl = encryptField(user.photoUrl, "photoUrl"),
                authProviders = user.authProviders ?: emptyList(),
                accountStatus = user.accountStatus,
                timezone = user.timezone,
                createdAt = user.createdAt,
                lastLoginAt = user.lastLoginAt,
                privacyPolicyAccepted = user.privacyPolicyAccepted,
                termsAccepted = user.termsAccepted,
                version = user.version,
                subscription = mapSubscriptionToDto(user.subscription),
                preferences = mapPreferencesToDto(user.preferences),
                permissions = mapPermissionsToDto(user.permissions),
                usage = mapUsageToDto(user.usage),
                syncInfo = mapSyncInfoToDto(user.syncInfo),
                appSettings = user.appSettings,
                communication = mapCommunicationToDto(user.communication),
                usageStats = mapUsageStatsToDto(user.usageStats),
                metadata = user.metadata
            )
            Result.Success(dtoUser)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    suspend fun mapToDtoData(user: User): Result<Map<String, Any>> {
        return try {
            val dtoResult = mapToDto(user)
            when (dtoResult) {
                is Result.Success -> {
                    val dtoMap = mutableMapOf<String, Any>()
                    val dto = dtoResult.data
                    
                    dto.userId?.let { dtoMap["userId"] = it }
                    dto.email?.let { dtoMap["email"] = it }
                    dto.displayName?.let { dtoMap["displayName"] = it }
                    dto.defaultAddressId?.let { dtoMap["defaultAddressId"] = it }
                    dto.photoUrl?.let { dtoMap["photoUrl"] = it }
                    dto.authProviders.let { dtoMap["authProviders"] = it }
                    dto.accountStatus?.let { dtoMap["accountStatus"] = it }
                    dto.timezone?.let { dtoMap["timezone"] = it }
                    dto.createdAt?.let { dtoMap["createdAt"] = it }
                    dto.lastLoginAt?.let { dtoMap["lastLoginAt"] = it }
                    dto.privacyPolicyAccepted?.let { dtoMap["privacyPolicyAccepted"] = it }
                    dto.termsAccepted?.let { dtoMap["termsAccepted"] = it }
                    dto.version?.let { dtoMap["version"] = it }
                    dto.subscription?.let { dtoMap["subscription"] = it }
                    dto.preferences?.let { dtoMap["preferences"] = it }
                    dto.permissions?.let { dtoMap["permissions"] = it }
                    dto.usage?.let { dtoMap["usage"] = it }
                    dto.syncInfo?.let { dtoMap["syncInfo"] = it }
                    dto.appSettings?.let { dtoMap["appSettings"] = it }
                    dto.communication?.let { dtoMap["communication"] = it }
                    dto.usageStats?.let { dtoMap["usageStats"] = it }
                    dto.metadata?.let { dtoMap["metadata"] = it }
                    
                    Result.Success(dtoMap)
                }
                is Result.Error -> dtoResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state in mapToDtoData"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }    // ===== BUSINESS LOGIC METHODS =====
    
    fun validateUserPreferences(preferences: UserPreferences?): Result<Unit> {
        return try {
            if (preferences == null) {
                return Result.Success(Unit)
            }
            
            // Add validation logic here
            // Example: validate DND settings, theme values, etc.
            
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }
    
    fun calculateDefaultPreferences(): UserPreferences {
        return UserPreferences(
            notificationsEnabled = false, // ✅ FIX: Default false - user must manually enable tip capture
            theme = "system",
            useLocation = false,
            dnd = null
        )
    }
    
    /**
     * Updates a specific preference setting for a user.
     * This method contains the business logic for preference updates that was moved from the repository.
     */
    fun updatePreferenceSetting(user: User, key: String, value: Any): Result<User> {
        return try {
            val currentPrefs = user.preferences ?: calculateDefaultPreferences()
            
            val updatedPrefs = when (key) {
                "theme" -> currentPrefs.copy(theme = value as? String)
                "notifications_enabled" -> currentPrefs.copy(notificationsEnabled = value as? Boolean)
                "onboarding_completed" -> {
                    // For onboarding, we might need to update a different field
                    // This is business logic that belongs in the mapper
                    currentPrefs
                }
                else -> currentPrefs
            }
            
            Result.Success(user.copy(preferences = updatedPrefs))
        } catch (e: Exception) {
            Result.Error(Exception("Error updating preference setting '$key': ${e.message}", e))
        }
    }
    
    fun isProUser(subscription: UserSubscription?): Boolean {
        return subscription?.let { sub ->
            (sub.isActive == true) &&
                    (sub.level == "premium_plus" || sub.level == "pro")
        } == true
    }
    
    // ===== PRIVATE HELPER METHODS =====
    
    private fun mapSubscriptionToDomain(dto: UserProfileDto.UserSubscription?): UserSubscription? {
        return dto?.let {
            UserSubscription(
                status = it.status,
                level = it.level,
                isActive = it.isActive,
                startDate = it.startDate,
                expiryDate = it.expiryDate,
                isLifetime = it.isLifetime,
                provider = it.provider,
                orderId = it.orderId,
                verification = it.verification?.let { verif ->
                    com.autogratuity.domain.model.VerificationDetails(
                        lastVerified = verif.lastVerified,
                        status = verif.status,
                        error = verif.error
                    )
                }
            )
        }
    }
    
    private fun mapSubscriptionToDto(domain: UserSubscription?): UserProfileDto.UserSubscription? {
        return domain?.let {
            UserProfileDto.UserSubscription(
                status = it.status,
                level = it.level,
                isActive = it.isActive,
                startDate = it.startDate,
                expiryDate = it.expiryDate,
                isLifetime = it.isLifetime,
                provider = it.provider,
                orderId = it.orderId,
                verification = it.verification?.let { verif ->
                    UserProfileDto.Verification(
                        lastVerified = verif.lastVerified,
                        status = verif.status,
                        error = verif.error
                    )
                }
            )
        }
    }    // ===== PREFERENCES MAPPING =====
    
    private fun mapPreferencesToDomain(dto: UserProfileDto.UserPreferences?): UserPreferences? {
        return dto?.let {
            UserPreferences(
                notificationsEnabled = it.notificationsEnabled,
                theme = it.theme,
                useLocation = it.useLocation,
                dnd = mapDndToDomain(it.dnd)
            )
        }
    }
    
    private fun mapPreferencesToDto(domain: UserPreferences?): UserProfileDto.UserPreferences? {
        return domain?.let {
            UserProfileDto.UserPreferences(
                notificationsEnabled = it.notificationsEnabled,
                theme = it.theme,
                useLocation = it.useLocation,
                dnd = mapDndToDto(it.dnd)
            )
        }
    }
    
    private fun mapDndToDomain(dto: UserProfileDto.Dnd?): DndDetails? {
        return dto?.let {
            DndDetails(
                customRule = mapCustomRuleToDomain(it.customRule)
            )
        }
    }
    
    private fun mapDndToDto(domain: DndDetails?): UserProfileDto.Dnd? {
        return domain?.let {
            UserProfileDto.Dnd(
                customRule = mapCustomRuleToDto(it.customRule)
            )
        }
    }
    
    private fun mapCustomRuleToDomain(dto: UserProfileDto.CustomRule?): CustomDndRule? {
        return dto?.let {
            CustomDndRule(
                isEnabled = it.isEnabled,
                tipAmountThreshold = it.tipAmountThreshold,
                comparisonType = ComparisonType.fromDto(it.comparisonType) ?: ComparisonType.LESS_THAN_OR_EQUAL_TO
            )
        }
    }
    
    private fun mapCustomRuleToDto(domain: CustomDndRule?): UserProfileDto.CustomRule? {
        return domain?.let {
            UserProfileDto.CustomRule(
                isEnabled = it.isEnabled,
                tipAmountThreshold = it.tipAmountThreshold,
                comparisonType = it.comparisonType.toDto()
            )
        }
    }

    // ===== PERMISSIONS MAPPING =====
    
    private fun mapPermissionsToDomain(dto: UserProfileDto.UserPermissions?): UserPermissions? {
        return dto?.let {
            UserPermissions(
                bypassLimits = it.bypassLimits,
                maxUploads = it.maxUploads
            )
        }
    }
    
    private fun mapPermissionsToDto(domain: UserPermissions?): UserProfileDto.UserPermissions? {
        return domain?.let {
            UserProfileDto.UserPermissions(
                bypassLimits = it.bypassLimits,
                maxUploads = it.maxUploads
            )
        }
    }

    // ===== USAGE MAPPING =====
    
    private fun mapUsageToDomain(dto: UserProfileDto.UserUsage?): UserUsage? {
        return dto?.let {
            UserUsage(
                mappingCount = it.mappingCount,
                deliveryCount = it.deliveryCount,
                addressCount = it.addressCount,
                dndMarkingsUsed = null, // Not available in DTO
                maxDndMarkings = null, // Not available in DTO
                autoCapturedOrders = null, // Not available in DTO
                lastUsageUpdate = it.lastUsageUpdate
            )
        }
    }
    
    private fun mapUsageToDto(domain: UserUsage?): UserProfileDto.UserUsage? {
        return domain?.let {
            UserProfileDto.UserUsage(
                mappingCount = it.mappingCount,
                deliveryCount = it.deliveryCount,
                addressCount = it.addressCount,
                lastUsageUpdate = it.lastUsageUpdate
                // Note: dndMarkingsUsed, maxDndMarkings, autoCapturedOrders are not available in DTO
            )
        }
    }

    // ===== SYNC INFO MAPPING =====
    
    private fun mapSyncInfoToDomain(dto: UserProfileDto.UserSyncInfo?): UserSyncInfo? {
        return dto?.let {
            UserSyncInfo(
                lastSyncTime = it.lastSyncTime,
                deviceIds = it.deviceIds,
                version = it.version
            )
        }
    }
    
    private fun mapSyncInfoToDto(domain: UserSyncInfo?): UserProfileDto.UserSyncInfo? {
        return domain?.let {
            UserProfileDto.UserSyncInfo(
                lastSyncTime = it.lastSyncTime,
                deviceIds = it.deviceIds,
                version = it.version
            )
        }
    }

    // ===== COMMUNICATION MAPPING =====
    
    private fun mapCommunicationToDomain(dto: UserProfileDto.UserCommunication?): UserCommunication? {
        return dto?.let {
            UserCommunication(
                emailOptIn = it.emailOptIn,
                marketingOptIn = it.marketingOptIn,
                pushNotificationsEnabled = it.pushNotificationsEnabled
            )
        }
    }
    
    private fun mapCommunicationToDto(domain: UserCommunication?): UserProfileDto.UserCommunication? {
        return domain?.let {
            UserProfileDto.UserCommunication(
                emailOptIn = it.emailOptIn,
                marketingOptIn = it.marketingOptIn,
                pushNotificationsEnabled = it.pushNotificationsEnabled
            )
        }
    }

    // ===== USAGE STATS MAPPING =====
    
    private fun mapUsageStatsToDomain(dto: UserProfileDto.UserUsageStats?): UserUsageStats? {
        return dto?.let {
            UserUsageStats(
                deliveryCount = it.deliveryCount,
                tipCount = null, // ✅ CLEANUP FIX: tipCount not in generated DTO yet, but maintained by cloud function
                addressCount = it.addressCount,
                lastUsageDate = it.lastUsageDate,
                totalRuns = it.totalRuns,
                activeDaysCount = it.activeDaysCount,
                totalTips = it.totalTips,
                featureUsage = emptyMap() // FeatureUsage is an open class, convert to empty map for now
            )
        }
    }
    
    private fun mapUsageStatsToDto(domain: UserUsageStats?): UserProfileDto.UserUsageStats? {
        return domain?.let {
            UserProfileDto.UserUsageStats(
                deliveryCount = it.deliveryCount,
                addressCount = it.addressCount,
                lastUsageDate = it.lastUsageDate,
                totalRuns = it.totalRuns,
                activeDaysCount = it.activeDaysCount,
                totalTips = it.totalTips,
                featureUsage = if (it.featureUsage != null) {
                    // FeatureUsage is an empty open class, so we create an instance
                    // The actual mapping would depend on how FeatureUsage is used in practice
                    UserProfileDto.FeatureUsage()
                } else null
            )
        }
    }
}