package com.autogratuity.data.repository.preference

import com.autogratuity.data.model.Result
import com.autogratuity.data.model.generated_kt.User_profile
import kotlinx.coroutines.flow.Flow

/**
 * Internal data layer interface for preference repository operations.
 * This interface is used internally by the data layer and should not be exposed to other layers.
 * 
 * Following delivery architecture gold standard:
 * - Does NOT extend DataRepository to avoid conflicts
 * - Contains only internal data layer operations
 * - Repository implementation should implement domain interface only
 */
internal interface PreferenceDataRepository {

    //-----------------------------------------------------------------------------------
    // Internal Data Layer Operations
    //-----------------------------------------------------------------------------------
    
    suspend fun updateUserProfileDto(profile: User_profile): Result<Unit>
    
    suspend fun updateUserProfileFieldsDto(fields: Map<String, Any>): Result<Unit>
    
    fun observeUserProfileDto(): Flow<Result<User_profile?>>

    //-----------------------------------------------------------------------------------
    // Internal convenience methods for data layer operations
    //-----------------------------------------------------------------------------------
    
    suspend fun setDisplayNameInternal(displayName: String)
    
    suspend fun <T : Any> getPreferenceSettingInternal(key: String, defaultValue: T): T
    
    suspend fun <T : Any> setPreferenceSettingInternal(key: String, value: T)
    
    suspend fun getThemePreferenceInternal(): String
    
    suspend fun setThemePreferenceInternal(theme: String)
    
    suspend fun getNotificationsEnabledInternal(): Boolean
    
    suspend fun setNotificationsEnabledInternal(enabled: Boolean)
    
    suspend fun getDefaultAddressIdInternal(): String
    
    suspend fun setDefaultAddressIdInternal(addressId: String)
    
    suspend fun isOnboardingCompletedInternal(): Boolean
    
    suspend fun setOnboardingCompletedInternal(completed: Boolean)
    
    suspend fun isDataCollectionOptedInInternal(): Boolean
    
    suspend fun setDataCollectionOptedInInternal(optedIn: Boolean)

    //-----------------------------------------------------------------------------------
    // Internal DND Preference Methods
    //-----------------------------------------------------------------------------------

    suspend fun getDndSettingsInternal(): User_profile.Dnd?

    fun observeDndSettingsInternal(): Flow<User_profile.Dnd?>

    suspend fun setDndEnabledInternal(enabled: Boolean)

    suspend fun setDndTipThresholdInternal(threshold: Double)

    suspend fun setDndComparisonTypeInternal(comparisonType: String)

    fun isProUserInternal(): Flow<Result<Boolean>>

    //-----------------------------------------------------------------------------------
    // Internal Methods for Settings Screen Summaries & Actions
    //-----------------------------------------------------------------------------------

    suspend fun getHomeAddressSummaryInternal(): String

    suspend fun getDndMarkingsUsedCountInternal(): Long

    suspend fun getMaxDndMarkingsInternal(): Long

    suspend fun getAutoCapturedOrdersCountInternal(): Long

    suspend fun clearAppCacheInternal()

    // Internal methods for modernized services
    suspend fun setPreferenceSettingsInternal(settings: Map<String, Any>): Result<Unit>

    fun <T : Any> getPreferenceSettingFlowInternal(key: String, defaultValue: T): Flow<T>
} 