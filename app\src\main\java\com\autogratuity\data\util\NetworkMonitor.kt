package com.autogratuity.data.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.util.Log
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

/**
 * Modern network connectivity monitor using Kotlin Flow and StateFlow.
 * Replaces legacy RxJava patterns with 2025 Kotlin standards including:
 * - StateFlow for reactive connectivity state
 * - Result-based error handling
 * - Standard Java atomic operations for thread-safe operations
 * - Structured concurrency patterns
 */

/**
 * Network connectivity state information
 */
data class NetworkState(
    val isConnected: Boolean,
    val connectionType: String,
    val isWifi: Boolean,
    val isMetered: <PERSON><PERSON><PERSON>,
    val timestamp: Long
) {
    companion object {
        val DISCONNECTED = NetworkState(
            isConnected = false,
            connectionType = "none",
            isWifi = false,
            isMetered = false,
            timestamp = 0L
        )
    }
}

/**
 * Network type enumeration
 */
enum class NetworkType {
    WIFI,
    CELLULAR,
    ETHERNET,
    VPN,
    NONE,
    UNKNOWN
}

/**
 * Network capability enumeration
 */
enum class NetworkCapability {
    INTERNET,
    VALIDATED,
    NOT_METERED,
    NOT_VPN,
    CAPTIVE_PORTAL,
    FOREGROUND
}

/**
 * Network monitoring errors
 */
sealed class NetworkMonitorError : Exception() {
    object ConnectivityManagerUnavailable : NetworkMonitorError()
    object NetworkCallbackRegistrationFailed : NetworkMonitorError()
    data class UnexpectedError(override val cause: Throwable) : NetworkMonitorError()
}

/**
 * Modern network monitor using StateFlow and structured concurrency
 */
@Singleton
class FlowNetworkMonitor @Inject constructor(
    private val context: Context
) {
    companion object {
        private const val TAG = "FlowNetworkMonitor"
    }

    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    // Use standard Java atomics for thread-safe state tracking (Kotlin recommended)
    private val isConnected = AtomicBoolean(false)
    private val connectionType = AtomicReference("unknown")
    // ✅ FIXED: Changed AtomicLong(0L) to AtomicLong(0) to resolve KSP compilation errors
    private val lastConnectionCheck = AtomicLong(0L)

    /**
     * Network state flow using modern callbackFlow pattern
     */
    val networkState: StateFlow<NetworkState> = callbackFlow {
        val networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                Log.d(TAG, "Network available: $network")
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                val newState = NetworkState(
                    isConnected = true,
                    connectionType = getConnectionType(capabilities),
                    isWifi = capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true,
                    isMetered = connectivityManager.isActiveNetworkMetered,
                    timestamp = System.currentTimeMillis()
                )
                
                // Update atomic state
                isConnected.set(true)
                connectionType.set(newState.connectionType)
                lastConnectionCheck.set(newState.timestamp)
                
                trySend(newState)
            }

            override fun onLost(network: Network) {
                Log.d(TAG, "Network lost: $network")
                val newState = NetworkState(
                    isConnected = false,
                    connectionType = "none",
                    isWifi = false,
                    isMetered = false,
                    timestamp = System.currentTimeMillis()
                )
                
                // Update atomic state
                isConnected.set(false)
                connectionType.set("none")
                lastConnectionCheck.set(newState.timestamp)
                
                trySend(newState)
            }

            override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
                Log.d(TAG, "Network capabilities changed: $network")
                val newState = NetworkState(
                    isConnected = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET),
                    connectionType = getConnectionType(networkCapabilities),
                    isWifi = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI),
                    isMetered = connectivityManager.isActiveNetworkMetered,
                    timestamp = System.currentTimeMillis()
                )
                
                // Update atomic state  
                isConnected.set(newState.isConnected)
                connectionType.set(newState.connectionType)
                lastConnectionCheck.set(newState.timestamp)
                
                trySend(newState)
            }
        }

        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()

        connectivityManager.registerNetworkCallback(networkRequest, networkCallback)

        // Send initial state
        val initialState = getCurrentNetworkState()
        trySend(initialState)

        awaitClose {
            connectivityManager.unregisterNetworkCallback(networkCallback)
        }
    }.stateIn(
        scope = kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Default),
        started = SharingStarted.WhileSubscribed(5.seconds),
        initialValue = getCurrentNetworkState()
    )

    /**
     * Get current network state synchronously using atomic reads
     */
    fun getCurrentNetworkState(): NetworkState {
        val activeNetwork = connectivityManager.activeNetwork
        val capabilities = activeNetwork?.let { connectivityManager.getNetworkCapabilities(it) }
        
        return NetworkState(
            isConnected = capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true,
            connectionType = getConnectionType(capabilities),
            isWifi = capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true,
            isMetered = connectivityManager.isActiveNetworkMetered,
            timestamp = System.currentTimeMillis()
        )
    }

    /**
     * Check if network is currently available using atomic read
     */
    fun isNetworkAvailable(): Boolean = isConnected.get()

    /**
     * Get current connection type using atomic read
     */
    fun getCurrentConnectionType(): String = connectionType.get()

    /**
     * Get last connection check timestamp using atomic read  
     */
    fun getLastConnectionCheck(): Long = lastConnectionCheck.get()

    /**
     * Determine connection type from network capabilities
     */
    private fun getConnectionType(capabilities: NetworkCapabilities?): String {
        return when {
            capabilities == null -> "none"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "wifi"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "cellular"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "ethernet"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> "bluetooth"
            else -> "unknown"
        }
    }

    /**
     * Check if device has any network connection
     */
    fun hasNetworkConnection(): Boolean = getCurrentNetworkState().isConnected

    /**
     * Check if device has validated internet access
     */
    fun hasValidatedInternet(): Boolean {
        val activeNetwork = connectivityManager.activeNetwork
        val capabilities = activeNetwork?.let { connectivityManager.getNetworkCapabilities(it) }
        return capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true &&
               capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
    }

    /**
     * Check if current connection is metered
     */
    fun isConnectionMetered(): Boolean = getCurrentNetworkState().isMetered
}

/**
 * Legacy compatibility class for gradual migration
 * @deprecated Use FlowNetworkMonitor instead
 */
@Deprecated(
    message = "Use FlowNetworkMonitor with StateFlow instead",
    replaceWith = ReplaceWith("FlowNetworkMonitor"),
    level = DeprecationLevel.WARNING
)
class NetworkMonitor(context: Context) {
    private val modernMonitor = FlowNetworkMonitor(context)

    @Deprecated("Use FlowNetworkMonitor.networkState.map { it.isConnected } instead")
    fun observe(): Flow<Boolean> = modernMonitor.networkState.map { it.isConnected }

    @Deprecated("Use FlowNetworkMonitor.hasNetworkConnection() instead") 
    fun isNetworkAvailable(): Boolean = modernMonitor.hasNetworkConnection()

    @Deprecated("Use FlowNetworkMonitor.hasNetworkConnection() instead")
    val isConnected: Boolean get() = modernMonitor.hasNetworkConnection()
} 