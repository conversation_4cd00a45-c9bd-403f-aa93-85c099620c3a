package com.autogratuity.data.model

/**
 * A generic class that holds a value with its loading status.
 * @param <T> Type of the value.
 */
sealed class Result<out T> {

    data class Success<out T>(val data: T) : Result<T>()
    data class Error(val exception: Exception) : Result<Nothing>()
    object Loading : Result<Nothing>() // Optional: if you want to represent a loading state for one-shot calls

    override fun toString(): String {
        return when (this) {
            is Success<*> -> "Success[data=$data]"
            is Error -> "Error[exception=$exception]"
            Loading -> "Loading"
        }
    }
}

/**
 * `true` if [Result] is of type [Success] & holds non-null [Success.data].
 */
val Result<*>.succeeded
    get() = this is Result.Success && data != null

fun <T> Result<T>.successOrNull(): T? {
    return (this as? Result.Success<T>)?.data
}

fun <T> Result<T>.exceptionOrNull(): Exception? {
    return (this as? Result.Error)?.exception
} 