package com.autogratuity.data.security

// import dagger.hilt.android.qualifiers.ApplicationContext // Hilt/Dagger related
import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import android.util.Log
import androidx.core.content.edit
import kotlinx.atomicfu.atomic
import kotlinx.atomicfu.update
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

// import javax.inject.Inject // Hilt/Dagger related
// import javax.inject.Singleton // Hilt/Dagger related

/**
 * Represents the result of a cryptographic operation.
 * Provides detailed error information and maintains security best practices.
 */
sealed class CryptoResult<out T> {
    /** Successful operation with the resulting data */
    data class Success<T>(val data: T) : CryptoResult<T>()
    
    /** Base class for all crypto operation failures */
    sealed class Failure : CryptoResult<Nothing>() {
        /** The error message, carefully crafted to avoid leaking sensitive information */
        abstract val message: String
        
        /** Timestamp when the error occurred */
        val timestamp: Instant = Clock.System.now()
    }
    
    /** Initialization failures */
    data class InitializationFailure(override val message: String, val cause: Throwable? = null) : Failure()
    
    /** Key access or generation failures */
    data class KeyFailure(override val message: String, val cause: Throwable? = null) : Failure()
    
    /** Encryption operation failures */
    data class EncryptionFailure(override val message: String, val cause: Throwable? = null) : Failure()
    
    /** Decryption operation failures */
    data class DecryptionFailure(override val message: String, val cause: Throwable? = null) : Failure()
    
    /** Errors related to input validation */
    data class InputValidationFailure(override val message: String) : Failure()
    
    /** Timeout failures */
    data class TimeoutFailure(override val message: String, val duration: Duration) : Failure()
    
    /** Authentication failures (e.g., message tampering detected in GCM) */
    data class AuthenticationFailure(override val message: String, val cause: Throwable? = null) : Failure()
    
    /** Returns true if this result represents success */
    val isSuccess: Boolean get() = this is Success
    
    /** Executes the given [action] if this instance represents [Success] */
    inline fun onSuccess(action: (T) -> Unit): CryptoResult<T> {
        if (this is Success) action(data)
        return this
    }
    
    /** Executes the given [action] if this instance represents [Failure] */
    inline fun onFailure(action: (Failure) -> Unit): CryptoResult<T> {
        if (this is Failure) action(this)
        return this
    }
    
    /** Maps a successful result using the given [transform] function */
    inline fun <R> map(transform: (T) -> R): CryptoResult<R> {
        return when (this) {
            is Success -> Success(transform(data))
            is Failure -> this
        }
    }
    
    /** Gets the encapsulated value if this instance represents success or null otherwise */
    fun getOrNull(): T? = if (this is Success) data else null
}

/**
 * Utility class for encrypting and decrypting sensitive data.
 * Uses the Android KeyStore to securely store encryption keys.
 * Implements 2025 Kotlin standards with atomicity, structured concurrency, and Result-based error handling.
 *
 * This class is thread-safe and supports concurrent access patterns common in modern Android applications.
 */
// @Singleton // Hilt/Dagger related
class EncryptionUtils /* @Inject */ constructor( 
    private val context: Context, // Made non-nullable, Koin will provide this
    private val ioDispatcher: CoroutineDispatcher = Dispatchers.IO
) {
    // Thread-safe initialization state tracking using atomicfu
    private val initialized = atomic(false)
    private val initializationMutex = Mutex()
    
    // Thread-safe key store reference with lazy initialization
    private val keyStore = atomic<KeyStore?>(null)
    
    // Performance tracking
    private val encryptionTimeNanos = atomic(0L)
    private val decryptionTimeNanos = atomic(0L)
    private val operationCount = atomic(0)
    
    /**
     * Whether the encryption system has been properly initialized
     * Thread-safe property using atomicfu
     */
    val isInitialized: Boolean
        get() = initialized.value

    companion object {
        private const val TAG = "EncryptionUtils"
        
        // KeyStore constants
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val MASTER_KEY_ALIAS = "autogratuity_master_key"
        
        // ✅ PERFORMANCE: Memory pool for byte arrays to reduce GC pressure
        private val byteArrayPool = mutableMapOf<Int, MutableList<ByteArray>>()
        private const val MAX_POOL_SIZE = 10
        
        private fun borrowByteArray(size: Int): ByteArray {
            return byteArrayPool[size]?.removeFirstOrNull() ?: ByteArray(size)
        }
        
        private fun returnByteArray(array: ByteArray) {
            val size = array.size
            val pool = byteArrayPool.getOrPut(size) { mutableListOf() }
            if (pool.size < MAX_POOL_SIZE) {
                array.fill(0) // Clear sensitive data
                pool.add(array)
            }
        }
        
        // Cryptographic algorithm specifications
        private const val KEY_ALGORITHM_AES = KeyProperties.KEY_ALGORITHM_AES
        private const val BLOCK_MODE_GCM = KeyProperties.BLOCK_MODE_GCM
        private const val ENCRYPTION_PADDING_NONE = KeyProperties.ENCRYPTION_PADDING_NONE
        private const val TRANSFORMATION = "$KEY_ALGORITHM_AES/$BLOCK_MODE_GCM/$ENCRYPTION_PADDING_NONE"
        
        // Version and format constants
        private const val ENCRYPTION_VERSION_PREFIX = "AGT01:"
        
        // GCM parameters
        private const val IV_SIZE_BYTES = 12 // GCM recommended IV length (96 bits)
        private const val TAG_LENGTH_BITS = 128 // GCM authentication tag length in bits
        
        // Default timeout values
        private val DEFAULT_INIT_TIMEOUT = 5.seconds
        private val DEFAULT_OPERATION_TIMEOUT = 2.seconds
    }

    /**
     * Returns performance metrics for monitoring and debugging.
     * Provides insights into encryption system performance without exposing sensitive data.
     * This is now an instance method.
     */
    fun getPerformanceMetrics(): Map<String, Any> {
        val currentOperationCount = operationCount.value // Access instance atomic property
        val currentEncryptionNanos = encryptionTimeNanos.value // Access instance atomic property
        val currentDecryptionNanos = decryptionTimeNanos.value // Access instance atomic property

        val avgEncryptionTimeMs = if (currentOperationCount > 0) {
            (currentEncryptionNanos / currentOperationCount) / 1_000_000.0
        } else 0.0
        
        val avgDecryptionTimeMs = if (currentOperationCount > 0) {
            (currentDecryptionNanos / currentOperationCount) / 1_000_000.0
        } else 0.0
        
        return mapOf(
            "operationCount" to currentOperationCount,
            "avgEncryptionTimeMs" to avgEncryptionTimeMs,
            "avgDecryptionTimeMs" to avgDecryptionTimeMs,
            "initialized" to initialized.value // Access instance atomic property
        )
    }

    /**
     * Initializes the KeyStore and ensures the master key exists.
     * This function is suspending, cancellable, and thread-safe with proper timeouts.
     *
     * @param timeout Optional timeout for the initialization process
     * @return CryptoResult indicating success or detailed error information
     */
    suspend fun initialize(timeout: Duration = 5.seconds): CryptoResult<Unit> {
        // Fast path for already initialized state
        if (initialized.value) return CryptoResult.Success(Unit)
        
        return try {
            // Apply timeout to the initialization process
            withTimeout(timeout) {
                initializationMutex.withLock {
                    // Double-check after acquiring the lock
                    if (initialized.value) return@withLock CryptoResult.Success(Unit)
                    
                    val startTime = System.nanoTime()
                    Log.d(TAG, "Initializing EncryptionUtils...")
                    
                    try {
                        // Thread-safe update of keyStore reference
                        keyStore.update { previousValue ->
                            KeyStore.getInstance(ANDROID_KEYSTORE).apply {
                                load(null) // Load the keystore
                                if (!containsAlias(MASTER_KEY_ALIAS)) {
                                    createMasterKey()
                                }
                            }
                        }
                        
                        // Mark as initialized atomically
                        initialized.value = true
                        
                        val duration = (System.nanoTime() - startTime) / 1_000_000 // ms
                        Log.i(TAG, "EncryptionUtils initialized successfully in $duration ms")
                        CryptoResult.Success(Unit)
                    } catch (e: Exception) {
                        val errorMsg = "Encryption system initialization failed"
                        Log.e(TAG, errorMsg, e)
                        CryptoResult.InitializationFailure(errorMsg, e)
                    }
                }
            }
        } catch (e: CancellationException) {
            // Properly handle coroutine cancellation
            Log.w(TAG, "Encryption initialization cancelled")
            throw e // Always rethrow CancellationException
        } catch (e: Exception) {
            // Handle timeout and other exceptions
            val errorMsg = when (e) {
                is kotlinx.coroutines.TimeoutCancellationException -> 
                    "Encryption initialization timed out after ${timeout.inWholeMilliseconds}ms"
                else -> "Unexpected error during encryption initialization: ${e.message}"
            }
            Log.e(TAG, errorMsg, e)
            CryptoResult.TimeoutFailure(errorMsg, timeout)
        }
    }
    
    /**
     * Ensures the encryption system is initialized before proceeding.
     * For internal use by encryption/decryption methods.
     *
     * @return CryptoResult indicating success or detailed initialization failure
     */
    private suspend fun ensureInitialized(): CryptoResult<Unit> {
        return if (initialized.value) {
            CryptoResult.Success(Unit)
        } else {
            initialize()
        }
    }

    /**
     * Creates a new master encryption key in the Android KeyStore
     * with modern security parameters suitable for 2025+ requirements.
     * Implements fallback strategy: StrongBox → Regular Keystore → Software fallback
     *
     * @return CryptoResult indicating success or detailed error information
     */
    private fun createMasterKey(): CryptoResult<Unit> {
        // Try StrongBox first (hardware-backed security)
        val strongBoxResult = tryCreateMasterKeyWithStrongBox()
        if (strongBoxResult is CryptoResult.Success) {
            Log.i(TAG, "Master key created successfully with StrongBox hardware security")
            return strongBoxResult
        }
        
        // Log StrongBox failure and try regular keystore
        Log.w(TAG, "StrongBox unavailable, falling back to regular Android Keystore: ${(strongBoxResult as? CryptoResult.Failure)?.message}")
        
        val regularKeystoreResult = tryCreateMasterKeyWithRegularKeystore()
        if (regularKeystoreResult is CryptoResult.Success) {
            Log.i(TAG, "Master key created successfully with regular Android Keystore")
            return regularKeystoreResult
        }
        
        // Log regular keystore failure and try software fallback
        Log.w(TAG, "Regular Keystore failed, falling back to software encryption: ${(regularKeystoreResult as? CryptoResult.Failure)?.message}")
        
        val softwareResult = tryCreateMasterKeyWithSoftwareFallback()
        if (softwareResult is CryptoResult.Success) {
            Log.w(TAG, "Master key created with software fallback - reduced security level")
            return softwareResult
        }
        
        // All methods failed
        val errorMsg = "All encryption methods failed: StrongBox, Keystore, and Software fallback"
        Log.e(TAG, errorMsg)
        return CryptoResult.KeyFailure(errorMsg, (softwareResult as? CryptoResult.KeyFailure)?.cause)
    }
    
    /**
     * Attempts to create master key with StrongBox hardware security
     */
    private fun tryCreateMasterKeyWithStrongBox(): CryptoResult<Unit> {
        return try {
            val keyGenerator = KeyGenerator.getInstance(KEY_ALGORITHM_AES, ANDROID_KEYSTORE)
            val keySpec = KeyGenParameterSpec.Builder(
                MASTER_KEY_ALIAS,
                KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
            )
                .setBlockModes(BLOCK_MODE_GCM)
                .setEncryptionPaddings(ENCRYPTION_PADDING_NONE)
                .setKeySize(256) // AES-256
                .setIsStrongBoxBacked(true) // Use StrongBox hardware security
                .setUserAuthenticationRequired(false)
                .setRandomizedEncryptionRequired(true)
                .setUnlockedDeviceRequired(true)
                .build()
                
            keyGenerator.init(keySpec)
            keyGenerator.generateKey()
            
            CryptoResult.Success(Unit)
        } catch (e: Exception) {
            CryptoResult.KeyFailure("StrongBox key creation failed", e)
        }
    }
    
    /**
     * Attempts to create master key with regular Android Keystore
     */
    private fun tryCreateMasterKeyWithRegularKeystore(): CryptoResult<Unit> {
        return try {
            val keyGenerator = KeyGenerator.getInstance(KEY_ALGORITHM_AES, ANDROID_KEYSTORE)
            val keySpec = KeyGenParameterSpec.Builder(
                MASTER_KEY_ALIAS,
                KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
            )
                .setBlockModes(BLOCK_MODE_GCM)
                .setEncryptionPaddings(ENCRYPTION_PADDING_NONE)
                .setKeySize(256) // AES-256
                .setIsStrongBoxBacked(false) // Regular keystore, not StrongBox
                .setUserAuthenticationRequired(false)
                .setRandomizedEncryptionRequired(true)
                .setUnlockedDeviceRequired(true)
                .build()
                
            keyGenerator.init(keySpec)
            keyGenerator.generateKey()
            
            CryptoResult.Success(Unit)
        } catch (e: Exception) {
            CryptoResult.KeyFailure("Regular Keystore key creation failed", e)
        }
    }
    
    /**
     * Software fallback for devices that don't support Android Keystore properly
     * Uses a device-specific key derived from Android ID and other device characteristics
     */
    private fun tryCreateMasterKeyWithSoftwareFallback(): CryptoResult<Unit> {
        return try {
            // For software fallback, we'll store a marker in SharedPreferences
            // The actual key will be derived from device characteristics when needed
            val prefs = context.getSharedPreferences("autogratuity_crypto", Context.MODE_PRIVATE)
            prefs.edit() {
                putBoolean("software_fallback_enabled", true)
                    .putLong("fallback_created_at", System.currentTimeMillis())
            }
            
            Log.w(TAG, "Using software encryption fallback - security level reduced")
            CryptoResult.Success(Unit)
        } catch (e: Exception) {
            CryptoResult.KeyFailure("Software fallback key creation failed", e)
        }
    }

    /**
     * Retrieves the master encryption key from the Android KeyStore
     * with proper error handling and security validations.
     * Handles software fallback when keystore is unavailable.
     *
     * @return CryptoResult containing the SecretKey or detailed error information
     */
    private fun getMasterKey(): CryptoResult<SecretKey> {
        if (!initialized.value) {
            return CryptoResult.InitializationFailure(
                "EncryptionUtils not initialized. Call initialize() first or use suspending functions."
            )
        }
        
        return try {
            // Check if we're using software fallback
            val prefs = context.getSharedPreferences("autogratuity_crypto", Context.MODE_PRIVATE)
            val usingSoftwareFallback = prefs.getBoolean("software_fallback_enabled", false)
            
            if (usingSoftwareFallback) {
                // Generate software fallback key from device characteristics
                return generateSoftwareFallbackKey()
            }
            
            // Try to get key from Android Keystore
            val ks = keyStore.value ?: return CryptoResult.KeyFailure("KeyStore is null despite initialization")
            val key = ks.getKey(MASTER_KEY_ALIAS, null) as? SecretKey
                ?: return CryptoResult.KeyFailure("Master key not found or wrong type")
                
            CryptoResult.Success(key)
        } catch (e: Exception) {
            val errorMsg = "Error retrieving master encryption key"
            Log.e(TAG, errorMsg, e)
            CryptoResult.KeyFailure(errorMsg, e)
        }
    }
    
    /**
     * Generates a software fallback key based on device characteristics
     * This is less secure but ensures functionality on all devices
     */
    private fun generateSoftwareFallbackKey(): CryptoResult<SecretKey> {
        return try {
            // Use device characteristics to generate a consistent key
            val deviceId = android.provider.Settings.Secure.getString(
                context.contentResolver,
                android.provider.Settings.Secure.ANDROID_ID
            ) ?: "fallback_device_id"
            
            // Create a deterministic key from device characteristics
            val keyMaterial = "autogratuity_fallback_$deviceId".toByteArray(StandardCharsets.UTF_8)
            val digest = java.security.MessageDigest.getInstance("SHA-256")
            val hashedKey = digest.digest(keyMaterial)
            
            // Take first 32 bytes for AES-256
            val keyBytes = hashedKey.copyOf(32)
            val secretKey = javax.crypto.spec.SecretKeySpec(keyBytes, "AES")
            
            Log.w(TAG, "Using software fallback key - reduced security")
            CryptoResult.Success(secretKey)
        } catch (e: Exception) {
            CryptoResult.KeyFailure("Failed to generate software fallback key", e)
        }
    }

    /**
     * Encrypts a plaintext string using modern cryptography practices.
     * Returns a structured CryptoResult for comprehensive error handling.
     *
     * @param plaintext The string to encrypt
     * @return CryptoResult containing the encrypted Base64 string or detailed error information
     */
    suspend fun encrypt(plaintext: String?): CryptoResult<String> {
        // Input validation
        if (plaintext == null) {
            return CryptoResult.InputValidationFailure("Cannot encrypt null data")
        }
        if (plaintext.isEmpty()) {
            return CryptoResult.InputValidationFailure("Cannot encrypt empty string")
        }
        
        // Ensure initialization
        val initResult = ensureInitialized()
        if (initResult !is CryptoResult.Success) {
            val initCause = when (val failureResult = initResult as? CryptoResult.Failure) {
                is CryptoResult.InitializationFailure -> failureResult.cause
                is CryptoResult.KeyFailure -> failureResult.cause
                is CryptoResult.AuthenticationFailure -> failureResult.cause
                is CryptoResult.DecryptionFailure -> failureResult.cause // Though unlikely from init
                is CryptoResult.EncryptionFailure -> failureResult.cause // Though unlikely from init
                // InputValidationFailure and TimeoutFailure do not have a 'cause' of type Throwable?
                else -> null // For Failure types without a 'cause' or if not a Failure
            }
            return CryptoResult.EncryptionFailure("Encryption failed: system not initialized", initCause)
        }
        
        return withContext(ioDispatcher) { // Perform crypto operations on IO dispatcher
            val startTime = System.nanoTime()
            
            try {
                // Get the master key
                val keyResult = getMasterKey()
                if (keyResult !is CryptoResult.Success) {
                    val keyGenCause = when (val failureResult = keyResult as? CryptoResult.Failure) {
                        is CryptoResult.InitializationFailure -> failureResult.cause
                        is CryptoResult.KeyFailure -> failureResult.cause
                        is CryptoResult.AuthenticationFailure -> failureResult.cause
                        else -> null
                    }
                    return@withContext CryptoResult.EncryptionFailure(
                        "Encryption failed: unable to access master key",
                        keyGenCause
                    )
                }
                
                // Initialize cipher
                val cipher = Cipher.getInstance(TRANSFORMATION)
                cipher.init(Cipher.ENCRYPT_MODE, keyResult.data)
                
                // Extract IV and encrypt data
                val iv = cipher.iv
                val encryptedBytes = cipher.doFinal(plaintext.toByteArray(StandardCharsets.UTF_8))
                val prefixBytes = ENCRYPTION_VERSION_PREFIX.toByteArray(StandardCharsets.UTF_8)
                
                // ✅ PERFORMANCE: Combine prefix, IV, and ciphertext efficiently
                val combined = ByteBuffer.allocate(prefixBytes.size + iv.size + encryptedBytes.size)
                    .put(prefixBytes)
                    .put(iv)
                    .put(encryptedBytes)
                    .array()
                
                // Base64 encode the result
                val encodedResult = Base64.encodeToString(combined, Base64.DEFAULT)
                
                // Update metrics
                val elapsedNanos = System.nanoTime() - startTime
                encryptionTimeNanos.update { current -> current + elapsedNanos }
                operationCount.update { current -> current + 1 }
                
                Log.d(TAG, "Encrypted ${plaintext.length} chars in ${elapsedNanos / 1_000_000}ms")
                CryptoResult.Success(encodedResult)
            } catch (e: Exception) {
                val errorMsg = "Encryption operation failed"
                Log.e(TAG, errorMsg, e)
                CryptoResult.EncryptionFailure(errorMsg, e)
            }
        }
    }
    
    /**
     * Flow-based encryption for reactive programming patterns.
     * Allows for easy composition with other Flow operations.
     *
     * @param plaintext The string to encrypt
     * @return Flow emitting a CryptoResult
     */
    fun encryptAsFlow(plaintext: String?): Flow<CryptoResult<String>> = flow {
        emit(encrypt(plaintext))
    }.flowOn(ioDispatcher)
    
    /**
     * Decrypts a previously encrypted string using modern cryptography practices.
     * Returns a structured CryptoResult for comprehensive error handling.
     *
     * @param encryptedText The encrypted Base64 string to decrypt
     * @return CryptoResult containing the decrypted plaintext or detailed error information
     */
    suspend fun decrypt(encryptedText: String?): CryptoResult<String> {
        // Input validation
        if (encryptedText == null) {
            return CryptoResult.InputValidationFailure("Cannot decrypt null data")
        }
        if (encryptedText.isEmpty()) {
            return CryptoResult.InputValidationFailure("Cannot decrypt empty string")
        }
        
        // Ensure initialization
        val initResult = ensureInitialized()
        if (initResult !is CryptoResult.Success) {
            val initCause = when (val failureResult = initResult as? CryptoResult.Failure) {
                is CryptoResult.InitializationFailure -> failureResult.cause
                is CryptoResult.KeyFailure -> failureResult.cause
                is CryptoResult.AuthenticationFailure -> failureResult.cause
                is CryptoResult.DecryptionFailure -> failureResult.cause // Though unlikely from init
                is CryptoResult.EncryptionFailure -> failureResult.cause // Though unlikely from init
                else -> null
            }
            return CryptoResult.DecryptionFailure("Decryption failed: system not initialized", initCause)
        }
        
        return withContext(ioDispatcher) {
            val startTime = System.nanoTime()
            
            try {
                // Get the master key
                val keyResult = getMasterKey()
                if (keyResult !is CryptoResult.Success) {
                    val keyGenCause = when (val failureResult = keyResult as? CryptoResult.Failure) {
                        is CryptoResult.InitializationFailure -> failureResult.cause
                        is CryptoResult.KeyFailure -> failureResult.cause
                        is CryptoResult.AuthenticationFailure -> failureResult.cause
                        else -> null
                    }
                    return@withContext CryptoResult.DecryptionFailure(
                        "Decryption failed: unable to access master key",
                        keyGenCause
                    )
                }
                
                // Decode Base64 data
                val encryptedData = try {
                    Base64.decode(encryptedText, Base64.DEFAULT)
                } catch (e: IllegalArgumentException) {
                    return@withContext CryptoResult.DecryptionFailure(
                        "Invalid Base64 encoding in encrypted text", e)
                }
                
                // Extract and validate the version prefix
                val prefixBytes = ENCRYPTION_VERSION_PREFIX.toByteArray(StandardCharsets.UTF_8)
                if (encryptedData.size < prefixBytes.size + IV_SIZE_BYTES) {
                    return@withContext CryptoResult.DecryptionFailure(
                        "Encrypted data has invalid format or is corrupted (too short)")
                }
                
                // Check version prefix
                for (i in prefixBytes.indices) {
                    if (encryptedData[i] != prefixBytes[i]) {
                        return@withContext CryptoResult.DecryptionFailure(
                            "Unknown encryption version or corrupted data")
                    }
                }
                
                // Extract IV and ciphertext
                val ivBytes = ByteArray(IV_SIZE_BYTES)
                val ciphertextBytes = ByteArray(encryptedData.size - prefixBytes.size - IV_SIZE_BYTES)
                
                // Copy IV
                System.arraycopy(encryptedData, prefixBytes.size, ivBytes, 0, IV_SIZE_BYTES)
                
                // Copy ciphertext
                System.arraycopy(
                    encryptedData,
                    prefixBytes.size + IV_SIZE_BYTES,
                    ciphertextBytes,
                    0,
                    ciphertextBytes.size
                )
                
                // Initialize cipher with IV
                val cipher = Cipher.getInstance(TRANSFORMATION)
                val ivSpec = GCMParameterSpec(TAG_LENGTH_BITS, ivBytes)
                cipher.init(Cipher.DECRYPT_MODE, keyResult.data, ivSpec)
                
                // Decrypt
                val decryptedBytes = cipher.doFinal(ciphertextBytes)
                val result = String(decryptedBytes, StandardCharsets.UTF_8)
                
                // Update metrics
                val elapsedNanos = System.nanoTime() - startTime
                decryptionTimeNanos.update { current -> current + elapsedNanos }
                operationCount.update { current -> current + 1 }
                
                Log.d(TAG, "Decrypted ${ciphertextBytes.size} bytes in ${elapsedNanos / 1_000_000}ms")
                CryptoResult.Success(result)
            } catch (e: javax.crypto.AEADBadTagException) {
                // Special handling for authentication failures (common with tampered data)
                val errorMsg = "Decryption authentication failed - data may be tampered with"
                Log.w(TAG, errorMsg, e)
                CryptoResult.AuthenticationFailure(errorMsg, e)
            } catch (e: Exception) {
                val errorMsg = "Decryption operation failed"
                Log.e(TAG, errorMsg, e)
                CryptoResult.DecryptionFailure(errorMsg, e)
            }
        }
    }
    
    /**
     * Flow-based decryption for reactive programming patterns.
     * Allows for easy composition with other Flow operations.
     *
     * @param encryptedText The encrypted Base64 string to decrypt
     * @return Flow emitting a CryptoResult
     */
    fun decryptAsFlow(encryptedText: String?): Flow<CryptoResult<String>> = flow {
        emit(decrypt(encryptedText))
    }.flowOn(ioDispatcher)

    /**
     * Checks if the provided text appears to be encrypted.
     * This is a simple heuristic based on the encryption prefix.
     * 
     * @param text The string to check
     * @return true if it starts with the known encryption prefix, false otherwise
     */
    fun isEncrypted(text: String?): Boolean {
        return text?.startsWith(ENCRYPTION_VERSION_PREFIX) == true
    }
}
