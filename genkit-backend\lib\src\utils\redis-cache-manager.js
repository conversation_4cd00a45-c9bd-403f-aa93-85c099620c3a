"use strict";
// ❌ REDIS REMOVED: Entire Redis cache manager disabled
// import { createClient, RedisClientType } from 'redis';
Object.defineProperty(exports, "__esModule", { value: true });
exports.redisCache = void 0;
async;
get(key, string);
Promise < string | null > {
    try: {
        : .connectionAttempted
    }
};
{
    await this.initializeConnection();
}
if (!this.client || !this.isConnected) {
    return null; // Fallback to Firestore
}
const value = await this.client.get(key);
if (value) {
    console.log(`[RedisCacheManager] Cache hit for key: ${key}`);
}
return value;
try { }
catch (error) {
    console.warn(`[RedisCacheManager] Error getting key ${key}, falling back to Firestore:`, error);
    return null; // Graceful fallback
}
async;
setex(key, string, ttlSeconds, number, value, string);
Promise < void  > {
    try: {
        : .connectionAttempted
    }
};
{
    await this.initializeConnection();
}
if (!this.client || !this.isConnected) {
    console.log(`[RedisCacheManager] Redis unavailable, skipping cache set for key: ${key}`);
    return; // Graceful fallback - don't cache but don't fail
}
await this.client.setEx(key, ttlSeconds, value);
console.log(`[RedisCacheManager] Cached key: ${key}, TTL: ${ttlSeconds}s`);
try { }
catch (error) {
    console.warn(`[RedisCacheManager] Error setting key ${key}, continuing without cache:`, error);
    // Don't throw - graceful degradation
}
async;
del(key, string);
Promise < void  > {
    try: {
        : .connectionAttempted
    }
};
{
    await this.initializeConnection();
}
if (!this.client || !this.isConnected) {
    console.log(`[RedisCacheManager] Redis unavailable, skipping cache delete for key: ${key}`);
    return; // Graceful fallback
}
await this.client.del(key);
console.log(`[RedisCacheManager] Deleted cache key: ${key}`);
try { }
catch (error) {
    console.warn(`[RedisCacheManager] Error deleting key ${key}, continuing:`, error);
    // Don't throw - graceful degradation
}
isAvailable();
boolean;
{
    return this.isConnected && this.client !== null;
}
async;
close();
Promise < void  > {
    : .client && this.isConnected
};
{
    try {
        await this.client.quit();
        console.log('[RedisCacheManager] Redis connection closed gracefully');
    }
    catch (error) {
        console.warn('[RedisCacheManager] Error closing Redis connection:', error);
    }
}
this.isConnected = false;
this.client = null;
// Export singleton instance
exports.redisCache = RedisCacheManager.getInstance();
//# sourceMappingURL=redis-cache-manager.js.map