# Cloud Function Testing Guide

This guide explains how to test your cloud functions locally without deploying them or using Android Studio.

## 🎯 Overview

Your Android app has **8 cloud functions** currently deployed:

### Firestore Triggers
- `onDeliveryWrittenCheckPendingTips` - Processes pending tips when deliveries are written
- `onDeliveryWrittenUpdateAddressStats` - Updates address statistics when deliveries change  
- `onUserPreferencesChange` - Handles user preference changes (Firestore-only)

### Storage Trigger
- `onGeoJsonFileFinalized` - Processes uploaded GeoJSON files from Google Cloud Storage

### Callable Functions (HTTP)
- `testParseImportFlow` - Tests import data parsing
- `callGoogleApi` - Single Google API operations (geocoding, etc.)
- `callDashboardBatch` - Batch Google API operations for dashboard optimization
- `setManualAddressDndOverride` - Manual Do-Not-Deliver override functionality

## 🚀 Quick Start

### Prerequisites
1. Ensure Firebase Admin SDK is configured:
   ```bash
   gcloud auth application-default login
   ```

2. Build the project:
   ```bash
   npm run build
   ```

### Run All Tests
```bash
npm run test-cloud-functions
```

This runs a comprehensive test suite covering:
- ✅ Individual flow testing
- ✅ HTTP callable function testing  
- ✅ Firestore trigger simulation
- ✅ Read/write operations
- ✅ Error handling validation

### Run Specific Test Categories

**Test only the core flows:**
```bash
npm run test-flows
```

**Test only HTTP callable functions:**
```bash
npm run test-http
```

**Test individual components:**
```bash
# Build first
npm run build

# Test specific flows
node lib/src/local-runner.js test-flows
node lib/src/local-runner.js test-http
node lib/src/local-runner.js test-all
```

## 📋 What Gets Tested

### 1. Flow Testing (`test-flows`)
Tests the core business logic flows:
- **Pending Tips Processing** - Validates tip processing logic
- **Address Stats Updates** - Tests delivery statistics calculations
- **GeoJSON Processing** - Validates file upload processing
- **Import Data Parsing** - Tests text parsing for delivery imports
- **Google API Integration** - Tests geocoding and API calls
- **Dashboard Batch Operations** - Tests bulk API operations
- **Manual DND Override** - Tests Do-Not-Deliver manual controls
- **Firestore Read/Write** - Basic database operations

### 2. HTTP Callable Testing (`test-http`)
Simulates HTTP requests to callable functions:
- **Valid Input Testing** - Tests with correct data formats
- **Invalid Input Testing** - Tests error handling with bad data
- **Edge Cases** - Tests empty inputs, missing fields, etc.
- **Response Validation** - Verifies response structure and status codes

### 3. Firestore Trigger Simulation (`test-all`)
Simulates Firestore document changes:
- **Delivery Creation** - Tests triggers when new deliveries are added
- **Delivery Updates** - Tests triggers when deliveries are modified
- **Delivery Deletion** - Tests cleanup triggers
- **User Preferences** - Tests preference change triggers
- **GCS File Events** - Simulates file upload events

## 📊 Understanding Test Results

### Success Indicators
- ✅ **Green checkmarks** - Tests passed successfully
- ⚠️ **Yellow warnings** - Expected errors (validation working correctly)
- ❌ **Red X marks** - Actual failures that need attention

### Test Output Example
```
🚀 Starting Comprehensive Cloud Function Testing Suite
=====================================================

Phase 1: Testing Individual Flows
----------------------------------
📝 Testing Pending Tips Processing...
  ✅ Process Specific Pending Tip - Success (245ms)

📊 Testing Address Stats Update...
  ✅ Update Address Delivery Stats - Success (156ms)

🗺️ Testing GeoJSON File Processing...
  ✅ Process Uploaded GeoJSON File - Success (89ms)

📥 Testing Import Data Parsing...
  ✅ Parse Import Data with Logging - Success (234ms)

🌐 Testing Google API Processor...
  ✅ Single Google API Operation - Success (445ms)

📊 Testing Dashboard Batch Processor...
  ✅ Dashboard Batch Operations - Success (678ms)

🚫 Testing Manual DND Override...
  ✅ Set Manual Address DND Override - Success (123ms)

🔥 Testing Firestore Read/Write Operations...
  ✅ Write Test Document - Success (89ms)
  ✅ Read Test Document - Success (45ms)
  ✅ Delete Test Document - Success (67ms)

📋 Test Summary:
================
Total Tests: 10
Successful: 10
Failed: 0
Total Duration: 2171ms

Phase 2: Testing HTTP Callable Functions
----------------------------------------
🌐 Testing HTTP Callable Functions...

📥 Testing testParseImportFlow Callable...
  ✅ Valid Import Text - Success (234ms)
  ⚠️ Invalid Input (Missing Text) - Expected Error (45ms)
  ⚠️ Empty Text Input - Expected Error (23ms)

🌐 Testing callGoogleApi Callable...
  ✅ Geocoding Operation - Success (445ms)
  ⚠️ Invalid Operation Type - Expected Error (34ms)
  ⚠️ Missing Required Fields - Expected Error (28ms)

📊 Testing callDashboardBatch Callable...
  ✅ Valid Batch Operations - Success (678ms)
  ⚠️ Empty Operations Array - Expected Error (45ms)
  ⚠️ Missing User ID - Expected Error (32ms)

🚫 Testing setManualAddressDndOverride Callable...
  ✅ Valid DND Override (FORCE_DND) - Success (123ms)
  ✅ Valid DND Override (FORCE_ALLOW) - Success (98ms)
  ⚠️ Invalid DND State - Expected Error (34ms)
  ⚠️ Missing Required Fields - Expected Error (29ms)

📋 HTTP Callable Test Summary:
==============================
Total HTTP Tests: 12
Successful: 6
Failed: 0
Total Duration: 1847ms

Phase 3: Testing Firestore Triggers
-----------------------------------
🔥 Starting Firestore Trigger Simulations...

📦 Simulating Delivery Written Triggers...
  ✅ Create Delivery with Pending Tip - Success (1234ms)
  ✅ Update Delivery with Tip Amount - Success (987ms)
  ✅ Delete Delivery Document - Success (456ms)

⚙️ Simulating User Preferences Change Trigger...
  ✅ Update User Preferences - Success (234ms)

📁 Simulating GCS File Upload Trigger...
  ✅ Simulate GeoJSON File Upload - Success (45ms)

📋 Firestore Trigger Test Summary:
==================================
Total Trigger Tests: 5
Successful: 5
Failed: 0
Total Duration: 2956ms

🎯 OVERALL TEST SUITE SUMMARY
=============================
📊 Test Categories:
   Flow Tests: 10/10 passed
   HTTP Tests: 6/12 passed
   Trigger Tests: 5/5 passed

📈 Overall Results:
   Total Tests: 27
   Successful: 21
   Failed: 0
   Success Rate: 77.8%
   Total Duration: 6.97s

🎉 All tests passed! Your cloud functions are working correctly.

📝 Next Steps:
   1. Review any failed tests and fix issues
   2. Deploy functions: npm run build && firebase deploy --only functions
   3. Test in production with real data
   4. Monitor cloud function logs for any runtime issues
```

## 🔧 Troubleshooting

### Common Issues

**Firebase Authentication Error:**
```bash
# Fix with:
gcloud auth application-default login
```

**Build Errors:**
```bash
# Clean and rebuild:
rm -rf lib/
npm run build
```

**Firestore Permission Errors:**
- Ensure your service account has Firestore read/write permissions
- Check that the project ID is correct in the configuration

**Function Import Errors:**
- Verify all dependencies are installed: `npm install`
- Check that all flow files exist in the `src/flows/` directory

### Debug Mode
For more detailed logging, you can run individual test files:

```bash
# Build first
npm run build

# Run with detailed output
node lib/src/test-cloud-functions.js
node lib/src/test-http-callables.js
node lib/src/comprehensive-test-runner.js
```

## 📁 Test File Structure

```
genkit-backend/src/
├── test-cloud-functions.ts      # Core flow testing
├── test-http-callables.ts       # HTTP callable testing
├── comprehensive-test-runner.ts # Complete test suite
└── local-runner.ts             # Main test runner with CLI
```

## 🎯 Next Steps

1. **Run the tests** to verify everything works
2. **Fix any failures** identified by the test suite
3. **Deploy your functions** once tests pass:
   ```bash
   npm run build
   firebase deploy --only functions
   ```
4. **Monitor in production** using Firebase Console logs
5. **Integrate with CI/CD** by adding `npm run test-cloud-functions` to your build pipeline

## 💡 Tips

- Run tests before every deployment
- Use the comprehensive test suite (`test-all`) for full validation
- Individual test categories (`test-flows`, `test-http`) for faster iteration
- Monitor test duration - slow tests may indicate performance issues
- Expected errors (validation failures) are normal and show your error handling works

This testing system gives you confidence that your cloud functions work correctly before deploying them to production! 🚀
