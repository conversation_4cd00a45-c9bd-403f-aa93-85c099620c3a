package com.autogratuity.data.model.state

/**
 * Represents the display state for Do Not Deliver (DND) functionality,
 * interpreted for client-side use from address flags.
 */
enum class DndDisplayState {
    /** No address data available to determine DND status. */
    NO_DATA,
    /** User has explicitly set the address to 'Do Not Deliver'. */
    MANUAL_DND,
    /** User has explicitly allowed deliveries, overriding any auto DND. */
    MANUAL_ALLOW,
    /** Address is automatically marked 'Do Not Deliver' by system rules (e.g., doNotDeliver flag is true and no manual override). */
    AUTO_DND,
    /** Deliveries are allowed (no DND flags active or manual override allows). */
    ALLOWED,
    /** DND status is unknown or could not be determined from the available data. */
    UNKNOWN
}
