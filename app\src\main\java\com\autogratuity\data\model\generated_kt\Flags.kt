/*
 * Flags.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

/**
 * Represents boolean flags, typically associated with an Address.
 */
data class Flags(
    val isFavorite: Boolean? = null,
    val isVerified: Boolean? = null,
    /** Flag indicating delivery should not be attempted */
    val doNotDeliver: Boolean? = null,
    val isApartment: Boolean? = null,
    /** Flag indicating if the address is archived */
    val isArchived: Boolean? = null,
    /** Flag indicating known access problems (gate code, etc.) */
    val hasAccessIssues: Boolean? = null,
    /** User's manual DND override for the address. null means automatic (rule-based), 'FORCE_DND' means user set DND, 'FORCE_ALLOW' means user allowed deliveries. */
    val manualDndState: String? = null,
    /** The primary source or reason the address DND flag is in its current state. */
    val dndSource: String? = null
)
