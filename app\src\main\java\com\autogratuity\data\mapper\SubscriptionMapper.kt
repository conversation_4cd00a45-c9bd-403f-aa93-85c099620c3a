package com.autogratuity.data.mapper

import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.generated_kt.User_profile as UserProfileDto
import com.autogratuity.domain.model.UserSubscription
import com.autogratuity.domain.model.VerificationDetails
import com.autogratuity.debug.ClarityArchitectureMonitor

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.withContext
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.Date
import javax.inject.Inject
import kotlin.time.TimeSource

/**
 * Mapper for converting between Subscription DTOs and SSoT domain models.
 * Handles DTO↔SSoT conversion + encryption + business logic.
 *
 * Follows the delivery domain pattern for mapper implementations.
 */
@ExperimentalCoroutinesApi
class SubscriptionMapper @Inject constructor(
    private val encryptionUtils: com.autogratuity.data.security.EncryptionUtils?,
    private val ioDispatcher: CoroutineDispatcher
) {

    companion object {
        private const val TAG = "SubscriptionMapper"
    }

    // ===== DTO TO SSOT CONVERSION =====

    suspend fun dtoToSsot(dto: UserProfileDto.UserSubscription?): Result<UserSubscription?> = withContext(ioDispatcher) {
        val mappingStartTime = TimeSource.Monotonic.markNow()
        try {
            if (dto == null) {
                // Monitor null DTO mapping
                ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                    mapperClass = "SubscriptionMapper",
                    entityType = "UserSubscription",
                    duration = mappingStartTime.elapsedNow(),
                    success = true,
                    fieldsTransformed = 0,
                    businessLogicApplied = listOf("null_handling"),
                    cacheUpdated = false,
                    entityId = "null",
                    dataSize = 0
                )
                return@withContext Result.Success(null)
            }

            Log.d(TAG, "dtoToSsot: Converting DTO to SSoT domain model")

            val domainSubscription = UserSubscription(
                status = dto.status,
                level = dto.level,
                isActive = dto.isActive,
                startDate = dto.startDate?.let { convertGeneratedDateTimeToOffsetDateTime(it) },
                expiryDate = dto.expiryDate?.let { convertGeneratedDateTimeToOffsetDateTime(it) },
                isLifetime = dto.isLifetime,
                provider = dto.provider,
                orderId = dto.orderId,
                verification = dto.verification?.let { verification ->
                    VerificationDetails(
                        lastVerified = verification.lastVerified?.let { convertGeneratedDateTimeToOffsetDateTime(it) },
                        status = verification.status,
                        error = verification.error
                    )
                }
            )

            Log.d(TAG, "dtoToSsot: Successfully converted DTO to SSoT")
            
            // 📊 COMPREHENSIVE BEFORE/AFTER STATE LOGGING
            val inputState = mapOf(
                "dto_status" to dto.status,
                "dto_level" to dto.level,
                "dto_isActive" to dto.isActive,
                "dto_startDate" to dto.startDate?.toString(),
                "dto_expiryDate" to dto.expiryDate?.toString(),
                "dto_isLifetime" to dto.isLifetime,
                "dto_provider" to dto.provider,
                "dto_orderId" to dto.orderId
            )
            
            val outputState = mapOf(
                "domain_status" to domainSubscription.status,
                "domain_level" to domainSubscription.level,
                "domain_isActive" to domainSubscription.isActive,
                "domain_startDate" to domainSubscription.startDate?.toString(),
                "domain_expiryDate" to domainSubscription.expiryDate?.toString(),
                "domain_isLifetime" to domainSubscription.isLifetime,
                "domain_provider" to domainSubscription.provider,
                "domain_orderId" to domainSubscription.orderId
            )
            
            val fieldTransformations = listOf(
                "startDate: DATE_CONVERSION",
                "expiryDate: DATE_CONVERSION",
                "verification: NESTED_MAPPING",
                "status: DIRECT_MAPPING",
                "level: DIRECT_MAPPING"
            )
            
            val businessLogicApplied = listOf(
                "date_conversion",
                "verification_mapping",
                "subscription_validation"
            )

            Log.d(TAG, "=== SUBSCRIPTION MAPPING TRANSFORMATION ===")
            Log.d(TAG, "Input DTO State: $inputState")
            Log.d(TAG, "Output Domain State: $outputState")
            Log.d(TAG, "Field Transformations: $fieldTransformations")
            Log.d(TAG, "Business Logic Applied: $businessLogicApplied")
            Log.d(TAG, "Mapping Duration: ${mappingStartTime.elapsedNow().inWholeMilliseconds}ms")
            
            // 🔍 SESSION CORRELATION: Add mapping success to session
            ClarityArchitectureMonitor.addSessionEvent("subscription_mapping_success:${dto.orderId ?: "unknown"}")

            // Monitor successful mapping
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "SubscriptionMapper",
                entityType = "UserSubscription",
                duration = mappingStartTime.elapsedNow(),
                success = true,
                fieldsTransformed = fieldTransformations.size,
                businessLogicApplied = businessLogicApplied,
                cacheUpdated = false,
                entityId = dto.orderId ?: "unknown",
                dataSize = dto.toString().length,
                userId = "subscription_mapping"
            )
            
            Result.Success(domainSubscription)
        } catch (e: Exception) {
            Log.e(TAG, "dtoToSsot: Error converting DTO to SSoT", e)
            
            // 🔍 SESSION CORRELATION: Add mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("subscription_mapping_failure:${dto?.orderId ?: "unknown"}:exception")
            
            // Monitor mapping error
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "SubscriptionMapper",
                entityType = "UserSubscription",
                duration = mappingStartTime.elapsedNow(),
                success = false,
                error = e,
                fieldsTransformed = 0,
                businessLogicApplied = listOf("mapping_failed"),
                cacheUpdated = false,
                entityId = "error",
                dataSize = dto?.toString()?.length ?: 0
            )
            
            Result.Error(e)
        }
    }

    // ===== SSOT TO DTO CONVERSION =====

    suspend fun ssotToDto(ssot: UserSubscription?): Result<UserProfileDto.UserSubscription?> = withContext(ioDispatcher) {
        try {
            if (ssot == null) {
                return@withContext Result.Success(null)
            }

            Log.d(TAG, "ssotToDto: Converting SSoT to DTO")

            val dto = UserProfileDto.UserSubscription(
                status = ssot.status,
                level = ssot.level,
                isActive = ssot.isActive,
                startDate = ssot.startDate?.let { convertOffsetDateTimeToGeneratedDateTime(it) },
                expiryDate = ssot.expiryDate?.let { convertOffsetDateTimeToGeneratedDateTime(it) },
                isLifetime = ssot.isLifetime,
                provider = ssot.provider,
                orderId = ssot.orderId,
                verification = ssot.verification?.let { verification ->
                    UserProfileDto.Verification(
                        lastVerified = verification.lastVerified?.let { convertOffsetDateTimeToGeneratedDateTime(it) },
                        status = verification.status,
                        error = verification.error
                    )
                }
            )

            Log.d(TAG, "ssotToDto: Successfully converted SSoT to DTO")
            Result.Success(dto)
        } catch (e: Exception) {
            Log.e(TAG, "ssotToDto: Error converting SSoT to DTO", e)
            Result.Error(e)
        }
    }

    // ===== BUSINESS LOGIC METHODS =====

    suspend fun calculateDefaultSubscription(): Result<UserSubscription> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "calculateDefaultSubscription: Creating default subscription")

            val defaultSubscription = UserSubscription(
                status = "active",
                level = "free",
                isActive = false,
                startDate = null,
                expiryDate = null,
                isLifetime = false,
                provider = null,
                orderId = null,
                verification = VerificationDetails(
                    lastVerified = OffsetDateTime.now(),
                    status = "verified",
                    error = null
                )
            )

            Log.d(TAG, "calculateDefaultSubscription: Successfully created default subscription")
            Result.Success(defaultSubscription)
        } catch (e: Exception) {
            Log.e(TAG, "calculateDefaultSubscription: Error creating default subscription", e)
            Result.Error(e)
        }
    }

    suspend fun isProUser(subscription: UserSubscription?): Result<Boolean> = withContext(ioDispatcher) {
        try {
            if (subscription == null) {
                return@withContext Result.Success(false)
            }

            val isProUser = (subscription.level == "pro" || subscription.level == "premium") && 
                           subscription.status == "active" &&
                           subscription.isActive == true &&
                           !isSubscriptionExpired(subscription)

            Log.d(TAG, "isProUser: User pro status = $isProUser")
            Result.Success(isProUser)
        } catch (e: Exception) {
            Log.e(TAG, "isProUser: Error checking pro user status", e)
            Result.Error(e)
        }
    }

    fun isSubscriptionExpired(subscription: UserSubscription?): Boolean {
        if (subscription == null) return false
        if (subscription.level == "free") return false
        if (subscription.isLifetime == true) return false
        
        val expirationDate = subscription.expiryDate ?: return false
        return OffsetDateTime.now().isAfter(expirationDate)
    }

    suspend fun isTrialAvailable(subscription: UserSubscription?): Result<Boolean> = withContext(ioDispatcher) {
        try {
            if (subscription == null) {
                return@withContext Result.Success(true) // Trial available for new users
            }

            // Trial is available if user is currently free
            val trialAvailable = subscription.level == "free"

            Log.d(TAG, "isTrialAvailable: Trial available = $trialAvailable")
            Result.Success(trialAvailable)
        } catch (e: Exception) {
            Log.e(TAG, "isTrialAvailable: Error checking trial availability", e)
            Result.Error(e)
        }
    }

    suspend fun validateUserSubscription(subscription: UserSubscription): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "validateUserSubscription: Validating subscription")

            // Basic validation
            require(!subscription.level.isNullOrBlank()) { "Subscription level cannot be blank" }
            require(!subscription.status.isNullOrBlank()) { "Status cannot be blank" }
            
            // Validate dates
            if (subscription.startDate != null && subscription.expiryDate != null) {
                require(subscription.startDate.isBefore(subscription.expiryDate)) {
                    "Start date must be before expiry date" 
                }
            }

            // Validate pro user logic
            if (subscription.isActive == true) {
                require(subscription.level != "free") { 
                    "Active users cannot have free subscription level" 
                }
            }

            Log.d(TAG, "validateUserSubscription: Subscription validation passed")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "validateUserSubscription: Subscription validation failed", e)
            Result.Error(e)
        }
    }

    suspend fun processSubscriptionUpgrade(
        currentSubscription: UserSubscription?,
        durationMonths: Int,
        paymentDetails: Map<String, Any>
    ): Result<UserSubscription> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "processSubscriptionUpgrade: Processing upgrade for $durationMonths months")

            val now = OffsetDateTime.now()
            val subscriptionLevel = if (durationMonths <= 0) "lifetime" else "pro"
            val expirationDate = if (durationMonths <= 0) null else now.plusMonths(durationMonths.toLong())

            val upgradedSubscription = UserSubscription(
                status = "active",
                level = subscriptionLevel,
                isActive = true,
                startDate = now,
                expiryDate = expirationDate,
                isLifetime = durationMonths <= 0,
                provider = paymentDetails["provider"]?.toString(),
                orderId = paymentDetails["orderId"]?.toString(),
                verification = VerificationDetails(
                    lastVerified = now,
                    status = "verified",
                    error = null
                )
            )

            Log.d(TAG, "processSubscriptionUpgrade: Successfully processed upgrade")
            Result.Success(upgradedSubscription)
        } catch (e: Exception) {
            Log.e(TAG, "processSubscriptionUpgrade: Error processing upgrade", e)
            Result.Error(e)
        }
    }

    suspend fun processSubscriptionCancellation(
        currentSubscription: UserSubscription?,
        immediate: Boolean
    ): Result<UserSubscription> = withContext(ioDispatcher) {
        try {
            if (currentSubscription == null) {
                return@withContext Result.Error(IllegalStateException("No subscription to cancel"))
            }

            Log.d(TAG, "processSubscriptionCancellation: Processing cancellation (immediate=$immediate)")

            val cancelledSubscription = if (immediate) {
                // Immediate cancellation - revert to free
                currentSubscription.copy(
                    level = "free",
                    isActive = false,
                    status = "cancelled",
                    verification = currentSubscription.verification?.copy(
                        lastVerified = OffsetDateTime.now(),
                        status = "cancelled"
                    )
                )
            } else {
                // Cancel at end of billing period
                currentSubscription.copy(
                    status = "cancelled_at_period_end",
                    verification = currentSubscription.verification?.copy(
                        lastVerified = OffsetDateTime.now(),
                        status = "cancelled_at_period_end"
                    )
                )
            }

            Log.d(TAG, "processSubscriptionCancellation: Successfully processed cancellation")
            Result.Success(cancelledSubscription)
        } catch (e: Exception) {
            Log.e(TAG, "processSubscriptionCancellation: Error processing cancellation", e)
            Result.Error(e)
        }
    }

    // ===== UTILITY METHODS =====

    private fun convertGeneratedDateTimeToOffsetDateTime(generatedDateTime: Any): OffsetDateTime? {
        // Handle conversion from generated OffsetDateTime to java.time.OffsetDateTime
        return when (generatedDateTime) {
            is OffsetDateTime -> {
                generatedDateTime
            }
            is Date -> {
                OffsetDateTime.ofInstant(generatedDateTime.toInstant(), ZoneOffset.UTC)
            }
            is Long -> {
                OffsetDateTime.ofInstant(java.time.Instant.ofEpochMilli(generatedDateTime), ZoneOffset.UTC)
            }
            else -> {
                Log.w(TAG, "Unknown date type: ${generatedDateTime::class.java}")
                OffsetDateTime.now() // ✅ CRITICAL FIX: Always provide timestamp - null breaks Firestore queries
            }
        }
    }

    private fun convertOffsetDateTimeToGeneratedDateTime(offsetDateTime: OffsetDateTime): OffsetDateTime {
        return offsetDateTime
    }
}