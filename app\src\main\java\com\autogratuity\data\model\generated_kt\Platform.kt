/*
 * Platform.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

/**
 * Represents information about the delivery platform.
 */
data class Platform(
    /** Name of the platform (e.g., Shipt, Instacart). */
    val name: String?,
    /** Type of platform (e.g., Grocery, Retail). */
    val type: String? = null,
    /** Version information related to the platform or app. */
    val version: String? = null,
    /** Source identifier related to the platform. */
    val source: String? = null,
    /** Display name for the platform (e.g., for UI) */
    val displayName: String? = null,
    /** URL for the platform's icon */
    val iconUrl: String? = null
)
