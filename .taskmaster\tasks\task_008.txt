# Task ID: 8
# Title: Modernize Repository Constructors
# Status: pending
# Dependencies: 7
# Priority: high
# Description: Update repository constructors to follow the SubscriptionRepositoryImpl pattern with all required infrastructure components.
# Details:
Update repository implementations (DeliveryRepositoryImpl, UserProfileRepositoryImpl, AddressRepositoryImpl, ConfigRepositoryImpl, PreferenceRepositoryImpl) to include all 6 infrastructure components: RequestDeduplicationManager, PriorityTaskScheduler, SessionManager, CacheWarmingManager, AuthenticationStateCoordinator, and RepositoryErrorHandler. Maintain critical method signatures (getEntity, saveEntity, observeEntity) while adding required integrations.

# Test Strategy:
1. Verify constructor signatures match exact pattern
2. Test repository methods with mock infrastructure components
3. Verify backward compatibility of existing methods
4. Validate integration points (deduplication, error handling, auth coordination)
5. Run verification commands to check implementation completeness

# Subtasks:
## 8.1. Update DeliveryRepositoryImpl constructor [pending]
### Dependencies: None
### Description: Add missing infrastructure components to DeliveryRepositoryImpl
### Details:
Add requestDeduplicationManager, priorityTaskScheduler, sessionManager, cacheWarmingManager, authStateCoordinator while preserving existing parameters

## 8.2. Update UserProfileRepositoryImpl constructor [pending]
### Dependencies: None
### Description: Add missing infrastructure components to UserProfileRepositoryImpl
### Details:
Add requestDeduplicationManager, priorityTaskScheduler, sessionManager, cacheWarmingManager, authStateCoordinator while preserving existing parameters

## 8.3. Update AddressRepositoryImpl constructor [pending]
### Dependencies: None
### Description: Add all 6 infrastructure components to AddressRepositoryImpl
### Details:
Add all required components while maintaining complex existing constructor parameters

## 8.4. Update ConfigRepositoryImpl constructor [pending]
### Dependencies: None
### Description: Add all 6 infrastructure components to ConfigRepositoryImpl
### Details:
Implement full constructor pattern for ConfigRepositoryImpl

## 8.5. Update PreferenceRepositoryImpl constructor [pending]
### Dependencies: None
### Description: Add all 6 infrastructure components to PreferenceRepositoryImpl
### Details:
Implement full constructor pattern for PreferenceRepositoryImpl

## 8.6. Implement infrastructure integrations [pending]
### Dependencies: None
### Description: Add required infrastructure integrations to all repositories
### Details:
1. Use requestDeduplicationManager for Firestore operations
2. Apply repositoryErrorHandler for error handling
3. Use authStateCoordinator before user operations
4. Integrate cacheWarmingManager for performance

## 8.7. Verify implementation completeness [pending]
### Dependencies: None
### Description: Run verification commands to ensure all repositories are updated
### Details:
Execute verification commands:
1. rg "class.*RepositoryImpl.*(" --type kt -A 15
2. rg "requestDeduplicationManager" --type kt
3. rg "suspend fun.*: Result<" --type kt

