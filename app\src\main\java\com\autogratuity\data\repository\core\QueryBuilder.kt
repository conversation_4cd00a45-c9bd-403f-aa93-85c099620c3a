package com.autogratuity.data.repository.core

import android.util.Log
import com.google.firebase.Timestamp
import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.FieldPath
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import java.util.Date
import java.util.Locale

//-----------------------------------------------------------------------------------
// Result extension functions for modern Kotlin patterns (2025 standards)
//-----------------------------------------------------------------------------------

/**
 * Maps the success value of a Result to another Result, allowing for chaining of operations that can fail.
 * This is the monadic flatMap operation for Result types.
 * 
 * @param transform Function that transforms the success value to another Result
 * @return Result containing the transformed value or the original failure
 */
inline fun <T, R> Result<T>.flatMap(transform: (value: T) -> Result<R>): Result<R> {
    return when {
        isSuccess -> transform(getOrThrow())
        else -> Result.failure(exceptionOrNull()!!)
    }
}

/**
 * Builder class for Firestore queries with modern Kotlin patterns.
 * Provides a fluent API for constructing complex queries with Result-based error handling.
 * 
 * Example usage:
 * ```
 * val queryResult = QueryBuilder.collection(db, "user_deliveries")
 *     .whereEqualTo("status", "pending")
 *     .orderBy("timestamp", Query.Direction.DESCENDING)
 *     .limit(10)
 *     .build()
 *     
 * queryResult.fold(
 *     onSuccess = { query -> /* use query */ },
 *     onFailure = { error -> /* handle error */ }
 * )
 * ```
 */
class QueryBuilder private constructor(
    db: FirebaseFirestore,
    collectionPath: String
) {
    // Base collection reference
    private var collectionRef: CollectionReference = db.collection(collectionPath)

    // Query components using proper mutable collections
    private val whereClauses = mutableListOf<WhereClause>()
    private val orderByClauses = mutableListOf<OrderByClause>()
    private var limitValue: Int? = null
    private var startAfterDocument: DocumentReference? = null
    private var endBeforeDocument: DocumentReference? = null
    private var startAtValues: Array<out Any>? = null
    private var startAfterValues: Array<out Any>? = null
    private var endAtValues: Array<out Any>? = null
    private var endBeforeValues: Array<out Any>? = null

    // Tracking for clauses added for validation using modern Kotlin collections
    private val orderByFieldTracker = mutableMapOf<String, Boolean>()

    /**
     * Class representing a where clause in the query
     */
    private data class WhereClause(
        val field: String,
        val operator: String,
        val value: Any?,
        val isFieldPath: Boolean = false
    ) {
        constructor(fieldPath: FieldPath, operator: String, value: Any?) : this(
            fieldPath.toString(),
            operator,
            value,
            isFieldPath = true
        )
    }

    /**
     * Class representing an order by clause in the query
     */
    private data class OrderByClause(
        val field: String,
        val direction: Query.Direction,
        val isFieldPath: Boolean = false
    ) {
        constructor(fieldPath: FieldPath, direction: Query.Direction) : this(
            fieldPath.toString(),
            direction,
            isFieldPath = true
        )
    }

    //-----------------------------------------------------------------------------------
    // Where clause methods - add filtering conditions to the query
    //-----------------------------------------------------------------------------------

    /**
     * Add a whereEqualTo clause to the query
     */
    fun whereEqualTo(field: String, value: Any?): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { field.validateValueForField(value) }
            .map { 
                whereClauses.add(WhereClause(field, "==", value))
                this
            }
    }

    /**
     * Add a whereEqualTo clause with FieldPath to the query
     */
    fun whereEqualTo(fieldPath: FieldPath, value: Any?): Result<QueryBuilder> {
        return fieldPath.validateFieldPath()
            .flatMap { fieldPath.validateValueForFieldPath(value) }
            .map {
                whereClauses.add(WhereClause(fieldPath, "==", value))
                this
            }
    }

    /**
     * Add a whereNotEqualTo clause to the query
     */
    fun whereNotEqualTo(field: String, value: Any?): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { field.validateValueForField(value) }
            .map {
                whereClauses.add(WhereClause(field, "!=", value))
                this
            }
    }

    /**
     * Add a whereNotEqualTo clause with FieldPath to the query
     */
    fun whereNotEqualTo(fieldPath: FieldPath, value: Any?): Result<QueryBuilder> {
        return fieldPath.validateFieldPath()
            .flatMap { fieldPath.validateValueForFieldPath(value) }
            .map {
                whereClauses.add(WhereClause(fieldPath, "!=", value))
                this
            }
    }

    /**
     * Add a whereGreaterThan clause to the query
     */
    fun whereGreaterThan(field: String, value: Any?): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { field.validateValueForField(value) }
            .map {
                whereClauses.add(WhereClause(field, ">", value))
                this
            }
    }

    /**
     * Add a whereGreaterThanOrEqualTo clause to the query
     */
    fun whereGreaterThanOrEqualTo(field: String, value: Any?): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { field.validateValueForField(value) }
            .map {
                whereClauses.add(WhereClause(field, ">=", value))
                this
            }
    }

    /**
     * Add a whereLessThan clause to the query
     */
    fun whereLessThan(field: String, value: Any?): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { field.validateValueForField(value) }
            .map {
                whereClauses.add(WhereClause(field, "<", value))
                this
            }
    }

    /**
     * Add a whereLessThanOrEqualTo clause to the query
     */
    fun whereLessThanOrEqualTo(field: String, value: Any?): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { field.validateValueForField(value) }
            .map {
                whereClauses.add(WhereClause(field, "<=", value))
                this
            }
    }

    /**
     * Add a whereArrayContains clause to the query
     */
    fun whereArrayContains(field: String, value: Any?): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { field.validateValueForField(value) }
            .map {
                whereClauses.add(WhereClause(field, "array-contains", value))
                this
            }
    }

    /**
     * Add a whereArrayContainsAny clause to the query
     */
    fun whereArrayContainsAny(field: String, values: List<*>): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { values.validateArray("whereArrayContainsAny") }
            .map {
                whereClauses.add(WhereClause(field, "array-contains-any", values))
                this
            }
    }

    /**
     * Add a whereIn clause to the query
     */
    fun whereIn(field: String, values: List<*>): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { values.validateArray("whereIn") }
            .map {
                whereClauses.add(WhereClause(field, "in", values))
                this
            }
    }

    /**
     * Add a whereNotIn clause to the query
     */
    fun whereNotIn(field: String, values: List<*>): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { values.validateArray("whereNotIn") }
            .map {
                whereClauses.add(WhereClause(field, "not-in", values))
                this
            }
    }

    /**
     * Add a whereIsNull clause to the query
     */
    fun whereIsNull(field: String): Result<QueryBuilder> {
        return field.validateField()
            .map {
                whereClauses.add(WhereClause(field, "==", null))
                this
            }
    }

    /**
     * Add a whereIsNotNull clause to the query
     */
    fun whereIsNotNull(field: String): Result<QueryBuilder> {
        return field.validateField()
            .map {
                whereClauses.add(WhereClause(field, "!=", null))
                this
            }
    }

    //-----------------------------------------------------------------------------------
    // Order by methods - add sorting to the query
    //-----------------------------------------------------------------------------------

    /**
     * Add an orderBy clause to the query with ascending direction (default)
     */
    fun orderBy(field: String): Result<QueryBuilder> = orderBy(field, Query.Direction.ASCENDING)

    /**
     * Add an orderBy clause to the query with specified direction
     */
    fun orderBy(field: String, direction: Query.Direction): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { validateOrderByField(field) }
            .map {
                orderByClauses.add(OrderByClause(field, direction))
                orderByFieldTracker[field] = true
                this
            }
    }

    /**
     * Add an orderBy clause with FieldPath to the query with ascending direction (default)
     */
    fun orderBy(fieldPath: FieldPath): Result<QueryBuilder> = orderBy(fieldPath, Query.Direction.ASCENDING)

    /**
     * Add an orderBy clause with FieldPath to the query with specified direction
     */
    fun orderBy(fieldPath: FieldPath, direction: Query.Direction): Result<QueryBuilder> {
        return fieldPath.validateFieldPath()
            .flatMap { validateOrderByField(fieldPath.toString()) }
            .map {
                orderByClauses.add(OrderByClause(fieldPath, direction))
                orderByFieldTracker[fieldPath.toString()] = true
                this
            }
    }

    //-----------------------------------------------------------------------------------
    // Limit methods - add result limitations to the query
    //-----------------------------------------------------------------------------------

    /**
     * Add a limit clause to the query
     */
    fun limit(limit: Int): Result<QueryBuilder> {
        return limit.validateLimit()
            .map {
                limitValue = limit
                this
            }
    }

    //-----------------------------------------------------------------------------------
    // Pagination methods - add pagination to the query
    //-----------------------------------------------------------------------------------

    /**
     * Add a startAt clause to the query using field values
     */
    fun startAt(vararg values: Any): Result<QueryBuilder> {
        return values.validateCursorValues("startAt")
            .map {
                this.startAtValues = values
                this.startAfterValues = null // Clear conflicting cursor
                this.startAfterDocument = null // Clear conflicting cursor
                this
            }
    }

    /**
     * Add a startAfter clause to the query using field values
     */
    fun startAfter(vararg values: Any): Result<QueryBuilder> {
        return values.validateCursorValues("startAfter")
            .map {
                this.startAfterValues = values
                this.startAtValues = null // Clear conflicting cursor
                this.startAfterDocument = null // Clear conflicting cursor
                this
            }
    }

    /**
     * Add a startAfter clause to the query using a document snapshot
     */
    fun startAfter(snapshot: DocumentReference): Result<QueryBuilder> {
        return Result.success(this).map {
            this.startAfterDocument = snapshot
            this.startAtValues = null // Clear conflicting cursor
            this.startAfterValues = null // Clear conflicting cursor
            this
        }
    }

    /**
     * Add an endAt clause to the query using field values
     */
    fun endAt(vararg values: Any): Result<QueryBuilder> {
        return values.validateCursorValues("endAt")
            .map {
                this.endAtValues = values
                this.endBeforeValues = null // Clear conflicting cursor
                this.endBeforeDocument = null // Clear conflicting cursor
                this
            }
    }

    /**
     * Add an endBefore clause to the query using field values
     */
    fun endBefore(vararg values: Any): Result<QueryBuilder> {
        return values.validateCursorValues("endBefore")
            .map {
                this.endBeforeValues = values
                this.endAtValues = null // Clear conflicting cursor
                this.endBeforeDocument = null // Clear conflicting cursor
                this
            }
    }

    /**
     * Add an endBefore clause to the query using a document snapshot
     */
    fun endBefore(snapshot: DocumentReference): Result<QueryBuilder> {
        return Result.success(this).map {
            this.endBeforeDocument = snapshot
            this.endAtValues = null // Clear conflicting cursor
            this.endBeforeValues = null // Clear conflicting cursor
            this
        }
    }

    //-----------------------------------------------------------------------------------
    // Convenience methods for common query patterns
    //-----------------------------------------------------------------------------------

    /**
     * Add a date range filter to the query
     */
    fun whereDateBetween(field: String, startDate: Date, endDate: Date): Result<QueryBuilder> {
        return startDate.validateDateRange(endDate)
            .flatMap { field.validateField() }
            .flatMap { whereGreaterThanOrEqualTo(field, startDate) }
            .flatMap { whereLessThanOrEqualTo(field, endDate) }
    }

    /**
     * Add a number range filter to the query
     */
    fun whereNumberBetween(field: String, min: Number, max: Number): Result<QueryBuilder> {
        return min.validateNumberRange(max)
            .flatMap { field.validateField() }
            .flatMap { whereGreaterThanOrEqualTo(field, min) }
            .flatMap { whereLessThanOrEqualTo(field, max) }
    }

    /**
     * Add a text search filter (case-insensitive prefix matching)
     */
    fun whereTextPrefixSearch(field: String, prefix: String): Result<QueryBuilder> {
        return field.validateField()
            .flatMap { prefix.validateString("prefix") }
            .flatMap { 
                val lowerPrefix = prefix.lowercase(Locale.getDefault())
                val upperBound = lowerPrefix.substring(0, lowerPrefix.length - 1) + 
                    (lowerPrefix.last() + 1).toString()
                
                // Apply both constraints in sequence
                whereGreaterThanOrEqualTo("${field}_lower", lowerPrefix)
                    .flatMap { whereLessThan("${field}_lower", upperBound) }
            }
    }

    //-----------------------------------------------------------------------------------
    // Build method - construct the final Query
    //-----------------------------------------------------------------------------------

    /**
     * Build the final Firestore Query
     */
    fun build(): Result<Query> {
        return validateQuery()
            .map { buildQuery() }
    }

    /**
     * Build the actual Firestore Query after validation
     */
    private fun buildQuery(): Query {
        var query: Query = collectionRef

        // Apply where clauses
        whereClauses.forEach { whereClause ->
            query = when {
                whereClause.isFieldPath -> {
                    val fieldPath = FieldPath.of(*whereClause.field.split(".").toTypedArray())
                    when (whereClause.operator) {
                        "==" -> query.whereEqualTo(fieldPath, whereClause.value)
                        "!=" -> query.whereNotEqualTo(fieldPath, whereClause.value)
                        ">" -> whereClause.value?.let { query.whereGreaterThan(fieldPath, it) } ?: query
                        ">=" -> whereClause.value?.let { query.whereGreaterThanOrEqualTo(fieldPath, it) } ?: query
                        "<" -> whereClause.value?.let { query.whereLessThan(fieldPath, it) } ?: query
                        "<=" -> whereClause.value?.let { query.whereLessThanOrEqualTo(fieldPath, it) } ?: query
                        "array-contains" -> whereClause.value?.let { query.whereArrayContains(fieldPath, it) } ?: query
                        "array-contains-any" -> (whereClause.value as? List<*>)?.let { query.whereArrayContainsAny(fieldPath, it) } ?: query
                        "in" -> (whereClause.value as? List<*>)?.let { query.whereIn(fieldPath, it) } ?: query
                        "not-in" -> (whereClause.value as? List<*>)?.let { query.whereNotIn(fieldPath, it) } ?: query
                        else -> query // Unknown operator, skip
                    }
                }
                else -> {
                    when (whereClause.operator) {
                        "==" -> query.whereEqualTo(whereClause.field, whereClause.value)
                        "!=" -> query.whereNotEqualTo(whereClause.field, whereClause.value)
                        ">" -> whereClause.value?.let { query.whereGreaterThan(whereClause.field, it) } ?: query
                        ">=" -> whereClause.value?.let { query.whereGreaterThanOrEqualTo(whereClause.field, it) } ?: query
                        "<" -> whereClause.value?.let { query.whereLessThan(whereClause.field, it) } ?: query
                        "<=" -> whereClause.value?.let { query.whereLessThanOrEqualTo(whereClause.field, it) } ?: query
                        "array-contains" -> whereClause.value?.let { query.whereArrayContains(whereClause.field, it) } ?: query
                        "array-contains-any" -> (whereClause.value as? List<*>)?.let { query.whereArrayContainsAny(whereClause.field, it) } ?: query
                        "in" -> (whereClause.value as? List<*>)?.let { query.whereIn(whereClause.field, it) } ?: query
                        "not-in" -> (whereClause.value as? List<*>)?.let { query.whereNotIn(whereClause.field, it) } ?: query
                        else -> query // Unknown operator, skip
                    }
                }
            }
        }

        // Apply order by clauses
        orderByClauses.forEach { orderByClause ->
            query = when {
                orderByClause.isFieldPath -> {
                    val fieldPath = FieldPath.of(*orderByClause.field.split(".").toTypedArray())
                    query.orderBy(fieldPath, orderByClause.direction)
                }
                else -> query.orderBy(orderByClause.field, orderByClause.direction)
            }
        }

        // Apply limit if specified
        limitValue?.let { query = query.limit(it.toLong()) }

        // Apply pagination cursors (document snapshots take precedence)
        startAfterDocument?.let {
            query = query.startAfter(it)
        } ?: startAfterValues?.let { values ->
            if (values.isNotEmpty()) query = query.startAfter(*values)
        } ?: startAtValues?.let { values ->
            if (values.isNotEmpty()) query = query.startAt(*values)
        }

        endBeforeDocument?.let {
            query = query.endBefore(it)
        } ?: endBeforeValues?.let { values ->
            if (values.isNotEmpty()) query = query.endBefore(*values)
        } ?: endAtValues?.let { values ->
            if (values.isNotEmpty()) query = query.endAt(*values)
        }

        return query
    }

    //-----------------------------------------------------------------------------------
    // Validation methods using extension functions and Result pattern
    //-----------------------------------------------------------------------------------

    /**
     * Validate the entire query for consistency and Firestore limitations
     */
    private fun validateQuery(): Result<Unit> {
        return runCatching {
            // Check maximum clause limits
            if (whereClauses.size > MAX_WHERE_CLAUSES) {
                return Result.failure(
                    FirestoreQueryException(
                        FirestoreQueryException.QueryErrorType.TOO_MANY_CLAUSES,
                        "Query exceeds maximum of $MAX_WHERE_CLAUSES where clauses"
                    )
                )
            }

            if (orderByClauses.size > MAX_ORDER_BY_CLAUSES) {
                return Result.failure(
                    FirestoreQueryException(
                        FirestoreQueryException.QueryErrorType.TOO_MANY_CLAUSES,
                        "Query exceeds maximum of $MAX_ORDER_BY_CLAUSES orderBy clauses"
                    )
                )
            }

            // Check for cursor/orderBy consistency
            val cursorValueCount = maxOf(
                startAtValues?.size ?: 0,
                startAfterValues?.size ?: 0,
                endAtValues?.size ?: 0,
                endBeforeValues?.size ?: 0
            )

            if (cursorValueCount > 0 && cursorValueCount > orderByClauses.size) {
                return Result.failure(
                    FirestoreQueryException(
                        FirestoreQueryException.QueryErrorType.VALIDATION_ERROR,
                        "Number of cursor values ($cursorValueCount) exceeds number of orderBy clauses (${orderByClauses.size})"
                    )
                )
            }

            // Check for inequality+orderBy consistency
            val inequalityFields = whereClauses
                .filter { it.operator in listOf(">", ">=", "<", "<=") }
                .associateBy({ it.field }, { it.operator })

            if (inequalityFields.isNotEmpty() && orderByClauses.isNotEmpty()) {
                val firstOrderByField = orderByClauses.first().field
                if (firstOrderByField !in inequalityFields) {
                    val firstInequalityField = inequalityFields.keys.first()
                    val operator = inequalityFields[firstInequalityField]

                    return Result.failure(
                        FirestoreQueryException(
                            FirestoreQueryException.QueryErrorType.VALIDATION_ERROR,
                            "The first orderBy field must match the inequality field '$firstInequalityField' with operator '$operator'",
                            firstInequalityField
                        )
                    )
                }
            }

            Unit
        }
    }

    //-----------------------------------------------------------------------------------
    // Private validation methods that have access to instance state
    //-----------------------------------------------------------------------------------

    /**
     * Validate an orderBy clause to prevent duplicates
     */
    private fun validateOrderByField(field: String): Result<Unit> {
        return if (orderByFieldTracker[field] == true) {
            Result.failure(
                FirestoreQueryException(
                    FirestoreQueryException.QueryErrorType.DUPLICATE_ORDER_BY,
                    "The field '$field' has already been specified in an orderBy clause",
                    field
                )
            )
        } else {
            Result.success(Unit)
        }
    }

    companion object {
        private const val TAG = "QueryBuilder"

        // Maximum values for Firestore limitations
        private const val MAX_LIMIT = 1000
        private const val MAX_WHERE_CLAUSES = 10
        private const val MAX_ORDER_BY_CLAUSES = 10

        /**
         * Create a new QueryBuilder for the specified collection
         */
        fun collection(collectionPath: String): Result<QueryBuilder> {
            return Result.success(QueryBuilder(FirebaseFirestore.getInstance(), collectionPath))
        }

        /**
         * Create a new QueryBuilder for the specified collection with a provided FirebaseFirestore instance
         */
        fun collection(db: FirebaseFirestore, collectionPath: String): Result<QueryBuilder> {
            return Result.success(QueryBuilder(db, collectionPath))
        }

        /**
         * Create a new QueryBuilder from an existing CollectionReference
         */
        fun fromCollection(collectionRef: CollectionReference): Result<QueryBuilder> {
            val builder = QueryBuilder(FirebaseFirestore.getInstance(), collectionRef.path)
            builder.collectionRef = collectionRef
            return Result.success(builder)
        }
    }
}

//-----------------------------------------------------------------------------------
// Extension functions for validation using modern Kotlin patterns
//-----------------------------------------------------------------------------------

/**
 * Validate a field name
 */
private fun String.validateField(): Result<String> {
    return if (this.trim().isEmpty()) {
        Result.failure(
            FirestoreQueryException(
                FirestoreQueryException.QueryErrorType.INVALID_FIELD,
                "Field name cannot be null or empty"
            )
        )
    } else {
        Result.success(this)
    }
}

/**
 * Validate a FieldPath
 */
private fun FieldPath.validateFieldPath(): Result<FieldPath> {
    return Result.success(this) // FieldPath validation handled by Firestore SDK
}

/**
 * Validate that a value is appropriate for a field
 */
private fun String.validateValueForField(value: Any?): Result<Unit> {
    val fieldLower = this.lowercase(Locale.getDefault())
    
    // Timestamp validation for date fields
    if ((fieldLower.contains("date") || fieldLower.contains("time") || fieldLower.contains("timestamp")) 
        && value != null && value !is Date && value !is Timestamp) {
        Log.w("QueryBuilder", "Warning: Field '$this' appears to be a date/time field but value is not a Date or Timestamp")
    }

    // Boolean validation
    if ((fieldLower.startsWith("is") || fieldLower.startsWith("has") || fieldLower.endsWith("flag"))
        && value != null && value !is Boolean) {
        Log.w("QueryBuilder", "Warning: Field '$this' appears to be a boolean field but value is not a Boolean")
    }

    return Result.success(Unit)
}

/**
 * Validate that a value is appropriate for a FieldPath
 */
private fun FieldPath.validateValueForFieldPath(value: Any?): Result<Unit> {
    // Basic validation for FieldPath
    return Result.success(Unit)
}

/**
 * Validate an array for whereIn, whereArrayContainsAny, and whereNotIn
 */
private fun List<*>.validateArray(methodName: String): Result<Unit> {
    return when {
        this.isEmpty() -> Result.failure(
            FirestoreQueryException(
                FirestoreQueryException.QueryErrorType.INVALID_VALUE_TYPE,
                "Value for $methodName cannot be an empty array"
            )
        )
        this.size > 10 -> Result.failure(
            FirestoreQueryException(
                FirestoreQueryException.QueryErrorType.INVALID_VALUE_TYPE,
                "$methodName supports a maximum of 10 items, but got ${this.size} items"
            )
        )
        this.any { it == null } -> {
            val nullIndex = this.indexOfFirst { it == null }
            Result.failure(
                FirestoreQueryException(
                    FirestoreQueryException.QueryErrorType.INVALID_VALUE_TYPE,
                    "$methodName does not support null values in the array (at index $nullIndex)"
                )
            )
        }
        else -> Result.success(Unit)
    }
}

/**
 * Validate a limit value
 */
private fun Int.validateLimit(): Result<Unit> {
    return when {
        this <= 0 -> Result.failure(
            FirestoreQueryException(
                FirestoreQueryException.QueryErrorType.VALIDATION_ERROR,
                "Limit must be greater than 0, but got $this"
            )
        )
        this > 1000 -> Result.failure(
            FirestoreQueryException(
                FirestoreQueryException.QueryErrorType.LIMIT_EXCEEDED,
                "Limit exceeds maximum of 1000, but got $this"
            )
        )
        else -> Result.success(Unit)
    }
}

/**
 * Validate cursor values for pagination methods
 */
private fun Array<out Any>.validateCursorValues(methodName: String): Result<Unit> {
    return if (this.isEmpty()) {
        Result.failure(
            FirestoreQueryException(
                FirestoreQueryException.QueryErrorType.VALIDATION_ERROR,
                "$methodName requires at least one value"
            )
        )
    } else {
        Result.success(Unit)
    }
}

/**
 * Validate a date range
 */
private fun Date.validateDateRange(endDate: Date): Result<Unit> {
    return if (this.after(endDate)) {
        Result.failure(
            FirestoreQueryException(
                FirestoreQueryException.QueryErrorType.VALIDATION_ERROR,
                "Start date cannot be after end date"
            )
        )
    } else {
        Result.success(Unit)
    }
}

/**
 * Validate a number range
 */
private fun Number.validateNumberRange(max: Number): Result<Unit> {
    return if (this.toDouble() >= max.toDouble()) {
        Result.failure(
            FirestoreQueryException(
                FirestoreQueryException.QueryErrorType.VALIDATION_ERROR,
                "Minimum value must be less than maximum value"
            )
        )
    } else {
        Result.success(Unit)
    }
}

/**
 * Validate a string value
 */
private fun String.validateString(paramName: String): Result<Unit> {
    return if (this.trim().isEmpty()) {
        Result.failure(
            FirestoreQueryException(
                FirestoreQueryException.QueryErrorType.VALIDATION_ERROR,
                "$paramName cannot be null or empty"
            )
        )
    } else {
        Result.success(Unit)
    }
}
