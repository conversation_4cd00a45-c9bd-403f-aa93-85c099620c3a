package com.autogratuity.data.mapper

import android.util.Log
import com.autogratuity.debug.ClarityArchitectureMonitor
import com.autogratuity.domain.model.Address
import com.autogratuity.domain.model.ComparisonType
import com.autogratuity.domain.model.CustomDndRule
import com.autogratuity.domain.model.Delivery
import com.autogratuity.domain.model.DndDetails
import com.autogratuity.domain.model.Flags
import com.autogratuity.domain.model.ManualDndState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.withContext
import java.time.OffsetDateTime

/**
 * ✅ MODERNIZED (2025-01-28): Address DND Mapper following Clarity Architecture
 *
 * Business Logic Responsibilities:
 * - Evaluates address-level DND status based on delivery history and user preferences
 * - Applies DND rule hierarchy (manual override → custom rules → default rules)
 * - Respects subscription tier limitations (freemium vs premium features)
 * - Determines when backend updates should be triggered
 * - Follows SSOT ownership principles (evaluates but doesn't modify CLOUD_OWNED fields)
 *
 * Architecture Compliance:
 * - Uses domain models only (no generated DTOs)
 * - Contains business logic as per Clarity Architecture
 * - Integrates with existing DeliveryMapper patterns
 * - Supports cloud functions philosophy (evaluate → trigger → backend executes)
 */
class AddressDndMapper {

    /**
     * Evaluates address-level DND status based on delivery history and user preferences.
     * Follows the DND rule hierarchy from tip-logic.md:
     * 1. Manual Override (highest priority)
     * 2. Custom Rules (premium users)
     * 3. Default Rules (freemium users or premium fallback)
     *
     * @param address The address to evaluate
     * @param deliveryHistory All deliveries for this address
     * @param userPreferences User's DND preferences (custom rules)
     * @param subscriptionTier User's subscription level
     * @return AddressDndEvaluation with recommendation and reasoning
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun evaluateAddressDndStatus(
        address: Address,
        deliveryHistory: List<Delivery>,
        userPreferences: DndDetails?,
        subscriptionTier: String
    ): AddressDndEvaluation = withContext(Dispatchers.Default) {
        
        Log.d(TAG, "Evaluating DND status for address: ${address.id}")
        
        // 🔍 SESSION CORRELATION: Add DND evaluation to session
        ClarityArchitectureMonitor.addSessionEvent("dnd_evaluation_start:${address.id}")
        
        try {
            // 1. Check manual override (highest priority)
            val manualState = address.flags?.manualDndState
            if (manualState != null) {
                // 🔍 SESSION CORRELATION: Add manual override detection to session
                ClarityArchitectureMonitor.addSessionEvent("dnd_manual_override:${address.id}:$manualState")
                
                return@withContext handleManualOverride(manualState)
            }
            
            // 2. Apply automatic rules based on subscription tier
            val evaluation = evaluateAutomaticRules(
                deliveryHistory = deliveryHistory,
                userPreferences = userPreferences,
                subscriptionTier = subscriptionTier
            )
            
            // 🔍 SESSION CORRELATION: Add successful DND evaluation to session
            ClarityArchitectureMonitor.addSessionEvent("dnd_evaluation_success:${address.id}:${evaluation.shouldBeDnd}")
            
            return@withContext evaluation
            
        } catch (e: Exception) {
            Log.e(TAG, "Error evaluating DND status for address: ${address.id}", e)
            
            // 🔍 SESSION CORRELATION: Add DND evaluation failure to session
            ClarityArchitectureMonitor.addSessionEvent("dnd_evaluation_failure:${address.id}:exception")
            
            return@withContext AddressDndEvaluation(
                shouldBeDnd = false,
                reason = null,
                isVerified = false,
                confidence = 0.0,
                evaluationDetails = "Error during evaluation: ${e.message}"
            )
        }
    }
    
    /**
     * Handles manual DND override states.
     * Manual overrides have the highest priority and bypass all automatic rules.
     */
    private fun handleManualOverride(manualState: ManualDndState): AddressDndEvaluation {
        return when (manualState) {
            ManualDndState.FORCE_DND -> AddressDndEvaluation(
                shouldBeDnd = true,
                reason = "MANUAL_USER_FORCE_DND",
                isVerified = true,
                confidence = 1.0,
                evaluationDetails = "User manually marked address as Do Not Deliver"
            )
            ManualDndState.FORCE_ALLOW -> AddressDndEvaluation(
                shouldBeDnd = false,
                reason = "MANUAL_USER_FORCE_ALLOW",
                isVerified = true,
                confidence = 1.0,
                evaluationDetails = "User manually marked address as Allow Deliveries"
            )
            ManualDndState.AUTOMATIC -> AddressDndEvaluation(
                shouldBeDnd = false,
                reason = null,
                isVerified = false,
                confidence = 0.0,
                evaluationDetails = "Manual state set to AUTOMATIC - should use automatic rules"
            )
            else -> {
                Log.w(TAG, "Unknown manual DND state: $manualState")
                AddressDndEvaluation(
                    shouldBeDnd = false,
                    reason = null,
                    isVerified = false,
                    confidence = 0.0,
                    evaluationDetails = "Unknown manual state: $manualState"
                )
            }
        }
    }
    
    /**
     * Evaluates automatic DND rules based on subscription tier and user preferences.
     */
    private fun evaluateAutomaticRules(
        deliveryHistory: List<Delivery>,
        userPreferences: DndDetails?,
        subscriptionTier: String
    ): AddressDndEvaluation {
        
        // Premium users can use custom rules
        if (isPremiumTier(subscriptionTier)) {
            val customRule = userPreferences?.customRule
            if (customRule?.isEnabled == true) {
                Log.d(TAG, "Applying custom DND rule for premium user")
                return evaluateCustomRule(deliveryHistory, customRule)
            }
        }
        
        // Default rules for freemium users or premium users without custom rules
        Log.d(TAG, "Applying default DND rules")
        return evaluateDefaultRules(deliveryHistory)
    }
    
    /**
     * Checks if the subscription tier allows premium features.
     */
    private fun isPremiumTier(subscriptionTier: String): Boolean {
        return subscriptionTier.equals("pro", ignoreCase = true) || 
               subscriptionTier.equals("premium", ignoreCase = true)
    }
    
    /**
     * Evaluates custom DND rule configured by premium users.
     */
    private fun evaluateCustomRule(
        deliveryHistory: List<Delivery>,
        customRule: CustomDndRule
    ): AddressDndEvaluation {
        
        // Get the most recent completed delivery for evaluation
        val recentDelivery = deliveryHistory
            .filter { it.details.status?.isCompleted == true }
            .maxByOrNull { it.details.times?.completedAt ?: OffsetDateTime.MIN }
            
        if (recentDelivery == null) {
            return AddressDndEvaluation(
                shouldBeDnd = false,
                reason = null,
                isVerified = false,
                confidence = 0.0,
                evaluationDetails = "No completed deliveries found for custom rule evaluation"
            )
        }
        
        val tipAmount = recentDelivery.details.amounts?.tipAmount ?: 0.0
        val threshold = customRule.tipAmountThreshold
        
        val shouldBeDnd = when (customRule.comparisonType) {
            ComparisonType.LESS_THAN -> tipAmount < threshold
            ComparisonType.LESS_THAN_OR_EQUAL_TO -> tipAmount <= threshold
            ComparisonType.EQUAL_TO -> tipAmount == threshold
        }
        
        val confidence = if (shouldBeDnd) 0.9 else 0.8 // High confidence for custom rules
        
        return AddressDndEvaluation(
            shouldBeDnd = shouldBeDnd,
            reason = if (shouldBeDnd) "RULE_BASED_USER_CUSTOM_RULE" else null,
            isVerified = shouldBeDnd,
            confidence = confidence,
            evaluationDetails = "Custom rule: tip $tipAmount ${customRule.comparisonType.name.lowercase()} $threshold = $shouldBeDnd"
        )
    }
    
    /**
     * Evaluates default DND rules for freemium users or premium users without custom rules.
     * Default rule: $0.00 tip = DND
     */
    private fun evaluateDefaultRules(deliveryHistory: List<Delivery>): AddressDndEvaluation {
        
        val completedDeliveries = deliveryHistory.filter { 
            it.details.status?.isCompleted == true
        }
        
        if (completedDeliveries.isEmpty()) {
            return AddressDndEvaluation(
                shouldBeDnd = false,
                reason = null,
                isVerified = false,
                confidence = 0.0,
                evaluationDetails = "No completed deliveries found for default rule evaluation"
            )
        }
        
        // Check for zero tip deliveries (default DND rule)
        val zeroTipDeliveries = completedDeliveries.filter { delivery ->
            delivery.details.amounts?.tipAmount == 0.0
        }
        
        val hasZeroTipDelivery = zeroTipDeliveries.isNotEmpty()
        val zeroTipRatio = zeroTipDeliveries.size.toDouble() / completedDeliveries.size
        
        // Calculate confidence based on delivery history
        val confidence = when {
            completedDeliveries.size >= 3 && zeroTipRatio >= 0.5 -> 0.9 // High confidence
            completedDeliveries.size >= 2 && hasZeroTipDelivery -> 0.7   // Medium confidence  
            hasZeroTipDelivery -> 0.5                                    // Low confidence
            else -> 0.8                                                  // High confidence for non-DND
        }
        
        return AddressDndEvaluation(
            shouldBeDnd = hasZeroTipDelivery,
            reason = if (hasZeroTipDelivery) "RULE_BASED_ZERO_TIP" else null,
            isVerified = hasZeroTipDelivery && completedDeliveries.size >= 2,
            confidence = confidence,
            evaluationDetails = "Default rule: ${zeroTipDeliveries.size}/${completedDeliveries.size} deliveries with $0 tip"
        )
    }
    
    /**
     * Determines if backend update should be triggered based on current flags vs evaluation.
     * Follows SSOT ownership - only recommends updates, doesn't perform them.
     */
    fun shouldTriggerBackendUpdate(
        currentFlags: Flags?,
        evaluatedStatus: AddressDndEvaluation
    ): Boolean {
        
        val currentDnd = currentFlags?.doNotDeliver == true // Note: This is checking raw flag state for comparison
        val currentSource = currentFlags?.dndSource
        val currentVerified = currentFlags?.isVerified == true
        
        val needsUpdate = currentDnd != evaluatedStatus.shouldBeDnd ||
                         currentSource != evaluatedStatus.reason ||
                         currentVerified != evaluatedStatus.isVerified
        
        if (needsUpdate) {
            Log.d(TAG, "Backend update needed: current=($currentDnd, $currentSource, $currentVerified) " +
                      "vs evaluated=(${evaluatedStatus.shouldBeDnd}, ${evaluatedStatus.reason}, ${evaluatedStatus.isVerified})")
        }
        
        return needsUpdate
    }
    
    companion object {
        private const val TAG = "AddressDndMapper"
    }
}

/**
 * Result of address DND evaluation containing recommendation and reasoning.
 */
data class AddressDndEvaluation(
    val shouldBeDnd: Boolean,
    val reason: String?,
    val isVerified: Boolean,
    val confidence: Double,
    val evaluationDetails: String
)