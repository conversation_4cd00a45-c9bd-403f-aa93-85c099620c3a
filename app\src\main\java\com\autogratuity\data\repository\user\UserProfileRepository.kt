package com.autogratuity.data.repository.user

// Import SSoT domain model (not DTO)
import com.autogratuity.data.model.Result
import com.autogratuity.domain.model.User
import kotlinx.coroutines.flow.Flow
import java.time.OffsetDateTime

/**
 * Data layer interface for User repository operations.
 * Follows AddressRepository pattern: comprehensive data layer interface with all operations.
 * All operations use SSoT User models, never DTOs.
 * 
 * This is the comprehensive interface that UserProfileRepositoryImpl implements,
 * similar to how AddressRepository serves as the comprehensive data layer interface.
 */
interface UserProfileRepository {

    // ===== CORE CRUD OPERATIONS =====
    suspend fun getUserById(id: String): Result<User?>
    suspend fun getCurrentUser(): Result<User?>
    suspend fun addUser(user: User): Result<Unit>
    suspend fun updateUser(user: User): Result<Unit>
    suspend fun deleteUser(id: String): Result<Unit>

    // ===== REACTIVE FLOW OPERATIONS =====
    fun observeUserById(id: String): Flow<Result<User?>>
    fun observeCurrentUser(): Flow<Result<User?>>

    // ===== BUSINESS OPERATIONS =====
    suspend fun updateUserProfile(userId: String, updates: Map<String, Any>): Result<Unit>
    suspend fun updateUserSubscription(userId: String, subscription: com.autogratuity.domain.model.UserSubscription): Result<Unit>
    suspend fun updateUserPreferences(userId: String, preferences: com.autogratuity.domain.model.UserPreferences): Result<Unit>
    suspend fun updateUserUsageStats(userId: String, stats: com.autogratuity.domain.model.UserUsageStats): Result<Unit>
    suspend fun incrementUsageStat(userId: String, statName: String, incrementBy: Long = 1L): Result<Unit>
    suspend fun setDefaultAddress(userId: String, addressId: String): Result<Unit>
    suspend fun performInitialSetupIfNeeded(userId: String): Result<Unit>

    // ===== VALIDATION AND UTILITY =====
    suspend fun validateUser(user: User): Result<Unit>
    suspend fun normalizeUser(user: User): User
    suspend fun userExistsByEmail(email: String): Result<Boolean>
    suspend fun createDefaultUser(userId: String, email: String?): Result<User>

    // ===== STATISTICS AND ANALYTICS =====
    suspend fun getUserStats(userId: String): Result<com.autogratuity.domain.model.UserUsageStats?>
    fun observeUserSubscription(userId: String): Flow<Result<com.autogratuity.domain.model.UserSubscription?>>
    suspend fun getAccountCreationDate(userId: String): Result<OffsetDateTime?>

    // ===== CACHE MANAGEMENT =====
    suspend fun prefetchUserData(userId: String): Result<Unit>
    suspend fun clearCache(userId: String): Result<Unit>
    suspend fun clearAllCache(): Result<Unit>
    suspend fun invalidateCache(userId: String): Result<Unit>
    suspend fun refreshFromRemote(userId: String): Result<Unit>

    // ===== REPOSITORY LIFECYCLE =====
    suspend fun initialize(): Result<Unit>
    suspend fun cleanup(): Result<Unit>

    // ===== AUTHENTICATION INTEGRATION =====
    // getCurrentUserId() is inherited from FirestoreRepository
    suspend fun handleUserSignIn(userId: String, authProvider: String): Result<Unit>
    suspend fun handleUserSignOut(userId: String): Result<Unit>
    suspend fun syncWithAuthProfile(userId: String): Result<Unit>

    // ===== IMPORT/EXPORT OPERATIONS =====
    suspend fun exportUserData(userId: String, format: String = "json"): Result<String>
    suspend fun importUserData(userId: String, data: Map<String, Any>): Result<Unit>

    // ===== BACKUP AND RECOVERY =====
    suspend fun createUserBackup(userId: String): Result<Map<String, Any>>
    suspend fun restoreUserBackup(userId: String, backup: Map<String, Any>): Result<Unit>
    suspend fun migrateUserData(userId: String, fromVersion: Long, toVersion: Long): Result<Unit>
}
