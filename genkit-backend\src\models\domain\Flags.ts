// Auto-generated from Flags.kt
// TypeScript equivalent of Android domain.model.Flags

/**
 * Domain model generated from Kotlin Flags
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface Flags {
  isFavorite?: boolean? = null;
  isVerified?: boolean? = null;
  doNotDeliver?: boolean? = null;
  dndSource?: string? = null;
  hasAccessIssues?: boolean? = null;
  manualDndState?: ManualDndState? = null // ✅ FIXED: Use enum directly for type safety;
  isDefault?: boolean? = null // REMOVED - isDefault is on SSoT Address directly;
}