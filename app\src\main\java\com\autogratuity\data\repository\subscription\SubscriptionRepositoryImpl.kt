package com.autogratuity.data.repository.subscription

import android.util.Log
import com.autogratuity.data.datasource.local.SubscriptionLocalDataSource
import com.autogratuity.data.datasource.remote.SubscriptionRemoteDataSource
import com.autogratuity.data.mapper.SubscriptionMapper
import com.autogratuity.data.model.Result
import com.autogratuity.data.repository.core.RepositoryErrorHandler
import com.autogratuity.data.security.AuthenticationManager
import com.autogratuity.debug.ClarityArchitectureMonitor
import com.autogratuity.domain.model.UserSubscription
import com.autogratuity.domain.repository.SubscriptionRepository
import com.autogratuity.data.repository.subscription.SubscriptionRepository as SubscriptionProfileRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.OffsetDateTime
import com.autogratuity.data.util.RequestKeys
import com.autogratuity.data.util.RequestTimeouts
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds
import kotlin.time.TimeSource

/**
 * ✅ PHASE 2: Simplified SubscriptionRepository following clarity.md principles
 * Only injects RemoteDataSource + LocalDataSource + Mapper as per architectural guidelines
 * 
 * REFACTORED: Removed all direct QueryBuilder and Firestore operations,
 * delegating them to SubscriptionRemoteDataSource per clarity.md requirements.
 */
@OptIn(ExperimentalCoroutinesApi::class)
class SubscriptionRepositoryImpl(
    // Core architectural dependencies following clarity.md principles
    private val remoteDataSource: SubscriptionRemoteDataSource,
    private val localDataSource: SubscriptionLocalDataSource,
    private val subscriptionMapper: SubscriptionMapper,
    // Repository orchestration dependencies
    private val authManager: AuthenticationManager,
    private val ioDispatcher: CoroutineDispatcher,
    // Infrastructure utilities moved from RemoteDataSource for proper orchestration
    private val requestDeduplicationManager: com.autogratuity.data.util.RequestDeduplicationManager,
    private val priorityTaskScheduler: com.autogratuity.data.util.ModernPriorityTaskScheduler,
    private val sessionManager: com.autogratuity.data.util.SessionManager,
    // ✅ REMOVED: CacheWarmingManager dependency removed from domain repositories
    private val authStateCoordinator: com.autogratuity.data.util.AuthenticationStateCoordinator,
    // Performance infrastructure
    private val repositoryErrorHandler: RepositoryErrorHandler,
    // JSON serialization with proper JSR310 support
    private val objectMapper: com.fasterxml.jackson.databind.ObjectMapper
) : SubscriptionRepository, SubscriptionProfileRepository {

    companion object {
        private const val TAG = "SubscriptionRepositoryImpl"
    }

    private suspend fun getCurrentUserIdSuspend(): String {
        Log.d(TAG, "getCurrentUserIdSuspend: Checking authentication state")
        
        // Enhanced authentication using AuthenticationStateCoordinator
        val authState = authStateCoordinator.waitForAuthentication(timeoutMs = 5000)
        
        return when (authState) {
            is com.autogratuity.data.util.AuthenticationStateCoordinator.AuthReadyState.Authenticated -> {
                Log.d(TAG, "getCurrentUserIdSuspend: Authentication confirmed for user ${authState.userId}")
                authState.userId
            }
            is com.autogratuity.data.util.AuthenticationStateCoordinator.AuthReadyState.Unauthenticated -> {
                Log.e(TAG, "getCurrentUserIdSuspend: User not authenticated")
                throw IllegalStateException("User not authenticated")
            }
            is com.autogratuity.data.util.AuthenticationStateCoordinator.AuthReadyState.AuthenticationInProgress -> {
                Log.e(TAG, "getCurrentUserIdSuspend: Authentication timeout - still in progress")
                throw IllegalStateException("Authentication timeout - authentication still in progress")
            }
            is com.autogratuity.data.util.AuthenticationStateCoordinator.AuthReadyState.Unknown -> {
                Log.e(TAG, "getCurrentUserIdSuspend: Authentication timeout - unknown state")
                throw IllegalStateException("Authentication timeout - unknown authentication state")
            }
            null -> {
                Log.e(TAG, "getCurrentUserIdSuspend: Authentication state is null")
                throw IllegalStateException("Authentication state is null")
            }
        }
    }

    // Implementation for SubscriptionRepository.clearCache() - no parameters
    override suspend fun clearCache(): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        return@withContext repositoryErrorHandler.handleSuspendFunction(
            operationName = "clearCache",
            entityType = "UserSubscription"
        ) {
            val userId = getCurrentUserIdSuspend()
            Log.i(TAG, "clearCache: Clearing subscription cache for user $userId")

            val clearResult = localDataSource.clearAllSubscriptions()
            when (clearResult) {
                is Result.Success -> {
                    Log.d(TAG, "clearCache: Successfully cleared subscription cache for user $userId")
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    Log.e(TAG, "clearCache: Error clearing cache for user $userId", clearResult.exception)
                    throw clearResult.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "clearCache: Unexpected Loading state from localDataSource.clearAllSubscriptions()")
                    throw IllegalStateException("LocalDataSource clearAllSubscriptions returned Loading unexpectedly")
                }
            }
        }.getOrThrow() // Extract result from RepositoryErrorHandler Result wrapper
    }

    // ===== DOMAIN INTERFACE IMPLEMENTATION =====

    override suspend fun getCurrentUserSubscription(): Result<UserSubscription?> = withContext(ioDispatcher) {
        val domainStartTime = TimeSource.Monotonic.markNow()
        
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "getCurrentUserSubscription",
            entityType = "UserSubscription"
        ) {
            val userId = getCurrentUserIdSuspend()
            
            // INFRASTRUCTURE PHASE 1: Priority task scheduling
            val task = com.autogratuity.data.util.PriorityTask(
                id = "getCurrentUserSubscription_${System.currentTimeMillis()}",
                priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "getCurrentUserSubscription"),
                operation = { 
                    kotlin.runCatching {
                        val result = getUserSubscription(userId)
                        
                        // Monitor domain repository operation
                        ClarityArchitectureMonitor.monitorRepositoryOperation(
                            repositoryClass = "SubscriptionRepository",
                            operation = "getCurrentUserSubscription",
                            duration = domainStartTime.elapsedNow(),
                            success = result is Result.Success,
                            cacheHit = result is Result.Success && result.data != null,
                            dataType = "UserSubscription",
                            entityId = userId,
                            userId = userId,
                            resultCount = if (result is Result.Success && result.data != null) 1 else 0,
                            dataSource = "cache_or_remote",
                            dataSize = result.toString().length,
                            cacheStrategy = "cache-first"
                        )
                        
                        when (result) {
                            is Result.Success -> result.data
                            is Result.Error -> throw result.exception
                            is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                        }
                    }
                },
                entityType = "UserSubscription",
                operationType = "getCurrentUserSubscription",
                estimatedDuration = 2.seconds,
                timeout = 8.seconds
            )
            
            val kotlinResult = priorityTaskScheduler.scheduleTask(task)
            kotlinResult.getOrThrow()
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull())
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                
                // Monitor failed operation
                ClarityArchitectureMonitor.monitorRepositoryOperation(
                    repositoryClass = "SubscriptionRepository",
                    operation = "getCurrentUserSubscription",
                    duration = domainStartTime.elapsedNow(),
                    success = false,
                    error = exception,
                    dataType = "UserSubscription",
                    entityId = "unknown",
                    userId = "unknown",
                    resultCount = 0,
                    dataSource = "error",
                    cacheStrategy = "cache-first"
                )
                
                Result.Error(exception)
            }
        }
    }

    override suspend fun getUserSubscription(userId: String): Result<UserSubscription?> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        return@withContext repositoryErrorHandler.handleSuspendFunction(
            operationName = "getUserSubscription",
            entityType = "UserSubscription"
        ) {
            // Note: Query profiling handled by ClarityArchitectureMonitor in remote data source

            val totalStartTime = TimeSource.Monotonic.markNow()
            var cacheCheckDuration: kotlin.time.Duration? = null
            var remoteFetchDuration: kotlin.time.Duration? = null
            var mappingDuration: kotlin.time.Duration? = null
            var cacheStoreDuration: kotlin.time.Duration? = null
            var cacheHit = false

            try {
            // INFRASTRUCTURE PHASE 1: Enhanced authentication check
            Log.d(TAG, "getUserSubscription: Verifying authentication for user $userId")
            val authState = authStateCoordinator.authReadyState.value
            if (authState !is com.autogratuity.data.util.AuthenticationStateCoordinator.AuthReadyState.Authenticated) {
                Log.w(TAG, "getUserSubscription: Authentication not ready, current state: $authState")
                ClarityArchitectureMonitor.addSessionEvent("auth_not_ready:getUserSubscription:$userId")
            }

            // INFRASTRUCTURE PHASE 2: Session management
            Log.d(TAG, "getUserSubscription: Managing session for user $userId")
            val session = sessionManager.getOrCreateSession(userId)
            Log.d(TAG, "getUserSubscription: Using session ${session.sessionId}")

            // INFRASTRUCTURE PHASE 2.5: Cache warming removed to prevent performance regression
            // Log.d(TAG, "getUserSubscription: Cache warming disabled to prevent duplicate requests")

            // INFRASTRUCTURE PHASE 3: Request deduplication - use the actual API
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userSubscription(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                // 1. CACHE CHECK PHASE
                val cacheCheckStart = TimeSource.Monotonic.markNow()
                val localResult = localDataSource.getUserSubscription(userId)
                cacheCheckDuration = cacheCheckStart.elapsedNow()
                
                when (localResult) {
                    is Result.Success -> {
                        if (localResult.data != null) {
                            cacheHit = true
                            
                            // ENHANCED: Add session correlation for subscription cache hit
                            ClarityArchitectureMonitor.addSessionEvent("subscription_cache_hit:$userId")
                            
                            // ENHANCED: Log cache hit with breakdown
                            ClarityArchitectureMonitor.monitorCacheSystemPerformance(
                                repositoryClass = "SubscriptionRepository",
                                operation = "getUserSubscription",
                                cacheCheckDuration = cacheCheckDuration,
                                remoteFetchDuration = null, // No remote call needed
                                mappingDuration = null,     // No mapping needed
                                cacheStoreDuration = null,  // No storage needed
                                cacheHit = true,
                                entityType = "UserSubscription",
                                entityId = userId,
                                userId = userId,
                                cacheMetrics = mapOf(
                                    "operation" to "cache_hit",
                                    "source" to "local_cache"
                                )
                            )
                            
                            return@deduplicateRequest localResult
                        }
                    }
                    is Result.Error -> Log.w(TAG, "Local cache error, trying remote", localResult.exception)
                    is Result.Loading -> {} // Continue to remote
                }

                // 2. REMOTE FETCH PHASE
                val remoteFetchStart = TimeSource.Monotonic.markNow()
                val remoteResult = remoteDataSource.getUserSubscriptionDto(userId)
                remoteFetchDuration = remoteFetchStart.elapsedNow()
                
                // Process the remote result and return the final result
                when (remoteResult) {
                    is Result.Success -> {
                        val dto = remoteResult.data
                        
                        // 3. MAPPING PHASE
                        val mappingStart = TimeSource.Monotonic.markNow()
                        val domainResult = subscriptionMapper.dtoToSsot(dto)
                        mappingDuration = mappingStart.elapsedNow()
                        
                        when (domainResult) {
                            is Result.Success -> {
                                // 4. CACHE STORAGE PHASE
                                val cacheStoreStart = TimeSource.Monotonic.markNow()
                                domainResult.data?.let { 
                                    localDataSource.saveUserSubscription(userId, it)
                                }
                                cacheStoreDuration = cacheStoreStart.elapsedNow()
                                
                                // ENHANCED: Add session correlation for subscription cache miss
                                ClarityArchitectureMonitor.addSessionEvent("subscription_cache_miss:$userId")
                                
                                // ENHANCED: Log complete cache miss breakdown
                                ClarityArchitectureMonitor.monitorCacheSystemPerformance(
                                    repositoryClass = "SubscriptionRepository",
                                    operation = "getUserSubscription",
                                    cacheCheckDuration = cacheCheckDuration,
                                    remoteFetchDuration = remoteFetchDuration,
                                    mappingDuration = mappingDuration,
                                    cacheStoreDuration = cacheStoreDuration,
                                    cacheHit = false,
                                    entityType = "UserSubscription",
                                    entityId = userId,
                                    userId = userId,
                                    cacheMetrics = mapOf(
                                        "operation" to "cache_miss_with_remote_fetch",
                                        "source" to "firestore",
                                        "total_time_ms" to totalStartTime.elapsedNow().inWholeMilliseconds,
                                        "cache_check_ms" to cacheCheckDuration.inWholeMilliseconds,
                                        "remote_fetch_ms" to remoteFetchDuration.inWholeMilliseconds,
                                        "mapping_ms" to mappingDuration.inWholeMilliseconds,
                                        "cache_store_ms" to cacheStoreDuration.inWholeMilliseconds
                                    )
                                )
                                
                                domainResult
                            }
                            is Result.Error -> domainResult
                            is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                        }
                    }
                    is Result.Error -> remoteResult
                    is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
                }
            }
            
            // Return the deduplicated result (handle null case)
            deduplicatedResult ?: Result.Error(Exception("Request deduplication returned null result"))
        } catch (e: Exception) {
                // ENHANCED: Log failed operation with timing breakdown
                ClarityArchitectureMonitor.monitorCacheSystemPerformance(
                    repositoryClass = "SubscriptionRepository",
                    operation = "getUserSubscription",
                    cacheCheckDuration = cacheCheckDuration ?: kotlin.time.Duration.ZERO,
                    remoteFetchDuration = remoteFetchDuration,
                    mappingDuration = mappingDuration,
                    cacheStoreDuration = cacheStoreDuration,
                    cacheHit = cacheHit,
                    entityType = "UserSubscription",
                    entityId = userId,
                    userId = userId,
                    cacheMetrics = mapOf(
                        "operation" to "failed",
                        "error" to e.javaClass.simpleName,
                        "total_time_ms" to totalStartTime.elapsedNow().inWholeMilliseconds
                    )
                )

                throw e // Re-throw for RepositoryErrorHandler to handle
            }
        }.getOrThrow() // Extract result from RepositoryErrorHandler Result wrapper
    }

    override suspend fun updateUserSubscription(userId: String, subscription: UserSubscription): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        return@withContext repositoryErrorHandler.handleSuspendFunction(
            operationName = "updateUserSubscription",
            entityType = "UserSubscription"
        ) {
            // INFRASTRUCTURE PHASE 1: Session management
            Log.d(TAG, "updateUserSubscription: Managing session for user $userId")
            val session = sessionManager.getOrCreateSession(userId)
            Log.d(TAG, "updateUserSubscription: Using session ${session.sessionId}")

            // INFRASTRUCTURE PHASE 2: Request deduplication
            val updateResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userSubscriptionUpdate(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                // Convert to DTO
                val dtoResult = subscriptionMapper.ssotToDto(subscription)
                when (dtoResult) {
                    is Result.Success -> {
                        val dto = dtoResult.data ?: throw IllegalStateException("DTO conversion failed")

                        // Call remote data source directly
                        val remoteResult = remoteDataSource.updateUserSubscriptionDto(userId, dto)

                        when (remoteResult) {
                            is Result.Success -> {
                                // Update local cache
                                localDataSource.updateUserSubscription(userId, subscription)
                                Result.Success(Unit)
                            }
                            is Result.Error -> remoteResult
                            is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                        }
                    }
                    is Result.Error -> dtoResult
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                }
            }
            
            // Return the update result (handle null case)
            updateResult ?: Result.Error(Exception("Request deduplication returned null result"))
        }.getOrThrow() // Extract result from RepositoryErrorHandler Result wrapper
    }

    override suspend fun createDefaultSubscription(userId: String): Result<UserSubscription> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "createDefaultSubscription",
            entityType = "UserSubscription"
        ) {
            // INFRASTRUCTURE PHASE 1: Session management
            Log.d(TAG, "createDefaultSubscription: Managing session for user $userId")
            val session = sessionManager.getOrCreateSession(userId)
            Log.d(TAG, "createDefaultSubscription: Using session ${session.sessionId}")

            // INFRASTRUCTURE PHASE 1.5: Cache warming removed to prevent performance regression
            // Log.d(TAG, "createDefaultSubscription: Cache warming disabled to prevent duplicate requests")

            // INFRASTRUCTURE PHASE 2: Request deduplication
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userSubscription(userId), // Use base subscription key for creation
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                // INFRASTRUCTURE PHASE 3: Priority task scheduling
                val task = com.autogratuity.data.util.PriorityTask(
                    id = "createDefaultSubscription_${userId}_${System.currentTimeMillis()}",
                    priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "createDefaultSubscription"),
                    operation = { 
                        // Wrap in kotlin.Result for PriorityTaskScheduler
                        kotlin.runCatching { 
                            subscriptionMapper.calculateDefaultSubscription()
                        }
                    },
                    entityType = "UserSubscription",
                    operationType = "createDefaultSubscription",
                    estimatedDuration = 2.seconds,
                    timeout = 10.seconds
                )
                
                val kotlinResult = priorityTaskScheduler.scheduleTask(task)
                kotlinResult.getOrThrow() // Extract the Result<UserSubscription>
            }
            
            deduplicatedResult ?: throw Exception("Request deduplication returned null result")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override fun observeCurrentUserSubscription(): Flow<Result<UserSubscription?>> {
        return try {
            // Use the safe current user ID method from AuthenticationStateCoordinator
            val userId = authStateCoordinator.getCurrentUserIdSafe()
            if (userId != null) {
                observeUserSubscription(userId)
            } else {
                flowOf(Result.Error(IllegalStateException("User not authenticated")))
            }
        } catch (e: Exception) {
            flowOf(Result.Error(e))
        }
    }

    override fun observeUserSubscription(userId: String): Flow<Result<UserSubscription?>> {
        // Enhanced Flow error handling with RepositoryErrorHandler
        return repositoryErrorHandler.handleFlow(
            flow = localDataSource.observeById(userId),
            operationName = "observeUserSubscription",
            entityType = "UserSubscription"
        ).map { kotlinResult ->
            // Convert kotlin.Result to com.autogratuity.data.model.Result
            when {
                kotlinResult.isSuccess -> Result.Success(kotlinResult.getOrNull())
                else -> {
                    val throwable = kotlinResult.exceptionOrNull()
                    val exception = when (throwable) {
                        is Exception -> throwable
                        else -> Exception("Unknown error", throwable)
                    }
                    Result.Error(exception)
                }
            }
        }
    }

    override suspend fun isProUser(): Result<Boolean> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "isProUser",
            entityType = "UserSubscription"
        ) {
            val userId = getCurrentUserIdSuspend()
            
            // INFRASTRUCTURE PHASE 1: Priority task scheduling
            val task = com.autogratuity.data.util.PriorityTask(
                id = "isProUser_${userId}_${System.currentTimeMillis()}",
                priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "isProUser"),
                operation = { 
                    kotlin.runCatching {
                        val subscriptionResult = getUserSubscription(userId)
                        when (subscriptionResult) {
                            is Result.Success -> {
                                val proUserResult = subscriptionMapper.isProUser(subscriptionResult.data)
                                when (proUserResult) {
                                    is Result.Success -> proUserResult.data
                                    is Result.Error -> throw proUserResult.exception
                                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                                }
                            }
                            is Result.Error -> throw subscriptionResult.exception
                            is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                        }
                    }
                },
                entityType = "UserSubscription",
                operationType = "isProUser",
                estimatedDuration = 500.milliseconds,
                timeout = 3.seconds
            )
            
            val kotlinResult = priorityTaskScheduler.scheduleTask(task)
            kotlinResult.getOrThrow()
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull() == true)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override fun observeIsProUser(): Flow<Boolean> {
        return try {
            // Use the safe current user ID method from AuthenticationStateCoordinator
            val userId = authStateCoordinator.getCurrentUserIdSafe()
            if (userId != null) {
                localDataSource.observeById(userId).map { subscription ->
                    (subscription?.level == "pro" || subscription?.level == "premium") && 
                    subscription.isActive == true
                }
            } else {
                flowOf(false)
            }
        } catch (_: Exception) {
            flowOf(false)
        }
    }

    override suspend fun isSubscriptionExpired(): Result<Boolean> = withContext(ioDispatcher) {
        try {
            val userId = getCurrentUserIdSuspend()
            val subscriptionResult = getUserSubscription(userId)
            when (subscriptionResult) {
                is Result.Success -> {
                    val isExpired = subscriptionMapper.isSubscriptionExpired(subscriptionResult.data)
                    Result.Success(isExpired)
                }
                is Result.Error -> subscriptionResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun isLifetimeSubscription(): Result<Boolean> = withContext(ioDispatcher) {
        try {
            val userId = getCurrentUserIdSuspend()
            val subscriptionResult = getUserSubscription(userId)
            when (subscriptionResult) {
                is Result.Success -> {
                    val isLifetime = subscriptionResult.data?.isLifetime == true
                    Result.Success(isLifetime)
                }
                is Result.Error -> subscriptionResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun getSubscriptionLevel(): Result<String> = withContext(ioDispatcher) {
        try {
            val userId = getCurrentUserIdSuspend()
            val subscriptionResult = getUserSubscription(userId)
            when (subscriptionResult) {
                is Result.Success -> {
                    val level = subscriptionResult.data?.level ?: "free"
                    Result.Success(level)
                }
                is Result.Error -> subscriptionResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun getSubscriptionExpiryDate(): Result<OffsetDateTime?> = withContext(ioDispatcher) {
        try {
            val userId = getCurrentUserIdSuspend()
            val subscriptionResult = getUserSubscription(userId)
            when (subscriptionResult) {
                is Result.Success -> {
                    val expiryDate = subscriptionResult.data?.expiryDate
                    Result.Success(expiryDate)
                }
                is Result.Error -> subscriptionResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun upgradeSubscription(durationMonths: Int, paymentDetails: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "upgradeSubscription",
            entityType = "UserSubscription"
        ) {
            val userId = getCurrentUserIdSuspend()
            
            // INFRASTRUCTURE PHASE 0: Enhanced authentication verification for critical operation
            val authVerified = verifyAuthenticationForCriticalOperation("upgradeSubscription", userId)
            if (!authVerified) {
                Log.e(TAG, "upgradeSubscription: Authentication verification failed for user $userId")
                throw IllegalStateException("Authentication verification failed")
            }
            
            // INFRASTRUCTURE PHASE 1: Session management
            Log.d(TAG, "upgradeSubscription: Managing session for user $userId")
            val session = sessionManager.getOrCreateSession(userId)
            Log.d(TAG, "upgradeSubscription: Using session ${session.sessionId}")

            // INFRASTRUCTURE PHASE 2: Request deduplication
            val requestKey = "upgradeSubscription:$userId"
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = requestKey,
                timeout = 15.seconds
            ) {
                // INFRASTRUCTURE PHASE 3: Priority task scheduling
                val task = com.autogratuity.data.util.PriorityTask(
                    id = "upgradeSubscription_${userId}_${System.currentTimeMillis()}",
                    priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "upgradeSubscription"),
                    operation = { 
                        kotlin.runCatching {
                            val currentSubscriptionResult = getUserSubscription(userId)
                            when (currentSubscriptionResult) {
                                is Result.Success -> {
                                    val upgradeResult = subscriptionMapper.processSubscriptionUpgrade(
                                        currentSubscriptionResult.data,
                                        durationMonths,
                                        paymentDetails
                                    )
                                    when (upgradeResult) {
                                        is Result.Success -> {
                                            val updateResult = updateUserSubscription(userId, upgradeResult.data)
                                            when (updateResult) {
                                                is Result.Success -> Unit
                                                is Result.Error -> throw Exception("Update failed", updateResult.exception)
                                                is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                                            }
                                        }
                                        is Result.Error -> throw Exception("Upgrade failed", upgradeResult.exception)
                                        is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                                    }
                                }
                                is Result.Error -> throw Exception("Failed to get current subscription", currentSubscriptionResult.exception)
                                is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                            }
                        }
                    },
                    entityType = "UserSubscription",
                    operationType = "upgradeSubscription",
                    estimatedDuration = 8.seconds,
                    timeout = 15.seconds
                )
                
                val kotlinResult = priorityTaskScheduler.scheduleTask(task)
                kotlinResult.getOrThrow()
            }
            
            deduplicatedResult ?: throw Exception("Request deduplication returned null result")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun cancelSubscription(immediate: Boolean): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "cancelSubscription",
            entityType = "UserSubscription"
        ) {
            val userId = getCurrentUserIdSuspend()
            
            // INFRASTRUCTURE PHASE 1: Session management
            Log.d(TAG, "cancelSubscription: Managing session for user $userId")
            val session = sessionManager.getOrCreateSession(userId)
            Log.d(TAG, "cancelSubscription: Using session ${session.sessionId}")

            // INFRASTRUCTURE PHASE 2: Request deduplication
            val requestKey = "cancelSubscription:$userId"
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = requestKey,
                timeout = 15.seconds
            ) {
                // INFRASTRUCTURE PHASE 3: Priority task scheduling
                val task = com.autogratuity.data.util.PriorityTask(
                    id = "cancelSubscription_${userId}_${System.currentTimeMillis()}",
                    priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "cancelSubscription"),
                    operation = { 
                        kotlin.runCatching {
                            val currentSubscriptionResult = getUserSubscription(userId)
                            when (currentSubscriptionResult) {
                                is Result.Success -> {
                                    val cancellationResult = subscriptionMapper.processSubscriptionCancellation(
                                        currentSubscriptionResult.data,
                                        immediate
                                    )
                                    when (cancellationResult) {
                                        is Result.Success -> {
                                            val updateResult = updateUserSubscription(userId, cancellationResult.data)
                                            when (updateResult) {
                                                is Result.Success -> Unit
                                                is Result.Error -> throw Exception("Update failed", updateResult.exception)
                                                is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                                            }
                                        }
                                        is Result.Error -> throw Exception("Cancellation failed", cancellationResult.exception)
                                        is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                                    }
                                }
                                is Result.Error -> throw Exception("Failed to get current subscription", currentSubscriptionResult.exception)
                                is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                            }
                        }
                    },
                    entityType = "UserSubscription",
                    operationType = "cancelSubscription",
                    estimatedDuration = 5.seconds,
                    timeout = 15.seconds
                )
                
                val kotlinResult = priorityTaskScheduler.scheduleTask(task)
                kotlinResult.getOrThrow()
            }
            
            deduplicatedResult ?: throw Exception("Request deduplication returned null result")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun isTrialAvailable(): Result<Boolean> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "isTrialAvailable",
            entityType = "UserSubscription"
        ) {
            val userId = getCurrentUserIdSuspend()
            
            // INFRASTRUCTURE PHASE 1: Priority task scheduling
            val task = com.autogratuity.data.util.PriorityTask(
                id = "isTrialAvailable_${userId}_${System.currentTimeMillis()}",
                priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "isTrialAvailable"),
                operation = { 
                    kotlin.runCatching {
                        val subscriptionResult = getUserSubscription(userId)
                        when (subscriptionResult) {
                            is Result.Success -> {
                                val trialResult = subscriptionMapper.isTrialAvailable(subscriptionResult.data)
                                when (trialResult) {
                                    is Result.Success -> trialResult.data
                                    is Result.Error -> throw trialResult.exception
                                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                                }
                            }
                            is Result.Error -> throw subscriptionResult.exception
                            is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                        }
                    }
                },
                entityType = "UserSubscription",
                operationType = "isTrialAvailable",
                estimatedDuration = 500.milliseconds,
                timeout = 3.seconds
            )
            
            val kotlinResult = priorityTaskScheduler.scheduleTask(task)
            kotlinResult.getOrThrow()
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull() == true)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override fun observeTrialAvailable(): Flow<Boolean> {
        return try {
            // Use the safe current user ID method from AuthenticationStateCoordinator
            val userId = authStateCoordinator.getCurrentUserIdSafe()
            if (userId != null) {
                localDataSource.observeById(userId).map { subscription ->
                    subscription?.level == "free"
                }
            } else {
                flowOf(false)
            }
        } catch (_: Exception) {
            flowOf(false)
        }
    }

    override suspend fun startTrial(trialDurationDays: Int): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "startTrial",
            entityType = "UserSubscription"
        ) {
            val userId = getCurrentUserIdSuspend()
            
            // INFRASTRUCTURE PHASE 1: Session management
            Log.d(TAG, "startTrial: Managing session for user $userId")
            val session = sessionManager.getOrCreateSession(userId)
            Log.d(TAG, "startTrial: Using session ${session.sessionId}")

            // INFRASTRUCTURE PHASE 2: Request deduplication
            val requestKey = "startTrial:$userId"
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = requestKey,
                timeout = 10.seconds
            ) {
                // INFRASTRUCTURE PHASE 3: Priority task scheduling
                val task = com.autogratuity.data.util.PriorityTask(
                    id = "startTrial_${userId}_${System.currentTimeMillis()}",
                    priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "startTrial"),
                    operation = { 
                        kotlin.runCatching {
                            val currentSubscriptionResult = getUserSubscription(userId)
                            when (currentSubscriptionResult) {
                                is Result.Success -> {
                                    val currentSubscription = currentSubscriptionResult.data
                                    val trialSubscription = currentSubscription?.copy(
                                        level = "trial",
                                        isActive = true,
                                        startDate = OffsetDateTime.now(),
                                        expiryDate = OffsetDateTime.now().plusDays(trialDurationDays.toLong())
                                    ) ?: throw IllegalStateException("No subscription found")
                                    
                                    val updateResult = updateUserSubscription(userId, trialSubscription)
                                    when (updateResult) {
                                        is Result.Success -> Unit
                                        is Result.Error -> throw updateResult.exception
                                        is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                                    }
                                }
                                is Result.Error -> throw currentSubscriptionResult.exception
                                is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                            }
                        }
                    },
                    entityType = "UserSubscription",
                    operationType = "startTrial",
                    estimatedDuration = 3.seconds,
                    timeout = 10.seconds
                )
                
                val kotlinResult = priorityTaskScheduler.scheduleTask(task)
                kotlinResult.getOrThrow()
            }
            
            deduplicatedResult ?: throw Exception("Request deduplication returned null result")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun clearCache(userId: String): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "clearCache",
            entityType = "UserSubscription"
        ) {
            // INFRASTRUCTURE PHASE 1: Request deduplication
            val requestKey = "clearCache:$userId"
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = requestKey,
                timeout = 5.seconds
            ) {
                // INFRASTRUCTURE PHASE 2: Priority task scheduling
                val task = com.autogratuity.data.util.PriorityTask(
                    id = "clearCache_${userId}_${System.currentTimeMillis()}",
                    priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "clearCache"),
                    operation = { 
                        kotlin.runCatching {
                            localDataSource.deleteUserSubscription(userId)
                            Unit
                        }
                    },
                    entityType = "UserSubscription",
                    operationType = "clearCache",
                    estimatedDuration = 1.seconds,
                    timeout = 5.seconds
                )
                
                val kotlinResult = priorityTaskScheduler.scheduleTask(task)
                kotlinResult.getOrThrow()
            }
            
            deduplicatedResult ?: throw Exception("Request deduplication returned null result")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun invalidateCache(userId: String): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "invalidateCache",
            entityType = "UserSubscription"
        ) {
            // INFRASTRUCTURE PHASE 1: Request deduplication
            val requestKey = "invalidateCache:$userId"
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = requestKey,
                timeout = 5.seconds
            ) {
                // INFRASTRUCTURE PHASE 2: Priority task scheduling
                val task = com.autogratuity.data.util.PriorityTask(
                    id = "invalidateCache_${userId}_${System.currentTimeMillis()}",
                    priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "invalidateCache"),
                    operation = { 
                        kotlin.runCatching {
                            localDataSource.invalidateUserSubscription(userId)
                            Unit
                        }
                    },
                    entityType = "UserSubscription",
                    operationType = "invalidateCache",
                    estimatedDuration = 1.seconds,
                    timeout = 5.seconds
                )
                
                val kotlinResult = priorityTaskScheduler.scheduleTask(task)
                kotlinResult.getOrThrow()
            }
            
            deduplicatedResult ?: throw Exception("Request deduplication returned null result")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun prefetchCriticalData(): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "prefetchCriticalData",
            entityType = "UserSubscription"
        ) {
            val userId = getCurrentUserIdSuspend()
            
            // ✅ REMOVED: Cache warming calls removed from domain repositories
            // Cache warming is now handled by infrastructure components using DomainCacheSystem instances directly
            Log.d(TAG, "prefetchCriticalData: Cache warming handled by infrastructure layer")

            // INFRASTRUCTURE PHASE 2: Request deduplication (global prefetch)
            val requestKey = "prefetchCriticalData:global"
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = requestKey,
                timeout = 15.seconds
            ) {
                // INFRASTRUCTURE PHASE 3: Priority task scheduling
                val task = com.autogratuity.data.util.PriorityTask(
                    id = "prefetchCriticalData_${System.currentTimeMillis()}",
                    priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "prefetchCriticalData"),
                    operation = { 
                        kotlin.runCatching { 
                            val prefetchResult = getUserSubscription(userId)
                            when (prefetchResult) {
                                is Result.Success -> Unit
                                is Result.Error -> throw prefetchResult.exception
                                is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                            }
                        }
                    },
                    entityType = "UserSubscription",
                    operationType = "prefetchCriticalData",
                    estimatedDuration = 5.seconds,
                    timeout = 15.seconds
                )
                
                val kotlinResult = priorityTaskScheduler.scheduleTask(task)
                kotlinResult.getOrThrow()
            }
            
            deduplicatedResult ?: throw Exception("Request deduplication returned null result")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun getCacheMetrics(): Result<Map<String, Any>> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "getCacheMetrics",
            entityType = "UserSubscription"
        ) {
            // INFRASTRUCTURE PHASE 1: Priority task scheduling
            val task = com.autogratuity.data.util.PriorityTask(
                id = "getCacheMetrics_${System.currentTimeMillis()}",
                priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "getCacheMetrics"),
                operation = { 
                    kotlin.runCatching {
                        val metricsResult = localDataSource.getCacheMetrics()
                        when (metricsResult) {
                            is Result.Success -> metricsResult.data
                            is Result.Error -> throw metricsResult.exception
                            is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                        }
                    }
                },
                entityType = "UserSubscription",
                operationType = "getCacheMetrics",
                estimatedDuration = 500.milliseconds,
                timeout = 3.seconds
            )
            
            val kotlinResult = priorityTaskScheduler.scheduleTask(task)
            kotlinResult.getOrThrow()
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull() ?: emptyMap())
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun initialize(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "initialize: Initializing SubscriptionRepository")
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun cleanup(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "cleanup: Cleaning up SubscriptionRepository")
            localDataSource.clearAllSubscriptions()
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun getSubscriptionHistory(): Result<List<Map<String, Any>>> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "getSubscriptionHistory",
            entityType = "UserSubscription"
        ) {
            val userId = getCurrentUserIdSuspend()
            
            // INFRASTRUCTURE PHASE 1: Request deduplication
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userSubscriptionHistory(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                // INFRASTRUCTURE PHASE 2: Priority task scheduling
                val task = com.autogratuity.data.util.PriorityTask(
                    id = "getSubscriptionHistory_${userId}_${System.currentTimeMillis()}",
                    priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "getSubscriptionHistory"),
                    operation = { 
                        kotlin.runCatching {
                            // For now, return empty list - would need to implement history tracking
                            emptyList<Map<String, Any>>()
                        }
                    },
                    entityType = "UserSubscription",
                    operationType = "getSubscriptionHistory",
                    estimatedDuration = 2.seconds,
                    timeout = 8.seconds
                )
                
                val kotlinResult = priorityTaskScheduler.scheduleTask(task)
                kotlinResult.getOrThrow()
            }
            
            deduplicatedResult ?: throw Exception("Request deduplication returned null result")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull() ?: emptyList())
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun validateSubscription(subscription: UserSubscription): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "validateSubscription",
            entityType = "UserSubscription"
        ) {
            // INFRASTRUCTURE PHASE 1: Priority task scheduling
            val task = com.autogratuity.data.util.PriorityTask(
                id = "validateSubscription_${System.currentTimeMillis()}",
                priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "validateSubscription"),
                operation = { 
                    kotlin.runCatching {
                        val validationResult = subscriptionMapper.validateUserSubscription(subscription)
                        when (validationResult) {
                            is Result.Success -> Unit
                            is Result.Error -> throw validationResult.exception
                            is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                        }
                    }
                },
                entityType = "UserSubscription",
                operationType = "validateSubscription",
                estimatedDuration = 1.seconds,
                timeout = 5.seconds
            )
            
            val kotlinResult = priorityTaskScheduler.scheduleTask(task)
            kotlinResult.getOrThrow()
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun handleBillingSystemUpdate(userId: String, billingData: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "handleBillingSystemUpdate",
            entityType = "UserSubscription"
        ) {
            // INFRASTRUCTURE PHASE 1: Session management
            Log.d(TAG, "handleBillingSystemUpdate: Managing session for user $userId")
            val session = sessionManager.getOrCreateSession(userId)
            Log.d(TAG, "handleBillingSystemUpdate: Using session ${session.sessionId}")

            // INFRASTRUCTURE PHASE 2: Request deduplication
            val requestKey = "handleBillingSystemUpdate:$userId"
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = requestKey,
                timeout = 10.seconds
            ) {
                // INFRASTRUCTURE PHASE 3: Priority task scheduling
                val task = com.autogratuity.data.util.PriorityTask(
                    id = "handleBillingSystemUpdate_${userId}_${System.currentTimeMillis()}",
                    priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "handleBillingSystemUpdate"),
                    operation = { 
                        kotlin.runCatching {
                            val currentSubscriptionResult = getUserSubscription(userId)
                            when (currentSubscriptionResult) {
                                is Result.Success -> {
                                    val currentSubscription = currentSubscriptionResult.data
                                    val updatedSubscription = currentSubscription?.copy(
                                        verification = currentSubscription.verification?.copy(
                                            lastVerified = OffsetDateTime.now(),
                                            status = billingData["status"]?.toString() ?: "verified"
                                        )
                                    ) ?: throw IllegalStateException("No subscription found")

                                    val updateResult = updateUserSubscription(userId, updatedSubscription)
                                    when (updateResult) {
                                        is Result.Success -> Unit
                                        is Result.Error -> throw Exception("Update failed", updateResult.exception)
                                        is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                                    }
                                }
                                is Result.Error -> throw Exception("Failed to get current subscription", currentSubscriptionResult.exception)
                                is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                            }
                        }
                    },
                    entityType = "UserSubscription",
                    operationType = "handleBillingSystemUpdate",
                    estimatedDuration = 3.seconds,
                    timeout = 10.seconds
                )
                
                val kotlinResult = priorityTaskScheduler.scheduleTask(task)
                kotlinResult.getOrThrow()
            }
            
            deduplicatedResult ?: throw Exception("Request deduplication returned null result")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun verifySubscription(): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "verifySubscription",
            entityType = "UserSubscription"
        ) {
            val userId = getCurrentUserIdSuspend()
            
            // INFRASTRUCTURE PHASE 1: Session management
            Log.d(TAG, "verifySubscription: Managing session for user $userId")
            val session = sessionManager.getOrCreateSession(userId)
            Log.d(TAG, "verifySubscription: Using session ${session.sessionId}")

            // INFRASTRUCTURE PHASE 1.5: Cache warming removed to prevent performance regression  
            // Log.d(TAG, "verifySubscription: Cache warming disabled to prevent duplicate requests")

            // INFRASTRUCTURE PHASE 2: Request deduplication
            val deduplicatedResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userSubscriptionVerify(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                // INFRASTRUCTURE PHASE 3: Priority task scheduling
                val task = com.autogratuity.data.util.PriorityTask(
                    id = "verifySubscription_${userId}_${System.currentTimeMillis()}",
                    priority = priorityTaskScheduler.getPriorityForOperation("UserSubscription", "verifySubscription"),
                    operation = { 
                        kotlin.runCatching {
                            val subscriptionResult = getUserSubscription(userId)
                            when (subscriptionResult) {
                                is Result.Success -> {
                                    // Verification logic would go here
                                    Log.d(TAG, "verifySubscription: Subscription verified for user $userId")
                                    Unit
                                }
                                is Result.Error -> throw Exception("Failed to get subscription for verification", subscriptionResult.exception)
                                is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                            }
                        }
                    },
                    entityType = "UserSubscription",
                    operationType = "verifySubscription",
                    estimatedDuration = 3.seconds,
                    timeout = 8.seconds
                )
                
                val kotlinResult = priorityTaskScheduler.scheduleTask(task)
                kotlinResult.getOrThrow()
            }
            
            deduplicatedResult ?: throw Exception("Request deduplication returned null result")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    // ===== SIMPLIFIED REPOSITORY METHODS - DELEGATING TO REMOTE DATA SOURCE =====

    /**
     * Enhanced method for subscription validation.
     * Now delegates to RemoteDataSource per clarity.md requirements.
     */
    suspend fun validateSubscriptionWithQuery(userId: String): Result<Boolean> = withContext(ioDispatcher) {
        return@withContext repositoryErrorHandler.handleSuspendFunction(
            operationName = "validateSubscriptionWithQuery",
            entityType = "UserSubscription"
        ) {
            // INFRASTRUCTURE PHASE 1: Session management
            Log.d(TAG, "validateSubscriptionWithQuery: Managing session for user $userId")
            val session = sessionManager.getOrCreateSession(userId)
            Log.d(TAG, "validateSubscriptionWithQuery: Using session ${session.sessionId}")

            // INFRASTRUCTURE PHASE 2: Request deduplication
            val validationResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userSubscriptionValidate(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                // Delegate to RemoteDataSource for QueryBuilder operations per clarity.md
                val queryResult = remoteDataSource.validateSubscriptionWithQuery(userId)
                
                when (queryResult) {
                    is Result.Success -> {
                        val snapshot = queryResult.data
                        val hasActiveSubscription = !snapshot.isEmpty
                        Log.d(TAG, "validateSubscriptionWithQuery: Found ${snapshot.size()} active subscriptions for user $userId")
                        Result.Success(hasActiveSubscription)
                    }
                    is Result.Error -> {
                        Log.e(TAG, "validateSubscriptionWithQuery: Query failed for user $userId", queryResult.exception)
                        queryResult
                    }
                    is Result.Loading -> {
                        Log.w(TAG, "validateSubscriptionWithQuery: Unexpected Loading state from RemoteDataSource")
                        Result.Error(IllegalStateException("RemoteDataSource returned Loading unexpectedly"))
                    }
                }
            }
            
            // Return the validation result (handle null case)
            validationResult ?: Result.Error(Exception("Request deduplication returned null result"))
        }.getOrThrow() // Extract result from RepositoryErrorHandler Result wrapper
    }

    /**
     * Query user subscriptions by status.
     * Now delegates to RemoteDataSource per clarity.md requirements.
     */
    suspend fun queryUserSubscriptionsByStatus(userId: String, status: String, limit: Int = 10): Result<List<UserSubscription>> = withContext(ioDispatcher) {
        return@withContext repositoryErrorHandler.handleSuspendFunction(
            operationName = "queryUserSubscriptionsByStatus",
            entityType = "UserSubscription"
        ) {
            // Delegate to RemoteDataSource for QueryBuilder operations per clarity.md
            val queryResult = remoteDataSource.queryUserSubscriptionDtosByStatus(userId, status, limit)
            when (queryResult) {
                is Result.Success -> {
                    // Convert DTOs to domain models using Mapper
                    val subscriptions = mutableListOf<UserSubscription>()
                    for (dto in queryResult.data) {
                        val mapperResult = subscriptionMapper.dtoToSsot(dto)
                        when (mapperResult) {
                            is Result.Success -> {
                                mapperResult.data?.let { subscriptions.add(it) }
                            }
                            is Result.Error -> {
                                Log.w(TAG, "Error mapping subscription DTO", mapperResult.exception)
                            }
                            is Result.Loading -> {
                                Log.w(TAG, "Unexpected Loading state from mapper")
                            }
                        }
                    }
                    
                    Log.d(TAG, "queryUserSubscriptionsByStatus: Found ${subscriptions.size} subscriptions for user $userId with status $status")
                    Result.Success(subscriptions)
                }
                is Result.Error -> {
                    Log.e(TAG, "queryUserSubscriptionsByStatus: Query failed for user $userId", queryResult.exception)
                    queryResult
                }
                is Result.Loading -> {
                    Log.w(TAG, "queryUserSubscriptionsByStatus: Unexpected Loading state from RemoteDataSource")
                    Result.Error(IllegalStateException("RemoteDataSource returned Loading unexpectedly"))
                }
            }
        }.getOrThrow() // Extract result from RepositoryErrorHandler Result wrapper
    }

    /**
     * Query active subscriptions.
     * Now delegates to RemoteDataSource per clarity.md requirements.
     */
    suspend fun queryActiveSubscriptions(userId: String): Result<List<UserSubscription>> = withContext(ioDispatcher) {
        return@withContext repositoryErrorHandler.handleSuspendFunction(
            operationName = "queryActiveSubscriptions",
            entityType = "UserSubscription"
        ) {
            // Delegate to RemoteDataSource for QueryBuilder operations per clarity.md
            val queryResult = remoteDataSource.queryActiveSubscriptionDtos(userId, 10)
            when (queryResult) {
                is Result.Success -> {
                    // Convert DTOs to domain models using Mapper
                    val subscriptions = mutableListOf<UserSubscription>()
                    for (dto in queryResult.data) {
                        val mapperResult = subscriptionMapper.dtoToSsot(dto)
                        when (mapperResult) {
                            is Result.Success -> {
                                mapperResult.data?.let { subscriptions.add(it) }
                            }
                            is Result.Error -> {
                                Log.w(TAG, "Error mapping subscription DTO", mapperResult.exception)
                            }
                            is Result.Loading -> {
                                Log.w(TAG, "Unexpected Loading state from mapper")
                            }
                        }
                    }
                    
                    Log.d(TAG, "queryActiveSubscriptions: Found ${subscriptions.size} active subscriptions for user $userId")
                    Result.Success(subscriptions)
                }
                is Result.Error -> {
                    Log.e(TAG, "queryActiveSubscriptions: Query failed for user $userId", queryResult.exception)
                    queryResult
                }
                is Result.Loading -> {
                    Log.w(TAG, "queryActiveSubscriptions: Unexpected Loading state from RemoteDataSource")
                    Result.Error(IllegalStateException("RemoteDataSource returned Loading unexpectedly"))
                }
            }
        }.getOrThrow() // Extract result from RepositoryErrorHandler Result wrapper
    }

    // =============================================================================
    // AUTHENTICATION INFRASTRUCTURE
    // =============================================================================

    /**
     * Verify authentication state for critical operations
     */
    private fun verifyAuthenticationForCriticalOperation(operationName: String, userId: String): Boolean {
        val authState = authStateCoordinator.authReadyState.value
        
        return when (authState) {
            is com.autogratuity.data.util.AuthenticationStateCoordinator.AuthReadyState.Authenticated -> {
                if (authState.userId == userId) {
                    Log.d(TAG, "$operationName: Authentication verified for user $userId")
                    ClarityArchitectureMonitor.addSessionEvent("auth_verified:$operationName:$userId")
                    true
                } else {
                    Log.w(TAG, "$operationName: User ID mismatch - expected $userId, got ${authState.userId}")
                    ClarityArchitectureMonitor.addSessionEvent("auth_user_mismatch:$operationName:$userId:${authState.userId}")
                    false
                }
            }
            else -> {
                Log.w(TAG, "$operationName: Authentication not ready for user $userId, state: $authState")
                ClarityArchitectureMonitor.addSessionEvent("auth_not_ready:$operationName:$userId:$authState")
                false
            }
        }
    }

    // =============================================================================
    // CACHE WARMING INFRASTRUCTURE - REMOVED
    // =============================================================================

    // ✅ REMOVED: Cache warming methods removed from domain repositories
    // Cache warming is now handled by infrastructure components using DomainCacheSystem instances directly



}
