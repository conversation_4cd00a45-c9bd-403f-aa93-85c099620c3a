/*
 * Delivery_stats.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

import kotlin.Double
import java.time.OffsetDateTime

/**
 * Represents aggregated statistics related to deliveries, often stored within Address or UserProfile.
 */
data class Delivery_stats(
    /** Total number of deliveries. */
    val deliveryCount: Long? = null,
    /** Number of deliveries that included a tip. */
    val tipCount: Long? = null,
    /** Total amount of tips received at this address. */
    val totalTips: Double? = null,
    /** The highest single tip amount received. */
    val highestTip: Double? = null,
    /** Count of deliveries with pending status (if tracked). */
    val pendingCount: Long? = null,
    /** Average delivery time in minutes (if tracked). */
    val averageTimeMinutes: Double? = null,
    /** Timestamp of the last delivery recorded. */
    val lastDeliveryDate: OffsetDateTime? = null,
    /** System-derived average tip amount for this address. Calculated from actual delivery records. Should not be set directly via bulk import. */
    val averageTipAmount: Double? = null,
    /** System-derived timestamp of the last recorded delivery to this address. Calculated from actual delivery records. Should not be set directly via bulk import. */
    val lastDeliveryTimestamp: OffsetDateTime? = null
)
