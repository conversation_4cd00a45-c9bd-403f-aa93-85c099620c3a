import { initializeApp } from 'firebase-admin/app';
import { runFlow } from '@genkit-ai/flow';
import { getFirestore } from 'firebase-admin/firestore';
import * as admin from 'firebase-admin';

// Import all the flows we want to test
// ❌ REMOVED: Legacy pending tips import - collections eliminated
import { updateAddressDeliveryStatsFlow } from './flows/address-stats-updater';
import { processUploadedGeoJsonFile } from './flows/process-uploaded-geojson';
import { parseImportDataWithLogging } from './flows/import-parsing';
import { setManualAddressDndOverrideFlow } from './flows/set-manual-address-dnd-override';

// Import schemas for validation
// ❌ REMOVED: Legacy pending tips schema import - collections eliminated
import { AddressStatsUpdateInputSchema } from './flows/address-stats-updater';
import { GcsEventDataInputSchema } from './flows/process-uploaded-geojson';
import { SetManualAddressDndInputSchema } from './flows/set-manual-address-dnd-override';

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
    initializeApp();
}

interface TestResult {
    functionName: string;
    testName: string;
    success: boolean;
    result?: any;
    error?: string;
    duration: number;
}

class CloudFunctionTester {
    private results: TestResult[] = [];
    private firestore = getFirestore();

    async runAllTests(): Promise<TestResult[]> {
        console.log('🚀 Starting Cloud Function Testing Simulation...\n');

        // Test each function category
        // ❌ REMOVED: testPendingTipsFlow() - legacy collections eliminated
        await this.testAddressStatsFlow();
        await this.testGeoJsonProcessing();
        await this.testImportParsing();
        await this.testManualDndOverride();
        await this.testFirestoreReadWrite();

        this.printSummary();
        return this.results;
    }

    // ❌ REMOVED: testPendingTipsFlow method - legacy collections eliminated
    // The proper domain model uses tipAmount=null for pending deliveries
    // Address Stats Updater already handles this correctly

    private async testAddressStatsFlow(): Promise<void> {
        console.log('📊 Testing Address Stats Update...');
        
        const testInput = {
            userId: 'test-user-123',
            addressId: 'test-address-456'
        };

        await this.runTest(
            'onDeliveryWrittenUpdateAddressStats',
            'Update Address Delivery Stats',
            async () => {
                const validation = AddressStatsUpdateInputSchema.safeParse(testInput);
                if (!validation.success) {
                    throw new Error(`Invalid test data: ${validation.error.message}`);
                }

                return await runFlow(updateAddressDeliveryStatsFlow, validation.data);
            }
        );
    }

    private async testGeoJsonProcessing(): Promise<void> {
        console.log('🗺️ Testing GeoJSON File Processing...');
        
        const testInput = {
            bucket: 'autogratuity-me.firebasestorage.app',
            file: 'user-uploads/test-user-123/test-geojson.json'
        };

        await this.runTest(
            'onGeoJsonFileFinalized',
            'Process Uploaded GeoJSON File',
            async () => {
                const validation = GcsEventDataInputSchema.safeParse(testInput);
                if (!validation.success) {
                    throw new Error(`Invalid test data: ${validation.error.message}`);
                }

                return await runFlow(processUploadedGeoJsonFile, validation.data);
            }
        );
    }

    private async testImportParsing(): Promise<void> {
        console.log('📥 Testing Import Data Parsing...');
        
        const testText = `
        Order #12345
        Delivery to: 123 Main St, City, State 12345
        Tip: $5.50
        Platform: Shipt
        Date: 2024-01-15
        `;

        await this.runTest(
            'testParseImportFlow',
            'Parse Import Data with Logging',
            async () => {
                return await runFlow(parseImportDataWithLogging, testText.trim());
            }
        );
    }

    // Google API testing removed - unused functionality

    // Dashboard batch testing removed - unused functionality

    private async testManualDndOverride(): Promise<void> {
        console.log('🚫 Testing Manual DND Override...');
        
        const testInput = {
            userId: 'test-user-123',
            addressId: 'test-address-456',
            desiredState: 'FORCE_DND'
        };

        await this.runTest(
            'setManualAddressDndOverride',
            'Set Manual Address DND Override',
            async () => {
                const validation = SetManualAddressDndInputSchema.safeParse(testInput);
                if (!validation.success) {
                    throw new Error(`Invalid test data: ${validation.error.message}`);
                }

                return await runFlow(setManualAddressDndOverrideFlow, validation.data);
            }
        );
    }

    private async testFirestoreReadWrite(): Promise<void> {
        console.log('🔥 Testing Firestore Read/Write Operations...');
        
        const testDocId = `test-doc-${Date.now()}`;
        const testData = {
            testField: 'test-value',
            timestamp: new Date(),
            userId: 'test-user-123'
        };

        // Test Write
        await this.runTest(
            'Firestore',
            'Write Test Document',
            async () => {
                await this.firestore.collection('test-collection').doc(testDocId).set(testData);
                return { success: true, docId: testDocId };
            }
        );

        // Test Read
        await this.runTest(
            'Firestore',
            'Read Test Document',
            async () => {
                const doc = await this.firestore.collection('test-collection').doc(testDocId).get();
                if (!doc.exists) {
                    throw new Error('Document not found');
                }
                return doc.data();
            }
        );

        // Test Delete (cleanup)
        await this.runTest(
            'Firestore',
            'Delete Test Document',
            async () => {
                await this.firestore.collection('test-collection').doc(testDocId).delete();
                return { success: true, deleted: testDocId };
            }
        );
    }

    private async runTest(
        functionName: string,
        testName: string,
        testFunction: () => Promise<any>
    ): Promise<void> {
        const startTime = Date.now();
        
        try {
            console.log(`  ⏳ Running: ${testName}...`);
            const result = await testFunction();
            const duration = Date.now() - startTime;
            
            this.results.push({
                functionName,
                testName,
                success: true,
                result,
                duration
            });
            
            console.log(`  ✅ ${testName} - Success (${duration}ms)`);
        } catch (error: any) {
            const duration = Date.now() - startTime;
            
            this.results.push({
                functionName,
                testName,
                success: false,
                error: error.message,
                duration
            });
            
            console.log(`  ❌ ${testName} - Failed: ${error.message} (${duration}ms)`);
        }
        
        console.log(''); // Add spacing
    }

    private printSummary(): void {
        console.log('📋 Test Summary:');
        console.log('================');
        
        const successful = this.results.filter(r => r.success).length;
        const failed = this.results.filter(r => !r.success).length;
        const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
        
        console.log(`Total Tests: ${this.results.length}`);
        console.log(`Successful: ${successful}`);
        console.log(`Failed: ${failed}`);
        console.log(`Total Duration: ${totalDuration}ms`);
        console.log('');
        
        // Group by function
        const byFunction = this.results.reduce((acc, result) => {
            if (!acc[result.functionName]) {
                acc[result.functionName] = [];
            }
            acc[result.functionName].push(result);
            return acc;
        }, {} as Record<string, TestResult[]>);
        
        Object.entries(byFunction).forEach(([functionName, results]) => {
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            console.log(`${functionName}: ${successCount}/${totalCount} tests passed`);
            
            results.forEach(result => {
                const status = result.success ? '✅' : '❌';
                console.log(`  ${status} ${result.testName} (${result.duration}ms)`);
                if (!result.success && result.error) {
                    console.log(`    Error: ${result.error}`);
                }
            });
            console.log('');
        });
    }
}

// Main execution function
async function main() {
    const tester = new CloudFunctionTester();
    const results = await tester.runAllTests();
    
    // Exit with error code if any tests failed
    const hasFailures = results.some(r => !r.success);
    process.exit(hasFailures ? 1 : 0);
}

// Export for use in other scripts
export { CloudFunctionTester, TestResult };

// Run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}
