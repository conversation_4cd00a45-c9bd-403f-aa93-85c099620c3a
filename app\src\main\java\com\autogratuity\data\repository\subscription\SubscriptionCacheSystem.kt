package com.autogratuity.data.repository.subscription

import android.util.Log
import com.autogratuity.data.repository.core.AtomicCacheSystem
import com.autogratuity.data.repository.core.CacheSource
import com.autogratuity.data.repository.core.SsotOwnership
import com.autogratuity.domain.model.UserSubscription
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes
import kotlin.time.TimeSource

/**
 * A+ Level domain-specific cache system for Subscription management following 2025 Kotlin standards.
 * 
 * Provides comprehensive subscription caching with:
 * - Subscription lifecycle management and state tracking
 * - Billing cycle awareness and expiration monitoring  
 * - Cloud function integration for payment processing updates
 * - Rich metadata for subscription analytics and billing intelligence
 * - Reactive programming for real-time subscription status updates
 * - Advanced invalidation strategies for subscription state changes
 * - Trial and upgrade/downgrade tracking
 */
@Singleton
class SubscriptionCacheSystem @Inject constructor(
    timeSource: TimeSource,
    private val ioDispatcher: kotlinx.coroutines.CoroutineDispatcher,
    private val applicationScope: kotlinx.coroutines.CoroutineScope
) : AtomicCacheSystem<String, UserSubscription>(timeSource) {
    
    private val TAG = "SubscriptionCacheSystem"
    
    // Reactive flows for real-time subscription updates
    private val _currentSubscriptionFlow = MutableStateFlow<UserSubscription?>(null)
    val currentSubscriptionFlow: StateFlow<UserSubscription?> = _currentSubscriptionFlow.asStateFlow()
    
    private val _subscriptionStatusFlow = MutableStateFlow<SubscriptionStatus>(SubscriptionStatus.UNKNOWN)
    val subscriptionStatusFlow: StateFlow<SubscriptionStatus> = _subscriptionStatusFlow.asStateFlow()
    
    private val _proUserFlow = MutableStateFlow(false)
    val proUserFlow: StateFlow<Boolean> = _proUserFlow.asStateFlow()
    
    private val _subscriptionLoadingFlow = MutableStateFlow(false)
    val subscriptionLoadingFlow: StateFlow<Boolean> = _subscriptionLoadingFlow.asStateFlow()
    
    private val _subscriptionErrorFlow = MutableStateFlow<String?>(null)
    val subscriptionErrorFlow: StateFlow<String?> = _subscriptionErrorFlow.asStateFlow()
    
    // Domain-specific configuration
    override val defaultTtl: Duration = 1.hours  // Shorter TTL for subscription data (billing sensitive)
    override val maxCacheSize: Int = 20         // Limited subscriptions per device

    // ✅ SMART CACHE TTL: DND-aware subscription caching
    private val proSubscriptionTtl: Duration = 15.minutes  // Pro status affects DND rules
    private val activeSubscriptionTtl: Duration = 30.minutes // Active subscriptions change DND behavior
    private val expiredSubscriptionTtl: Duration = 2.hours   // Expired subscriptions are more stable
    private val trialSubscriptionTtl: Duration = 10.minutes  // Trial status may change frequently

    /**
     * ✅ SMART CACHE TTL: Determine appropriate TTL based on subscription status
     * Pro and active subscriptions get shorter TTL since they affect DND rule availability
     */
    private fun getSmartSubscriptionTtl(subscription: UserSubscription, metadata: Map<String, Any> = emptyMap()): Duration {
        val isProUser = subscription.isActive == true &&
                       (subscription.level == "pro" || subscription.level == "premium")
        val isActive = subscription.isActive == true
        val isExpired = subscription.expiryDate?.let {
            Instant.fromEpochMilliseconds(it.toInstant().toEpochMilli()) <= Clock.System.now()
        } ?: false
        val isTrial = subscription.verification?.lastVerified != null
        val isNearingExpiration = metadata["isNearingExpiration"] as? Boolean ?: false

        return when {
            // Pro users get shortest TTL (affects DND custom rules)
            isProUser -> proSubscriptionTtl

            // Trial subscriptions may change to pro soon
            isTrial -> trialSubscriptionTtl

            // Active subscriptions affect DND behavior
            isActive && !isExpired -> activeSubscriptionTtl

            // Subscriptions nearing expiration may change soon
            isNearingExpiration -> proSubscriptionTtl

            // Expired subscriptions are more stable
            isExpired -> expiredSubscriptionTtl

            // Everything else uses default TTL
            else -> defaultTtl
        }
    }
    
    companion object {
        // Subscription expiration warning thresholds
        private val EXPIRATION_WARNING_THRESHOLD = 24.hours * 7 // 1 week
        private val EXPIRATION_CRITICAL_THRESHOLD = 24.hours * 3 // 3 days
        
        // SSOT field ownership for subscription fields
        private val SUBSCRIPTION_FIELD_OWNERSHIP = mapOf(
            // Cloud-owned fields (billing system manages these)
            "subscription_type" to SsotOwnership.CLOUD_OWNED,
            "is_pro_user" to SsotOwnership.CLOUD_OWNED,
            "expiration_date" to SsotOwnership.CLOUD_OWNED,
            "billing_cycle" to SsotOwnership.CLOUD_OWNED,
            "payment_status" to SsotOwnership.CLOUD_OWNED,
            "auto_renew" to SsotOwnership.CLOUD_OWNED,
            "trial_end_date" to SsotOwnership.CLOUD_OWNED,
            "purchase_date" to SsotOwnership.READ_ONLY,
            "last_payment_date" to SsotOwnership.READ_ONLY,
            
            // Shared fields (both client and cloud can modify)
            "notifications_enabled" to SsotOwnership.SHARED,
            "preferences" to SsotOwnership.SHARED
        )
    }
    
    
    
    enum class SubscriptionStatus {
        UNKNOWN,
        FREE,
        TRIAL,
        ACTIVE_PRO,
        EXPIRED,
        CANCELLED,
        SUSPENDED,
        GRACE_PERIOD
    }
    
    /**
     * Domain-specific atomic operation: Cache subscription with billing intelligence
     */
    suspend fun cacheSubscription(
        userId: String,
        subscription: UserSubscription,
        source: CacheSource = CacheSource.FIRESTORE
    ) {
        setLoading(true)
        setError(null)
        
        try {
            // Extract rich metadata for billing analytics
            val metadata = buildSubscriptionMetadata(userId, subscription, source)
            
            // ✅ SMART CACHE TTL: Use subscription-aware TTL
            val smartTtl = getSmartSubscriptionTtl(subscription, metadata)
            put(userId, subscription, smartTtl, metadata, source)
            
            // Update reactive flows
            _currentSubscriptionFlow.value = subscription
            _proUserFlow.value = subscription.isActive == true
            _subscriptionStatusFlow.value = determineSubscriptionStatus(subscription)
            
            Log.d(TAG, "Cached subscription for $userId from $source (status: ${_subscriptionStatusFlow.value})")
        } catch (e: Exception) {
            Log.e(TAG, "Error caching subscription for $userId", e)
            setError("Failed to cache subscription: ${e.message}")
        } finally {
            setLoading(false)
        }
    }
    
    /**
     * Domain-specific atomic operation: Update subscription from billing system
     */
    suspend fun updateFromBillingSystem(
        userId: String,
        billingSubscription: UserSubscription
    ) {
        val existingEntry = getWithMetadata(userId)
        
        if (existingEntry != null) {
            val (existingSubscription, _) = existingEntry
            
            // Merge billing-managed fields while preserving user preferences
            val mergedSubscription = mergeBillingUpdate(existingSubscription, billingSubscription)
            
            // Update cache with merged subscription
            cacheSubscription(userId, mergedSubscription, CacheSource.CLOUD_FUNCTION)
            
            Log.d(TAG, "Updated subscription from billing system for user $userId")
        } else {
            // No existing subscription, cache billing subscription directly
            cacheSubscription(userId, billingSubscription, CacheSource.CLOUD_FUNCTION)
            
            Log.d(TAG, "Cached new billing subscription for user $userId")
        }
    }
    
    /**
     * Domain-specific atomic operation: Process subscription upgrade/downgrade
     */
    suspend fun processSubscriptionChange(
        userId: String,
        newSubscriptionType: String,
        effectiveDate: Date? = null
    ) {
        val existingEntry = getWithMetadata(userId)
        
        if (existingEntry != null) {
            val (existingSubscription, metadata) = existingEntry
            
            // Create updated subscription with change tracking
            val updatedSubscription = existingSubscription.copy(
                level = newSubscriptionType,
                isActive = newSubscriptionType != "free"
            )
            
            val changeMetadata = metadata + mapOf(
                "changeType" to "subscription_change",
                "previousType" to (existingSubscription.level ?: "free"),
                "newType" to newSubscriptionType,
                "changeDate" to (effectiveDate?.time ?: Clock.System.now().toEpochMilliseconds()),
                "lastModified" to Clock.System.now().toEpochMilliseconds()
            )
            
            // ✅ SMART CACHE TTL: Use subscription-aware TTL for changes
            val smartTtl = getSmartSubscriptionTtl(updatedSubscription, changeMetadata)
            put(userId, updatedSubscription, smartTtl, changeMetadata, CacheSource.CLOUD_FUNCTION)
            
            // Update reactive flows
            _currentSubscriptionFlow.value = updatedSubscription
            _proUserFlow.value = updatedSubscription.isActive == true
            _subscriptionStatusFlow.value = determineSubscriptionStatus(updatedSubscription)
            
            Log.d(TAG, "Processed subscription change for $userId: ${existingSubscription.level} -> $newSubscriptionType")
        } else {
            Log.w(TAG, "Cannot process subscription change for non-existent user $userId")
            setError("Subscription not found for user $userId")
        }
    }
    
    /**
     * Domain-specific operation: Get subscriptions nearing expiration
     */
    suspend fun getSubscriptionsNearingExpiration(withinHours: Int = 168): List<Pair<String, UserSubscription>> {
        val expirationThreshold = Clock.System.now().plus(withinHours.hours)
        
        return observeAll().first().entries
            .filter { (_, subscription) ->
                subscription.expiryDate?.let { expiryDate ->
                    Instant.fromEpochMilliseconds(expiryDate.toInstant().toEpochMilli()) <= expirationThreshold
                } == true
            }
            .map { it.key to it.value }
    }
    
    /**
     * Domain-specific operation: Get expired subscriptions
     */
    suspend fun getExpiredSubscriptions(): List<Pair<String, UserSubscription>> {
        val now = Clock.System.now()
        
        return observeAll().first().entries
            .filter { (_, subscription) ->
                subscription.expiryDate?.let { expiryDate ->
                    Instant.fromEpochMilliseconds(expiryDate.toInstant().toEpochMilli()) <= now
                } == true
            }
            .map { it.key to it.value }
    }
    
    /**
     * Domain-specific operation: Get subscriptions by type
     */
    suspend fun getSubscriptionsByType(subscriptionType: String): List<Pair<String, UserSubscription>> {
        return observeAll().first().entries
            .filter { (_, subscription) -> subscription.level == subscriptionType }
            .map { it.key to it.value }
    }
    
    /**
     * Domain-specific operation: Get trial subscriptions
     */
    suspend fun getTrialSubscriptions(): List<Pair<String, UserSubscription>> {
        return observeAll().first().entries
            .filter { (_, subscription) -> subscription.verification?.lastVerified != null }
            .map { it.key to it.value }
    }
    
    /**
     * Domain-specific operation: Invalidate subscription for user
     */
    suspend fun invalidateUserSubscription(userId: String) {
        remove(userId)
        
        // Reset reactive flows
        _currentSubscriptionFlow.value = null
        _proUserFlow.value = false
        _subscriptionStatusFlow.value = SubscriptionStatus.UNKNOWN
        _subscriptionErrorFlow.value = null
        
        Log.d(TAG, "Invalidated subscription for user $userId")
    }
    
    /**
     * Domain-specific operation: Invalidate subscriptions by status
     */
    suspend fun invalidateSubscriptionsByStatus(status: SubscriptionStatus) {
        invalidateByPredicate { _, metadata ->
            metadata["subscriptionStatus"] == status.name
        }
        
        Log.d(TAG, "Invalidated subscriptions with status: $status")
    }
    
    /**
     * Domain-specific operation: Invalidate expired subscriptions
     */
    suspend fun invalidateExpiredSubscriptions() {
        val now = Clock.System.now()
        
        invalidateByPredicate { _, metadata ->
            val expirationTime = metadata["expirationTime"] as? Long ?: return@invalidateByPredicate false
            Instant.fromEpochMilliseconds(expirationTime) <= now
        }
        
        Log.d(TAG, "Invalidated expired subscriptions")
    }
    
    /**
     * Observe subscription status reactively
     */
    fun observeSubscriptionStatus(): Flow<SubscriptionStatus> = subscriptionStatusFlow
    
    /**
     * Observe pro user status reactively
     */
    fun observeProUserStatus(): Flow<Boolean> = proUserFlow
    
    /**
     * Observe current subscription reactively
     */
    fun observeCurrentSubscription(): Flow<UserSubscription?> = currentSubscriptionFlow
    
    /**
     * Observe subscription loading state
     */
    fun observeSubscriptionLoading(): Flow<Boolean> = subscriptionLoadingFlow
    
    /**
     * Observe subscription errors
     */
    fun observeSubscriptionErrors(): Flow<String?> = subscriptionErrorFlow.map { it }.distinctUntilChanged()
    
    /**
     * Check if subscription is nearing expiration
     */
    suspend fun isSubscriptionNearingExpiration(userId: String, withinHours: Int = 168): Boolean {
        val subscription = get(userId) ?: return false
        val expirationThreshold = Clock.System.now().plus(withinHours.hours)
        
        return subscription.expiryDate?.let { expiryDate ->
            Instant.fromEpochMilliseconds(expiryDate.toInstant().toEpochMilli()) <= expirationThreshold
        } == true
    }
    
    /**
     * Get comprehensive subscription cache metrics
     */
    suspend fun getSubscriptionCacheMetrics(): Map<String, Any> {
        val baseMetrics = getDetailedStats()
        
        val subscriptionsData = observeAll().first().values
        val proUserCount = subscriptionsData.count { it.isActive == true }
        val trialCount = subscriptionsData.count { it.verification?.lastVerified != null }
        val expiredCount = getExpiredSubscriptions().size
        val nearingExpirationCount = getSubscriptionsNearingExpiration().size
        
        val subscriptionTypeDistribution = subscriptionsData
            .groupingBy { it.level ?: "free" }
            .eachCount()
        
        return baseMetrics + mapOf(
            "totalSubscriptions" to subscriptionsData.size,
            "proUsers" to proUserCount,
            "freeUsers" to (subscriptionsData.size - proUserCount),
            "trialUsers" to trialCount,
            "expiredSubscriptions" to expiredCount,
            "nearingExpiration" to nearingExpirationCount,
            "subscriptionTypes" to subscriptionTypeDistribution,
            "currentUserIsLoaded" to (_currentSubscriptionFlow.value != null),
            "currentUserIsPro" to _proUserFlow.value,
            "hasErrors" to (_subscriptionErrorFlow.value != null)
        )
    }
    
    // === Private Helper Methods ===
    
    private fun buildSubscriptionMetadata(
        userId: String,
        subscription: UserSubscription,
        source: CacheSource
    ): Map<String, Any> {
        return buildMap {
            put("userId", userId)
            put("subscriptionType", subscription.level ?: "free")
            put("isProUser", subscription.isActive == true)
            put("lastCached", Clock.System.now().toEpochMilliseconds())
            put("source", source.name)
            
            // Billing information
            subscription.expiryDate?.let { 
                put("expirationTime", it.toInstant().toEpochMilli())
                put("daysUntilExpiry", daysBetween(Clock.System.now(), Instant.fromEpochMilliseconds(it.toInstant().toEpochMilli())))
            }
            
            subscription.verification?.lastVerified?.let { 
                put("trialEndTime", it.toInstant().toEpochMilli())
                put("isTrialActive", it.toInstant().toEpochMilli() > System.currentTimeMillis())
            }
            
            subscription.provider?.let { put("provider", it) }
            subscription.status?.let { put("paymentStatus", it) }
            
            // Subscription status
            val status = determineSubscriptionStatus(subscription)
            put("subscriptionStatus", status.name)
            put("needsAttention", status in listOf(SubscriptionStatus.EXPIRED, SubscriptionStatus.SUSPENDED))
            
            // Expiration warnings
            subscription.expiryDate?.let { expiryDate ->
                val timeUntilExpiry = Instant.fromEpochMilliseconds(expiryDate.toInstant().toEpochMilli()) - Clock.System.now()
                put("isNearingExpiration", timeUntilExpiry <= EXPIRATION_WARNING_THRESHOLD)
                put("isCriticalExpiration", timeUntilExpiry <= EXPIRATION_CRITICAL_THRESHOLD)
            }
            
            // SSOT tracking
            put("billingManagedFields", SUBSCRIPTION_FIELD_OWNERSHIP.filter { it.value == SsotOwnership.CLOUD_OWNED }.keys.toList())
            put("sharedFields", SUBSCRIPTION_FIELD_OWNERSHIP.filter { it.value == SsotOwnership.SHARED }.keys.toList())
        }
    }
    
    private fun determineSubscriptionStatus(subscription: UserSubscription): SubscriptionStatus {
        val now = Clock.System.now()
        
        // Check if trial is active
        subscription.verification?.lastVerified?.let { trialEnd ->
            if (Instant.fromEpochMilliseconds(trialEnd.toInstant().toEpochMilli()) > now) {
                return SubscriptionStatus.TRIAL
            }
        }
        
        // Check expiration
        subscription.expiryDate?.let { expiry ->
            val expiryInstant = Instant.fromEpochMilliseconds(expiry.toInstant().toEpochMilli())
            if (expiryInstant <= now) {
                return SubscriptionStatus.EXPIRED
            }
        }
        
        // Check payment status
        subscription.status?.let { status ->
            when (status.lowercase()) {
                "suspended", "failed" -> return SubscriptionStatus.SUSPENDED
                "cancelled" -> return SubscriptionStatus.CANCELLED
                else -> { /* Continue to next check */ }
            }
        }
        
        // Determine active status
        return when (subscription.level) {
            "free", null -> SubscriptionStatus.FREE
            else -> if (subscription.isActive == true) SubscriptionStatus.ACTIVE_PRO else SubscriptionStatus.FREE
        }
    }
    
    /**
     * Merge billing-managed fields while preserving user preferences
     */
    private fun mergeBillingUpdate(
        existing: UserSubscription,
        billing: UserSubscription
    ): UserSubscription {
        return existing.copy(
            status = billing.status ?: existing.status,
            level = billing.level ?: existing.level,
            isActive = billing.isActive ?: existing.isActive,
            startDate = billing.startDate ?: existing.startDate,
            expiryDate = billing.expiryDate ?: existing.expiryDate,
            isLifetime = billing.isLifetime ?: existing.isLifetime,
            provider = billing.provider ?: existing.provider,
            orderId = billing.orderId ?: existing.orderId,
            verification = billing.verification ?: existing.verification
        )
    }
    
    /**
     * Calculate days between two instants
     */
    private fun daysBetween(start: Instant, end: Instant): Long {
        return (end - start).inWholeDays
    }
    
    /**
     * Set loading state for reactive flows
     */
    private fun setLoading(isLoading: Boolean) {
        _subscriptionLoadingFlow.value = isLoading
    }
    
    /**
     * Set error state for reactive flows
     */
    private fun setError(error: String?) {
        _subscriptionErrorFlow.value = error
    }

    /**
     * ✅ LIFECYCLE: Cleanup resources when cache system is destroyed
     * Required by CacheLifecycleManager for standardization
     */
    fun cleanup() {
        try {
            // Reset all reactive flows
            _currentSubscriptionFlow.value = null
            _subscriptionStatusFlow.value = SubscriptionStatus.UNKNOWN
            _proUserFlow.value = false
            _subscriptionLoadingFlow.value = false
            _subscriptionErrorFlow.value = null

            Log.d(TAG, "SubscriptionCacheSystem cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during SubscriptionCacheSystem cleanup", e)
        }
    }

}