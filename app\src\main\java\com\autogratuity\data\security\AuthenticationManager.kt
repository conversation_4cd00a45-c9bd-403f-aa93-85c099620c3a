package com.autogratuity.data.security

// RepositoryError removed - using Result<T> pattern for error handling per Clarity Architecture
import com.google.firebase.auth.FirebaseUser
import kotlinx.coroutines.flow.StateFlow
import kotlinx.datetime.Instant
import kotlin.time.Duration

/**
 * Modern authentication manager interface following 2025 Kotlin standards.
 * 
 * This interface provides a clean abstraction over Firebase Authentication,
 * supporting reactive authentication state observation, token management,
 * and comprehensive error handling using the Result pattern.
 * 
 * Key features:
 * - Flow-based reactive authentication state observation
 * - Result-based error handling for all operations
 * - Token management with TTL and automatic refresh
 * - kotlinx-datetime for modern time handling
 * - Thread-safe operations using atomic references
 * 
 * @since 2025.05.22
 */
interface AuthenticationManager {
    
    /**
     * Retrieves the currently authenticated Firebase user.
     * 
     * @return The current FirebaseUser or null if not authenticated
     */
    fun getCurrentUser(): FirebaseUser?
    
    /**
     * Retrieves the unique ID of the currently authenticated user.
     * 
     * @return The user ID string, or null if no user is authenticated
     */
    fun getCurrentUserId(): String?

    /**
     * Checks if there is a currently authenticated user.
     * 
     * @return True if a user is authenticated, false otherwise
     */
    fun isAuthenticated(): Boolean

    /**
     * Observes changes in the authentication state as a StateFlow.
     * 
     * This flow will emit the current state upon collection and subsequent changes.
     * The StateFlow ensures that collectors always receive the most recent value.
     * 
     * @return A StateFlow emitting boolean values representing the authentication state
     */
    fun observeAuthenticationState(): StateFlow<Boolean>
    
    /**
     * Observes the current user as a StateFlow.
     * 
     * Emits the current FirebaseUser when authenticated, or null when not authenticated.
     * This is useful for reactive UI that needs to respond to user changes.
     * 
     * @return A StateFlow emitting the current user or null
     */
    fun observeCurrentUser(): StateFlow<FirebaseUser?>

    /**
     * Retrieves a fresh authentication token for the current user.
     * 
     * This method implements intelligent caching with TTL support. Tokens are
     * cached and reused if still valid, reducing unnecessary network calls.
     * 
     * @param forceRefresh If true, a new token will be fetched from the server, bypassing any cache
     * @return Result containing the authentication token string or an Exception
     */
    suspend fun getAuthToken(forceRefresh: Boolean = false): Result<String>

    /**
     * Forces a refresh of the current user's authentication token.
     * 
     * This is equivalent to calling getAuthToken(forceRefresh = true) but
     * returns a simple success/failure result.
     * 
     * @return Result containing Unit on success or an Exception on failure
     */
    suspend fun refreshToken(): Result<Unit>

    /**
     * Retrieves the expiration time of the current user's cached authentication token.
     * 
     * @return Result containing the expiration Instant or a RepositoryError
     */
    suspend fun getTokenExpirationTime(): Result<Instant>
    
    /**
     * Clears all cached authentication data.
     * 
     * This includes cached tokens and any other authentication-related data.
     * Useful for logout scenarios or when switching users.
     */
    fun clearAuthenticationCache()
    
    /**
     * Signs out the current user.
     * 
     * This method signs out the user from Firebase Auth and clears all
     * cached authentication data.
     * 
     * @return Result containing Unit on success or an Exception on failure
     */
    suspend fun signOut(): Result<Unit>
    
    /**
     * Gets detailed authentication information for the current user.
     * 
     * This includes provider information, verification status, and metadata.
     * 
     * @return Result containing AuthenticationInfo or a RepositoryError
     */
    suspend fun getAuthenticationInfo(): Result<AuthenticationInfo>
}

/**
 * Detailed authentication information for a user.
 * 
 * @property userId The unique user identifier
 * @property email The user's email address (may be null for some providers)
 * @property emailVerified Whether the email has been verified
 * @property displayName The user's display name (may be null)
 * @property photoUrl The user's photo URL (may be null)
 * @property providers List of authentication provider IDs (e.g., "google.com", "password")
 * @property createdAt When the user account was created
 * @property lastSignInAt When the user last signed in
 * @property isAnonymous Whether this is an anonymous account
 * @property phoneNumber The user's phone number (may be null)
 * @property metadata Additional metadata about the authentication
 */
data class AuthenticationInfo(
    val userId: String,
    val email: String?,
    val emailVerified: Boolean,
    val displayName: String?,
    val photoUrl: String?,
    val providers: List<String>,
    val createdAt: Instant?,
    val lastSignInAt: Instant?,
    val isAnonymous: Boolean,
    val phoneNumber: String?,
    val metadata: Map<String, Any> = emptyMap()
)

/**
 * Token information with metadata.
 * 
 * @property token The authentication token string
 * @property expiresAt When the token expires
 * @property issuedAt When the token was issued
 * @property claims Custom claims in the token
 */
data class TokenInfo(
    val token: String,
    val expiresAt: Instant,
    val issuedAt: Instant,
    val claims: Map<String, Any> = emptyMap()
) {
    /**
     * Checks if the token is still valid with a safety margin.
     * 
     * @param margin Safety margin to consider before actual expiration
     * @return True if the token is valid, false if expired or about to expire
     */
    fun isValid(margin: Duration = Duration.ZERO, now: Instant = kotlinx.datetime.Clock.System.now()): Boolean {
        return expiresAt > (now + margin)
    }
    
    /**
     * Gets the remaining validity duration of the token.
     * 
     * @param now Current time (defaults to system time)
     * @return Duration until expiration, or Duration.ZERO if already expired
     */
    fun remainingValidity(now: Instant = kotlinx.datetime.Clock.System.now()): Duration {
        val remaining = expiresAt - now
        return if (remaining.isPositive()) remaining else Duration.ZERO
    }
}