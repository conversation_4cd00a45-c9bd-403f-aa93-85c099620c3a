/*
 * Metadata.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

import java.time.OffsetDateTime

/**
 * Represents metadata commonly stored within Firestore documents.
 */
data class Metadata(
    /** Timestamp when the document was created. */
    val createdAt: OffsetDateTime? = null,
    /** Timestamp when the document was last updated. */
    val updatedAt: OffsetDateTime? = null,
    /** Timestamp when the data was imported, specifically for records originating from an import process like GeoJSON. */
    val importedAt: OffsetDateTime? = null,
    /** Origin of the data (e.g., 'manual', 'import', 'capture'). */
    val source: String? = null,
    /** Identifier if the data originated from an import process. */
    val importId: String? = null,
    /** Identifier if the data originated from a capture process. */
    val captureId: String? = null,
    /** Schema or data version number. */
    val version: Long? = null,
    /** A flexible object for storing additional, non-standardized data. Explicitly typed as Map for Java generation. */
    val customData: CustomData? = null
) {

    /**
     * A flexible object for storing additional, non-standardized data. Explicitly typed as Map for Java generation.
     */
    open class CustomData

}
