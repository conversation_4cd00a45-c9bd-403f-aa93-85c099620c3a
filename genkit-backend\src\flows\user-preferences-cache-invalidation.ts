import { onDocumentUpdated } from 'firebase-functions/v2/firestore';
import { FieldValue } from 'firebase-admin/firestore';
import { DndPreferencesCache } from '../utils/dnd-preferences-cache';

/**
 * ✅ FIRESTORE-ONLY: User Preferences Change Trigger
 * 
 * Monitors user DND preferences and subscription changes to maintain data consistency.
 * No longer uses Redis cache - just logs changes for monitoring and debugging.
 */
export const onUserPreferencesChange = onDocumentUpdated(
  'users/{userId}',
  async (event) => {
    const userId = event.params.userId;
    const logPrefix = `[UserPreferencesChange UserId: ${userId}] -`;
    
    try {
      const before = event.data?.before.data();
      const after = event.data?.after.data();

      if (!before || !after) {
        console.log(`${logPrefix} Skipping - missing before/after data`);
        return;
      }

      // Check if DND preferences or subscription changed
      const preferencesChanged = DndPreferencesCache.dndPreferencesChanged(before, after);

      if (preferencesChanged) {
        console.log(`${logPrefix} DND preferences or subscription changed - logging for monitoring`);

        // ✅ FIRESTORE-ONLY: Just update metadata for tracking (no cache to invalidate)
        try {
          if (event.data?.after?.ref) {
            await event.data.after.ref.update({
              'profileData.metadata.lastPreferencesUpdate': FieldValue.serverTimestamp(),
              'profileData.metadata.preferencesVersion': FieldValue.increment(1)
            });
            console.log(`${logPrefix} Updated preferences metadata for user ${userId}`);
          }
        } catch (error) {
          console.warn(`${logPrefix} Error updating preferences metadata:`, error);
          // Continue - metadata update is not critical
        }

        console.log(`${logPrefix} Successfully processed preferences change for user ${userId}`);
      } else {
        console.log(`${logPrefix} No DND preference changes detected`);
      }

    } catch (error) {
      console.error(`${logPrefix} Error in preferences change trigger:`, error);
      // Don't throw - preference monitoring should not break user operations
    }
  }
);