import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';

// ✅ ONLY use generated models from schemas
import type {
  UserProfileSchema
} from '../models/generated/user_profile.schema';

const db = getFirestore();

// Input/Output schemas
const UpdateUserStatsInputSchema = z.object({
  userId: z.string(),
  deltas: z.object({
    deliveryCount: z.number().optional(),
    tipCount: z.number().optional(),
    totalTips: z.number().optional(),
    addressCount: z.number().optional(),
    dndMarkingsUsed: z.number().optional()
  })
});

const UpdateUserStatsOutputSchema = z.object({
  success: z.boolean(),
  updatedStats: z.object({
    deliveryCount: z.number(),
    tipCount: z.number(),
    totalTips: z.number(),
    dndMarkingsUsed: z.number()
  }).optional(),
  error: z.string().optional()
});

/**
 * ✅ FOCUSED FUNCTION: Update user profile statistics
 * 
 * Purpose: Apply incremental updates to user usage statistics
 * Uses: Only generated schema models
 * Updates: User profile usage stats only
 */
export const updateUserStatsFlow = defineFlow(
  {
    name: 'updateUserStats',
    inputSchema: UpdateUserStatsInputSchema,
    outputSchema: UpdateUserStatsOutputSchema,
  },
  async (input) => {
    const logPrefix = `[UpdateUserStats][${input.userId}]`;
    const startTime = Date.now();
    const deltaCount = Object.keys(input.deltas).filter(key => input.deltas[key as keyof typeof input.deltas] !== 0).length;
    console.log(`${logPrefix} Updating user stats with ${deltaCount} deltas:`, input.deltas);

    try {
      const transactionStartTime = Date.now();
      return await db.runTransaction(async (transaction) => {
        // Get current user profile
        const fetchStartTime = Date.now();
        const userDoc = await transaction.get(db.doc(`users/${input.userId}`));
        const fetchDuration = Date.now() - fetchStartTime;

        const userExists = userDoc.exists;
        console.log(`${logPrefix} User fetch completed in ${fetchDuration}ms - exists: ${userExists}`);

        if (!userDoc.exists) {
          // Create basic user profile if it doesn't exist
          const newUserProfile: Partial<UserProfileSchema> = {
            userId: input.userId,
            usage: {
              deliveryCount: 0,
              tipCount: 0,
              totalTips: 0,
              dndMarkingsUsed: 0
            },
            usageStats: {
              totalRuns: 0,
              activeDaysCount: 0,
              totalTips: 0,
              featureUsage: {}
            },
            metadata: {
              createdAt: FieldValue.serverTimestamp() as any,
              updatedAt: FieldValue.serverTimestamp() as any
            }
          };

          transaction.set(userDoc.ref, newUserProfile);
          console.log(`${logPrefix} Created new user profile with initial stats`);
        }

        // Build update object with increments
        const updateData: any = {
          'metadata.updatedAt': FieldValue.serverTimestamp()
        };

        // Apply deltas using FieldValue.increment
        if (input.deltas.deliveryCount !== undefined && input.deltas.deliveryCount !== 0) {
          updateData['usage.deliveryCount'] = FieldValue.increment(input.deltas.deliveryCount);
          updateData['usageStats.totalRuns'] = FieldValue.increment(1); // Track function runs
        }

        if (input.deltas.tipCount !== undefined && input.deltas.tipCount !== 0) {
          // Note: Schema doesn't have tipCount in usage, using custom field
          updateData['usage.tipCount'] = FieldValue.increment(input.deltas.tipCount);
        }

        if (input.deltas.totalTips !== undefined && input.deltas.totalTips !== 0) {
          updateData['usageStats.totalTips'] = FieldValue.increment(input.deltas.totalTips);
        }

        if (input.deltas.addressCount !== undefined && input.deltas.addressCount !== 0) {
          updateData['usage.addressCount'] = FieldValue.increment(input.deltas.addressCount);
        }

        if (input.deltas.dndMarkingsUsed !== undefined && input.deltas.dndMarkingsUsed !== 0) {
          // Note: Custom field not in schema, but needed for DND quota tracking
          updateData['usage.dndMarkingsUsed'] = FieldValue.increment(input.deltas.dndMarkingsUsed);
        }

        // Update daily tracking
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
        updateData[`usageStats.featureUsage.daily_${today}`] = FieldValue.increment(1);

        // Apply updates
        const updateStartTime = Date.now();
        transaction.update(userDoc.ref, updateData);
        const updateDuration = Date.now() - updateStartTime;

        const transactionDuration = Date.now() - transactionStartTime;
        console.log(`${logPrefix} Transaction completed in ${transactionDuration}ms (update: ${updateDuration}ms) - applied ${Object.keys(updateData).length} fields`);

        // Get updated values for response (approximate)
        const currentUser: UserProfileSchema = userDoc.exists ? userDoc.data() as UserProfileSchema : {} as UserProfileSchema;
        // Note: Schema UserUsage only has deliveryCount, addressCount, mappingCount
        // We'll use usageStats for tip-related data
        const currentUsage = currentUser.usage || {};
        const currentUsageStats = currentUser.usageStats || {};

        const totalDuration = Date.now() - startTime;
        console.log(`${logPrefix} User stats update completed successfully in ${totalDuration}ms`);

        return {
          success: true,
          updatedStats: {
            deliveryCount: (currentUsage.deliveryCount || 0) + (input.deltas.deliveryCount || 0),
            tipCount: (currentUsageStats.deliveryCount || 0) + (input.deltas.tipCount || 0), // Using deliveryCount as proxy
            totalTips: (currentUsageStats.totalTips || 0) + (input.deltas.totalTips || 0),
            dndMarkingsUsed: ((currentUsage as any).dndMarkingsUsed || 0) + (input.deltas.dndMarkingsUsed || 0) // Custom field
          }
        };
      });

    } catch (error) {
      const totalDuration = Date.now() - startTime;
      console.error(`${logPrefix} Error updating user stats after ${totalDuration}ms:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
);
