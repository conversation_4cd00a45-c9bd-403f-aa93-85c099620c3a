{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/media/sf_ReifiedAsh"], "disabled": true}, "perplexity": {"autoApprove": ["reason", "deep_research"], "disabled": true, "timeout": 300, "command": "npx", "args": ["-y", "perplexity-mcp"], "env": {"PERPLEXITY_API_KEY": "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"}, "transportType": "stdio"}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}, "disabled": true}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": true, "disabledTools": ["sequentialthinking"]}, "tavily-mcp": {"command": "npx", "args": ["-y", "tavily-mcp@0.1.2"], "env": {"TAVILY_API_KEY": "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"}, "disabled": true}, "just-prompt-server": {"disabled": false, "timeout": 300, "command": "uv", "args": ["--directory", "/media/sf_ReifiedAsh/AppData/Roaming/just-prompt", "run", "just-prompt", "--default-models", "openai:anthropic/claude-3.7-sonnet:thinking,openai:openai/o3-mini-high,openai:openai/o4-mini-high,openai:openai/gpt-4.1"], "transportType": "stdio", "cwd": "/media/sf_ReifiedAsh/AppData/Roaming/just-prompt"}}}