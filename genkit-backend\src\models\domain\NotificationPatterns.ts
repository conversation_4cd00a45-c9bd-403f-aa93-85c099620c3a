// Auto-generated from NotificationPatterns.kt
// TypeScript equivalent of Android domain.model.NotificationPatterns

/**
 * Domain model generated from Kotlin NotificationPatterns
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface NotificationPatterns {
  extractors: NotificationExtractors;
  patterns: PlatformPatterns;
  updatedAt?: Date? = null;
  version: number;
  shipt: ShiptExtractor;
  orderId: string;
  tipAmount: string;
  doordash: string[];
  shipt: string[];
  ubereats: string[];
}