package com.autogratuity.data.repository.subscription

import com.autogratuity.data.model.Result
import com.autogratuity.domain.model.UserSubscription
import kotlinx.coroutines.flow.Flow
import java.time.OffsetDateTime

/**
 * Data layer interface for Subscription operations.
 * This interface is internal to the data layer and matches the domain interface patterns.
 * 
 * Following the delivery domain pattern where data layer interface mirrors domain interface.
 */
interface SubscriptionRepository {

    // ===== CORE SUBSCRIPTION OPERATIONS =====

    suspend fun getCurrentUserSubscription(): Result<UserSubscription?>
    suspend fun getUserSubscription(userId: String): Result<UserSubscription?>
    suspend fun updateUserSubscription(userId: String, subscription: UserSubscription): Result<Unit>
    suspend fun createDefaultSubscription(userId: String): Result<UserSubscription>

    // ===== REACTIVE OPERATIONS =====

    fun observeCurrentUserSubscription(): Flow<Result<UserSubscription?>>
    fun observeUserSubscription(userId: String): Flow<Result<UserSubscription?>>

    // ===== SUBSCRIPTION STATUS OPERATIONS =====

    suspend fun isProUser(): Result<Boolean>
    fun observeIsProUser(): Flow<Boolean>
    suspend fun isSubscriptionExpired(): Result<Boolean>
    suspend fun isLifetimeSubscription(): Result<Boolean>
    suspend fun getSubscriptionLevel(): Result<String>
    suspend fun getSubscriptionExpiryDate(): Result<OffsetDateTime?>

    // ===== SUBSCRIPTION MANAGEMENT =====

    suspend fun upgradeSubscription(durationMonths: Int, paymentDetails: Map<String, Any>): Result<Unit>
    suspend fun cancelSubscription(immediate: Boolean): Result<Unit>
    suspend fun verifySubscription(): Result<Unit>

    // ===== TRIAL OPERATIONS =====

    suspend fun isTrialAvailable(): Result<Boolean>
    fun observeTrialAvailable(): Flow<Boolean>
    suspend fun startTrial(trialDurationDays: Int): Result<Unit>

    // ===== SUBSCRIPTION HISTORY =====

    suspend fun getSubscriptionHistory(): Result<List<Map<String, Any>>>

    // ===== VALIDATION AND UTILITY =====

    suspend fun validateSubscription(subscription: UserSubscription): Result<Unit>
    suspend fun handleBillingSystemUpdate(userId: String, billingData: Map<String, Any>): Result<Unit>

    // ===== CACHE MANAGEMENT =====

    suspend fun clearCache(): Result<Unit>
    suspend fun clearCache(userId: String): Result<Unit>
    suspend fun invalidateCache(userId: String): Result<Unit>
    suspend fun prefetchCriticalData(): Result<Unit>
    suspend fun getCacheMetrics(): Result<Map<String, Any>>

    // ===== REPOSITORY LIFECYCLE =====

    suspend fun initialize(): Result<Unit>
    suspend fun cleanup(): Result<Unit>
}
