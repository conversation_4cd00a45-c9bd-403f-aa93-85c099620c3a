package com.autogratuity.data.mapper

// ===== OPTIMIZED IMPORTS =====
// DTO Models with aliases

// Domain Models (SSoT) with aliases

// Data & Result Types

// Security & Encryption

// Standard Libraries
import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.data.repository.core.RepositoryException
import com.autogratuity.data.security.CryptoResult
import com.autogratuity.data.security.EncryptionUtils
import com.autogratuity.data.util.ValidationEngine
import com.autogratuity.domain.model.Address
import com.autogratuity.domain.model.AddressComponents
import com.autogratuity.domain.model.Coordinates
import java.time.OffsetDateTime
import javax.inject.Inject
import kotlin.time.TimeSource
// Performance monitoring
import com.autogratuity.debug.ClarityArchitectureMonitor
import kotlinx.coroutines.ExperimentalCoroutinesApi
import com.autogratuity.data.model.generated_kt.Address as AddressDto
import com.autogratuity.data.model.generated_kt.Address.Components as ComponentsDto
import com.autogratuity.data.model.generated_kt.Coordinates as CoordinatesDto
import com.autogratuity.data.model.generated_kt.Flags as FlagsDtoGenerated
import com.autogratuity.data.model.generated_kt.Platform as PlatformDto
import com.autogratuity.domain.model.Flags as FlagsDomain
import com.autogratuity.domain.model.ManualDndState
import com.autogratuity.domain.model.Platform as PlatformDomain

@ExperimentalCoroutinesApi
class AddressMapper @Inject constructor(
    private val encryptionUtils: EncryptionUtils,
    private val addressStatsMapper: AddressStatsMapper,
    private val addressDndMapper: AddressDndMapper,
    private val validationEngine: ValidationEngine
) {
    
    companion object {
        private const val TAG = "AddressMapper"
    }

    // Helper to handle CryptoResult for a single field decryption
    private suspend fun decryptField(encrypted: String?, fieldName: String): String? {
        return encrypted?.let {
            if (encryptionUtils.isEncrypted(it)) {
                when (val cryptoOutcome = encryptionUtils.decrypt(it)) {
                    is CryptoResult.Success -> cryptoOutcome.data
                    is CryptoResult.DecryptionFailure -> throw RepositoryException.CryptographicError("Failed to decrypt $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "decryption_failure")
                    is CryptoResult.AuthenticationFailure -> throw RepositoryException.CryptographicError("Authentication failure during decryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "authentication_failure")
                    is CryptoResult.InitializationFailure -> throw RepositoryException.CryptographicError("Crypto init failure during decryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "initialization_failure")
                    is CryptoResult.InputValidationFailure -> throw RepositoryException.CryptographicError("Input validation failure during decryption of $fieldName: ${cryptoOutcome.message}", null, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "input_validation_failure")
                    is CryptoResult.KeyFailure -> throw RepositoryException.CryptographicError("Key failure during decryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "key_failure")
                    is CryptoResult.TimeoutFailure -> throw RepositoryException.CryptographicError("Timeout during decryption of $fieldName: ${cryptoOutcome.message}", null, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "timeout_failure")
                    // Add other specific CryptoResult.Failure types if needed
                    is CryptoResult.Failure -> throw RepositoryException.CryptographicError("Generic crypto failure during decryption of $fieldName: ${cryptoOutcome.message}", null, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "generic_failure") // Fallback for other Failure types
                }
            } else {
                // If not prefixed, assume it's plaintext (e.g. legacy data or error). This might need policy decision.
                // For now, returning as is. Consider logging a warning or failing if plaintext is unexpected for a PII field.
                it
            }
        }
    }

    // Helper to handle CryptoResult for a single field encryption
    private suspend fun encryptField(plaintext: String?, fieldName: String): String? {
        return plaintext?.let {
            when (val cryptoOutcome = encryptionUtils.encrypt(it)) {
                is CryptoResult.Success -> cryptoOutcome.data
                is CryptoResult.EncryptionFailure -> throw RepositoryException.CryptographicError("Failed to encrypt $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "encryption_failure")
                is CryptoResult.InitializationFailure -> throw RepositoryException.CryptographicError("Crypto init failure during encryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "initialization_failure")
                is CryptoResult.InputValidationFailure -> throw RepositoryException.CryptographicError("Input validation failure during encryption of $fieldName: ${cryptoOutcome.message}", null, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "input_validation_failure")
                is CryptoResult.KeyFailure -> throw RepositoryException.CryptographicError("Key failure during encryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "key_failure")
                is CryptoResult.TimeoutFailure -> throw RepositoryException.CryptographicError("Timeout during encryption of $fieldName: ${cryptoOutcome.message}", null, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "timeout_failure")
                // Add other specific CryptoResult.Failure types if needed
                is CryptoResult.Failure -> throw RepositoryException.CryptographicError("Generic crypto failure during encryption of $fieldName: ${cryptoOutcome.message}", null, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "generic_failure") // Fallback for other Failure types
            }
        }
    }

    private fun mapFlagsDtoToDomain(dtoFlags: FlagsDtoGenerated?): FlagsDomain? {
        if (dtoFlags == null) return null
        return FlagsDomain(
            isFavorite = dtoFlags.isFavorite,
            isVerified = dtoFlags.isVerified,
            doNotDeliver = dtoFlags.doNotDeliver,
            dndSource = dtoFlags.dndSource,
            hasAccessIssues = dtoFlags.hasAccessIssues,
            manualDndState = ManualDndState.fromString(dtoFlags.manualDndState) // ✅ FIXED: Convert string to enum
            // isDefault is NOT in SSoT FlagsDomain, it's on SSoT Address directly
        )
    }

    private fun mapFlagsDomainToDto(domainFlags: FlagsDomain?): FlagsDtoGenerated? {
        if (domainFlags == null) return null
        return FlagsDtoGenerated(
            isFavorite = domainFlags.isFavorite,
            isVerified = domainFlags.isVerified,
            doNotDeliver = domainFlags.doNotDeliver,
            dndSource = domainFlags.dndSource,
            hasAccessIssues = domainFlags.hasAccessIssues,
            manualDndState = domainFlags.getManualDndStateString() // ✅ FIXED: Convert enum to string
            // isDefault is NOT in DTO FlagsDtoGenerated, it's on DTO AddressData directly
        )
    }

    private fun mapPlatformDtoToDomain(dtoPlatform: PlatformDto?): PlatformDomain? {
        if (dtoPlatform == null) return null
        return PlatformDomain(
            name = dtoPlatform.name,
            type = dtoPlatform.type,
            version = dtoPlatform.version,
            source = dtoPlatform.source,
            displayName = dtoPlatform.displayName,
            iconUrl = dtoPlatform.iconUrl
        )
    }

    private fun mapPlatformDomainToDto(domainPlatform: PlatformDomain?): PlatformDto? {
        if (domainPlatform == null) return null
        return PlatformDto(
            name = domainPlatform.name,
            type = domainPlatform.type,
            version = domainPlatform.version,
            source = domainPlatform.source,
            displayName = domainPlatform.displayName,
            iconUrl = domainPlatform.iconUrl
        )
    }

    suspend fun mapToDomain(dtoId: String, dataDto: AddressDto.AddressData?): Result<Address> {
        if (dataDto == null) return Result.Error(IllegalArgumentException("AddressData DTO cannot be null for mapping to domain"))

        val mappingStartTime = TimeSource.Monotonic.markNow()
        var piiFieldsProcessed = 0
        val validationErrors = mutableListOf<String>()
        val fieldMismatches = mutableListOf<String>()

        return try {
            val decryptedComponents = dataDto.components?.let {
                piiFieldsProcessed += 6 // streetNumber, streetName, city, state, postalCode, country
                AddressComponents(
                    streetNumber = decryptField(it.streetNumber, "components.streetNumber"),
                    streetName = decryptField(it.streetName, "components.streetName"),
                    city = decryptField(it.city, "components.city"),
                    state = decryptField(it.state, "components.state"),
                    postalCode = decryptField(it.postalCode, "components.postalCode"),
                    country = decryptField(it.country, "components.country")
                )
            }

            val decryptedCoordinates = dataDto.coordinates?.let {
                Coordinates(
                    latitude = decryptField(it.latitude?.toString(), "coordinates.latitude")?.toDoubleOrNull(),
                    longitude = decryptField(it.longitude?.toString(), "coordinates.longitude")?.toDoubleOrNull()
                )
            }

            val decryptedTags = dataDto.tags?.mapNotNull { encryptedTag ->
                piiFieldsProcessed += 1 // each tag
                decryptField(encryptedTag, "tags.entry")
            }

            // Count PII fields being processed
            piiFieldsProcessed += 4 // fullAddress, normalizedAddress, placeId, notes

            val domainAddress = Address(
                id = dtoId,
                userId = dataDto.userId,
                fullAddress = decryptField(dataDto.fullAddress, "fullAddress"),
                normalizedAddress = decryptField(dataDto.normalizedAddress, "normalizedAddress"),
                placeId = decryptField(dataDto.placeId, "placeId"),
                notes = decryptField(dataDto.notes, "notes"),
                tags = decryptedTags,
                components = decryptedComponents,
                coordinates = decryptedCoordinates,
                isDefault = dataDto.isDefault, // SSoT Address.isDefault comes from DTO AddressData.isDefault
                orderIds = dataDto.orderIds,
                searchTerms = dataDto.searchTerms,
                searchFields = dataDto.searchFields,
                deliveryStats = dataDto.deliveryStats,
                flags = mapFlagsDtoToDomain(dataDto.flags), // USE HELPER HERE
                metadata = dataDto.metadata,
                platform = mapPlatformDtoToDomain(dataDto.platform) // USE HELPER HERE
            )

            // Validate required fields
            if (dtoId.isBlank()) validationErrors.add("Address ID is blank")
            if (dataDto.userId.isNullOrBlank()) validationErrors.add("User ID is missing")

            val mappingDuration = mappingStartTime.elapsedNow()
            
            // 📊 COMPREHENSIVE BEFORE/AFTER STATE LOGGING
            val inputState = mapOf(
                "dto_id" to dtoId,
                "dto_userId" to dataDto.userId,
                "dto_fullAddress" to dataDto.fullAddress,
                "dto_coordinates" to dataDto.coordinates?.toString(),
                "dto_tags_count" to dataDto.tags?.size,
                "dto_isDefault" to dataDto.isDefault
            )
            
            val outputState = mapOf(
                "domain_id" to domainAddress.id,
                "domain_userId" to domainAddress.userId,
                "domain_fullAddress" to domainAddress.fullAddress,
                "domain_coordinates" to domainAddress.coordinates?.toString(),
                "domain_tags_count" to domainAddress.tags?.size,
                "domain_isDefault" to domainAddress.isDefault
            )
            
            val fieldTransformations = listOf(
                "fullAddress: DECRYPT",
                "normalizedAddress: DECRYPT", 
                "placeId: DECRYPT",
                "notes: DECRYPT",
                "coordinates: DECRYPT_COMPONENTS",
                "tags: DECRYPT_LIST",
                "components: DECRYPT_NESTED"
            )
            
            val businessLogicApplied = listOf(
                "pii_decryption",
                "field_validation",
                "flags_mapping",
                "platform_mapping"
            )

            Log.d("AddressMapper", "=== ADDRESS MAPPING TRANSFORMATION ===")
            Log.d("AddressMapper", "Input DTO State: $inputState")
            Log.d("AddressMapper", "Output Domain State: $outputState") 
            Log.d("AddressMapper", "Field Transformations: $fieldTransformations")
            Log.d("AddressMapper", "Business Logic Applied: $businessLogicApplied")
            Log.d("AddressMapper", "PII Fields Processed: $piiFieldsProcessed")
            Log.d("AddressMapper", "Mapping Duration: ${mappingDuration.inWholeMilliseconds}ms")
            
            // 🔍 SESSION CORRELATION: Add mapping success to session
            ClarityArchitectureMonitor.addSessionEvent("address_mapping_success:$dtoId")

            // 🚨 COMPREHENSIVE MAPPING MONITORING (always monitor for complete visibility)
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "AddressMapper",
                entityType = "Address",
                duration = mappingDuration,
                success = true,
                piiFieldsProcessed = piiFieldsProcessed,
                validationErrors = validationErrors,
                fieldMismatches = fieldMismatches,
                entityId = dtoId,
                userId = dataDto.userId,
                dataSize = dataDto.toString().length,
                encryptionTime = mappingDuration, // approximate PII processing time
                fieldsTransformed = fieldTransformations.size,
                businessLogicApplied = businessLogicApplied,
                cacheUpdated = false
            )

            Result.Success(domainAddress)
        } catch (e: RepositoryException.CryptographicError) {
            // 🔍 SESSION CORRELATION: Add mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("address_mapping_failure:$dtoId:crypto_error")
            
            // Always monitor cryptographic errors
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "AddressMapper",
                entityType = "Address",
                duration = mappingStartTime.elapsedNow(),
                success = false,
                piiFieldsProcessed = piiFieldsProcessed,
                validationErrors = validationErrors + "Cryptographic error: ${e.message}",
                fieldMismatches = fieldMismatches,
                error = e,
                entityId = dtoId,
                userId = dataDto.userId,
                dataSize = dataDto.toString().length,
                businessLogicApplied = listOf("pii_decryption_failed")
            )
            Result.Error(e)
        } catch (e: Exception) {
            // 🔍 SESSION CORRELATION: Add mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("address_mapping_failure:$dtoId:exception")
            
            // Always monitor mapping errors
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "AddressMapper",
                entityType = "Address",
                duration = mappingStartTime.elapsedNow(),
                success = false,
                piiFieldsProcessed = piiFieldsProcessed,
                validationErrors = validationErrors + "Mapping error: ${e.message}",
                fieldMismatches = fieldMismatches,
                error = e,
                entityId = dtoId,
                userId = dataDto.userId,
                dataSize = dataDto.toString().length
            )
            Result.Error(Exception("Error mapping AddressDTO (ID: $dtoId) to domain: ${e.message}", e))
        }
    }

    suspend fun mapToDtoData(ssot: Address): Result<AddressDto.AddressData> {
        val mappingStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        var encryptedFieldsCount = 0
        var validationErrors = mutableListOf<String>()
        
        return try {
            // ENHANCED: Log conversion start through monitoring system
            com.autogratuity.debug.SimpleClarityDashboard.logMapperConversionStart(
                mapperClass = "AddressMapper",
                conversionType = "SSoT->DTO",
                entityType = "Address",
                entityId = ssot.id,
                inputDetails = "fullAddress=${ssot.fullAddress?.take(50)}... | components=${ssot.components != null} | coordinates=${ssot.coordinates != null}"
            )
            
            val encryptedComponentsDto = ssot.components?.let { components ->
                com.autogratuity.debug.SimpleClarityDashboard.logMapperBusinessLogic(
                    mapperClass = "AddressMapper",
                    entityType = "Address", 
                    entityId = ssot.id,
                    logicType = "component_conversion",
                    details = "${components.city}, ${components.state}"
                )
                ComponentsDto(
                    streetNumber = encryptField(components.streetNumber, "components.streetNumber").also { encryptedFieldsCount++ },
                    streetName = encryptField(components.streetName, "components.streetName").also { encryptedFieldsCount++ },
                    city = encryptField(components.city, "components.city").also { encryptedFieldsCount++ },
                    state = encryptField(components.state, "components.state").also { encryptedFieldsCount++ },
                    postalCode = encryptField(components.postalCode, "components.postalCode").also { encryptedFieldsCount++ },
                    country = encryptField(components.country, "components.country").also { encryptedFieldsCount++ }
                )
            }

            val encryptedCoordinatesDto = ssot.coordinates?.let { coords ->
                Log.d(TAG, "  Converting coordinates: lat=${coords.latitude}, lng=${coords.longitude}")
                CoordinatesDto(
                    latitude = encryptField(coords.latitude?.toString(), "coordinates.latitude")?.toDoubleOrNull().also { encryptedFieldsCount++ },
                    longitude = encryptField(coords.longitude?.toString(), "coordinates.longitude")?.toDoubleOrNull().also { encryptedFieldsCount++ }
                )
            }

            val encryptedTags = ssot.tags?.mapNotNull { plaintextTag ->
                Log.d(TAG, "  Converting tag: $plaintextTag")
                encryptField(plaintextTag, "tags.entry").also { encryptedFieldsCount++ }
            }

            // Validate critical fields
            if (ssot.fullAddress.isNullOrBlank()) {
                validationErrors.add("fullAddress is null or blank")
            }
            if (ssot.userId.isNullOrBlank()) {
                validationErrors.add("userId is null or blank")
            }

            val addressDataDto = AddressDto.AddressData(
                userId = ssot.userId,
                fullAddress = encryptField(ssot.fullAddress, "fullAddress").also { encryptedFieldsCount++ },
                normalizedAddress = encryptField(ssot.normalizedAddress, "normalizedAddress").also { encryptedFieldsCount++ },
                placeId = encryptField(ssot.placeId, "placeId").also { encryptedFieldsCount++ },
                isDefault = ssot.isDefault,
                notes = encryptField(ssot.notes, "notes").also { encryptedFieldsCount++ },
                tags = encryptedTags,
                components = encryptedComponentsDto,
                coordinates = encryptedCoordinatesDto,
                orderIds = ssot.orderIds,
                searchTerms = ssot.searchTerms,
                searchFields = ssot.searchFields,
                deliveryStats = ssot.deliveryStats ?: com.autogratuity.data.model.generated_kt.Delivery_stats(
                    // ✅ Initialize with default values like backend does
                    deliveryCount = 0L,
                    tipCount = 0L,
                    totalTips = 0.0,
                    averageTipAmount = null,
                    highestTip = null,
                    pendingCount = 0L,
                    averageTimeMinutes = null,
                    lastDeliveryDate = null,
                    lastDeliveryTimestamp = null
                ),
                flags = mapFlagsDomainToDto(ssot.flags),
                metadata = ssot.metadata ?: com.autogratuity.data.model.generated_kt.Metadata(
                    // ✅ CRITICAL FIX: Set proper timestamps - null timestamps break Firestore queries and statistics
                    createdAt = OffsetDateTime.now(),
                    updatedAt = OffsetDateTime.now(),
                    importedAt = null,
                    source = "manual",
                    importId = null,
                    captureId = null,
                    version = 1L,
                    customData = null
                ),
                platform = mapPlatformDomainToDto(ssot.platform)
            )
            
            val mappingDuration = mappingStartTime.elapsedNow()
            
            // ENHANCED: Monitor successful SSoT to DTO conversion
            ClarityArchitectureMonitor.monitorSsotToDtoMapping(
                mapperClass = "AddressMapper",
                entityType = "Address",
                duration = mappingDuration,
                success = true,
                piiFieldsProcessed = encryptedFieldsCount,
                validationErrors = validationErrors,
                fieldMismatches = emptyList(),
                entityId = ssot.id,
                userId = ssot.userId,
                dataSize = addressDataDto.toString().length
            )
            
            // ENHANCED: Log successful conversion through monitoring system
            com.autogratuity.debug.SimpleClarityDashboard.logMapperConversion(
                mapperClass = "AddressMapper",
                conversionType = "SSoT->DTO",
                entityType = "Address",
                entityId = ssot.id,
                success = true,
                durationMs = mappingDuration.inWholeMilliseconds,
                outputDetails = "encrypted_fields=$encryptedFieldsCount | validation_errors=${validationErrors.size} | size=${addressDataDto.toString().length}bytes",
                businessLogic = listOf("encryption", "validation", "flag_mapping", "platform_mapping"),
                validationErrors = validationErrors,
                encryptedFields = encryptedFieldsCount
            )
            
            Result.Success(addressDataDto)
        } catch (e: RepositoryException.CryptographicError) {
            val mappingDuration = mappingStartTime.elapsedNow()
            
            // ENHANCED: Monitor cryptographic error
            ClarityArchitectureMonitor.monitorSsotToDtoMapping(
                mapperClass = "AddressMapper",
                entityType = "Address",
                duration = mappingDuration,
                success = false,
                piiFieldsProcessed = encryptedFieldsCount,
                validationErrors = validationErrors + "Cryptographic error: ${e.message}",
                fieldMismatches = emptyList(),
                error = e,
                entityId = ssot.id,
                userId = ssot.userId,
                dataSize = 0
            )
            
            // ENHANCED: Log failed conversion through monitoring system
            com.autogratuity.debug.SimpleClarityDashboard.logMapperConversion(
                mapperClass = "AddressMapper",
                conversionType = "SSoT->DTO",
                entityType = "Address",
                entityId = ssot.id,
                success = false,
                durationMs = mappingDuration.inWholeMilliseconds,
                outputDetails = "encrypted_fields=$encryptedFieldsCount | error=cryptographic",
                businessLogic = listOf("encryption_failed"),
                validationErrors = validationErrors + "Cryptographic error: ${e.message}",
                encryptedFields = encryptedFieldsCount
            )
            
            Result.Error(e)
        } catch (e: Exception) {
            val mappingDuration = mappingStartTime.elapsedNow()
            
            // ENHANCED: Monitor general mapping error
            ClarityArchitectureMonitor.monitorSsotToDtoMapping(
                mapperClass = "AddressMapper",
                entityType = "Address",
                duration = mappingDuration,
                success = false,
                piiFieldsProcessed = encryptedFieldsCount,
                validationErrors = validationErrors + "Mapping error: ${e.message}",
                fieldMismatches = emptyList(),
                error = e,
                entityId = ssot.id,
                userId = ssot.userId,
                dataSize = 0
            )
            
            // ENHANCED: Log failed conversion through monitoring system
            com.autogratuity.debug.SimpleClarityDashboard.logMapperConversion(
                mapperClass = "AddressMapper",
                conversionType = "SSoT->DTO",
                entityType = "Address",
                entityId = ssot.id,
                success = false,
                durationMs = mappingDuration.inWholeMilliseconds,
                outputDetails = "encrypted_fields=$encryptedFieldsCount | error=mapping",
                businessLogic = listOf("mapping_failed"),
                validationErrors = validationErrors + "Mapping error: ${e.message}",
                encryptedFields = encryptedFieldsCount
            )
            
            Result.Error(Exception("Error mapping Address SSoT (ID: ${ssot.id}) to DTO: ${e.message}", e))
        }
    }

    suspend fun mapToDto(ssot: Address): Result<AddressDto> {
        if (ssot.id.isBlank()) {
            return Result.Error(IllegalArgumentException("SSoT Address must have an ID to be mapped to a full DTO"))
        }
        return when (val addressDataResult = mapToDtoData(ssot)) {
            is Result.Success -> Result.Success(AddressDto(id = ssot.id, addressData = addressDataResult.data))
            is Result.Error -> Result.Error(addressDataResult.exception)
            is Result.Loading -> Result.Error(Exception("mapToDtoData returned Loading state unexpectedly"))
        }
    }
}