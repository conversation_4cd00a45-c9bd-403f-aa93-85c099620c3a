package com.autogratuity.data.datasource.local

import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.util_kt.parseUniversalTimestamp
import com.autogratuity.data.repository.config.ConfigCacheSystem
import com.autogratuity.domain.model.AppConfig
import com.autogratuity.domain.model.NotificationPatterns
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Interface for local data operations (caching) related to Config.
 * All methods operate with SSoT domain models (AppConfig, NotificationPatterns).
 *
 * Follows the delivery domain pattern for local data sources.
 */
interface ConfigLocalDataSource {
    suspend fun getAppConfig(): Result<AppConfig?>
    suspend fun saveAppConfig(appConfig: AppConfig): Result<Unit>
    suspend fun deleteAppConfig(): Result<Unit>

    suspend fun getNotificationPatterns(): Result<NotificationPatterns?>
    suspend fun saveNotificationPatterns(patterns: NotificationPatterns): Result<Unit>
    suspend fun deleteNotificationPatterns(): Result<Unit>

    suspend fun clearAllCaches(): Result<Unit>

    fun observeAppConfig(): Flow<Result<AppConfig?>>
    fun observeNotificationPatterns(): Flow<Result<NotificationPatterns?>>
}

/**
 * ConfigLocalDataSource implementation wrapping ConfigCacheSystem.
 * Operates exclusively with SSoT domain models.
 *
 * Follows the delivery domain pattern exactly - interface + implementation in same file.
 */
class ConfigLocalDataSourceImpl @Inject constructor(
    private val cacheSystem: ConfigCacheSystem,
    private val ioDispatcher: CoroutineDispatcher // Koin will provide this via module definition
) : ConfigLocalDataSource {

    companion object {
        private const val TAG = "ConfigLocalDataSource"
        private const val APP_CONFIG_CACHE_KEY = "app_config"
        private const val NOTIFICATION_PATTERNS_CACHE_KEY = "notification_patterns"
    }

    // ===== APP CONFIG OPERATIONS =====

    override suspend fun getAppConfig(): Result<AppConfig?> = withContext(ioDispatcher) {
        try {
            // Get from cache system - this returns Map<String, Any>?
            val cachedData = cacheSystem.get(APP_CONFIG_CACHE_KEY)

            // Convert Map to AppConfig domain model if data exists
            val appConfig = cachedData?.let { convertMapToAppConfig(it) }

            Log.d(TAG, "Retrieved AppConfig from cache: ${appConfig != null}")
            Result.Success(appConfig)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting AppConfig from cache", e)
            Result.Error(e)
        }
    }

    override suspend fun saveAppConfig(appConfig: AppConfig): Result<Unit> = withContext(ioDispatcher) {
        try {
            // Convert AppConfig domain model to Map for cache storage
            val dataMap = convertAppConfigToMap(appConfig)

            // Cache using ConfigCacheSystem
            cacheSystem.cacheConfiguration(
                configKey = APP_CONFIG_CACHE_KEY,
                configData = dataMap,
                source = com.autogratuity.data.repository.core.CacheSource.MANUAL,
                version = appConfig.version.toString()
            )

            Log.d(TAG, "Saved AppConfig to cache")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error saving AppConfig to cache", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteAppConfig(): Result<Unit> = withContext(ioDispatcher) {
        try {
            cacheSystem.remove(APP_CONFIG_CACHE_KEY)
            Log.d(TAG, "Deleted AppConfig from cache")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting AppConfig from cache", e)
            Result.Error(e)
        }
    }

    override fun observeAppConfig(): Flow<Result<AppConfig?>> {
        return cacheSystem.observeAppConfiguration().map { cachedData ->
            try {
                val appConfig = cachedData?.let { convertMapToAppConfig(it) }
                Result.Success(appConfig)
            } catch (e: Exception) {
                Log.e(TAG, "Error converting cached data to AppConfig", e)
                Result.Error(e)
            }
        }
    }

    // ===== NOTIFICATION PATTERNS OPERATIONS =====

    override suspend fun getNotificationPatterns(): Result<NotificationPatterns?> = withContext(ioDispatcher) {
        try {
            // Get from cache system
            val cachedData = cacheSystem.get(NOTIFICATION_PATTERNS_CACHE_KEY)

            // Convert Map to NotificationPatterns domain model if data exists
            val patterns = cachedData?.let { convertMapToNotificationPatterns(it) }

            Log.d(TAG, "Retrieved NotificationPatterns from cache: ${patterns != null}")
            Result.Success(patterns)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting NotificationPatterns from cache", e)
            Result.Error(e)
        }
    }

    override suspend fun saveNotificationPatterns(patterns: NotificationPatterns): Result<Unit> = withContext(ioDispatcher) {
        try {
            // Convert NotificationPatterns domain model to Map for cache storage
            val dataMap = convertNotificationPatternsToMap(patterns)

            // Cache using ConfigCacheSystem
            cacheSystem.cacheConfiguration(
                configKey = NOTIFICATION_PATTERNS_CACHE_KEY,
                configData = dataMap,
                source = com.autogratuity.data.repository.core.CacheSource.MANUAL,
                version = patterns.version.toString()
            )

            Log.d(TAG, "Saved NotificationPatterns to cache")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error saving NotificationPatterns to cache", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteNotificationPatterns(): Result<Unit> = withContext(ioDispatcher) {
        try {
            cacheSystem.remove(NOTIFICATION_PATTERNS_CACHE_KEY)
            Log.d(TAG, "Deleted NotificationPatterns from cache")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting NotificationPatterns from cache", e)
            Result.Error(e)
        }
    }

    override fun observeNotificationPatterns(): Flow<Result<NotificationPatterns?>> {
        // ConfigCacheSystem doesn't have specific observe for notification patterns
        // We'll use a generic approach or extend the cache system if needed
        return cacheSystem.observeAppConfiguration().map { _ ->
            try {
                // Try to get notification patterns from cache
                val cachedData = cacheSystem.get(NOTIFICATION_PATTERNS_CACHE_KEY)
                val patterns = cachedData?.let { convertMapToNotificationPatterns(it) }
                Result.Success(patterns)
            } catch (e: Exception) {
                Log.e(TAG, "Error converting cached data to NotificationPatterns", e)
                Result.Error(e)
            }
        }
    }

    // ===== CACHE MANAGEMENT =====

    override suspend fun clearAllCaches(): Result<Unit> = withContext(ioDispatcher) {
        try {
            cacheSystem.cleanup()
            Log.d(TAG, "Cleared all config caches")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all config caches", e)
            Result.Error(e)
        }
    }

    // ===== HELPER CONVERSION METHODS =====

    /**
     * Convert AppConfig domain model to Map<String, Any> for cache storage.
     */
    private fun convertAppConfigToMap(appConfig: AppConfig): Map<String, Any> {
        return buildMap {
            put("versions", mapOf(
                "minimum" to appConfig.versions.minimum,
                "recommended" to appConfig.versions.recommended,
                "latest" to appConfig.versions.latest
            ))
            put("features", mapOf(
                "useNewSyncSystem" to appConfig.features.useNewSyncSystem,
                "enableOfflineMode" to appConfig.features.enableOfflineMode,
                "enableAnalytics" to appConfig.features.enableAnalytics,
                "enableBackgroundSync" to appConfig.features.enableBackgroundSync,
                "enforceVersionCheck" to appConfig.features.enforceVersionCheck
            ))
            put("limits", mapOf(
                "freeTier" to mapOf(
                    "mappingLimit" to appConfig.limits.freeTier.mappingLimit,
                    "importLimit" to appConfig.limits.freeTier.importLimit,
                    "exportLimit" to appConfig.limits.freeTier.exportLimit
                ),
                "proTier" to mapOf(
                    "mappingLimit" to appConfig.limits.proTier.mappingLimit,
                    "importLimit" to appConfig.limits.proTier.importLimit,
                    "exportLimit" to appConfig.limits.proTier.exportLimit
                )
            ))
            put("sync", mapOf(
                "interval" to appConfig.sync.interval,
                "backgroundInterval" to appConfig.sync.backgroundInterval,
                "maxBatchSize" to appConfig.sync.maxBatchSize,
                "conflictStrategy" to appConfig.sync.conflictStrategy
            ))
            put("maintenance", buildMap {
                put("isInMaintenance", appConfig.maintenance.isInMaintenance)
                appConfig.maintenance.maintenanceMessage?.let { put("maintenanceMessage", it) }
                appConfig.maintenance.estimatedEndTime?.let { put("estimatedEndTime", it.toString()) }
            })
            appConfig.updatedAt?.let { put("updatedAt", it.toString()) }
            put("version", appConfig.version)
            appConfig.customData?.let { put("customData", it) }
            put("cached_at", System.currentTimeMillis())
        }
    }

    /**
     * Convert Map<String, Any> from cache to AppConfig domain model.
     */
    @Suppress("UNCHECKED_CAST")
    private fun convertMapToAppConfig(dataMap: Map<String, Any>): AppConfig? {
        return try {
            val versionsMap = dataMap["versions"] as? Map<String, Any> ?: return null
            val featuresMap = dataMap["features"] as? Map<String, Any> ?: return null
            val limitsMap = dataMap["limits"] as? Map<String, Any> ?: return null
            val syncMap = dataMap["sync"] as? Map<String, Any> ?: return null
            val maintenanceMap = dataMap["maintenance"] as? Map<String, Any> ?: return null

            val freeTierMap = (limitsMap["freeTier"] as? Map<String, Any>) ?: return null
            val proTierMap = (limitsMap["proTier"] as? Map<String, Any>) ?: return null

            AppConfig(
                versions = AppConfig.AppVersions(
                    minimum = versionsMap["minimum"] as? String ?: "",
                    recommended = versionsMap["recommended"] as? String ?: "",
                    latest = versionsMap["latest"] as? String ?: ""
                ),
                features = AppConfig.AppFeatures(
                    useNewSyncSystem = featuresMap["useNewSyncSystem"] as? Boolean != false,
                    enableOfflineMode = featuresMap["enableOfflineMode"] as? Boolean != false,
                    enableAnalytics = featuresMap["enableAnalytics"] as? Boolean != false,
                    enableBackgroundSync = featuresMap["enableBackgroundSync"] as? Boolean != false,
                    enforceVersionCheck = featuresMap["enforceVersionCheck"] as? Boolean == true
                ),
                limits = AppConfig.AppLimits(
                    freeTier = AppConfig.AppLimits.TierLimits(
                        mappingLimit = (freeTierMap["mappingLimit"] as? Number)?.toLong() ?: 0L,
                        importLimit = (freeTierMap["importLimit"] as? Number)?.toLong() ?: 0L,
                        exportLimit = (freeTierMap["exportLimit"] as? Number)?.toLong() ?: 0L
                    ),
                    proTier = AppConfig.AppLimits.TierLimits(
                        mappingLimit = (proTierMap["mappingLimit"] as? Number)?.toLong() ?: 0L,
                        importLimit = (proTierMap["importLimit"] as? Number)?.toLong() ?: 0L,
                        exportLimit = (proTierMap["exportLimit"] as? Number)?.toLong() ?: 0L
                    )
                ),
                sync = AppConfig.AppSync(
                    interval = (syncMap["interval"] as? Number)?.toLong() ?: 300L,
                    backgroundInterval = (syncMap["backgroundInterval"] as? Number)?.toLong() ?: 900L,
                    maxBatchSize = (syncMap["maxBatchSize"] as? Number)?.toLong() ?: 50L,
                    conflictStrategy = syncMap["conflictStrategy"] as? String ?: "server_wins"
                ),
                maintenance = AppConfig.AppMaintenance(
                    isInMaintenance = maintenanceMap["isInMaintenance"] as? Boolean == true,
                    maintenanceMessage = maintenanceMap["maintenanceMessage"] as? String,
                    estimatedEndTime = parseUniversalTimestamp(maintenanceMap["estimatedEndTime"])
                ),
                updatedAt = parseUniversalTimestamp(dataMap["updatedAt"]),
                version = (dataMap["version"] as? Number)?.toLong() ?: 1L,
                customData = dataMap["customData"] as? Map<String, Any>
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error converting Map to AppConfig", e)
            null
        }
    }

    /**
     * Convert NotificationPatterns domain model to Map<String, Any> for cache storage.
     */
    private fun convertNotificationPatternsToMap(patterns: NotificationPatterns): Map<String, Any> {
        return buildMap {
            put("extractors", mapOf(
                "shipt" to mapOf(
                    "orderId" to patterns.extractors.shipt.orderId,
                    "tipAmount" to patterns.extractors.shipt.tipAmount
                )
            ))
            put("patterns", mapOf(
                "doordash" to patterns.patterns.doordash,
                "shipt" to patterns.patterns.shipt,
                "ubereats" to patterns.patterns.ubereats
            ))
            patterns.updatedAt?.let { put("updatedAt", it.toString()) }
            put("version", patterns.version)
            put("cached_at", System.currentTimeMillis())
        }
    }

    /**
     * Convert Map<String, Any> from cache to NotificationPatterns domain model.
     */
    @Suppress("UNCHECKED_CAST")
    private fun convertMapToNotificationPatterns(dataMap: Map<String, Any>): NotificationPatterns? {
        return try {
            val extractorsMap = dataMap["extractors"] as? Map<String, Any> ?: return null
            val patternsMap = dataMap["patterns"] as? Map<String, Any> ?: return null

            val shiptExtractorMap = (extractorsMap["shipt"] as? Map<String, Any>) ?: return null

            NotificationPatterns(
                extractors = NotificationPatterns.NotificationExtractors(
                    shipt = NotificationPatterns.NotificationExtractors.ShiptExtractor(
                        orderId = shiptExtractorMap["orderId"] as? String ?: "",
                        tipAmount = shiptExtractorMap["tipAmount"] as? String ?: ""
                    )
                ),
                patterns = NotificationPatterns.PlatformPatterns(
                    doordash = (patternsMap["doordash"] as? List<String>) ?: emptyList(),
                    shipt = (patternsMap["shipt"] as? List<String>) ?: emptyList(),
                    ubereats = (patternsMap["ubereats"] as? List<String>) ?: emptyList()
                ),
                updatedAt = parseUniversalTimestamp(dataMap["updatedAt"]),
                version = (dataMap["version"] as? Number)?.toLong() ?: 1L
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error converting Map to NotificationPatterns", e)
            null
        }
    }
}