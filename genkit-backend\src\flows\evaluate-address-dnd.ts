import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';

// ✅ ONLY use generated models from schemas
import type {
  Address
} from '../models/generated/address.schema';

import type {
  UserProfileSchema
} from '../models/generated/user_profile.schema';

const db = getFirestore();

// Input/Output schemas
const EvaluateAddressDndInputSchema = z.object({
  userId: z.string(),
  addressId: z.string()
});

const EvaluateAddressDndOutputSchema = z.object({
  success: z.boolean(),
  doNotDeliver: z.boolean(),
  dndSource: z.string().nullable(),
  isVerified: z.boolean(),
  error: z.string().optional()
});

/**
 * ✅ FOCUSED FUNCTION: Evaluate DND status for a single address
 *
 * Purpose: Apply DND rules and set final DND flags with retroactive delivery updates
 * Uses: Only generated schema models
 * Updates: Address flags AND all delivery documents for the address
 */
export const evaluateAddressDndFlow = defineFlow(
  {
    name: 'evaluateAddressDnd',
    inputSchema: EvaluateAddressDndInputSchema,
    outputSchema: EvaluateAddressDndOutputSchema,
  },
  async (input) => {
    const logPrefix = `[EvaluateAddressDnd][${input.userId}][${input.addressId}]`;
    const startTime = Date.now();
    console.log(`${logPrefix} Starting DND evaluation`);

    try {
      // PHASE 1: Parallel data fetching including deliveries for retroactive updates
      const fetchStartTime = Date.now();
      const [addressDoc, userDoc, deliveriesSnapshot] = await Promise.all([
        db.doc(`users/${input.userId}/user_addresses/${input.addressId}`).get(),
        db.doc(`users/${input.userId}`).get(),
        db.collection(`users/${input.userId}/user_deliveries`)
          .where('deliveryData.reference.addressId', '==', input.addressId)
          .limit(50)
          .get()
      ]);

      const fetchDuration = Date.now() - fetchStartTime;
      const deliveryCount = deliveriesSnapshot.docs.length;
      const addressExists = addressDoc.exists;
      const userExists = userDoc.exists;

      console.log(`${logPrefix} Data fetch completed in ${fetchDuration}ms - address: ${addressExists}, user: ${userExists}, deliveries: ${deliveryCount}`);

      if (!addressDoc.exists) {
        console.error(`${logPrefix} Address document not found`);
        throw new Error('Address not found');
      }

      const address: Address = addressDoc.data() as Address;
      const userProfile: UserProfileSchema = userDoc.exists ? userDoc.data() as UserProfileSchema : {} as UserProfileSchema;

      // Extract deliveries for comprehensive evaluation
      const deliveries = deliveriesSnapshot.docs.map(doc => ({
        id: doc.id,
        deliveryData: doc.data().deliveryData
      }));

      const isPremium = userProfile.subscription?.isActive === true;
      const currentManualState = address.addressData?.flags?.manualDndState;

      console.log(`${logPrefix} Evaluating DND rules - deliveries: ${deliveries.length}, premium: ${isPremium}, manualState: ${currentManualState}`);

      // Evaluate DND using comprehensive delivery analysis
      const evaluationStartTime = Date.now();
      const dndResult = evaluateDndRulesComprehensive(address, userProfile, deliveries, logPrefix);
      const evaluationDuration = Date.now() - evaluationStartTime;

      console.log(`${logPrefix} DND evaluation completed in ${evaluationDuration}ms - result: ${dndResult.doNotDeliver}, source: ${dndResult.dndSource}`);

      // PHASE 2: Atomic transaction to update address and all delivery documents
      const transactionStartTime = Date.now();
      let deliveriesUpdated = 0;

      await db.runTransaction(async (transaction) => {
        // Update address flags
        transaction.update(addressDoc.ref, {
          'addressData.flags.doNotDeliver': dndResult.doNotDeliver,
          'addressData.flags.dndSource': dndResult.dndSource,
          'addressData.flags.isVerified': dndResult.isVerified,
          'addressData.metadata.updatedAt': FieldValue.serverTimestamp()
        });

        // ✅ RETROACTIVE UPDATES: Update all delivery documents with new DND status
        console.log(`${logPrefix} Updating ${deliveries.length} delivery documents with DND status: ${dndResult.doNotDeliver}`);

        for (const delivery of deliveries) {
          if (delivery.id) {
            const deliveryRef = db.doc(`users/${input.userId}/user_deliveries/${delivery.id}`);

            // Only update if DND status actually changed (idempotent)
            const currentDndStatus = delivery.deliveryData?.status?.doNotDeliver ?? false;
            const currentDndReason = delivery.deliveryData?.status?.dndReason ?? null;

            if (currentDndStatus !== dndResult.doNotDeliver || currentDndReason !== dndResult.dndSource) {
              transaction.update(deliveryRef, {
                'deliveryData.status.doNotDeliver': dndResult.doNotDeliver,
                'deliveryData.status.dndReason': dndResult.dndSource,
                'deliveryData.metadata.updatedAt': FieldValue.serverTimestamp(),
                'deliveryData.metadata.updatedByDndEvaluation': true
              });
              deliveriesUpdated++;
            }
          }
        }
      });

      const transactionDuration = Date.now() - transactionStartTime;
      const totalDuration = Date.now() - startTime;

      console.log(`${logPrefix} Transaction completed in ${transactionDuration}ms - updated ${deliveriesUpdated}/${deliveries.length} deliveries`);
      console.log(`${logPrefix} DND evaluation completed successfully in ${totalDuration}ms - final status: ${dndResult.doNotDeliver}`);

      return {
        success: true,
        ...dndResult
      };

    } catch (error) {
      const totalDuration = Date.now() - startTime;
      console.error(`${logPrefix} Error evaluating DND after ${totalDuration}ms:`, error);
      return {
        success: false,
        doNotDeliver: false,
        dndSource: null,
        isVerified: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
);

/**
 * ✅ COMPREHENSIVE: Evaluate DND rules with delivery-level analysis
 * Includes explicit import flags, pending vs confirmed logic, and incomplete history detection
 */
function evaluateDndRulesComprehensive(
  address: Address,
  userProfile: UserProfileSchema,
  deliveries: any[],
  logPrefix: string
): {
  doNotDeliver: boolean;
  dndSource: string | null;
  isVerified: boolean;
} {
  const flags = address.addressData?.flags;
  const manualState = flags?.manualDndState;

  console.log(`${logPrefix} Manual state: ${manualState}`);

  // 1. HIGHEST PRIORITY: Manual override using schema enum
  if (manualState === "FORCE_DND") {
    console.log(`${logPrefix} Manual DND override - FORCE_DND`);
    return {
      doNotDeliver: true,
      dndSource: "MANUAL_USER_FORCE_DND",
      isVerified: true
    };
  }

  if (manualState === "FORCE_ALLOW") {
    console.log(`${logPrefix} Manual DND override - FORCE_ALLOW`);
    return {
      doNotDeliver: false,
      dndSource: "MANUAL_USER_FORCE_ALLOW",
      isVerified: true
    };
  }

  // 2. SECOND HIGHEST PRIORITY: Explicit import DND flags (permanent, cannot be overridden)
  const hasExplicitImportDnd = deliveries.some(delivery => {
    const status = delivery.deliveryData?.status;
    return status?.dndReason === 'EXPLICIT_IMPORT';
  });

  if (hasExplicitImportDnd) {
    console.log(`${logPrefix} Explicit import DND flag detected - permanent DND`);
    return {
      doNotDeliver: true,
      dndSource: "RULE_BASED_EXPLICIT_IMPORT",
      isVerified: true
    };
  }

  // 3. DELIVERY-LEVEL ANALYSIS: Distinguish between pending and confirmed deliveries
  const confirmedDeliveries = deliveries.filter(delivery => {
    const tipAmount = delivery.deliveryData?.amounts?.tipAmount ?? null;
    const status = delivery.deliveryData?.status;
    const isCompleted = status?.isCompleted ?? false;

    // Only confirmed deliveries (tipAmount set AND completed)
    return tipAmount != null && isCompleted;
  });

  console.log(`${logPrefix} Found ${confirmedDeliveries.length} confirmed deliveries out of ${deliveries.length} total`);

  if (confirmedDeliveries.length === 0) {
    console.log(`${logPrefix} No confirmed deliveries - allowing deliveries (pending tips may arrive)`);
    return {
      doNotDeliver: false,
      dndSource: null,
      isVerified: false
    };
  }

  // 4. INCOMPLETE HISTORY DETECTION: Adjust thresholds if we hit query limits
  const hasIncompleteHistory = deliveries.length >= 50; // Query limit reached
  console.log(`${logPrefix} Incomplete history detected: ${hasIncompleteHistory}`);

  // 5. APPLY TIER-SPECIFIC RULES with confirmed deliveries only
  const subscription = userProfile.subscription;
  const preferences = userProfile.preferences;
  const isPremium = subscription?.isActive === true;
  const customRule = preferences?.dnd?.customRule;

  console.log(`${logPrefix} Applying automatic rules - isPremium: ${isPremium}, customRule enabled: ${customRule?.isEnabled}`);

  if (isPremium && customRule?.isEnabled) {
    // Premium user: Custom threshold with confirmed deliveries
    const threshold = customRule.tipAmountThreshold || 0;
    const lowTipDeliveries = confirmedDeliveries.filter(delivery => {
      const tipAmount = delivery.deliveryData?.amounts?.tipAmount ?? 0;
      return tipAmount <= threshold;
    });

    // Adjust thresholds based on incomplete history
    const requiredDeliveries = hasIncompleteHistory ? 1 : 2;
    const requiredLowTips = hasIncompleteHistory ? 1 : 1;

    console.log(`${logPrefix} Premium rule - confirmed: ${confirmedDeliveries.length}, lowTip: ${lowTipDeliveries.length}, required: ${requiredDeliveries}/${requiredLowTips}`);

    if (confirmedDeliveries.length >= requiredDeliveries && lowTipDeliveries.length >= requiredLowTips) {
      return {
        doNotDeliver: true,
        dndSource: "RULE_BASED_USER_PREFERENCES",
        isVerified: true
      };
    }
  } else {
    // Freemium user: $0 tips trigger DND
    const zeroTipDeliveries = confirmedDeliveries.filter(delivery => {
      const tipAmount = delivery.deliveryData?.amounts?.tipAmount ?? 0;
      return tipAmount === 0;
    });

    // For incomplete history, be more conservative
    const requiredZeroTips = hasIncompleteHistory ? 1 : 1;

    console.log(`${logPrefix} Freemium rule - confirmed: ${confirmedDeliveries.length}, zeroTip: ${zeroTipDeliveries.length}, required: ${requiredZeroTips}`);

    if (zeroTipDeliveries.length >= requiredZeroTips) {
      return {
        doNotDeliver: true,
        dndSource: "RULE_BASED_USER_PREFERENCES",
        isVerified: true
      };
    }
  }

  // Default: Allow deliveries
  console.log(`${logPrefix} No DND rules triggered - allowing deliveries`);
  return {
    doNotDeliver: false,
    dndSource: null,
    isVerified: true
  };
}
