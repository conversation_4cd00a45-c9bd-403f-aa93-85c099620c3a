package com.autogratuity.data.mapper

// ===== OPTIMIZED IMPORTS =====
// DTO Models with aliases

// Domain Models (SSoT)

// Security & Encryption

// Standard Libraries
// Performance monitoring
import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.generated_kt.User_profile
import com.autogratuity.data.repository.core.RepositoryException
import com.autogratuity.data.security.CryptoResult
import com.autogratuity.data.security.EncryptionUtils
import com.autogratuity.data.util.ValidationEngine
import com.autogratuity.debug.ClarityArchitectureMonitor
import com.autogratuity.domain.model.ComparisonType
import com.autogratuity.domain.model.CustomDndRule
import com.autogratuity.domain.model.DndDetails
import com.autogratuity.domain.model.User
import com.autogratuity.domain.model.UserCommunication
import com.autogratuity.domain.model.UserPermissions
import com.autogratuity.domain.model.UserPreferences
import com.autogratuity.domain.model.UserSubscription
import com.autogratuity.domain.model.UserSyncInfo
import com.autogratuity.domain.model.UserUsage
import com.autogratuity.domain.model.UserUsageStats
import com.autogratuity.domain.model.VerificationDetails
import kotlinx.coroutines.ExperimentalCoroutinesApi
import java.time.OffsetDateTime
import javax.inject.Inject
import com.autogratuity.data.model.util_kt.parseUniversalTimestamp
import kotlin.time.TimeSource
import com.autogratuity.data.model.generated_kt.Metadata as MetadataDto
import com.autogratuity.data.model.generated_kt.User_profile as UserProfileDto

@ExperimentalCoroutinesApi
class UserMapper @Inject constructor(
    private val encryptionUtils: EncryptionUtils,
    private val validationEngine: ValidationEngine
) {

    companion object {
        private const val TAG = "UserMapper"
    }


    // Helper to handle CryptoResult for a single field decryption with fallback support
    private suspend fun decryptFieldWithFallback(encrypted: String?, fieldName: String): String? {
        return encrypted?.let { rawValue ->
            // 🔧 FIX: Handle double-encoded JSON strings from Firestore
            val cleanValue = rawValue.trim().removeSurrounding("\"").trim()
            
            when {
                cleanValue.startsWith("PLAINTEXT:") -> {
                    // Handle fallback plaintext data
                    Log.w(TAG, "Found plaintext fallback data for $fieldName")
                    cleanValue.removePrefix("PLAINTEXT:")
                }
                rawValue.startsWith("PLAINTEXT:") -> {
                    // Handle fallback plaintext data (original format)
                    Log.w(TAG, "Found plaintext fallback data for $fieldName")
                    rawValue.removePrefix("PLAINTEXT:")
                }
                encryptionUtils.isEncrypted(cleanValue) -> {
                    when (val cryptoResult = encryptionUtils.decrypt(cleanValue)) {
                        is CryptoResult.Success -> cryptoResult.data
                        is CryptoResult.Failure -> {
                            Log.w(TAG, "Decryption failed for $fieldName, treating as plaintext: ${cryptoResult.message}")
                            cleanValue // Return cleaned value if decryption fails
                        }
                    }
                }
                encryptionUtils.isEncrypted(rawValue) -> {
                    when (val cryptoResult = encryptionUtils.decrypt(rawValue)) {
                        is CryptoResult.Success -> cryptoResult.data
                        is CryptoResult.Failure -> {
                            Log.w(TAG, "Decryption failed for $fieldName, treating as plaintext: ${cryptoResult.message}")
                            rawValue // Return as-is if decryption fails
                        }
                    }
                }
                else -> {
                    cleanValue // Return cleaned value as plaintext
                }
            }
        }
    }


    // Helper to handle CryptoResult for a single field encryption with graceful degradation
    private suspend fun encryptFieldWithFallback(plaintext: String?, fieldName: String): String? {
        return plaintext?.let {
            when (val cryptoResult = encryptionUtils.encrypt(it)) {
                is CryptoResult.Success -> cryptoResult.data
                is CryptoResult.Failure -> {
                    // Log the encryption failure but continue with plaintext
                    Log.w(TAG, "Encryption failed for $fieldName, storing as plaintext: ${cryptoResult.message}")
                    // Store with a prefix to indicate it's unencrypted
                    "PLAINTEXT:$it"
                }
            }
        }
    }

    // --- DTO to Domain Mapping Helpers ---
    private suspend fun mapVerificationToDomain(dto: UserProfileDto.Verification?): VerificationDetails? {
        if (dto == null) return null
        return VerificationDetails(
            lastVerified = dto.lastVerified,
            status = dto.status,
            error = decryptFieldWithFallback(dto.error, "subscription.verification.error")
        )
    }

    private suspend fun mapSubscriptionToDomain(dto: UserProfileDto.UserSubscription?): UserSubscription? {
        if (dto == null) return null
        return UserSubscription(
            status = dto.status,
            level = dto.level,
            isActive = dto.isActive,
            startDate = dto.startDate,
            expiryDate = dto.expiryDate,
            isLifetime = dto.isLifetime,
            provider = decryptFieldWithFallback(dto.provider, "subscription.provider"),
            orderId = decryptFieldWithFallback(dto.orderId, "subscription.orderId"),
            verification = mapVerificationToDomain(dto.verification)
        )
    }

    private fun mapCustomRuleToDomain(dto: UserProfileDto.CustomRule?): CustomDndRule? {
        if (dto == null) return null
        return CustomDndRule(
            isEnabled = dto.isEnabled,
            tipAmountThreshold = dto.tipAmountThreshold,
            comparisonType = ComparisonType.fromDto(dto.comparisonType) ?: ComparisonType.LESS_THAN_OR_EQUAL_TO
        )
    }

    private fun mapDndToDomain(dto: UserProfileDto.Dnd?): DndDetails? {
        if (dto == null) return null
        return DndDetails(
            customRule = mapCustomRuleToDomain(dto.customRule)
        )
    }

    private fun mapPreferencesToDomain(dto: UserProfileDto.UserPreferences?): UserPreferences? {
        if (dto == null) return null
        return UserPreferences(
            notificationsEnabled = dto.notificationsEnabled,
            theme = dto.theme,
            useLocation = dto.useLocation,
            dnd = mapDndToDomain(dto.dnd)
        )
    }

    private fun mapPermissionsToDomain(dto: UserProfileDto.UserPermissions?): UserPermissions? {
        if (dto == null) return null
        return UserPermissions(
            bypassLimits = dto.bypassLimits,
            maxUploads = dto.maxUploads
        )
    }

    private fun mapUsageToDomain(dto: UserProfileDto.UserUsage?, subscription: UserProfileDto.UserSubscription?): UserUsage? {
        if (dto == null) return null

        // ✅ DND QUOTA MAPPING: Set defaults based on subscription status
        // Backend stores actual quota in stats.manualDndCount, but DTO doesn't have this field
        // For now, use subscription status to set appropriate defaults
        val isPremiumUser = subscription?.isActive == true &&
                           (subscription.level?.lowercase() in listOf("pro", "premium"))

        val maxDndMarkings = if (isPremiumUser) -1L else UserUsage.FREEMIUM_DND_LIMIT.toLong()
        val dndMarkingsUsed = 0L // Default to 0, will be updated by backend sync

        return UserUsage(
            mappingCount = dto.mappingCount,
            deliveryCount = dto.deliveryCount,
            addressCount = dto.addressCount,
            dndMarkingsUsed = dndMarkingsUsed, // ✅ FIXED: Set default based on subscription
            maxDndMarkings = maxDndMarkings, // ✅ FIXED: Set limit based on subscription
            autoCapturedOrders = null, // Not available in DTO, use default
            lastUsageUpdate = dto.lastUsageUpdate
        )
    }

    private fun mapSyncInfoToDomain(dto: UserProfileDto.UserSyncInfo?): UserSyncInfo? {
        if (dto == null) return null
        return UserSyncInfo(
            lastSyncTime = dto.lastSyncTime,
            deviceIds = dto.deviceIds,
            version = dto.version
        )
    }

    private fun mapAppSettingsToDomain(dto: UserProfileDto.UserAppSettings?): User_profile.UserAppSettings? {
        if (dto == null) return null
        return User_profile.UserAppSettings(
            dataCollectionOptIn = dto.dataCollectionOptIn,
            lastVersion = dto.lastVersion,
            onboardingCompleted = dto.onboardingCompleted
        )
    }

    private fun mapCommunicationToDomain(dto: UserProfileDto.UserCommunication?): UserCommunication? {
        if (dto == null) return null
        return UserCommunication(
            emailOptIn = dto.emailOptIn,
            marketingOptIn = dto.marketingOptIn,
            pushNotificationsEnabled = dto.pushNotificationsEnabled
        )
    }

    @Suppress("UNCHECKED_CAST")
    private fun mapUsageStatsToDomain(dto: UserProfileDto.UserUsageStats?): UserUsageStats? {
        if (dto == null) return null
        // The DTO's FeatureUsage is an open class, intended to be a Map.
        // Explicit casting might be needed if it's not directly a Map<String, Long>.
        // Assuming for now it can be treated as such or will be null.
        val featureUsageMap = dto.featureUsage as? Map<String, Long>
        return UserUsageStats(
            deliveryCount = dto.deliveryCount,
            tipCount = null, // ✅ CLEANUP FIX: tipCount not in generated DTO yet, but maintained by cloud function
            addressCount = dto.addressCount,
            lastUsageDate = dto.lastUsageDate,
            totalRuns = dto.totalRuns,
            activeDaysCount = dto.activeDaysCount,
            totalTips = dto.totalTips,
            featureUsage = featureUsageMap
        )
    }

    suspend fun mapToDomain(dtoId: String, userProfileDto: UserProfileDto?): Result<User> {
        val mappingStartTime = TimeSource.Monotonic.markNow()
        val validationErrors = mutableListOf<String>()
        val fieldMismatches = mutableListOf<String>()
        var piiFieldsProcessed = 0

        if (userProfileDto == null) return Result.Error(IllegalArgumentException("UserProfileDto cannot be null for mapping to domain"))

        // NOTE: DTO validation removed per Clarity Architecture principles  
        // DTOs from Firestore contain encrypted PII that won't pass standard validation (e.g., email format)
        // Validation should only occur on decrypted SSoT models after mapping is complete

        return try {
            val domainUser = User(
                id = dtoId, // Assuming dtoId is the Firestore document ID
                userId = userProfileDto.userId,
                email = decryptFieldWithFallback(userProfileDto.email, "email"),
                displayName = userProfileDto.displayName, // ✅ FIXED: Display names now stored as plaintext
                defaultAddressId = decryptFieldWithFallback(userProfileDto.defaultAddressId, "defaultAddressId"),
                photoUrl = decryptFieldWithFallback(userProfileDto.photoUrl, "photoUrl"),
                authProviders = userProfileDto.authProviders,
                accountStatus = userProfileDto.accountStatus,
                timezone = userProfileDto.timezone,
                createdAt = userProfileDto.createdAt,
                lastLoginAt = userProfileDto.lastLoginAt,
                privacyPolicyAccepted = userProfileDto.privacyPolicyAccepted,
                termsAccepted = userProfileDto.termsAccepted,
                version = userProfileDto.version,
                subscription = mapSubscriptionToDomain(userProfileDto.subscription),
                preferences = mapPreferencesToDomain(userProfileDto.preferences),
                permissions = mapPermissionsToDomain(userProfileDto.permissions),
                usage = mapUsageToDomain(userProfileDto.usage, userProfileDto.subscription),
                syncInfo = mapSyncInfoToDomain(userProfileDto.syncInfo),
                appSettings = mapAppSettingsToDomain(userProfileDto.appSettings),
                communication = mapCommunicationToDomain(userProfileDto.communication),
                usageStats = mapUsageStatsToDomain(userProfileDto.usageStats),
                metadata = userProfileDto.metadata // Directly use DTO's Metadata if it's compatible
            )

            // Count PII fields processed
            if (userProfileDto.email != null) piiFieldsProcessed++
            if (userProfileDto.displayName != null) piiFieldsProcessed++
            if (userProfileDto.defaultAddressId != null) piiFieldsProcessed++
            if (userProfileDto.photoUrl != null) piiFieldsProcessed++

            // Validate required fields
            if (dtoId.isBlank()) validationErrors.add("User ID is blank")
            if (userProfileDto.userId.isNullOrBlank()) validationErrors.add("User ID is missing")

            // ✅ VALIDATE DOMAIN OUTPUT
            val domainValidationResult = validationEngine.validateUser(domainUser)
            if (!domainValidationResult.isValid) {
                Log.w(TAG, "Domain User validation failed in mapper for user $dtoId: ${domainValidationResult.errors}")
                validationErrors.addAll(domainValidationResult.errors.map { "Domain validation: $it" })
                
                // Log but don't fail - this is a data quality issue that should be tracked
                ClarityArchitectureMonitor.addSessionEvent("user_mapping_validation_warning:$dtoId")
            }
            
            if (domainValidationResult.warnings.isNotEmpty()) {
                Log.w(TAG, "Domain User validation warnings in mapper for user $dtoId: ${domainValidationResult.warnings}")
            }

            val mappingDuration = mappingStartTime.elapsedNow()
            
            // 📊 COMPREHENSIVE BEFORE/AFTER STATE LOGGING
            val inputState = mapOf(
                "dto_id" to dtoId,
                "dto_userId" to userProfileDto.userId,
                "dto_displayName" to userProfileDto.displayName,
                "dto_email" to userProfileDto.email,
                "dto_defaultAddressId" to userProfileDto.defaultAddressId,
                "dto_subscription_active" to userProfileDto.subscription?.isActive
            )
            
            val outputState = mapOf(
                "domain_id" to domainUser.id,
                "domain_userId" to domainUser.userId,
                "domain_displayName" to domainUser.displayName,
                "domain_email" to domainUser.email,
                "domain_defaultAddressId" to domainUser.defaultAddressId,
                "domain_subscription_active" to domainUser.subscription?.isActive
            )
            
            val fieldTransformations = listOf(
                "name: DECRYPT",
                "email: DECRYPT",
                "phone: DECRYPT",
                "preferences: COMPLEX_MAPPING",
                "subscription: NESTED_MAPPING",
                "verification: STATUS_MAPPING"
            )
            
            val businessLogicApplied = listOf(
                "pii_decryption",
                "field_validation",
                "subscription_mapping",
                "preferences_parsing",
                "verification_status"
            )

            Log.d("UserMapper", "=== USER MAPPING TRANSFORMATION ===")
            Log.d("UserMapper", "Input DTO State: $inputState")
            Log.d("UserMapper", "Output Domain State: $outputState")
            Log.d("UserMapper", "Field Transformations: $fieldTransformations")
            Log.d("UserMapper", "Business Logic Applied: $businessLogicApplied")
            Log.d("UserMapper", "PII Fields Processed: $piiFieldsProcessed")
            Log.d("UserMapper", "Mapping Duration: ${mappingDuration.inWholeMilliseconds}ms")
            
            // 🔍 SESSION CORRELATION: Add mapping success to session
            ClarityArchitectureMonitor.addSessionEvent("user_mapping_success:$dtoId")

            // 🚨 COMPREHENSIVE MAPPING MONITORING (always monitor for complete visibility)
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "UserMapper",
                entityType = "User",
                duration = mappingDuration,
                success = true,
                piiFieldsProcessed = piiFieldsProcessed,
                validationErrors = validationErrors,
                fieldMismatches = fieldMismatches,
                entityId = dtoId,
                userId = userProfileDto.userId,
                dataSize = userProfileDto.toString().length,
                fieldsTransformed = fieldTransformations.size,
                businessLogicApplied = businessLogicApplied,
                cacheUpdated = true
            )

            Result.Success(domainUser)
        } catch (e: RepositoryException.CryptographicError) {
            // 🔍 SESSION CORRELATION: Add mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("user_mapping_failure:$dtoId:crypto_error")
            
            // Always monitor cryptographic errors
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "UserMapper",
                entityType = "User",
                duration = mappingStartTime.elapsedNow(),
                success = false,
                piiFieldsProcessed = piiFieldsProcessed,
                validationErrors = validationErrors + "Cryptographic error: ${e.message}",
                fieldMismatches = fieldMismatches,
                error = e,
                entityId = dtoId,
                userId = userProfileDto.userId,
                dataSize = userProfileDto.toString().length,
                fieldsTransformed = 0,
                businessLogicApplied = listOf("decryption_failed"),
                cacheUpdated = false
            )
            Result.Error(e)
        } catch (e: Exception) {
            // 🔍 SESSION CORRELATION: Add mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("user_mapping_failure:$dtoId:exception")
            
            // Always monitor mapping errors
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "UserMapper",
                entityType = "User",
                duration = mappingStartTime.elapsedNow(),
                success = false,
                piiFieldsProcessed = piiFieldsProcessed,
                validationErrors = validationErrors + "Mapping error: ${e.message}",
                fieldMismatches = fieldMismatches,
                error = e,
                entityId = dtoId,
                userId = userProfileDto.userId,
                dataSize = userProfileDto.toString().length,
                fieldsTransformed = 0,
                businessLogicApplied = listOf("mapping_failed"),
                cacheUpdated = false
            )
            Result.Error(Exception("Error mapping UserProfileDto (ID: $dtoId) to domain: ${e.message}", e))
        }
    }

    // --- Domain to DTO Mapping Helpers ---
    private suspend fun mapVerificationToDto(domain: VerificationDetails?): UserProfileDto.Verification? {
        if (domain == null) return null
        return UserProfileDto.Verification(
            lastVerified = domain.lastVerified,
            status = domain.status,
            error = encryptFieldWithFallback(domain.error, "subscription.verification.error")
        )
    }

    private suspend fun mapSubscriptionToDto(domain: UserSubscription?): UserProfileDto.UserSubscription? {
        if (domain == null) return null
        return UserProfileDto.UserSubscription(
            status = domain.status,
            level = domain.level,
            isActive = domain.isActive,
            startDate = domain.startDate,
            expiryDate = domain.expiryDate,
            isLifetime = domain.isLifetime,
            provider = encryptFieldWithFallback(domain.provider, "subscription.provider"),
            orderId = encryptFieldWithFallback(domain.orderId, "subscription.orderId"),
            verification = mapVerificationToDto(domain.verification)
        )
    }

    private fun mapCustomRuleToDto(domain: CustomDndRule?): UserProfileDto.CustomRule? {
        if (domain == null) return null
        return UserProfileDto.CustomRule(
            isEnabled = domain.isEnabled,
            tipAmountThreshold = domain.tipAmountThreshold,
            comparisonType = domain.comparisonType.toDto()
        )
    }

    private fun mapDndToDto(domain: DndDetails?): UserProfileDto.Dnd? {
        if (domain == null) return null
        return UserProfileDto.Dnd(
            customRule = mapCustomRuleToDto(domain.customRule)
        )
    }

    private fun mapPreferencesToDto(domain: UserPreferences?): UserProfileDto.UserPreferences? {
        if (domain == null) return null
        return UserProfileDto.UserPreferences(
            notificationsEnabled = domain.notificationsEnabled,
            theme = domain.theme,
            useLocation = domain.useLocation,
            dnd = mapDndToDto(domain.dnd)
        )
    }

    private fun mapPermissionsToDto(domain: UserPermissions?): UserProfileDto.UserPermissions? {
        if (domain == null) return null
        return UserProfileDto.UserPermissions(
            bypassLimits = domain.bypassLimits,
            maxUploads = domain.maxUploads
        )
    }

    private fun mapUsageToDto(domain: UserUsage?): UserProfileDto.UserUsage? {
        if (domain == null) return null
        return UserProfileDto.UserUsage(
            mappingCount = domain.mappingCount,
            deliveryCount = domain.deliveryCount,
            addressCount = domain.addressCount,
            lastUsageUpdate = domain.lastUsageUpdate
            // Note: dndMarkingsUsed, maxDndMarkings, autoCapturedOrders are not available in DTO
            // These fields are managed by backend and stored in separate stats field
        )
    }

    private fun mapSyncInfoToDto(domain: UserSyncInfo?): UserProfileDto.UserSyncInfo? {
        if (domain == null) return null
        return UserProfileDto.UserSyncInfo(
            lastSyncTime = domain.lastSyncTime,
            deviceIds = domain.deviceIds,
            version = domain.version
        )
    }

    private fun mapAppSettingsToDto(domain: User_profile.UserAppSettings?): UserProfileDto.UserAppSettings? {
        if (domain == null) return null
        return UserProfileDto.UserAppSettings(
            dataCollectionOptIn = domain.dataCollectionOptIn == true,
            lastVersion = domain.lastVersion ?: "",
            onboardingCompleted = domain.onboardingCompleted == true
        )
    }

    private fun mapCommunicationToDto(domain: UserCommunication?): UserProfileDto.UserCommunication? {
        if (domain == null) return null
        return UserProfileDto.UserCommunication(
            emailOptIn = domain.emailOptIn,
            marketingOptIn = domain.marketingOptIn,
            pushNotificationsEnabled = domain.pushNotificationsEnabled
        )
    }

    private fun mapUsageStatsToDto(domain: UserUsageStats?): UserProfileDto.UserUsageStats? {
        if (domain == null) return null

        // Handle featureUsage mapping - DTO expects UserProfileDto.FeatureUsage which is an open class
        // For now, set to null as the structure is unclear
        val featureUsageDto: UserProfileDto.FeatureUsage? = null // TODO: Implement proper mapping when DTO structure is clarified

        return UserProfileDto.UserUsageStats(
            deliveryCount = domain.deliveryCount,
            addressCount = domain.addressCount,
            lastUsageDate = domain.lastUsageDate,
            totalRuns = domain.totalRuns,
            activeDaysCount = domain.activeDaysCount,
            totalTips = domain.totalTips,
            featureUsage = featureUsageDto
        )
    }

    suspend fun mapToDto(ssotUser: User): Result<UserProfileDto> {
        return try {
            // 🔍 CLARITY ARCHITECTURE MONITOR: User Mapping Validation
            ClarityArchitectureMonitor.logEvent("[MAPPER] === USER SSOT TO DTO MAPPING START ===")
            ClarityArchitectureMonitor.logEvent("[MAPPER] SSoT User ID: ${ssotUser.userId}")
            ClarityArchitectureMonitor.logEvent("[MAPPER] SSoT User ID null check: ${ssotUser.userId != null}")
            ClarityArchitectureMonitor.logEvent("[MAPPER] SSoT User email: ${ssotUser.email}")
            ClarityArchitectureMonitor.logEvent("[MAPPER] SSoT User displayName: ${ssotUser.displayName}")
            
            val userProfileDto = UserProfileDto(
                userId = ssotUser.userId,
                email = encryptFieldWithFallback(ssotUser.email, "email"),
                displayName = ssotUser.displayName, // ✅ FIXED: Display names are public data, not PII
                defaultAddressId = encryptFieldWithFallback(ssotUser.defaultAddressId, "defaultAddressId"),
                photoUrl = encryptFieldWithFallback(ssotUser.photoUrl, "photoUrl"),
                authProviders = ssotUser.authProviders ?: listOf(), // DTO expects non-null list
                accountStatus = ssotUser.accountStatus,
                timezone = ssotUser.timezone,
                createdAt = ssotUser.createdAt,
                lastLoginAt = ssotUser.lastLoginAt,
                privacyPolicyAccepted = ssotUser.privacyPolicyAccepted,
                termsAccepted = ssotUser.termsAccepted,
                version = ssotUser.version,
                subscription = mapSubscriptionToDto(ssotUser.subscription),
                preferences = mapPreferencesToDto(ssotUser.preferences),
                permissions = mapPermissionsToDto(ssotUser.permissions),
                usage = mapUsageToDto(ssotUser.usage),
                syncInfo = mapSyncInfoToDto(ssotUser.syncInfo),
                appSettings = mapAppSettingsToDto(ssotUser.appSettings),
                communication = mapCommunicationToDto(ssotUser.communication),
                usageStats = mapUsageStatsToDto(ssotUser.usageStats),
                metadata = ssotUser.metadata // Directly use SSoT's Metadata if compatible or map if needed
            )
            
            // 🔍 CLARITY ARCHITECTURE MONITOR: DTO Validation Post-Creation
            ClarityArchitectureMonitor.logEvent("[MAPPER] === DTO CREATION VALIDATION ===")
            ClarityArchitectureMonitor.logEvent("[MAPPER] DTO userId: ${userProfileDto.userId}")
            ClarityArchitectureMonitor.logEvent("[MAPPER] DTO userId null check: ${userProfileDto.userId != null}")
            ClarityArchitectureMonitor.logEvent("[MAPPER] DTO userId matches SSoT: ${userProfileDto.userId == ssotUser.userId}")
            ClarityArchitectureMonitor.logEvent("[MAPPER] DTO email encrypted: ${userProfileDto.email}")
            ClarityArchitectureMonitor.logEvent("[MAPPER] DTO displayName encrypted: ${userProfileDto.displayName}")
            ClarityArchitectureMonitor.logEvent("[MAPPER] DTO accountStatus: ${userProfileDto.accountStatus}")
            ClarityArchitectureMonitor.logEvent("[MAPPER] DTO version: ${userProfileDto.version}")
            
            // Critical field validation for Firestore security rules
            val criticalFieldsValid = userProfileDto.userId != null && 
                                    userProfileDto.userId == ssotUser.userId &&
                                    userProfileDto.accountStatus != null &&
                                    userProfileDto.version != null
            ClarityArchitectureMonitor.logEvent("[MAPPER] Critical fields valid for Firestore: $criticalFieldsValid")
            ClarityArchitectureMonitor.logEvent("[MAPPER] === USER SSOT TO DTO MAPPING END ===")
            
            Result.Success(userProfileDto)
        } catch (e: RepositoryException.CryptographicError) {
            Result.Error(e)
        } catch (e: Exception) {
            Result.Error(Exception("Error mapping User SSoT (ID: ${ssotUser.id}) to DTO: ${e.message}", e))
        }
    }

    // ===== BUSINESS LOGIC FUNCTIONS =====
    // Moved from UserProfileRepositoryImpl.kt following Clarity.md principles





    /**
     * Normalize a user model by applying business logic transformations.
     * Moved from UserProfileRepositoryImpl to follow Clarity.md principles.
     *
     * @param user The SSoT User model to normalize
     * @return Normalized SSoT User model
     */
    fun normalizeUser(user: User): User {
        return try {
            val now = OffsetDateTime.now()

            // Normalize email to lowercase
            val normalizedEmail = user.email?.lowercase()?.trim()

            // Ensure display name is set
            val displayName = if (user.displayName.isNullOrBlank()) {
                normalizedEmail?.substringBefore('@') ?: "User"
            } else {
                user.displayName.trim()
            }

            // Ensure metadata is updated
            val updatedMetadata = user.metadata?.copy(
                updatedAt = now
            ) ?: MetadataDto(
                createdAt = now,
                updatedAt = now,
                importedAt = null,
                source = "app_creation",
                importId = null,
                captureId = null,
                version = 1L,
                customData = null // Fix: Don't instantiate empty CustomData class
            )

            // Ensure usage stats exist
            val normalizedUsageStats = user.usageStats ?: UserUsageStats(
                deliveryCount = 0L,
                tipCount = 0L, // ✅ CLEANUP FIX: Added tipCount field maintained by address-stats-updater
                addressCount = 0L,
                lastUsageDate = now,
                totalRuns = 0L,
                activeDaysCount = 0L,
                totalTips = 0.0,
                featureUsage = emptyMap()
            )

            user.copy(
                email = normalizedEmail,
                displayName = displayName,
                metadata = updatedMetadata,
                usageStats = normalizedUsageStats
            )
        } catch (e: Exception) {
            // Return original user if normalization fails
            Log.w("UserMapper", "Failed to normalize user ${user.id}: ${e.message}")
            user
        }
    }

    // Stats handled by address-stats-updater cloud function

    // ===== BUSINESS LOGIC METHODS =====
    // Following AddressMapper pattern: all business logic centralized in mapper

    /**
     * Validate user model for business compliance.
     * Mirrors AddressMapper's comprehensive validation approach.
     */
    fun validateUser(user: User): Result<Unit> {
        return try {
            // Core validation rules
            if (user.id.isBlank()) {
                return Result.Error(IllegalArgumentException("User ID cannot be blank"))
            }

            if (user.userId.isNullOrBlank()) {
                return Result.Error(IllegalArgumentException("User userId cannot be blank"))
            }

            if (user.id != user.userId) {
                return Result.Error(IllegalArgumentException("User ID and userId must match"))
            }

            // Email validation
            if (user.email != null) {
                if (!isValidEmail(user.email)) {
                    return Result.Error(IllegalArgumentException("Invalid email format: ${user.email}"))
                }
            }

            // Display name validation
            if (user.displayName != null && user.displayName.length > 100) {
                return Result.Error(IllegalArgumentException("Display name too long: ${user.displayName.length} characters"))
            }

            // Account status validation
            if (user.accountStatus != null && user.accountStatus !in listOf("active", "suspended", "pending", "deleted")) {
                return Result.Error(IllegalArgumentException("Invalid account status: ${user.accountStatus}"))
            }

            // Subscription validation
            user.subscription?.let { subscription ->
                if (subscription.status != null && subscription.status !in listOf("free", "premium", "trial", "expired", "cancelled")) {
                    return Result.Error(IllegalArgumentException("Invalid subscription status: ${subscription.status}"))
                }
            }

            Log.d("UserMapper", "User validation passed for ${user.id}")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("UserMapper", "Error validating user ${user.id}", e)
            Result.Error(e)
        }
    }

    /**
     * Create default user with comprehensive business logic.
     * Mirrors AddressMapper's createDefaultAddress approach.
     */
    fun createDefaultUser(userId: String, email: String?, displayName: String? = null): Result<User> {
        return try {
            if (userId.isBlank()) {
                return Result.Error(IllegalArgumentException("User ID cannot be blank for default user creation"))
            }

            val now = OffsetDateTime.now()

            // Validate email if provided
            if (email != null && !isValidEmail(email)) {
                return Result.Error(IllegalArgumentException("Invalid email format for default user: $email"))
            }

            val defaultUser = User(
                id = userId,
                userId = userId,
                email = email,
                displayName = displayName ?: email?.substringBefore('@') ?: "New User",
                defaultAddressId = null,
                photoUrl = null,
                authProviders = emptyList(),
                accountStatus = "active",
                timezone = null,
                createdAt = now,
                lastLoginAt = now,
                privacyPolicyAccepted = null,
                termsAccepted = null,
                version = 1L,
                subscription = createDefaultSubscription(),
                preferences = createDefaultPreferences(),
                permissions = createDefaultPermissions(),
                usage = null,
                syncInfo = null,
                appSettings = createDefaultAppSettings(),
                communication = createDefaultCommunication(),
                usageStats = createDefaultUsageStats(),
                metadata = MetadataDto(
                    createdAt = now,
                    updatedAt = now,
                    importedAt = null,
                    source = "app_creation",
                    importId = null,
                    captureId = null,
                    version = 1L,
                    customData = null // Fix: Don't instantiate empty CustomData class
                )
            )

            // Validate the created user
            val validationResult = validateUser(defaultUser)
            if (validationResult is Result.Error) {
                return Result.Error(Exception("Default user validation failed: ${validationResult.exception.message}", validationResult.exception))
            }

            Log.d("UserMapper", "Created and validated default user for $userId")
            Result.Success(defaultUser)
        } catch (e: Exception) {
            Log.e("UserMapper", "Error creating default user for $userId", e)
            Result.Error(e)
        }
    }

    /**
     * Increment usage statistic with business logic validation.
     * Mirrors AddressMapper's business calculation approach.
     */
    fun incrementUsageStat(userId: String, statName: String, incrementBy: Long): Result<Unit> {
        return try {
            if (userId.isBlank()) {
                return Result.Error(IllegalArgumentException("User ID cannot be blank for usage stat increment"))
            }

            if (statName.isBlank()) {
                return Result.Error(IllegalArgumentException("Stat name cannot be blank"))
            }

            if (incrementBy < 0) {
                return Result.Error(IllegalArgumentException("Increment value must be non-negative: $incrementBy"))
            }

            // Validate stat name against known statistics
            val validStatNames = setOf(
                "deliveryCount", "addressCount", "totalRuns", "activeDaysCount",
                "mappingCount", "loginCount", "featureUsage"
            )

            if (statName !in validStatNames) {
                Log.w("UserMapper", "Unknown stat name '$statName' for user $userId")
            }

            Log.d("UserMapper", "Incrementing usage stat '$statName' by $incrementBy for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("UserMapper", "Error incrementing usage stat '$statName' for user $userId", e)
            Result.Error(e)
        }
    }

    /**
     * Perform initial setup with comprehensive business logic.
     * Mirrors AddressMapper's setup validation approach.
     */
    fun performInitialSetupIfNeeded(userId: String): Result<Unit> {
        return try {
            if (userId.isBlank()) {
                return Result.Error(IllegalArgumentException("User ID cannot be blank for initial setup"))
            }

            Log.d("UserMapper", "Performing initial setup validation for user $userId")

            // Business logic for initial setup validation
            // This would typically check if user needs onboarding, default preferences, etc.
            val setupTasks = mutableListOf<String>()

            // Add setup tasks based on business rules
            setupTasks.add("validate_user_profile")
            setupTasks.add("initialize_preferences")
            setupTasks.add("setup_default_permissions")
            setupTasks.add("create_usage_tracking")

            Log.d("UserMapper", "Initial setup tasks for user $userId: ${setupTasks.joinToString(", ")}")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("UserMapper", "Error performing initial setup for user $userId", e)
            Result.Error(e)
        }
    }

    /**
     * Handle user sign in with business logic validation.
     * Mirrors AddressMapper's comprehensive business rule handling.
     */
    fun handleUserSignIn(userId: String, authProvider: String): Result<Unit> {
        return try {
            if (userId.isBlank()) {
                return Result.Error(IllegalArgumentException("User ID cannot be blank for sign in"))
            }

            if (authProvider.isBlank()) {
                return Result.Error(IllegalArgumentException("Auth provider cannot be blank"))
            }

            // Validate auth provider
            val validProviders = setOf("google", "facebook", "apple", "email", "phone", "anonymous")
            if (authProvider.lowercase() !in validProviders) {
                Log.w("UserMapper", "Unknown auth provider '$authProvider' for user $userId")
            }

            val now = OffsetDateTime.now()
            Log.d("UserMapper", "Handling user sign in for $userId with provider $authProvider at $now")

            // Business logic for sign in would include:
            // - Update last login timestamp
            // - Track auth provider usage
            // - Update user session info
            // - Validate account status

            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("UserMapper", "Error handling user sign in for $userId with provider $authProvider", e)
            Result.Error(e)
        }
    }

    /**
     * Handle user sign out with cleanup business logic.
     * Mirrors AddressMapper's comprehensive cleanup approach.
     */
    fun handleUserSignOut(userId: String): Result<Unit> {
        return try {
            if (userId.isBlank()) {
                return Result.Error(IllegalArgumentException("User ID cannot be blank for sign out"))
            }

            val now = OffsetDateTime.now()
            Log.d("UserMapper", "Handling user sign out for $userId at $now")

            // Business logic for sign out would include:
            // - Clear sensitive cached data
            // - Update session end time
            // - Save final usage statistics
            // - Cleanup temporary data

            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("UserMapper", "Error handling user sign out for $userId", e)
            Result.Error(e)
        }
    }

    /**
     * Sync with auth profile with comprehensive validation.
     * Mirrors AddressMapper's external data sync approach.
     */
    fun syncWithAuthProfile(userId: String): Result<Unit> {
        return try {
            if (userId.isBlank()) {
                return Result.Error(IllegalArgumentException("User ID cannot be blank for auth sync"))
            }

            Log.d("UserMapper", "Syncing with auth profile for $userId")

            // Business logic for auth sync would include:
            // - Validate auth provider data
            // - Update profile information
            // - Sync permissions and roles
            // - Handle profile conflicts
            // - Update metadata timestamps

            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("UserMapper", "Error syncing with auth profile for $userId", e)
            Result.Error(e)
        }
    }

    // ===== HELPER METHODS =====

    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }

    private fun createDefaultSubscription(): UserSubscription {
        return UserSubscription(
            status = "free",
            level = "free",
            isActive = true,
            startDate = OffsetDateTime.now(),
            expiryDate = null,
            isLifetime = false,
            provider = null,
            orderId = null,
            verification = null
        )
    }

    private fun createDefaultPreferences(): UserPreferences {
        return UserPreferences(
            notificationsEnabled = false, // ✅ FIX: Default false - user must manually enable tip capture
            theme = "system",
            useLocation = true,
            dnd = null
        )
    }

    private fun createDefaultPermissions(): UserPermissions {
        return UserPermissions(
            bypassLimits = false,
            maxUploads = 50L
        )
    }

    private fun createDefaultAppSettings(): User_profile.UserAppSettings {
        return User_profile.UserAppSettings(
            dataCollectionOptIn = null,
            lastVersion = null,
            onboardingCompleted = false
        )
    }

    private fun createDefaultUsageStats(): UserUsageStats {
        return UserUsageStats(
            deliveryCount = 0L,
            tipCount = 0L, // ✅ CLEANUP FIX: Added tipCount field maintained by address-stats-updater
            addressCount = 0L,
            lastUsageDate = null,
            totalRuns = 0L,
            activeDaysCount = 0L,
            totalTips = 0.0,
            featureUsage = emptyMap()
        )
    }

    /**
     * Create default communication settings following business rules.
     * Mirrors AddressMapper's default object creation pattern.
     */
    private fun createDefaultCommunication(): UserCommunication {
        return UserCommunication(
            emailOptIn = false,
            marketingOptIn = false,
            pushNotificationsEnabled = true
        )
    }

    // ===== DATA MANAGEMENT METHODS =====
    // Following Clarity.md principles: all business logic in Mapper

    /**
     * Export user data with business logic validation.
     * Mirrors AddressMapper's comprehensive data handling approach.
     */
    fun exportUserData(user: User, format: String): Result<String> {
        return try {
            if (format.isBlank()) {
                return Result.Error(IllegalArgumentException("Export format cannot be blank"))
            }

            val validFormats = setOf("json", "csv", "xml")
            if (format.lowercase() !in validFormats) {
                return Result.Error(IllegalArgumentException("Unsupported export format: $format"))
            }

            // Business logic for data export
            val exportData = when (format.lowercase()) {
                "json" -> exportToJson(user)
                "csv" -> exportToCsv(user)
                "xml" -> exportToXml(user)
                else -> return Result.Error(IllegalArgumentException("Unsupported format: $format"))
            }

            Log.d("UserMapper", "Successfully exported user data for ${user.id} in $format format")
            Result.Success(exportData)
        } catch (e: Exception) {
            Log.e("UserMapper", "Error exporting user data for ${user.id}", e)
            Result.Error(e)
        }
    }

    /**
     * Import user data with business logic validation.
     * Mirrors AddressMapper's comprehensive validation approach.
     */
    fun importUserData(userId: String, data: Map<String, Any>): Result<User> {
        return try {
            if (userId.isBlank()) {
                return Result.Error(IllegalArgumentException("User ID cannot be blank for import"))
            }

            if (data.isEmpty()) {
                return Result.Error(IllegalArgumentException("Import data cannot be empty"))
            }

            // Business logic for data validation and import
            val importedUser = parseImportData(userId, data)
            val validationResult = validateUser(importedUser)
            
            when (validationResult) {
                is Result.Success -> {
                    Log.d("UserMapper", "Successfully imported and validated user data for $userId")
                    Result.Success(importedUser)
                }
                is Result.Error -> {
                    Log.e("UserMapper", "Validation failed for imported user data: ${validationResult.exception.message}")
                    validationResult
                }
                is Result.Loading -> Result.Error(IllegalStateException("Validation returned Loading unexpectedly"))
            }
        } catch (e: Exception) {
            Log.e("UserMapper", "Error importing user data for $userId", e)
            Result.Error(e)
        }
    }

    /**
     * Create user backup with business logic.
     * Mirrors AddressMapper's comprehensive data handling approach.
     */
    fun createUserBackup(user: User): Result<Map<String, Any>> {
        return try {
            val backupData = mutableMapOf<String, Any>()
            
            // Core user data
            backupData["userId"] = user.userId ?: ""
            backupData["email"] = user.email ?: ""
            backupData["displayName"] = user.displayName ?: ""
            backupData["accountStatus"] = user.accountStatus ?: "active"
            backupData["createdAt"] = user.createdAt?.toString() ?: ""
            backupData["version"] = user.version ?: 1L
            
            // Preferences and settings
            user.preferences?.let { prefs ->
                backupData["preferences"] = mapOf(
                    "notificationsEnabled" to prefs.notificationsEnabled,
                    "theme" to (prefs.theme ?: "system"),
                    "useLocation" to prefs.useLocation
                )
            }
            
            // Usage statistics
            user.usageStats?.let { stats ->
                backupData["usageStats"] = mapOf(
                    "deliveryCount" to stats.deliveryCount,
                    "addressCount" to stats.addressCount,
                    "totalRuns" to stats.totalRuns,
                    "totalTips" to stats.totalTips
                )
            }
            
            // Metadata
            backupData["backupTimestamp"] = OffsetDateTime.now().toString()
            backupData["backupVersion"] = "1.0"
            
            Log.d("UserMapper", "Successfully created backup for user ${user.id}")
            Result.Success(backupData.toMap())
        } catch (e: Exception) {
            Log.e("UserMapper", "Error creating backup for user ${user.id}", e)
            Result.Error(e)
        }
    }

    /**
     * Restore user backup with business logic validation.
     * Mirrors AddressMapper's comprehensive validation approach.
     */
    fun restoreUserBackup(userId: String, backup: Map<String, Any>): Result<User> {
        return try {
            if (userId.isBlank()) {
                return Result.Error(IllegalArgumentException("User ID cannot be blank for restore"))
            }

            if (backup.isEmpty()) {
                return Result.Error(IllegalArgumentException("Backup data cannot be empty"))
            }

            // Business logic for backup validation and restoration
            val restoredUser = parseBackupData(userId, backup)
            val validationResult = validateUser(restoredUser)
            
            when (validationResult) {
                is Result.Success -> {
                    Log.d("UserMapper", "Successfully restored and validated user backup for $userId")
                    Result.Success(restoredUser)
                }
                is Result.Error -> {
                    Log.e("UserMapper", "Validation failed for restored user backup: ${validationResult.exception.message}")
                    validationResult
                }
                is Result.Loading -> Result.Error(IllegalStateException("Validation returned Loading unexpectedly"))
            }
        } catch (e: Exception) {
            Log.e("UserMapper", "Error restoring user backup for $userId", e)
            Result.Error(e)
        }
    }

    /**
     * Migrate user data with business logic validation.
     * Mirrors AddressMapper's comprehensive migration approach.
     */
    fun migrateUserData(user: User, fromVersion: Long, toVersion: Long): Result<User> {
        return try {
            if (fromVersion == toVersion) {
                Log.d("UserMapper", "No migration needed for user ${user.id}: versions match ($fromVersion)")
                return Result.Success(user)
            }

            if (fromVersion > toVersion) {
                return Result.Error(IllegalArgumentException("Cannot migrate backwards: from $fromVersion to $toVersion"))
            }

            // Business logic for data migration
            var migratedUser = user
            for (version in (fromVersion + 1)..toVersion) {
                migratedUser = when (version) {
                    2L -> migrateToVersion2(migratedUser)
                    3L -> migrateToVersion3(migratedUser)
                    // Add more migration steps as needed
                    else -> {
                        Log.w("UserMapper", "No migration logic for version $version")
                        migratedUser
                    }
                }
            }

            // Update version and validate
            migratedUser = migratedUser.copy(version = toVersion)
            val validationResult = validateUser(migratedUser)
            
            when (validationResult) {
                is Result.Success -> {
                    Log.d("UserMapper", "Successfully migrated user ${user.id} from version $fromVersion to $toVersion")
                    Result.Success(migratedUser)
                }
                is Result.Error -> {
                    Log.e("UserMapper", "Validation failed for migrated user: ${validationResult.exception.message}")
                    validationResult
                }
                is Result.Loading -> Result.Error(IllegalStateException("Validation returned Loading unexpectedly"))
            }
        } catch (e: Exception) {
            Log.e("UserMapper", "Error migrating user data for ${user.id}", e)
            Result.Error(e)
        }
    }

    // ===== PRIVATE HELPER METHODS =====

    private fun exportToJson(user: User): String {
        // Simplified JSON export - in real implementation, use proper JSON serialization
        return """{"userId":"${user.userId}","email":"${user.email}","displayName":"${user.displayName}"}"""
    }

    private fun exportToCsv(user: User): String {
        // Simplified CSV export
        return "userId,email,displayName\n${user.userId},${user.email},${user.displayName}"
    }

    private fun exportToXml(user: User): String {
        // Simplified XML export
        return "<user><userId>${user.userId}</userId><email>${user.email}</email><displayName>${user.displayName}</displayName></user>"
    }

    private fun parseImportData(userId: String, data: Map<String, Any>): User {
        val now = OffsetDateTime.now()
        return User(
            id = userId,
            userId = userId,
            email = data["email"] as? String,
            displayName = data["displayName"] as? String ?: "Imported User",
            defaultAddressId = data["defaultAddressId"] as? String,
            photoUrl = data["photoUrl"] as? String,
            authProviders = (data["authProviders"] as? List<*>)?.filterIsInstance<String>() ?: emptyList(),
            accountStatus = data["accountStatus"] as? String ?: "active",
            timezone = data["timezone"] as? String,
            createdAt = now,
            lastLoginAt = now,
            privacyPolicyAccepted = parseUniversalTimestamp(data["privacyPolicyAccepted"]),
            termsAccepted = parseUniversalTimestamp(data["termsAccepted"]),
            version = (data["version"] as? Number)?.toLong() ?: 1L,
            subscription = createDefaultSubscription(),
            preferences = createDefaultPreferences(),
            permissions = createDefaultPermissions(),
            usage = null,
            syncInfo = null,
            appSettings = createDefaultAppSettings(),
            communication = createDefaultCommunication(),
            usageStats = createDefaultUsageStats(),
            metadata = MetadataDto(
                createdAt = now,
                updatedAt = now,
                importedAt = null,
                source = "import",
                importId = null,
                captureId = null,
                version = 1L,
                customData = null // Fix: Don't instantiate empty CustomData class
            )
        )
    }

    private fun parseBackupData(userId: String, backup: Map<String, Any>): User {
        val now = OffsetDateTime.now()
        return User(
            id = userId,
            userId = userId,
            email = backup["email"] as? String,
            displayName = backup["displayName"] as? String ?: "Restored User",
            defaultAddressId = backup["defaultAddressId"] as? String,
            photoUrl = backup["photoUrl"] as? String,
            authProviders = (backup["authProviders"] as? List<*>)?.filterIsInstance<String>() ?: emptyList(),
            accountStatus = backup["accountStatus"] as? String ?: "active",
            timezone = backup["timezone"] as? String,
            createdAt = now,
            lastLoginAt = now,
            privacyPolicyAccepted = parseUniversalTimestamp(backup["privacyPolicyAccepted"]),
            termsAccepted = parseUniversalTimestamp(backup["termsAccepted"]),
            version = (backup["version"] as? Number)?.toLong() ?: 1L,
            subscription = createDefaultSubscription(),
            preferences = createDefaultPreferences(),
            permissions = createDefaultPermissions(),
            usage = null,
            syncInfo = null,
            appSettings = createDefaultAppSettings(),
            communication = createDefaultCommunication(),
            usageStats = createDefaultUsageStats(),
            metadata = MetadataDto(
                createdAt = now,
                updatedAt = now,
                importedAt = null,
                source = "restore",
                importId = null,
                captureId = null,
                version = 1L,
                customData = null // Fix: Don't instantiate empty CustomData class
            )
        )
    }

    private fun migrateToVersion2(user: User): User {
        // Example migration logic for version 2
        return user.copy(
            // Add any version 2 specific changes
            metadata = user.metadata?.copy(updatedAt = OffsetDateTime.now())
        )
    }

    private fun migrateToVersion3(user: User): User {
        // Example migration logic for version 3
        return user.copy(
            // Add any version 3 specific changes
            metadata = user.metadata?.copy(updatedAt = OffsetDateTime.now())
        )
    }

    // ===== ADDITIONAL BUSINESS LOGIC FUNCTIONS =====
    // Following DeliveryMapper pattern: ALL domain business logic belongs in mapper



    /**
     * Get user statistics from User model
     */
    fun getUserStats(user: User): UserUsageStats? {
        return user.usageStats
    }

    /**
     * Get account creation date from User model
     */
    fun getAccountCreationDate(user: User): OffsetDateTime? {
        return user.createdAt
    }
}