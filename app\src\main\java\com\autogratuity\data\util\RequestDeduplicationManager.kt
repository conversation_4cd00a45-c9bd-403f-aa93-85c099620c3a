package com.autogratuity.data.util

import android.util.Log
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.withTimeoutOrNull
import java.util.concurrent.ConcurrentHashMap
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds
import kotlin.time.TimeSource

/**
 * Request Deduplication Manager for preventing duplicate Firestore operations.
 * 
 * Addresses the critical performance issue where the same Firestore documents
 * are being fetched multiple times simultaneously, causing 8+ second delays.
 * 
 * Key Features:
 * - Deduplicates requests by operation key (collection + document ID)
 * - Automatic cleanup of completed requests
 * - Timeout handling to prevent memory leaks
 * - Comprehensive monitoring integration
 * - Thread-safe concurrent operations
 * 
 * Usage:
 * ```kotlin
 * val result = requestDeduplicationManager.deduplicateRequest(
 *     key = "users/userId123",
 *     timeout = 5.seconds,
 *     operation = { firestore.collection("users").document("userId123").get().await() }
 * )
 * ```
 */
class RequestDeduplicationManager {
    
    private val TAG = "RequestDeduplicationManager"
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // Active requests mapped by operation key
    private val activeRequests = ConcurrentHashMap<String, ActiveRequest<*>>()
    
    // Request statistics for monitoring
    private var totalRequests = 0L
    private var deduplicatedRequests = 0L
    private var timeoutRequests = 0L
    
    data class ActiveRequest<T>(
        val deferred: Deferred<T>,
        val startTime: TimeSource.Monotonic.ValueTimeMark,
        val timeout: Duration,
        val requestCount: Int = 1
    )
    
    /**
     * Deduplicate a request by key. If a request with the same key is already in progress,
     * returns the result of that request instead of starting a new one.
     * 
     * @param key Unique identifier for the operation (e.g., "users/userId")
     * @param timeout Maximum time to wait for the operation
     * @param operation The suspend function to execute if no duplicate is found
     * @return Result of the operation or null if timeout occurred
     */
    suspend fun <T> deduplicateRequest(
        key: String,
        timeout: Duration = 5.seconds,
        operation: suspend () -> T
    ): T? {
        val startTime = TimeSource.Monotonic.markNow()
        totalRequests++
        
        try {
            // Check if request is already in progress
            val existingRequest = activeRequests[key]
            
            if (existingRequest != null) {
                // Request already in progress - wait for it
                deduplicatedRequests++
                Log.d(TAG, "DEDUPLICATION: Found existing request for key '$key', waiting for result")
                
                // Update request count for monitoring
                val updatedRequest = existingRequest.copy(requestCount = existingRequest.requestCount + 1)
                activeRequests[key] = updatedRequest
                
                // Wait for existing request with timeout
                val result = withTimeoutOrNull(timeout) {
                    @Suppress("UNCHECKED_CAST")
                    (existingRequest.deferred as Deferred<T>).await()
                }
                
                if (result == null) {
                    timeoutRequests++
                    Log.w(TAG, "DEDUPLICATION: Timeout waiting for existing request '$key' after ${timeout.inWholeMilliseconds}ms")
                    // ✅ DEBUG: Log active requests to help diagnose stale entries
                    val activeInfo = getActiveRequestsInfo()
                    Log.d(TAG, "DEDUPLICATION: Active requests after timeout: ${activeInfo.size} entries")
                    activeInfo.forEach { info ->
                        Log.d(TAG, "DEDUPLICATION: Active request '${info.key}' - elapsed: ${info.elapsedTime.inWholeMilliseconds}ms, timeout: ${info.timeout.inWholeMilliseconds}ms, timedOut: ${info.isTimedOut}")
                    }
                }
                
                val duration = startTime.elapsedNow()
                Log.d(TAG, "DEDUPLICATION: Completed waiting for '$key' in ${duration.inWholeMilliseconds}ms, result: ${result != null}")
                
                return result
            } else {
                // No existing request - start new one
                Log.d(TAG, "DEDUPLICATION: Starting new request for key '$key'")
                
                val deferred = scope.async {
                    try {
                        operation()
                    } catch (e: Exception) {
                        Log.e(TAG, "DEDUPLICATION: Operation failed for key '$key'", e)
                        throw e
                    } finally {
                        // Clean up completed request
                        activeRequests.remove(key)
                        Log.d(TAG, "DEDUPLICATION: Cleaned up completed request for key '$key'")
                    }
                }
                
                // Store the active request
                val activeRequest = ActiveRequest(
                    deferred = deferred,
                    startTime = startTime,
                    timeout = timeout
                )
                activeRequests[key] = activeRequest
                
                // Wait for the operation with timeout
                val result = withTimeoutOrNull(timeout) {
                    deferred.await()
                }
                
                if (result == null) {
                    timeoutRequests++
                    Log.w(TAG, "DEDUPLICATION: Timeout for new request '$key' after ${timeout.inWholeMilliseconds}ms")
                    // ✅ DEBUG: Log timeout details for new requests
                    Log.d(TAG, "DEDUPLICATION: New request timeout - key: '$key', configured timeout: ${timeout.inWholeMilliseconds}ms, actual elapsed: ${startTime.elapsedNow().inWholeMilliseconds}ms")
                    // Cancel the deferred operation
                    deferred.cancel()
                    activeRequests.remove(key)
                }
                
                val duration = startTime.elapsedNow()
                Log.d(TAG, "DEDUPLICATION: Completed new request '$key' in ${duration.inWholeMilliseconds}ms, result: ${result != null}")
                
                return result
            }
        } catch (e: Exception) {
            Log.e(TAG, "DEDUPLICATION: Unexpected error for key '$key'", e)
            activeRequests.remove(key)
            return null
        }
    }
    
    /**
     * Get current deduplication statistics for monitoring
     */
    fun getStatistics(): DeduplicationStatistics {
        return DeduplicationStatistics(
            totalRequests = totalRequests,
            deduplicatedRequests = deduplicatedRequests,
            timeoutRequests = timeoutRequests,
            activeRequestsCount = activeRequests.size,
            deduplicationRate = if (totalRequests > 0) deduplicatedRequests.toDouble() / totalRequests else 0.0
        )
    }
    
    /**
     * Clear all active requests (useful for testing or emergency cleanup)
     */
    fun clearActiveRequests() {
        Log.w(TAG, "DEDUPLICATION: Clearing ${activeRequests.size} active requests")
        activeRequests.values.forEach { it.deferred.cancel() }
        activeRequests.clear()
    }
    
    /**
     * Clean up timed-out requests to prevent memory leaks
     */
    fun cleanupTimedOutRequests() {
        val now = TimeSource.Monotonic.markNow()
        val toRemove = mutableListOf<String>()
        
        activeRequests.forEach { (key, request) ->
            if (request.startTime.elapsedNow() > request.timeout) {
                toRemove.add(key)
                request.deferred.cancel()
            }
        }
        
        toRemove.forEach { key ->
            activeRequests.remove(key)
            Log.d(TAG, "DEDUPLICATION: Cleaned up timed-out request for key '$key'")
        }
        
        if (toRemove.isNotEmpty()) {
            Log.i(TAG, "DEDUPLICATION: Cleaned up ${toRemove.size} timed-out requests")
        }
    }
    
    /**
     * Get detailed information about active requests for debugging
     */
    fun getActiveRequestsInfo(): List<ActiveRequestInfo> {
        val now = TimeSource.Monotonic.markNow()
        return activeRequests.map { (key, request) ->
            ActiveRequestInfo(
                key = key,
                elapsedTime = request.startTime.elapsedNow(),
                timeout = request.timeout,
                requestCount = request.requestCount,
                isTimedOut = request.startTime.elapsedNow() > request.timeout
            )
        }
    }
    
    data class DeduplicationStatistics(
        val totalRequests: Long,
        val deduplicatedRequests: Long,
        val timeoutRequests: Long,
        val activeRequestsCount: Int,
        val deduplicationRate: Double
    )
    
    data class ActiveRequestInfo(
        val key: String,
        val elapsedTime: Duration,
        val timeout: Duration,
        val requestCount: Int,
        val isTimedOut: Boolean
    )
}

/**
 * Standardized deduplication keys for all repository operations.
 *
 * Key Format Standards:
 * - Reflects actual Firestore structure: users/{userId}/subcollection
 * - Use forward slashes for hierarchical paths matching Firestore paths
 * - Include operation context to prevent cross-operation conflicts
 * - Consistent with actual data access patterns
 *
 * Firestore Structure:
 * - users/{userId} (profile data)
 * - users/{userId}/addresses/{addressId}
 * - users/{userId}/deliveries/{deliveryId}
 * - users/{userId}/subscription (subscription data)
 * - config/app (global app config)
 * - config/notifications (global notification patterns)
 *
 * Timeout Standards:
 * - Quick operations (cache checks): 3-5 seconds
 * - Standard CRUD operations: 8-10 seconds
 * - Complex operations (creation, updates): 12-15 seconds
 * - Bulk operations: 20+ seconds
 */
object RequestKeys {

    // ===== USER OPERATIONS =====
    fun userProfile(userId: String) = "users/$userId"
    fun userProfileCreate(userId: String) = "users/create/$userId"
    fun userProfileUpdate(userId: String) = "users/update/$userId"
    fun userExistsByEmail(email: String) = "users/exists/email/${email.hashCode()}" // Hash email for privacy
    fun userCreateDefault(userId: String) = "users/create/default/$userId"

    // ===== SUBSCRIPTION OPERATIONS (nested under users) =====
    fun userSubscription(userId: String) = "users/$userId/subscription"
    fun userSubscriptionUpdate(userId: String) = "users/$userId/subscription/update"
    fun userSubscriptionHistory(userId: String) = "users/$userId/subscription/history"
    fun userSubscriptionVerify(userId: String) = "users/$userId/subscription/verify"
    fun userSubscriptionValidate(userId: String) = "users/$userId/subscription/validate"

    // ===== ADDRESS OPERATIONS (nested under users) =====
    fun userAddresses(userId: String) = "users/$userId/addresses"
    fun addressById(userId: String, addressId: String) = "users/$userId/addresses/$addressId"
    fun addressCreate(userId: String) = "users/$userId/addresses/create"
    fun addressUpdate(userId: String, addressId: String) = "users/$userId/addresses/update/$addressId"
    fun addressDelete(userId: String, addressId: String) = "users/$userId/addresses/delete/$addressId"
    fun addressSetDefault(userId: String, addressId: String) = "users/$userId/addresses/default/$addressId"
    fun addressDndUpdate(userId: String, addressId: String) = "users/$userId/addresses/dnd/$addressId"
    fun addressFlagsUpdate(userId: String, addressId: String) = "users/$userId/addresses/flags/$addressId"
    fun addressFromPlace(userId: String, placeId: String) = "users/$userId/addresses/place/${placeId.hashCode()}"

    // ===== ADDRESS FIELD UPDATE OPERATIONS =====
    fun addressFieldUpdate(userId: String, addressId: String, fieldName: String) = "users/$userId/addresses/field/$addressId/$fieldName"
    fun addressFavoriteUpdate(userId: String, addressId: String) = "users/$userId/addresses/favorite/$addressId"
    fun addressVerifiedUpdate(userId: String, addressId: String) = "users/$userId/addresses/verified/$addressId"
    fun addressAccessIssuesUpdate(userId: String, addressId: String) = "users/$userId/addresses/access/$addressId"
    fun addressNotesUpdate(userId: String, addressId: String) = "users/$userId/addresses/notes/$addressId"
    fun addressTagsUpdate(userId: String, addressId: String) = "users/$userId/addresses/tags/$addressId"

    // ===== DELIVERY OPERATIONS (nested under users) =====
    fun userDeliveries(userId: String) = "users/$userId/deliveries"
    fun userDeliveriesRecent(userId: String, limit: Int) = "users/$userId/deliveries/recent/$limit"
    fun deliveryById(userId: String, deliveryId: String) = "users/$userId/deliveries/$deliveryId"
    fun deliveryCreate(userId: String) = "users/$userId/deliveries/create"
    fun deliveryUpdate(userId: String, deliveryId: String) = "users/$userId/deliveries/update/$deliveryId"
    fun deliveryDelete(userId: String, deliveryId: String) = "users/$userId/deliveries/delete/$deliveryId"

    // ===== DELIVERY QUERY OPERATIONS =====
    fun deliveryCount(userId: String) = "users/$userId/deliveries/count"
    fun deliveriesByStatus(userId: String, status: String) = "users/$userId/deliveries/status/${status.hashCode()}"
    fun deliveriesTipped(userId: String, limit: Int) = "users/$userId/deliveries/tipped/$limit"
    fun deliveriesUntipped(userId: String, limit: Int) = "users/$userId/deliveries/untipped/$limit"
    fun deliveriesCompletedUntipped(userId: String) = "users/$userId/deliveries/completed_untipped"
    fun deliveriesByTimeRange(userId: String, startDate: String, endDate: String) = "users/$userId/deliveries/timerange/${startDate}_$endDate"
    fun deliveriesByOrderId(userId: String, orderId: String) = "users/$userId/deliveries/orderid/${orderId.hashCode()}"
    fun deliveriesByAddressId(userId: String, addressId: String) = "users/$userId/deliveries/address/$addressId"
    fun deliveriesByMetadataSource(userId: String, source: String) = "users/$userId/deliveries/metadata_source/${source.hashCode()}"
    fun deliveriesImportedVerifiedDnd(userId: String, beforeDate: String) = "users/$userId/deliveries/imported_verified_dnd/${beforeDate.hashCode()}"
    fun deliveryExistsByOrderId(userId: String, orderId: String) = "users/$userId/deliveries/exists_orderid/${orderId.hashCode()}"
    fun deliveryByMetadataOrderId(userId: String, orderId: String) = "users/$userId/deliveries/metadata_orderid/${orderId.hashCode()}"

    // ===== CONFIG OPERATIONS (global, not user-specific) =====
    fun appConfig() = "config/app/global"
    fun notificationPatterns() = "config/notifications/patterns"
    fun configValue(key: String) = "config/value/${key.hashCode()}" // Hash key for consistency

    // ===== CACHE WARMING OPERATIONS =====
    fun cacheWarmingComplete(userId: String) = "cache/warming/complete/$userId"
    fun cacheWarmingUser(userId: String) = "cache/warming/users/$userId"
    fun cacheWarmingAddresses(userId: String) = "cache/warming/users/$userId/addresses"
    fun cacheWarmingDeliveries(userId: String) = "cache/warming/users/$userId/deliveries"
    fun cacheWarmingSubscription(userId: String) = "cache/warming/users/$userId/subscription"
    fun cacheWarmingPreferences(userId: String) = "cache/warming/users/$userId/preferences"
    fun cacheWarmingConfig(userId: String) = "cache/warming/config/global" // Config is global, not user-specific
}

/**
 * Standardized timeout values for different operation types.
 * Use these constants to ensure consistent timeout behavior across repositories.
 */
object RequestTimeouts {
    val QUICK_OPERATION = 5.seconds        // Cache checks, simple queries
    val STANDARD_OPERATION = 10.seconds    // Standard CRUD operations
    val COMPLEX_OPERATION = 15.seconds     // Creation, updates, complex queries
    val BULK_OPERATION = 25.seconds        // Bulk operations, imports
    val CACHE_WARMING = 8.seconds          // Cache warming operations
}
