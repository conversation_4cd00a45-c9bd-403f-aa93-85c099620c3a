<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="12dp"
    android:background="@drawable/info_window_background"
    android:minWidth="200dp">

    <!-- Address Title -->
    <TextView
        android:id="@+id/addressText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Address"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:maxLines="2"
        android:ellipsize="end" />

    <!-- Status Row -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="4dp"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/statusIcon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="6dp"
            android:src="@drawable/ic_pending_transparent" />

        <TextView
            android:id="@+id/statusText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Status"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray" />

    </LinearLayout>

    <!-- Stats Row -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="4dp">

        <TextView
            android:id="@+id/deliveryCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0 deliveries"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/avgTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Avg: $0.00"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray" />

    </LinearLayout>

</LinearLayout>