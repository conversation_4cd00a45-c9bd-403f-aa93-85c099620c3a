/*
 * Times.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

import java.time.OffsetDateTime

/**
 * Represents timestamps related to a Delivery lifecycle.
 */
data class Times(
    /** Timestamp when the delivery was accepted. */
    val acceptedAt: OffsetDateTime? = null,
    /** Timestamp when the delivery was completed. */
    val completedAt: OffsetDateTime? = null,
    /** Timestamp when the tip was received. */
    val tippedAt: OffsetDateTime? = null
)
