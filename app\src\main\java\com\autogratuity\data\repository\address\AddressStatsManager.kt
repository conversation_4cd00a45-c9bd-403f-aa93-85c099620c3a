package com.autogratuity.data.repository.address

import android.util.Log
import com.autogratuity.data.model.generated_kt.Address
import com.autogratuity.data.model.state.DndDisplayState
import com.autogratuity.data.mapper.AddressDndMapper
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.tasks.await
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton
import com.autogratuity.data.model.generated_kt.Coordinates
import com.autogratuity.data.model.generated_kt.Delivery_stats
import com.autogratuity.data.model.generated_kt.Flags
import com.autogratuity.data.model.generated_kt.Metadata
import com.autogratuity.data.model.generated_kt.Platform
import java.time.OffsetDateTime
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

@Singleton
class AddressStatsManager @Inject constructor(
    private val firestore: FirebaseFirestore
) {

    private val TAG = "AddressStatsManager"

    private companion object {
        private const val USERS_COLLECTION = "users"
        private const val USER_ADDRESSES_SUBCOLLECTION = "user_addresses"
        
        // ===== SAFE CASTING UTILITIES =====
        
        /**
         * Safely casts Any? to Map<String, Any?> without unchecked cast warnings
         */
        @Suppress("UNCHECKED_CAST")
        private fun Any?.safeCastToStringMap(): Map<String, Any?>? {
            return this as? Map<String, Any?>
        }

        /**
         * Safely extracts String value from map
         */
        private fun Map<String, Any?>.getStringValueSafely(key: String): String? {
            return this[key] as? String
        }

        /**
         * Safely extracts Boolean value from map
         */
        private fun Map<String, Any?>.getBooleanValueSafely(key: String): Boolean? {
            return this[key] as? Boolean
        }

        /**
         * Safely extracts Long value from map
         */
        private fun Map<String, Any?>.getLongValueSafely(key: String): Long? {
            return this[key] as? Long
        }

        /**
         * Safely extracts Double value from map
         */
        private fun Map<String, Any?>.getDoubleValueSafely(key: String): Double? {
            return this[key] as? Double
        }

        /**
         * Safely extracts List<String> value from map
         */
        @Suppress("UNCHECKED_CAST")
        private fun Map<String, Any?>.getStringListSafely(key: String): List<String>? {
            return this[key] as? List<String>
        }

        /**
         * Safely extracts nested map from map
         */
        private fun Map<String, Any?>.getNestedMapSafely(key: String): Map<String, Any?>? {
            return this[key].safeCastToStringMap()
        }

        // ===== END SAFE CASTING UTILITIES =====
        
        /**
         * ✅ DEPRECATED: Use parseUniversalTimestamp() from DtoUtils for better standardization
         * Kept for backward compatibility but delegates to the universal function.
         */
        @Deprecated("Use parseUniversalTimestamp() from DtoUtils for better standardization")
        private fun parseOffsetDateTime(dateObj: Any?): OffsetDateTime? {
            return com.autogratuity.data.model.util_kt.parseUniversalTimestamp(dateObj)
        }

        // ✅ MODERN TYPESAFE: Updated mapToAddressObject with safe casting utilities
        private fun mapToAddressObject(docId: String, addressDataMap: Map<String, Any?>?): Address? {
            if (addressDataMap == null) {
                Log.w("AddressStatsManager", "mapToAddressObject: addressDataMap is null for docId $docId")
                return null
            }

            fun getNestedMap(key: String): Map<String, Any?>? {
                return addressDataMap[key].safeCastToStringMap()
            }

            val components = getNestedMap("components")?.let {
                Address.Components(
                    streetNumber = it.getStringValueSafely("streetNumber"),
                    streetName = it.getStringValueSafely("streetName"),
                    city = it.getStringValueSafely("city"),
                    state = it.getStringValueSafely("state"),
                    postalCode = it.getStringValueSafely("postalCode"),
                    country = it.getStringValueSafely("country")
                )
            }

            val coordinates = getNestedMap("coordinates")?.let {
                Coordinates(
                    latitude = it.getDoubleValueSafely("latitude"),
                    longitude = it.getDoubleValueSafely("longitude")
                )
            }

            val searchFields = getNestedMap("searchFields")?.let {
                Address.SearchFields(
                    searchTerms = it.getStringListSafely("searchTerms"),
                    normalizedKey = it.getStringValueSafely("normalizedKey")
                )
            }

            val deliveryStats = getNestedMap("deliveryStats")?.let {
                Delivery_stats(
                    deliveryCount = it.getLongValueSafely("deliveryCount"),
                    tipCount = it.getLongValueSafely("tipCount"),
                    totalTips = it.getDoubleValueSafely("totalTips"),
                    highestTip = it.getDoubleValueSafely("highestTip"),
                    pendingCount = it.getLongValueSafely("pendingCount"),
                    averageTimeMinutes = it.getDoubleValueSafely("averageTimeMinutes"),
                    lastDeliveryDate = com.autogratuity.data.model.util_kt.parseUniversalTimestamp(it["lastDeliveryDate"]),
                    averageTipAmount = it.getDoubleValueSafely("averageTipAmount"),
                    lastDeliveryTimestamp = com.autogratuity.data.model.util_kt.parseUniversalTimestamp(it["lastDeliveryTimestamp"])
                )
            }

            val flags = getNestedMap("flags")?.let {
                Flags(
                    isFavorite = it.getBooleanValueSafely("isFavorite"),
                    isVerified = it.getBooleanValueSafely("isVerified"),
                    doNotDeliver = it.getBooleanValueSafely("doNotDeliver"),
                    isApartment = it.getBooleanValueSafely("isApartment"),
                    isArchived = it.getBooleanValueSafely("isArchived"),
                    hasAccessIssues = it.getBooleanValueSafely("hasAccessIssues"),
                    manualDndState = it.getStringValueSafely("manualDndState"),
                    dndSource = it.getStringValueSafely("dndSource")
                )
            }

            val metadata = getNestedMap("metadata")?.let {
                val customDataMap = it.getNestedMapSafely("customData")
                val customData = null // Fix: Don't instantiate empty CustomData class
                Metadata(
                    createdAt = com.autogratuity.data.model.util_kt.parseUniversalTimestamp(it["createdAt"]),
                    updatedAt = com.autogratuity.data.model.util_kt.parseUniversalTimestamp(it["updatedAt"]),
                    importedAt = com.autogratuity.data.model.util_kt.parseUniversalTimestamp(it["importedAt"]),
                    source = it.getStringValueSafely("source"),
                    importId = it.getStringValueSafely("importId"),
                    captureId = it.getStringValueSafely("captureId"),
                    version = it.getLongValueSafely("version"),
                    customData = customData
                )
            }

            val platform = getNestedMap("platform")?.let {
                Platform(
                    name = it.getStringValueSafely("name"),
                    type = it.getStringValueSafely("type"),
                    version = it.getStringValueSafely("version"),
                    source = it.getStringValueSafely("source"),
                    displayName = it.getStringValueSafely("displayName"),
                    iconUrl = it.getStringValueSafely("iconUrl")
                )
            }

            val addressData = Address.AddressData(
                userId = addressDataMap.getStringValueSafely("userId"),
                fullAddress = addressDataMap.getStringValueSafely("fullAddress"),
                normalizedAddress = addressDataMap.getStringValueSafely("normalizedAddress"),
                placeId = addressDataMap.getStringValueSafely("placeId"),
                isDefault = addressDataMap.getBooleanValueSafely("isDefault"),
                notes = addressDataMap.getStringValueSafely("notes"),
                tags = addressDataMap.getStringListSafely("tags"),
                orderIds = addressDataMap.getStringListSafely("orderIds"),
                searchTerms = addressDataMap.getStringListSafely("searchTerms"),
                components = components,
                coordinates = coordinates,
                searchFields = searchFields,
                deliveryStats = deliveryStats,
                flags = flags,
                metadata = metadata,
                platform = platform
            )
            return Address(id = docId, addressData = addressData)
        }
    }

    // ===== INSTANCE-LEVEL SAFE CASTING UTILITIES FOR PUBLIC METHODS =====
    
    /**
     * Safely casts Any? to Map<String, Any?> without unchecked cast warnings
     */
    @Suppress("UNCHECKED_CAST")
    private fun Any?.safeCastToStringMap(): Map<String, Any?>? {
        return this as? Map<String, Any?>
    }

    // ===== END INSTANCE-LEVEL SAFE CASTING UTILITIES =====

    @Deprecated("Replaced by AddressRepositoryImpl, which uses AddressFirestoreAccess.getBestTippingAddresses. This method directly queries Firestore, uses outdated model instantiation, and should no longer be called directly.")
    @Suppress("UNUSED_PARAMETER")
    fun getBestTippingAddresses(limit: Int, userId: String?): List<Address> {
        Log.e(TAG, "DEPRECATED: AddressStatsManager.getBestTippingAddresses was called. This method is outdated and should not be used. Use AddressRepositoryImpl to fetch best tipping addresses.")
        return emptyList()
    }

    suspend fun getRecentlyUsedAddresses(limit: Int, userId: String?): List<Address> {
        if (userId.isNullOrBlank()) {
            Log.w(TAG, "User ID is blank. Cannot fetch recently used addresses.")
            return emptyList()
        }

        val querySnapshot = firestore.collection(USERS_COLLECTION).document(userId)
            .collection(USER_ADDRESSES_SUBCOLLECTION)
            .whereNotEqualTo("addressData.deliveryStats.lastDeliveryTimestamp", null)
            .orderBy("addressData.deliveryStats.lastDeliveryTimestamp", Query.Direction.DESCENDING)
            .limit(limit.toLong())
            .get()
            .await()

        return querySnapshot.documents.mapNotNull { doc ->
            try {
                val docId = doc.id
                // ✅ FIXED: Using safeCastToStringMap() to eliminate unchecked cast warning
                val addressDataMap = doc.data?.get("addressData").safeCastToStringMap()
                mapToAddressObject(docId, addressDataMap)
            } catch (ex: Exception) {
                Log.e(TAG, "Error converting recently used address ${doc.id}", ex)
                null
            }
        }
    }

    suspend fun getAddressesWithCoordinates(userId: String?): List<Address> {
        if (userId.isNullOrBlank()) {
            Log.w(TAG, "User ID is blank. Cannot fetch addresses with coordinates.")
            return emptyList()
        }

        val querySnapshot = firestore.collection(USERS_COLLECTION).document(userId)
            .collection(USER_ADDRESSES_SUBCOLLECTION)
            .get()
            .await()

        return querySnapshot.documents.mapNotNull { doc ->
            try {
                val docId = doc.id
                // ✅ FIXED: Using safeCastToStringMap() to eliminate unchecked cast warning
                val addressDataMap = doc.data?.get("addressData").safeCastToStringMap()
                val address = mapToAddressObject(docId, addressDataMap)
                
                address?.let {
                    val coords = it.addressData.coordinates
                    if (coords?.latitude != null && coords.longitude != null &&
                        coords.latitude != 0.0 && coords.longitude != 0.0) {
                        it
                    } else {
                        null
                    }
                }
            } catch (ex: Exception) {
                Log.e(TAG, "Error converting address with coordinates ${doc.id}", ex)
                null
            }
        }
    }

    suspend fun getAddressesNearLocation(latitude: Double, longitude: Double, radiusKm: Double, userId: String?): List<Address> {
        if (userId.isNullOrBlank()) {
            Log.w(TAG, "User ID is blank. Cannot fetch addresses near location.")
            return emptyList()
        }

        val addressesWithCoordinates = getAddressesWithCoordinates(userId)
        val earthRadiusKm = 6371.0

        return addressesWithCoordinates.filter { address ->
            val addrData = address.addressData
            val addrLat = addrData.coordinates?.latitude ?: 0.0
            val addrLon = addrData.coordinates?.longitude ?: 0.0

            if (addrLat != 0.0 && addrLon != 0.0) {
                val latDistance = Math.toRadians(addrLat - latitude)
                val lonDistance = Math.toRadians(addrLon - longitude)
                val a = sin(latDistance / 2) * sin(latDistance / 2) +
                        cos(Math.toRadians(latitude)) * cos(Math.toRadians(addrLat)) *
                        sin(lonDistance / 2) * sin(lonDistance / 2)
                val c = 2 * atan2(sqrt(a), sqrt(1 - a))
                val distance = earthRadiusKm * c
                distance <= radiusKm
            } else {
                false
            }
        }
    }

    suspend fun searchAddresses(query: String, userId: String?): List<Address> {
        if (query.isBlank() || userId.isNullOrBlank()) {
            Log.w(TAG, "Query or User ID is blank. Cannot search addresses.")
            return emptyList()
        }

        val lowerCaseQuery = query.lowercase(Locale.getDefault())

        val querySnapshot = firestore.collection(USERS_COLLECTION).document(userId)
            .collection(USER_ADDRESSES_SUBCOLLECTION)
            .get()
            .await()

        return querySnapshot.documents.mapNotNull { doc ->
            try {
                val docId = doc.id
                // ✅ FIXED: Using safeCastToStringMap() to eliminate unchecked cast warning
                val addressDataMap = doc.data?.get("addressData").safeCastToStringMap()
                val address = mapToAddressObject(docId, addressDataMap)

                address?.let { addr ->
                    val addressData = addr.addressData
                    val fullAddress = addressData.fullAddress?.lowercase(Locale.getDefault()) ?: ""
                    val notes = addressData.notes?.lowercase(Locale.getDefault()) ?: ""
                    val components = addressData.components
                    val street = components?.streetName?.lowercase(Locale.getDefault()) ?: ""
                    val city = components?.city?.lowercase(Locale.getDefault()) ?: ""
                    val zip = components?.postalCode?.lowercase(Locale.getDefault()) ?: ""

                    if (fullAddress.contains(lowerCaseQuery) ||
                        notes.contains(lowerCaseQuery) ||
                        street.contains(lowerCaseQuery) ||
                        city.contains(lowerCaseQuery) ||
                        zip.contains(lowerCaseQuery) ||
                        (addressData.searchFields?.searchTerms?.any { term ->
                            term.lowercase(Locale.getDefault()).contains(lowerCaseQuery)
                        } == true)) {
                        addr
                    } else {
                        null
                    }
                }
            } catch (ex: Exception) {
                Log.e(TAG, "Error converting searched address ${doc.id}", ex)
                null
            }
        }
    }

    // ✅ MODERNIZED: Use domain model with type-safe methods
    fun interpretDndStatusForDisplay(address: com.autogratuity.domain.model.Address?): DndDisplayState {
        if (address == null) return DndDisplayState.NO_DATA

        val flags = address.flags
        return when {
            flags?.isManuallyDnd() == true -> DndDisplayState.MANUAL_DND
            flags?.isManuallyAllowed() == true -> DndDisplayState.MANUAL_ALLOW
            address.isDndEnforced() -> DndDisplayState.AUTO_DND
            else -> DndDisplayState.ALLOWED
        }
    }

    // ✅ LEGACY SUPPORT: Keep old method for backward compatibility during transition
    @Deprecated("Use domain model version instead", ReplaceWith("interpretDndStatusForDisplay(domainAddress)"))
    fun interpretDndStatusForDisplayLegacy(address: Address?): DndDisplayState {
        if (address == null) return DndDisplayState.NO_DATA

        val flags = address.addressData.flags
        return when (flags?.manualDndState) {
            "FORCE_DND" -> DndDisplayState.MANUAL_DND
            "FORCE_ALLOW" -> DndDisplayState.MANUAL_ALLOW
            null -> {
                if (flags?.doNotDeliver == true) {
                    DndDisplayState.AUTO_DND
                } else {
                    DndDisplayState.ALLOWED
                }
            }
            else -> DndDisplayState.UNKNOWN
        }
    }
}