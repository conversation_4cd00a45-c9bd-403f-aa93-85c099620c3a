package com.autogratuity.data.datasource.remote

// Imports for Flow-based observers
import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.util_kt.documentSnapshotToAddressDtoOnly


import com.autogratuity.data.util.PageResult
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import javax.inject.Inject
import com.autogratuity.data.repository.core.CacheSource
import com.autogratuity.data.model.generated_kt.Address as AddressDto

/**
 * Interface for remote data operations related to Addresses.
 * All methods operate with AddressDto objects.
 */
interface AddressRemoteDataSource {
    suspend fun getAddressById(userId: String, addressId: String): Result<AddressDto?>
    suspend fun getAllAddresses(userId: String): Result<List<AddressDto>>
    suspend fun getDefaultAddress(userId: String): Result<AddressDto?>
    suspend fun addAddress(userId: String, addressData: AddressDto.AddressData): Result<DocumentReference>
    suspend fun updateAddress(userId: String, addressDto: AddressDto): Result<Unit>
    suspend fun updateAddressFields(userId: String, addressId: String, fields: Map<String, Any>): Result<Unit>
    suspend fun deleteAddress(userId: String, addressId: String): Result<Unit>

    fun observeAddressById(userId: String, addressId: String): Flow<AddressDto?>
    fun observeAddresses(userId: String): Flow<List<AddressDto>>

    // New methods needed by AddressRepositoryImpl and PlaceToAddressService
    /**
     * Retrieves addresses with pagination for the current user.
     * @param userId The ID of the user.
     * @param limit The maximum number of addresses to return.
     * @param startAfter The DocumentSnapshot to start after (for pagination), or null for the first page.
     * @return Result holding a list of DTO [AddressDto] objects or an error.
     */
    suspend fun getAddresses(userId: String, limit: Int, startAfter: DocumentSnapshot?): Result<List<AddressDto>>

    /**
     * Finds an address by its Google Place ID for the current user.
     * @param userId The ID of the user.
     * @param placeId The Google Place ID of the address.
     * @return Result holding the DTO [AddressDto] if found, or null if not found, or an error.
     */
    suspend fun findAddressByPlaceId(userId: String, placeId: String): Result<AddressDto?>

    /**
     * Finds an address by its normalized form for the current user.
     * Used during import and manual entry to avoid duplicates.
     * @param userId The ID of the user.
     * @param normalizedAddress The normalized address string to search for.
     * @return Result holding the DTO [AddressDto] if found, or null if not found, or an error.
     */
    suspend fun findAddressByNormalizedAddress(userId: String, normalizedAddress: String): Result<AddressDto?>

    /**
     * Load a page of addresses for pagination support.
     * @param userId The ID of the user.
     * @param pageSize The maximum number of addresses to return.
     * @param pageKey The DocumentSnapshot to start after (for pagination), or null for the first page.
     * @return Result holding PageResult with addresses and next page key.
     */
    suspend fun loadAddressPage(userId: String, pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<AddressDto, DocumentSnapshot>>
    
    // Potentially more methods to mirror AddressFirestoreAccess functionality, like:
    // suspend fun getBestTippingAddresses(userId: String, limit: Int): Result<List<AddressDto>>
    // suspend fun getAddressesNearLocation(userId: String, latitude: Double, longitude: Double, radiusKm: Double): Result<List<AddressDto>>
}

@ExperimentalCoroutinesApi
class AddressRemoteDataSourceImpl @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val ioDispatcher: CoroutineDispatcher
) : AddressRemoteDataSource {

    private val TAG = "AddressRemoteDataSourceImpl"

    // Constants for Firestore paths (can be shared or moved to a companion object)
    companion object {
        const val USERS_COLLECTION = "users"
        const val USER_ADDRESSES_SUBCOLLECTION = "user_addresses"
    }

    private fun getUserAddressReference(userId: String, addressId: String): DocumentReference {
        if (userId.isBlank() || addressId.isBlank()) {
            // Consider throwing IllegalArgumentException here if this is a strict internal requirement
            // For now, mirroring AddressFirestoreAccess which would log and return null/empty in public methods
            throw IllegalArgumentException("User ID or Address ID cannot be blank for Firestore reference")
        }
        return firestore.collection(USERS_COLLECTION).document(userId)
            .collection(USER_ADDRESSES_SUBCOLLECTION).document(addressId)
    }

    private fun getUserAddressesCollection(userId: String): com.google.firebase.firestore.CollectionReference {
        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for Firestore collection reference")
        }
        return firestore.collection(USERS_COLLECTION).document(userId)
            .collection(USER_ADDRESSES_SUBCOLLECTION)
    }

    private fun getNewAddressDocumentReference(userId: String): DocumentReference {
        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for new address document reference")
        }
        return firestore.collection(USERS_COLLECTION).document(userId)
            .collection(USER_ADDRESSES_SUBCOLLECTION).document() // Auto-generates ID
    }

    override suspend fun getAddressById(userId: String, addressId: String): Result<AddressDto?> = withContext(ioDispatcher) {
        if (userId.isBlank() || addressId.isBlank()) {
            Log.w(TAG, "getAddressById: userId or addressId is blank. Cannot query.")
        }
        
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$USERS_COLLECTION/$userId/$USER_ADDRESSES_SUBCOLLECTION/$addressId"
        
        try {
            Log.d(TAG, "getAddressById: Fetching address $addressId for user $userId from path: $fullPath")
            
            val documentSnapshot = getUserAddressReference(userId, addressId).get().await()
            val firestoreDuration = firestoreStartTime.elapsedNow()
            val documentExists = documentSnapshot.exists()
            val dataSize = if (documentExists) documentSnapshot.data?.toString()?.length ?: 0 else 0
            
            // 🚨 COMPREHENSIVE FIRESTORE READ MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = USER_ADDRESSES_SUBCOLLECTION,
                documentId = addressId,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                queryType = "GET_DOCUMENT",
                queryFilters = listOf("address_by_id"),
                resultCount = if (documentExists) 1 else 0,
                userId = userId,
                cacheSource = "SERVER"
            )
            
            // 🔍 SESSION CORRELATION: Add to current session
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("address_fetch:$addressId")
            
            if (documentExists) {
                val addressDto = documentSnapshotToAddressDtoOnly(documentSnapshot)
                
                // 📊 LOG RAW FIRESTORE DOCUMENT CONTENT
                Log.d(TAG, "getAddressById: Successfully fetched address for user $userId")
                Log.d(TAG, "  Firestore Path: $fullPath")
                Log.d(TAG, "  Document Size: $dataSize bytes")
                Log.d(TAG, "  Firestore Duration: ${firestoreDuration.inWholeMilliseconds}ms")
                Log.d(TAG, "  Raw Document Data: ${documentSnapshot.data}")
                
                Result.Success(addressDto)
            } else {
                Log.d(TAG, "No address found with ID $addressId for user $userId.")
                Result.Success(null)
            }
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // 🚨 FIRESTORE ERROR MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = USER_ADDRESSES_SUBCOLLECTION,
                documentId = addressId,
                duration = firestoreDuration,
                success = false,
                error = e,
                fullPath = fullPath,
                queryType = "GET_DOCUMENT",
                queryFilters = listOf("address_by_id"),
                userId = userId,
                cacheSource = "ERROR"
            )
            
            Log.e(TAG, "getAddressById: Error fetching address $addressId for user $userId", e)
            Result.Error(e) 
        }
    }

    override suspend fun getAllAddresses(userId: String): Result<List<AddressDto>> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            Log.w(TAG, "getAllAddresses: userId is blank. Cannot query.")
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank"))
        }
        try {
            val querySnapshot = getUserAddressesCollection(userId).get().await()
            val dtoList = querySnapshot.documents.mapNotNull { document ->
                try {
                    documentSnapshotToAddressDtoOnly(document)
                } catch (e: Exception) {
                    Log.e(TAG, "getAllAddresses: Error processing address document ${document.id} for user $userId", e)
                    null // Skip faulty document
                }
            }
            Result.Success(dtoList)
        } catch (e: Exception) {
            Log.e(TAG, "getAllAddresses: Error fetching addresses for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun getDefaultAddress(userId: String): Result<AddressDto?> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            Log.w(TAG, "getDefaultAddress: userId is blank. Cannot query.")
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank"))
        }
        try {
            val query = getUserAddressesCollection(userId)
                .whereEqualTo("addressData.isDefault", true)
                .limit(1)
            
            // Execute query (monitoring handled by ClarityArchitectureMonitor)
            val querySnapshot = query.get().await()

            if (!querySnapshot.isEmpty) {
                val defaultAddressDoc = querySnapshot.documents.first()
                Result.Success(documentSnapshotToAddressDtoOnly(defaultAddressDoc))
            } else {
                Log.d(TAG, "No default address found for user $userId.")
                Result.Success(null)
            }
        } catch (e: Exception) {
            Log.e(TAG, "getDefaultAddress: Error fetching default address for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun addAddress(userId: String, addressData: AddressDto.AddressData): Result<DocumentReference> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            Log.w(TAG, "addAddress: userId is blank.")
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank"))
        }
        
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val newAddressRef = getNewAddressDocumentReference(userId)
        val fullPath = "$USERS_COLLECTION/$userId/$USER_ADDRESSES_SUBCOLLECTION/${newAddressRef.id}"
        
        try {
            Log.d(TAG, "addAddress: Creating new address ${newAddressRef.id} for user $userId at path: $fullPath")
            
            // Ensure the AddressData has the correct userId, overriding if necessary.
            val finalAddressData = if (addressData.userId != userId) {
                Log.w(TAG, "addAddress: Mismatch in userId for AddressData (${addressData.userId}) and parameter ($userId). Using parameter.")
                addressData.copy(userId = userId)
            } else {
                addressData
            }
            
            val addressToSave = AddressDto(id = newAddressRef.id, addressData = finalAddressData)
            val dataSize = addressToSave.toString().length
            
            // 📊 LOG RAW WRITE DATA
            Log.d(TAG, "addAddress: Writing address data:")
            Log.d(TAG, "  Document ID: ${newAddressRef.id}")
            Log.d(TAG, "  Data Size: $dataSize bytes")
            Log.d(TAG, "  Write Data: $addressToSave")
            
            newAddressRef.set(addressToSave).await()
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // 🚨 COMPREHENSIVE FIRESTORE WRITE MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USER_ADDRESSES_SUBCOLLECTION,
                documentId = newAddressRef.id,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                writeType = "SET",
                fieldsUpdated = listOf("addressData", "id"),
                userId = userId,
                documentData = mapOf(
                    "id" to newAddressRef.id,
                    "addressData" to finalAddressData
                )
            )
            
            // 🔍 SESSION CORRELATION: Add to current session
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("address_create:${newAddressRef.id}")
            
            Log.d(TAG, "Successfully added new address ${newAddressRef.id} for user $userId in ${firestoreDuration.inWholeMilliseconds}ms")
            Result.Success(newAddressRef)
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // 🚨 FIRESTORE WRITE ERROR MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USER_ADDRESSES_SUBCOLLECTION,
                documentId = newAddressRef.id,
                duration = firestoreDuration,
                success = false,
                error = e,
                fullPath = fullPath,
                writeType = "SET",
                userId = userId
            )
            
            Log.e(TAG, "addAddress: Error adding new address for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun updateAddress(userId: String, addressDto: AddressDto): Result<Unit> = withContext(ioDispatcher) {
        if (userId.isBlank() || addressDto.id.isBlank()) {
            Log.w(TAG, "updateAddress: userId or addressDto.id is blank.")
            return@withContext Result.Error(IllegalArgumentException("User ID and Address ID cannot be blank"))
        }
        
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$USERS_COLLECTION/$userId/$USER_ADDRESSES_SUBCOLLECTION/${addressDto.id}"
        
        try {
            Log.d(TAG, "updateAddress: Updating address ${addressDto.id} for user $userId at path: $fullPath")
            
            val dataSize = addressDto.toString().length
            
            // 📊 LOG RAW UPDATE DATA
            Log.d(TAG, "updateAddress: Updating address data:")
            Log.d(TAG, "  Document ID: ${addressDto.id}")
            Log.d(TAG, "  Data Size: $dataSize bytes")
            Log.d(TAG, "  Update Data: $addressDto")
            
            // 🔒 CRITICAL FIX: Use SetOptions.merge() to preserve existing fields not in DTO
            // This prevents loss of business-critical fields like orderIds, deliveryStats, etc.
            getUserAddressReference(userId, addressDto.id).set(addressDto, com.google.firebase.firestore.SetOptions.merge()).await()
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // 🚨 COMPREHENSIVE FIRESTORE WRITE MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USER_ADDRESSES_SUBCOLLECTION,
                documentId = addressDto.id,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                writeType = "UPDATE",
                fieldsUpdated = listOf("addressData", "id"),
                userId = userId,
                documentData = mapOf(
                    "id" to addressDto.id,
                    "addressData" to addressDto.addressData
                )
            )
            
            // 🔍 SESSION CORRELATION: Add to current session
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("address_update:${addressDto.id}")
            
            Log.d(TAG, "Successfully updated address ${addressDto.id} for user $userId in ${firestoreDuration.inWholeMilliseconds}ms")
            Result.Success(Unit)
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()
            
            // 🚨 FIRESTORE WRITE ERROR MONITORING
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USER_ADDRESSES_SUBCOLLECTION,
                documentId = addressDto.id,
                duration = firestoreDuration,
                success = false,
                error = e,
                fullPath = fullPath,
                writeType = "UPDATE",
                userId = userId
            )
            
            Log.e(TAG, "updateAddress: Error updating address ${addressDto.id} for user $userId", e)
            Result.Error(e)
        }
        // Optional: Verify userId in addressDto matches the userId parameter if addressDto.addressData.userId is reliably set.
        // if (addressDto.addressData.userId != null && addressDto.addressData.userId != userId) {
        //     Log.e(TAG, "updateAddress: Mismatch in userId for AddressDto.addressData.userId (${addressDto.addressData.userId}) and parameter ($userId).")
        //     return@withContext Result.Error(IllegalArgumentException("User ID mismatch"))
        // }

        try {
            val addressRef = getUserAddressReference(userId, addressDto.id)
            // Using addressDto directly which contains AddressData as a nested property.
            // Firestore will serialize the entire AddressDto object.
            // SetOptions.merge() is crucial to only update provided fields in addressDto.addressData and not overwrite others.
            addressRef.set(addressDto, com.google.firebase.firestore.SetOptions.merge()).await()
            Log.d(TAG, "Successfully updated address ${addressDto.id} for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "updateAddress: Error updating address ${addressDto.id} for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun updateAddressFields(userId: String, addressId: String, fields: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        if (userId.isBlank() || addressId.isBlank()) {
            Log.w(TAG, "updateAddressFields: userId or addressId is blank.")
            return@withContext Result.Error(IllegalArgumentException("User ID and Address ID cannot be blank"))
        }
        if (fields.isEmpty()) {
            Log.w(TAG, "updateAddressFields: fields map is empty for address $addressId. No update performed.")
            return@withContext Result.Success(Unit) // Or an error indicating no fields provided
        }

        try {
            val addressRef = getUserAddressReference(userId, addressId)
            addressRef.update(fields).await()
            Log.d(TAG, "Successfully updated fields for address $addressId for user $userId. Fields: $fields")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "updateAddressFields: Error updating fields for address $addressId, user $userId. Fields: $fields", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteAddress(userId: String, addressId: String): Result<Unit> = withContext(ioDispatcher) {
        if (userId.isBlank() || addressId.isBlank()) {
            Log.w(TAG, "deleteAddress: userId or addressId is blank.")
            return@withContext Result.Error(IllegalArgumentException("User ID and Address ID cannot be blank"))
        }
        try {
            val addressRef = getUserAddressReference(userId, addressId)
            addressRef.delete().await()
            Log.d(TAG, "Successfully deleted address $addressId for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "deleteAddress: Error deleting address $addressId for user $userId", e)
            Result.Error(e)
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeAddressById(userId: String, addressId: String): Flow<AddressDto?> {
        if (userId.isBlank() || addressId.isBlank()) {
            Log.w(TAG, "observeAddressById: userId or addressId is blank. Returning empty flow.")
            return flowOf(null) // Or an empty flow: flow { }
        }
        return callbackFlow {
            val docRef = getUserAddressReference(userId, addressId)
            val listenerRegistration = docRef.addSnapshotListener { snapshot, error ->
                if (error != null) {
                    Log.e(TAG, "ObserveAddressById listener failed for address $addressId, user $userId", error)
                    close(error) // Close the flow with an error
                    return@addSnapshotListener
                }

                if (snapshot != null && snapshot.exists()) {
                    try {
                        val addressDto = documentSnapshotToAddressDtoOnly(snapshot)
                        trySend(addressDto).isSuccess // trySend doesn't throw on failure, check isSuccess
                    } catch (parseEx: Exception) {
                        Log.e(TAG, "Error parsing address document ${snapshot.id} in observeAddressById", parseEx)
                        // Decide: send null, close with error, or ignore faulty emission
                        // Sending null to indicate data presence but parse failure (or data malformed)
                        trySend(null).isSuccess 
                    }
                } else {
                    Log.d(TAG, "ObserveAddressById: Document null or does not exist for $addressId, user $userId")
                    trySend(null).isSuccess
                }
            }
            awaitClose {
                Log.d(TAG, "ObserveAddressById: Cancelling listener for $addressId, user $userId")
                listenerRegistration.remove()
            }
        }.flowOn(ioDispatcher) // Ensure observation and emission are on the IO dispatcher
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeAddresses(userId: String): Flow<List<AddressDto>> {
        if (userId.isBlank()) {
            Log.w(TAG, "observeAddresses: userId is blank. Returning empty flow.")
            return flowOf(emptyList()) // Or an empty flow: flow { }
        }
        return callbackFlow<List<AddressDto>> {
            val collectionRef = getUserAddressesCollection(userId)
            // Optional: Add .orderBy("addressData.fullAddress", Query.Direction.ASCENDING) or similar if needed
            val listenerRegistration = collectionRef.addSnapshotListener { snapshots, error ->
                if (error != null) {
                    Log.e(TAG, "ObserveAddresses listener failed for user $userId", error)
                    close(error) // Close the flow with an error
                    return@addSnapshotListener
                }

                if (snapshots != null) {
                    val addresses = snapshots.documents.mapNotNull { document ->
                        try {
                            documentSnapshotToAddressDtoOnly(document)
                        } catch (parseEx: Exception) {
                            Log.e(TAG, "Error parsing address document ${document.id} in observeAddresses for user $userId", parseEx)
                            null // Skip faulty documents
                        }
                    }
                    trySend(addresses).isSuccess
                } else {
                    // This case (snapshots == null && error == null) is unlikely with Firestore listeners
                    // but included for completeness. Typically, if snapshots is null, error would be non-null.
                    Log.d(TAG, "ObserveAddresses: Snapshots are null for user $userId (and no error reported)")
                    trySend(emptyList()).isSuccess
                }
            }
            awaitClose {
                Log.d(TAG, "ObserveAddresses: Cancelling listener for user $userId")
                listenerRegistration.remove()
            }
        }.flowOn(ioDispatcher) // Ensure observation and emission are on the IO dispatcher
    }

    override suspend fun getAddresses(userId: String, limit: Int, startAfter: DocumentSnapshot?): Result<List<AddressDto>> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            Log.w(TAG, "getAddresses (paginated): userId is blank. Cannot query.")
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank"))
        }
        if (limit <= 0) {
            Log.w(TAG, "getAddresses (paginated): limit must be positive. Was: $limit")
            // Consider returning an error or an empty list. Empty list for now.
            return@withContext Result.Success(emptyList())
        }

        try {
            var firestoreQuery: Query = getUserAddressesCollection(userId)
                .orderBy("addressData.metadata.updatedAt", Query.Direction.DESCENDING) // Default order by last update
                .limit(limit.toLong())

            startAfter?.let {
                firestoreQuery = firestoreQuery.startAfter(it)
            }

            val querySnapshot = firestoreQuery.get().await()
            val dtoList = querySnapshot.documents.mapNotNull { document ->
                try {
                    documentSnapshotToAddressDtoOnly(document)
                } catch (e: Exception) {
                    Log.e(TAG, "getAddresses (paginated): Error processing address document ${document.id} for user $userId", e)
                    null // Skip faulty document
                }
            }
            Result.Success(dtoList)
        } catch (e: Exception) {
            Log.e(TAG, "getAddresses (paginated): Error fetching addresses for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun findAddressByPlaceId(userId: String, placeId: String): Result<AddressDto?> = withContext(ioDispatcher) {
        if (userId.isBlank() || placeId.isBlank()) {
            Log.w(TAG, "findAddressByPlaceId: userId or placeId is blank. Cannot query.")
            return@withContext Result.Error(IllegalArgumentException("User ID and Place ID cannot be blank"))
        }
        try {
            val query = getUserAddressesCollection(userId)
                .whereEqualTo("addressData.placeId", placeId)
                .limit(1)
            
            // Execute query (monitoring handled by ClarityArchitectureMonitor)
            val querySnapshot = query.get().await()

            if (!querySnapshot.isEmpty) {
                val addressDoc = querySnapshot.documents.first()
                Result.Success(documentSnapshotToAddressDtoOnly(addressDoc))
            } else {
                Log.d(TAG, "No address found for user $userId with placeId $placeId.")
                Result.Success(null)
            }
        } catch (e: Exception) {
            Log.e(TAG, "findAddressByPlaceId: Error fetching address for user $userId with placeId $placeId", e)
            Result.Error(e)
        }
    }
    
    override suspend fun findAddressByNormalizedAddress(userId: String, normalizedAddress: String): Result<AddressDto?> = withContext(ioDispatcher) {
        if (userId.isBlank() || normalizedAddress.isBlank()) {
            Log.w(TAG, "findAddressByNormalizedAddress: userId or normalizedAddress is blank. Cannot query.")
            return@withContext Result.Error(IllegalArgumentException("User ID and normalized address cannot be blank"))
        }
        try {
            val query = getUserAddressesCollection(userId)
                .whereEqualTo("addressData.normalizedAddress", normalizedAddress)
                .limit(1)
            
            // Execute query (monitoring handled by ClarityArchitectureMonitor)
            val querySnapshot = query.get().await()

            if (!querySnapshot.isEmpty) {
                val addressDoc = querySnapshot.documents.first()
                Log.d(TAG, "findAddressByNormalizedAddress: Found address for user $userId with normalized address: $normalizedAddress")
                Result.Success(documentSnapshotToAddressDtoOnly(addressDoc))
            } else {
                Log.d(TAG, "findAddressByNormalizedAddress: No address found for user $userId with normalized address: $normalizedAddress")
                Result.Success(null)
            }
        } catch (e: Exception) {
            Log.e(TAG, "findAddressByNormalizedAddress: Error fetching address for user $userId with normalized address: $normalizedAddress", e)
            Result.Error(e)
        }
    }

    override suspend fun loadAddressPage(userId: String, pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<AddressDto, DocumentSnapshot>> = withContext(ioDispatcher) {
        val methodStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        
        if (userId.isBlank()) {
            Log.w(TAG, "loadAddressPage: userId is blank. Cannot query.")
            // Early exit with error monitoring
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "AddressRemoteDataSource",
                operation = "loadAddressPage",
                duration = methodStartTime.elapsedNow(),
                success = false,
                error = IllegalArgumentException("User ID cannot be blank"),
                dataType = "AddressDto",
                userId = userId,
                resultCount = 0,
                dataSource = "validation_error"
            )
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank"))
        }
        
        Log.d(TAG, "loadAddressPage: Loading address page with size: $pageSize, key: ${pageKey?.id}")
        val collectionPath = "$USERS_COLLECTION/$userId/$USER_ADDRESSES_SUBCOLLECTION"
        
        try {
            // Build query directly with Firestore SDK
            var firebaseQuery: Query = getUserAddressesCollection(userId)
                .orderBy("addressData.metadata.updatedAt", Query.Direction.DESCENDING)
                .limit(pageSize.toLong())
            
            pageKey?.let { key ->
                firebaseQuery = firebaseQuery.startAfter(key)
            }
            
            // Execute query (monitoring handled by ClarityArchitectureMonitor)
            val firestoreQueryStartTime = kotlin.time.TimeSource.Monotonic.markNow()
            lateinit var querySnapshot: com.google.firebase.firestore.QuerySnapshot
            var firestoreQueryDuration: kotlin.time.Duration
            var firestoreError: Exception? = null

            try {
                Log.d(TAG, "loadAddressPage: Executing Firestore query for addresses")
                querySnapshot = firebaseQuery.get().await()
                firestoreQueryDuration = firestoreQueryStartTime.elapsedNow()
            } catch (e: Exception) {
                firestoreQueryDuration = firestoreQueryStartTime.elapsedNow()
                firestoreError = e
                // Specific monitoring for Firestore query failure
                com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreQuery(
                    collection = USER_ADDRESSES_SUBCOLLECTION,
                    queryDescription = "address_pagination_error(pageSize=$pageSize,key=${pageKey?.id ?: "none"})",
                    duration = firestoreQueryDuration,
                    success = false,
                    error = e,
                    resultCount = 0
                )
                throw e // Re-throw to be caught by outer try-catch
            }

            // If we reach here, query was successful
            val resultCount = querySnapshot.documents.size
            val dataSizeBytes = querySnapshot.documents.sumOf { it.data?.toString()?.length ?: 0 }

            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreQuery(
                collection = USER_ADDRESSES_SUBCOLLECTION,
                queryDescription = "address_pagination(pageSize=$pageSize,key=${pageKey?.id ?: "none"})",
                duration = firestoreQueryDuration,
                success = true,
                resultCount = resultCount
            )
            
            val addressDtos = querySnapshot.documents.mapNotNull { document ->
                try {
                    documentSnapshotToAddressDtoOnly(document)
                } catch (e: Exception) {
                    Log.e(TAG, "loadAddressPage: Error processing address document ${document.id}", e)
                    // Optionally monitor this specific mapping error if needed
                    null
                }
            }
            
            val nextPageKey = if (addressDtos.size == pageSize && querySnapshot.documents.isNotEmpty()) {
                querySnapshot.documents.last()
            } else null
            
            // Session correlation
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("address_page_load_dto:${addressDtos.size}")
            
            val totalMethodDuration = methodStartTime.elapsedNow()
            Log.d(TAG, "loadAddressPage: Successfully loaded ${addressDtos.size} DTOs (${totalMethodDuration.inWholeMilliseconds}ms total, query: ${firestoreQueryDuration.inWholeMilliseconds}ms)")
            Result.Success(PageResult(addressDtos, nextPageKey))
            

        } catch (e: Exception) {
            // Catches general errors, including re-thrown query execution errors
            val totalMethodDuration = methodStartTime.elapsedNow()
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "AddressRemoteDataSource",
                operation = "loadAddressPage",
                duration = totalMethodDuration,
                success = false,
                error = e,
                dataType = "AddressDto",
                userId = userId,
                resultCount = 0,
                dataSource = "error"
            )
            Log.e(TAG, "loadAddressPage: General error loading address page (${totalMethodDuration.inWholeMilliseconds}ms)", e)
            Result.Error(e)
        }
    }

    // --- Google API Integration Methods ---

    /**
     * Geocode an address - cloud functionality removed
     */
    @Suppress("unused") // Public API method for AddressRepository
    suspend fun geocodeAddress(address: String, userId: String): Result<AddressDto> = withContext(ioDispatcher) {
        Log.d(TAG, "geocodeAddress: Cloud functionality removed, returning error for address: $address")
        Result.Error(Exception("Geocoding not available - cloud functionality removed"))
    }

    /**
     * Validate an address - cloud functionality removed
     */
    suspend fun validateAddress(address: String, userId: String): Result<AddressDto> = withContext(ioDispatcher) {
        Log.d(TAG, "validateAddress: Cloud functionality removed, returning error for address: $address")
        Result.Error(Exception("Address validation not available - cloud functionality removed"))
    }

    // Removed: convertGeocodingResponseToAddressDto and convertValidationResponseToAddressDto
    // These methods used cloud functionality which has been removed
}