@file:OptIn(ExperimentalCoroutinesApi::class)

package com.autogratuity.data.repository.delivery

import android.util.Log
import com.autogratuity.data.datasource.remote.AddressRemoteDataSource
import com.autogratuity.data.datasource.remote.DeliveryRemoteDataSource
import com.autogratuity.data.mapper.DeliveryMapper
// DTO Models with aliases (following DeliveryMapper pattern)
import com.autogratuity.data.model.generated_kt.Delivery as DeliveryDto
import com.autogratuity.data.model.generated_kt.Status as StatusDto
import com.autogratuity.data.model.generated_kt.Times as TimesDto
import com.autogratuity.data.model.generated_kt.Amounts as AmountsDto
import com.autogratuity.data.model.generated_kt.Reference as ReferenceDto
import com.autogratuity.data.model.generated_kt.Platform as PlatformDto
import com.autogratuity.data.model.generated_kt.Metadata as MetadataDto
import com.autogratuity.data.model.generated_kt.User_profile

// Domain Models (SSoT)
import com.autogratuity.domain.model.Address
import com.autogratuity.data.model.util_kt.documentSnapshotToUserProfileDto
import com.autogratuity.data.repository.address.AddressRepository

import com.autogratuity.debug.ClarityArchitectureMonitor
import com.autogratuity.domain.repository.UserRepository
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.FieldValue
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.time.OffsetDateTime
import kotlin.time.TimeSource

/**
 * Manages complex, multi-entity Firestore transactions involving deliveries.
 *
 * This class handles operations that require atomicity across multiple documents,
 * such as updating a delivery while also updating related user profile statistics
 * or address-related data.
 *
 * Follows the clarity architecture by using RemoteDataSource components for
 * Firestore operations instead of legacy FirestoreAccess classes.
 */
class DeliveryTransactionManager(
    private val firestore: FirebaseFirestore,
    private val deliveryMapper: DeliveryMapper,
    private val objectMapper: ObjectMapper,
    private val addressRepository: AddressRepository,
    private val userRepository: UserRepository,
    private val deliveryRemoteDataSource: DeliveryRemoteDataSource,
    private val addressRemoteDataSource: AddressRemoteDataSource,
    private val ioDispatcher: CoroutineDispatcher
) {
    private val TAG = "DeliveryTransactionManager"

    companion object {
        private const val USERS_COLLECTION = "users"
        private const val USER_DELIVERIES_SUBCOLLECTION = "user_deliveries"
        private const val USER_ADDRESSES_SUBCOLLECTION = "user_addresses"

        // Profile fields - Usage Stats (detailed statistics)
        private const val FIELD_PROFILE_USAGE_STATS_DELIVERY_COUNT = "profileData.usageStats.deliveryCount"
        private const val FIELD_PROFILE_USAGE_STATS_LAST_USAGE_DATE = "profileData.usageStats.lastUsageDate"
        private const val FIELD_PROFILE_USAGE_STATS_TOTAL_TIPS = "profileData.usageStats.totalTips"
        private const val FIELD_PROFILE_USAGE_STATS_ADDRESS_COUNT = "profileData.usageStats.addressCount"

        // Profile fields - Basic Usage (simple counters)
        private const val FIELD_PROFILE_USAGE_DELIVERY_COUNT = "profileData.usage.deliveryCount"
        private const val FIELD_PROFILE_USAGE_ADDRESS_COUNT = "profileData.usage.addressCount"
        private const val FIELD_PROFILE_USAGE_LAST_UPDATE = "profileData.usage.lastUsageUpdate"

        // Profile metadata
        private const val FIELD_PROFILE_METADATA_UPDATED_AT = "profileData.metadata.updatedAt"
        private const val FIELD_PROFILE_VERSION = "profileData.version"

        // Delivery fields
        private const val FIELD_DELIVERY_TIP_AMOUNT = "deliveryData.amounts.tipAmount"
        private const val FIELD_DELIVERY_STATUS = "deliveryData.status"
        private const val FIELD_DELIVERY_STATUS_IS_TIPPED = "deliveryData.status.isTipped"
        private const val FIELD_DELIVERY_STATUS_DO_NOT_DELIVER = "deliveryData.status.doNotDeliver"
        private const val FIELD_DELIVERY_TIMES_TIPPED_AT = "deliveryData.times.tippedAt"
        private const val FIELD_DELIVERY_TIMES_COMPLETED_AT = "deliveryData.times.completedAt"
        private const val FIELD_DELIVERY_METADATA_UPDATED_AT = "deliveryData.metadata.updatedAt"
        private const val FIELD_DELIVERY_ADDRESS_ID = "deliveryData.address.id"
        private const val FIELD_DELIVERY_REFERENCE_ADDRESS_ID = "deliveryData.reference.addressId"
        private const val FIELD_DELIVERY_DATA = "deliveryData"

        // Address fields (previously from AddressFirestoreAccess)
        private const val FIELD_ADDRESS_STATS_DELIVERY_COUNT = "addressData.deliveryStats.deliveryCount"
        private const val FIELD_ADDRESS_STATS_TIP_TOTAL = "addressData.deliveryStats.totalTips"
        private const val FIELD_ADDRESS_STATS_LAST_DELIVERY_DATE = "addressData.deliveryStats.lastDeliveryDate"
        private const val FIELD_ADDRESS_STATS_UPDATED_AT = "addressData.metadata.updatedAt"
        private const val FIELD_ADDRESS_METADATA_VERSION = "addressData.metadata.version"

        // User document reference helper
        private fun getUserDocumentReference(firestore: FirebaseFirestore, userId: String): DocumentReference {
            return firestore.collection(USERS_COLLECTION).document(userId)
        }

        // Address document reference helper
        private fun getUserAddressReference(firestore: FirebaseFirestore, userId: String, addressId: String): DocumentReference {
            return firestore.collection(USERS_COLLECTION).document(userId)
                .collection(USER_ADDRESSES_SUBCOLLECTION).document(addressId)
        }
    }

    // ===== SAFE CASTING UTILITIES =====
    // These utilities eliminate unchecked cast warnings while maintaining type safety

    /**
     * Safely casts Any? to Map<String, Any?> without unchecked cast warnings
     */
    @Suppress("UNCHECKED_CAST")
    private fun Any?.safeCastToStringMap(): Map<String, Any?>? {
        return this as? Map<String, Any?>
    }

    /**
     * Safely extracts deliveryData map from Firestore document snapshot
     */
    private fun getDeliveryDataMapSafely(documentSnapshot: com.google.firebase.firestore.DocumentSnapshot): Map<String, Any?>? {
        return documentSnapshot.data?.get("deliveryData").safeCastToStringMap()
    }

    /**
     * Safely extracts amounts map from delivery data
     */
    private fun Map<String, Any?>.getAmountsMapSafely(): Map<String, Any?>? {
        return this["amounts"].safeCastToStringMap()
    }

    /**
     * Safely extracts status map from delivery data
     */
    private fun Map<String, Any?>.getStatusMapSafely(): Map<String, Any?>? {
        return this["status"].safeCastToStringMap()
    }

    /**
     * Safely extracts reference map from delivery data
     */
    private fun Map<String, Any?>.getReferenceMapSafely(): Map<String, Any?>? {
        return this["reference"].safeCastToStringMap()
    }

    /**
     * Safely extracts address map from delivery data
     */
    private fun Map<String, Any?>.getAddressMapSafely(): Map<String, Any?>? {
        return this["address"].safeCastToStringMap()
    }

    /**
     * Safely extracts Double value from map
     */
    private fun Map<String, Any?>.getDoubleValueSafely(key: String): Double? {
        return this[key] as? Double
    }

    /**
     * Safely extracts Boolean value from map
     */
    private fun Map<String, Any?>.getBooleanValueSafely(key: String): Boolean? {
        return this[key] as? Boolean
    }

    /**
     * Safely extracts String value from map
     */
    private fun Map<String, Any?>.getStringValueSafely(key: String): String? {
        return this[key] as? String
    }

    /**
     * Safely extracts Number value from map and converts to Double
     */
    private fun Map<String, Any?>.getNumberAsDoubleSafely(key: String): Double? {
        return (this[key] as? Number)?.toDouble()
    }

    // ===== END SAFE CASTING UTILITIES =====

    /**
     * Updates a delivery's tip amount and related fields in a transaction.
     * Also updates the corresponding address stats if an address ID is provided.
     *
     * @param userId The user ID.
     * @param deliveryId The delivery ID.
     * @param newTipAmount The new tip amount.
     * @param addressId Optional address ID to update stats for.
     * @param timestamp Optional timestamp for the tip update.
     */
    suspend fun updateDeliveryTipTransaction(
        userId: String,
        deliveryId: String,
        newTipAmount: Double,
        addressId: String? = null,
        timestamp: OffsetDateTime? = null
    ): Unit = withContext(ioDispatcher) {
        val transactionStartTime = TimeSource.Monotonic.markNow()
        if (userId.isBlank() || deliveryId.isBlank()) {
            throw IllegalArgumentException("User ID and delivery ID cannot be blank")
        }

        val actualTimestamp = timestamp ?: OffsetDateTime.now()

        try {
            firestore.runTransaction { transaction ->
                // Get the delivery reference
                val deliveryRef = firestore.collection(USERS_COLLECTION).document(userId)
                    .collection(USER_DELIVERIES_SUBCOLLECTION).document(deliveryId)

                // Get the current delivery
                val deliverySnapshot = transaction.get(deliveryRef)
                if (!deliverySnapshot.exists()) {
                    throw IllegalStateException("Delivery $deliveryId not found")
                }

                // Get data as map instead of full Delivery object initially
                val deliveryDataMap = getDeliveryDataMapSafely(deliverySnapshot)
                    ?: throw IllegalStateException("Could not get deliveryData map from document $deliveryId")

                // Extract current tip amount from the map
                val currentTipAmount = deliveryDataMap.getAmountsMapSafely()?.getDoubleValueSafely("tipAmount") ?: 0.0

                // If the tip amount hasn't changed, nothing to do (idempotency)
                if (currentTipAmount == newTipAmount) {
                    Log.d(TAG, "Tip amount unchanged ($newTipAmount), skipping transaction for delivery $deliveryId")
                    return@runTransaction
                }

                // ✅ FIXED: Removed DND evaluation from tip updates to prevent state corruption
                // DND evaluation is now handled exclusively by address-stats-updater.ts cloud function
                // This preserves completion state and follows SSOT principles

                // Determine the 'isTipped' state based on the new tip amount
                val newIsTipped = newTipAmount > 0

                // Get user reference for profile updates (no DND evaluation needed)
                val userRef = firestore.collection(USERS_COLLECTION).document(userId)

                // Update the delivery document with ONLY tip-related fields
                // ✅ CRITICAL FIX: Do NOT modify DND flags - let cloud function handle this
                val updates = hashMapOf<String, Any>(
                    FIELD_DELIVERY_TIP_AMOUNT to newTipAmount,
                    FIELD_DELIVERY_TIMES_TIPPED_AT to actualTimestamp,
                    FIELD_DELIVERY_METADATA_UPDATED_AT to actualTimestamp,
                    FIELD_DELIVERY_STATUS_IS_TIPPED to newIsTipped
                    // ✅ REMOVED: FIELD_DELIVERY_STATUS_DO_NOT_DELIVER - Cloud function will handle this
                )

                transaction.update(deliveryRef, updates)

                // Stats handled by address-stats-updater cloud function
            }.await()

            Log.d(TAG, "Successfully updated tip amount for delivery $deliveryId to $newTipAmount")
            // Monitor successful transaction
            ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = "users",
                documentId = userId,
                duration = transactionStartTime.elapsedNow(),
                success = true,
                dataSizeBytes = newTipAmount.toString().length,
                fullPath = "users/$userId/deliveries/$deliveryId",
                writeType = "TRANSACTION",
                fieldsUpdated = listOf("tipAmount", "deliveryStats"),
                userId = userId,
                transactionId = "tip_update_$deliveryId"
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error updating tip amount for delivery $deliveryId", e)
            
            // Monitor failed transaction
            ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = "users",
                documentId = userId,
                duration = transactionStartTime.elapsedNow(),
                success = false,
                error = e,
                fullPath = "users/$userId/deliveries/$deliveryId",
                writeType = "TRANSACTION",
                fieldsUpdated = listOf("tipAmount"),
                userId = userId,
                transactionId = "tip_update_$deliveryId"
            )
            
            throw e
        }
    }

    /**
     * Updates a delivery's status in a transaction.
     * Also updates user profile delivery count if the delivery is being marked as completed.
     *
     * @param userId The user ID.
     * @param deliveryId The delivery ID.
     * @param newStatus The new status.
     * @param timestamp Optional timestamp for the status update.
     */
    /**
     * Reassociates a delivery with a different address and updates statistics for both addresses.
     * This operation is performed in a single transaction to ensure consistency.
     *
     * @param userId The user ID
     * @param deliveryId The delivery ID to update
     * @param originalAddressId The current address ID associated with the delivery
     * @param newAddressId The new address ID to associate with the delivery
     * @param newDeliveryDataMap Optional map of updated delivery data (already processed by ModelTransformations)
     * @param timestamp Optional timestamp for the operation
     */
    suspend fun reassociateDeliveryAddressAndUpdateStats(
        userId: String,
        deliveryId: String,
        originalAddressId: String,
        newAddressId: String,
        newDeliveryDataMap: Map<String, Any?>? = null,
        timestamp: OffsetDateTime? = null
    ): Unit = withContext(ioDispatcher) {
        if (userId.isBlank() || deliveryId.isBlank() || originalAddressId.isBlank() || newAddressId.isBlank()) {
            throw IllegalArgumentException("User ID, delivery ID, original address ID, and new address ID cannot be blank")
        }

        val actualTimestamp = timestamp ?: OffsetDateTime.now()

        try {
            firestore.runTransaction { transaction ->
                val deliveryRef = firestore.collection(USERS_COLLECTION).document(userId)
                    .collection(USER_DELIVERIES_SUBCOLLECTION).document(deliveryId)
                val deliverySnapshot = transaction.get(deliveryRef)
                if (!deliverySnapshot.exists()) {
                    throw FirebaseFirestoreException("Delivery $deliveryId not found", FirebaseFirestoreException.Code.NOT_FOUND)
                }
                // Get data as map
                val deliveryDataFromSnapshot = getDeliveryDataMapSafely(deliverySnapshot)
                     ?: throw FirebaseFirestoreException("Could not get deliveryData map from $deliveryId", FirebaseFirestoreException.Code.DATA_LOSS)

                // Verify the delivery is currently associated with the original address (optional, for logging)
                val currentDeliveryAddressId = deliveryDataFromSnapshot.getReferenceMapSafely()?.getStringValueSafely("addressId")
                if (currentDeliveryAddressId != originalAddressId) {
                    Log.w(TAG, "Delivery $deliveryId has address ID $currentDeliveryAddressId, not $originalAddressId as expected. Proceeding with reassociation.")
                }

                // Prepare updates for the delivery document
                if (newDeliveryDataMap != null) {
                    // If newDeliveryDataMap is provided, it represents the entire new 'deliveryData' field content.
                    // It should already include the new addressId within its structure if applicable,
                    // and metadata.updatedAt should also be correctly set by ModelTransformations.
                    transaction.update(deliveryRef, FIELD_DELIVERY_DATA, newDeliveryDataMap)
                    Log.d(TAG, "Updated delivery $deliveryId with newDeliveryDataMap.")
                } else {
                    // If no full data map is provided, just update the address reference and timestamp.
                    val deliveryUpdates = mapOf(
                        FIELD_DELIVERY_REFERENCE_ADDRESS_ID to newAddressId, // Path updated
                        FIELD_DELIVERY_METADATA_UPDATED_AT to actualTimestamp.toString() // Ensure stored as string
                    )
                    transaction.update(deliveryRef, deliveryUpdates)
                    Log.d(TAG, "Updated delivery $deliveryId address reference to $newAddressId.")
                }

                // Stats handled by address-stats-updater cloud function

                // Stats handled by address-stats-updater cloud function

                // Update user profile last usage date (optional, if this action counts as usage)
                // val userRef = deliveryFirestoreAccess.getUserDocumentReference(userId) // Assuming getUserDocumentReference exists
                // transaction.update(userRef, mapOf(
                //     UserRepository.FIELD_PROFILE_LAST_USAGE_DATE to actualTimestamp.toString(),
                //     UserRepository.FIELD_PROFILE_METADATA_UPDATED_AT to actualTimestamp.toString()
                // ))

            }.await()

            Log.d(TAG, "Successfully reassociated delivery $deliveryId from address $originalAddressId to $newAddressId")
        } catch (e: Exception) {
            Log.e(TAG, "Error reassociating delivery $deliveryId: ${e.message}", e)
            throw e // Re-throw to allow for higher-level error handling
        }
    }

    suspend fun updateDeliveryStatusTransaction(
        userId: String,
        deliveryId: String,
        newStatus: StatusDto,
        timestamp: OffsetDateTime? = null
    ): Unit = withContext(ioDispatcher) {
        if (userId.isBlank() || deliveryId.isBlank()) {
            throw IllegalArgumentException("User ID and delivery ID cannot be blank")
        }

        val actualTimestamp = timestamp ?: OffsetDateTime.now()

        try {
            firestore.runTransaction { transaction ->
                // Get the delivery reference
                val deliveryRef = firestore.collection(USERS_COLLECTION).document(userId)
                    .collection(USER_DELIVERIES_SUBCOLLECTION).document(deliveryId)

                // Get the current delivery
                val deliverySnapshot = transaction.get(deliveryRef)
                if (!deliverySnapshot.exists()) {
                    throw IllegalStateException("Delivery $deliveryId not found")
                }

                // Get the current delivery data as a map
                val currentDeliveryDataMap = getDeliveryDataMapSafely(deliverySnapshot)
                    ?: throw IllegalStateException("Could not get deliveryData map from document $deliveryId")

                // Convert newStatus object to a map for Firestore update
                val newStatusMap = mapOf(
                    "state" to newStatus.state,
                    "isCompleted" to newStatus.isCompleted,
                    "isTipped" to newStatus.isTipped,
                    "isVerified" to newStatus.isVerified,
                    "doNotDeliver" to newStatus.doNotDeliver
                )

                // Update the delivery document
                val updates = hashMapOf<String, Any>(
                    FIELD_DELIVERY_STATUS to newStatusMap, // Use the map here
                    FIELD_DELIVERY_METADATA_UPDATED_AT to actualTimestamp.toString() // Ensure stored as string
                )

                // If marking as completed, set the completion timestamp
                val currentIsCompleted = currentDeliveryDataMap.getStatusMapSafely()?.getBooleanValueSafely("isCompleted") == true
                if (newStatus.isCompleted == true && !currentIsCompleted) {
                    updates[FIELD_DELIVERY_TIMES_COMPLETED_AT] = actualTimestamp.toString()
                    // Stats handled by address-stats-updater cloud function
                }

                transaction.update(deliveryRef, updates)
            }.await()

            Log.d(TAG, "Successfully updated status for delivery $deliveryId")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating status for delivery $deliveryId", e)
            throw e
        }
    }

    /**
     * Deletes a delivery and updates related statistics in a transaction.
     *
     * @param userId The user ID.
     * @param deliveryId The delivery ID.
     * @param updateUserProfile Whether to update the user profile's delivery count.
     * @param updateAddressStats Whether to update address statistics.
     */
    suspend fun deleteDeliveryTransaction(
        userId: String,
        deliveryId: String,
        updateUserProfile: Boolean = true,
        updateAddressStats: Boolean = true
    ): Unit = withContext(ioDispatcher) {
        if (userId.isBlank() || deliveryId.isBlank()) {
            throw IllegalArgumentException("User ID and delivery ID cannot be blank")
        }

        try {
            firestore.runTransaction { transaction ->
                // ===== PHASE 1: ALL READS FIRST =====

                // Get the delivery reference
                val deliveryRef = firestore.collection(USERS_COLLECTION).document(userId)
                    .collection(USER_DELIVERIES_SUBCOLLECTION).document(deliveryId)

                // Get the current delivery
                val deliverySnapshot = transaction.get(deliveryRef)
                if (!deliverySnapshot.exists()) {
                    Log.w(TAG, "Delivery $deliveryId not found, nothing to delete")
                    return@runTransaction
                }

                // Get delivery data as map
                val deliveryDataMap = getDeliveryDataMapSafely(deliverySnapshot)
                    ?: throw IllegalStateException("Could not get deliveryData map for $deliveryId during delete")

                // ✅ FIXED: Read user profile BEFORE any writes (if needed)
                val userProfileRef = if (updateUserProfile) {
                    firestore.collection(USERS_COLLECTION).document(userId)
                } else null
                val userProfileSnapshot = userProfileRef?.let { transaction.get(it) }

                // ✅ FIXED: Read address BEFORE any writes (if needed)
                val addressId = deliveryDataMap.getReferenceMapSafely()?.getStringValueSafely("addressId")
                val addressRef = if (updateAddressStats && !addressId.isNullOrBlank()) {
                    firestore.collection(USERS_COLLECTION).document(userId)
                        .collection(USER_ADDRESSES_SUBCOLLECTION).document(addressId)
                } else null
                val addressSnapshot = addressRef?.let { transaction.get(it) }

                // ===== PHASE 2: ALL WRITES AFTER READS =====

                // Delete the delivery
                transaction.delete(deliveryRef)

                // Stats handled by address-stats-updater cloud function

                // Stats handled by address-stats-updater cloud function
            }.await()

            Log.d(TAG, "Successfully deleted delivery $deliveryId with related updates")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting delivery $deliveryId", e)
            throw e
        }
    }

    /**
     * Updates a delivery's address association in a transaction.
     * This method handles moving a delivery from one address to another,
     * updating the stats for both addresses.
     *
     * @param userId The user ID.
     * @param deliveryId The delivery ID.
     * @param originalAddressId The original address ID.
     * @param newAddressId The new address ID.
     */
    suspend fun updateDeliveryAddressAssociationTransaction(
        userId: String,
        deliveryId: String,
        originalAddressId: String,
        newAddressId: String
    ): Unit = withContext(ioDispatcher) {
        if (userId.isBlank() || deliveryId.isBlank() || originalAddressId.isBlank() || newAddressId.isBlank()) {
            throw IllegalArgumentException("User ID, delivery ID, original address ID, and new address ID cannot be blank")
        }

        if (originalAddressId == newAddressId) {
            Log.d(TAG, "Original and new address IDs are the same ($originalAddressId), skipping transaction")
            return@withContext
        }

        try {
            firestore.runTransaction { transaction ->
                // Get the delivery reference
                val deliveryRef = firestore.collection(USERS_COLLECTION).document(userId)
                    .collection(USER_DELIVERIES_SUBCOLLECTION).document(deliveryId)

                // Get the current delivery
                val deliverySnapshot = transaction.get(deliveryRef)
                if (!deliverySnapshot.exists()) {
                    throw IllegalStateException("Delivery $deliveryId not found")
                }

                // Get the current delivery data as a map
                val currentDeliveryDataMap = getDeliveryDataMapSafely(deliverySnapshot)
                    ?: throw IllegalStateException("Could not get deliveryData map from document $deliveryId")

                // Verify the delivery is currently associated with the original address
                val currentAddressId = currentDeliveryDataMap.getAddressMapSafely()?.getStringValueSafely("id")
                if (currentAddressId != originalAddressId) {
                    Log.w(TAG, "Delivery $deliveryId is not associated with address $originalAddressId but with $currentAddressId")
                    throw IllegalStateException("Delivery is not associated with the expected address")
                }

                // Update the delivery's address reference
                // The address ID in the map is nested under 'address.id' or 'reference.addressId'
                // As per current DeliveryData structure, 'address' is SimpleAddress, 'reference' has addressId
                // We are updating the reference.addressId.
                transaction.update(deliveryRef, mapOf(
                    FIELD_DELIVERY_REFERENCE_ADDRESS_ID to newAddressId, // Corrected path
                    FIELD_DELIVERY_METADATA_UPDATED_AT to OffsetDateTime.now().toString() // Ensure stored as string
                ))

                // Get the original address reference
                val originalAddressRef = firestore.collection(USERS_COLLECTION).document(userId)
                    .collection(USER_ADDRESSES_SUBCOLLECTION).document(originalAddressId)

                // Get the new address reference
                val newAddressRef = firestore.collection(USERS_COLLECTION).document(userId)
                    .collection(USER_ADDRESSES_SUBCOLLECTION).document(newAddressId)

                // Check if the addresses exist
                val originalAddressSnapshot = transaction.get(originalAddressRef)
                val newAddressSnapshot = transaction.get(newAddressRef)

                // Stats handled by address-stats-updater cloud function
            }.await()

            Log.d(TAG, "Successfully updated delivery $deliveryId address from $originalAddressId to $newAddressId")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating delivery $deliveryId address association", e)
            throw e
        }
    }

    // Stats handled by address-stats-updater cloud function

    // Stats handled by address-stats-updater cloud function

    /**
     * Atomically adds a new delivery and updates associated user profile and address statistics.
     *
     * @param userId The ID of the user for whom the delivery is being added.
     * @param deliveryDataMap The map containing the delivery data. It's assumed that if an address is linked,
     *                       its ID is populated in `deliveryDataMap.reference.addressId` unless `isAssociateAddressIfNotFound` is true.
     * @param isTrulyNewDelivery A flag indicating if this delivery should increment counts (e.g.,
     *                           it's not an update being mistakenly routed here).
     * @param rawAddressDetails Optional raw address data (if `deliveryDataMap.reference.addressId` is null and `isAssociateAddressIfNotFound` is true).
     * @param isAssociateAddressIfNotFound If true and `deliveryDataMap.reference.addressId` is null (within the map) and `rawAddressDetails` is provided,
     *                                   an attempt will be made to find or create the address transactionally.
     * @return The DocumentReference of the saved Delivery.
     * @throws Exception if the transaction fails.
     */
    suspend fun addNewDeliveryAndUpdateAllStatsTransaction(
        userId: String,
        deliveryDataMap: Map<*, *>,
        isTrulyNewDelivery: Boolean,
        rawAddressDetails: Address? = null,
        isAssociateAddressIfNotFound: Boolean = false
    ): DocumentReference = withContext(ioDispatcher) {
        Log.d(TAG, "Attempting to add new delivery (from map) and update stats for user: $userId")
        Log.d(TAG, "isAssociateAddressIfNotFound: $isAssociateAddressIfNotFound, rawAddressDetails provided: ${rawAddressDetails != null}")

        var mutableDeliveryDataMap = deliveryDataMap.toMutableMap() // Make it mutable for potential address updates

        try {
            val deliveryDocRef: DocumentReference = firestore.runTransaction { transaction ->
                var effectiveAddressId: String? = null

                // Attempt to access addressId from the map, assuming structure like: { "reference": { "addressId": "someId" } }
                val referenceMap = mutableDeliveryDataMap["reference"].safeCastToStringMap()
                effectiveAddressId = referenceMap?.getStringValueSafely("addressId")

                if (isAssociateAddressIfNotFound && effectiveAddressId.isNullOrBlank() && rawAddressDetails != null && !rawAddressDetails.fullAddress.isNullOrBlank()) {
                    Log.d(TAG, "Attempting to find or create address transactionally for: ${rawAddressDetails.fullAddress}")
                    try {
                        // TODO: Need to convert domain Address to AddressData - this method signature needs to be updated
                        // For now, commenting out this call as it requires architectural changes
                        throw NotImplementedError("findOrCreateAddressTransactional needs to accept domain Address model, not AddressData")
                        /*
                        val foundOrCreatedAddressId = addressRepository.findOrCreateAddressTransactional(
                            addressData = rawAddressDetails,
                            userId = userId,
                            transaction = transaction
                        )
                        Log.d(TAG, "Found or created address with ID: $foundOrCreatedAddressId")
                        effectiveAddressId = foundOrCreatedAddressId
                        */

                        /*
                        // Update the mutableDeliveryDataMap with the new/found addressId and SimpleAddress details
                        val updatedReferenceMap = (mutableDeliveryDataMap["reference"].safeCastToStringMap()?.toMutableMap() ?: mutableMapOf()).apply {
                            this["addressId"] = effectiveAddressId
                        }
                        mutableDeliveryDataMap["reference"] = updatedReferenceMap

                        mutableDeliveryDataMap["address"] = mapOf(
                            "id" to effectiveAddressId,
                            "fullAddress" to (rawAddressDetails.fullAddress ?: ""),
                            "latitude" to (rawAddressDetails.coordinates?.latitude ?: 0.0),
                            "longitude" to (rawAddressDetails.coordinates?.longitude ?: 0.0),
                            "placeId" to (rawAddressDetails.placeId ?: "")
                        )
                        Log.d(TAG, "Updated mutableDeliveryDataMap with new addressId: $effectiveAddressId and populated SimpleAddress map")
                        */

                    } catch (e: NotImplementedError) {
                        Log.e(TAG, "AddressRepository.findOrCreateAddressTransactional is not implemented! Cannot associate address.", e)
                        throw e
                    } catch (e: Exception) {
                        Log.e(TAG, "Error during address find/create in transaction for ${rawAddressDetails.fullAddress}", e)
                        throw FirebaseFirestoreException(
                            "Failed to find or create address: ${e.message}",
                            FirebaseFirestoreException.Code.ABORTED,
                            e
                        )
                    }
                } else {
                    if (isAssociateAddressIfNotFound && rawAddressDetails != null && rawAddressDetails.fullAddress.isNullOrBlank()){
                        Log.w(TAG, "isAssociateAddressIfNotFound is true, but rawAddressDetails.fullAddress is blank. Cannot find/create address.")
                    }
                }

                // ✅ FIRESTORE TRANSACTION FIX: ALL READS MUST BE EXECUTED BEFORE ALL WRITES

                // 1. READS FIRST: Read all documents that need to be checked/updated
                val userProfileRef = getUserDocumentReference(firestore, userId)
                val userProfileSnapshot = transaction.get(userProfileRef)

                // Get address ID for stats update (if available)
                val finalAddressIdForStats = mutableDeliveryDataMap["reference"].safeCastToStringMap()?.getStringValueSafely("addressId")
                val addressSnapshot = if (!finalAddressIdForStats.isNullOrBlank()) {
                    val addressRef = getUserAddressReference(firestore, userId, finalAddressIdForStats)
                    transaction.get(addressRef)
                } else {
                    null
                }

                // 2. WRITES SECOND: Now perform all write operations
                val serverTimestamp = FieldValue.serverTimestamp() // Use for metadata updates
                val now = OffsetDateTime.now() // Use for immediate visibility timestamps

                // Convert map to DeliveryData for the transaction
                val deliveryData = objectMapper.convertValue(mutableDeliveryDataMap, DeliveryDto.DeliveryData::class.java)
                val newDeliveryRef = deliveryRemoteDataSource.addDeliveryToTransaction(transaction, userId, deliveryData)
                Log.d(TAG, "Delivery (from map) added to transaction with ID: ${newDeliveryRef.id}")

                // Update User Profile Stats - Both usage and usageStats (following backend pattern)
                if (userProfileSnapshot.exists()) {
                    val userProfileUpdates = hashMapOf<String, Any>()

                    // Basic usage counters (use actual timestamp for immediate visibility)
                    userProfileUpdates[FIELD_PROFILE_USAGE_LAST_UPDATE] = now.toString()

                    // Detailed usage stats (use actual timestamp for immediate visibility)
                    userProfileUpdates[FIELD_PROFILE_USAGE_STATS_LAST_USAGE_DATE] = now.toString()

                    // Metadata
                    userProfileUpdates[FIELD_PROFILE_METADATA_UPDATED_AT] = serverTimestamp
                    userProfileUpdates[FIELD_PROFILE_VERSION] = FieldValue.increment(1)

                    // ✅ SSOT COMPLIANCE: Stats handled by address-stats-updater cloud function
                    // Only update essential metadata - all counting is handled server-side
                    Log.d(TAG, "Delivery creation completed - stats will be updated by address-stats-updater cloud function for $userId.")
                    transaction.update(userProfileRef, userProfileUpdates)
                } else {
                    Log.w(TAG, "User profile $userId not found, cannot update stats.")
                }

                // Stats handled by address-stats-updater cloud function

                newDeliveryRef // Return the DocumentReference from the transaction block
            }.await() // Execute the transaction and get the DocumentReference

            Log.i(TAG, "Successfully added new delivery (from map) with ID: ${deliveryDocRef.id} and updated all stats for user $userId")
            deliveryDocRef // Return the DocumentReference

        } catch (e: Exception) {
            Log.e(TAG, "Error in addNewDeliveryAndUpdateAllStatsTransaction (map version) for user $userId: ${e.message}", e)
            throw e
        }
    }

    /**
     * Atomically updates the status for a list of delivery documents using a Firestore WriteBatch.
     *
     * @param userId The ID of the user.
     * @param deliveryIds A list of delivery document IDs to update.
     * @param newStatus The new [Status] object to apply.
     * @param timestamp Optional [OffsetDateTime] for when the status update occurred. If null, current time is implied for metadata.
     * @throws IllegalArgumentException if userId or deliveryIds are invalid.
     * @throws FirebaseFirestoreException if the batch write fails.
     */
    suspend fun batchUpdateDeliveryStatusesTransaction(
        userId: String,
        deliveryIds: List<String>,
        newStatus: StatusDto, // com.autogratuity.data.model.generated_kt.Status
        timestamp: OffsetDateTime? = null
    ): Unit = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for batch status update.")
        }
        if (deliveryIds.isEmpty()) {
            Log.w(TAG, "batchUpdateDeliveryStatusesTransaction called with empty list of delivery IDs for user $userId.")
            return@withContext // Nothing to do
        }

        val actualTimestamp = timestamp ?: OffsetDateTime.now()
        val batch = firestore.batch()

        for (deliveryId in deliveryIds) {
            if (deliveryId.isBlank()) {
                Log.w(TAG, "Skipping blank deliveryId in batchUpdateDeliveryStatusesTransaction for user $userId")
                continue
            }
            val deliveryRef = deliveryRemoteDataSource.getDeliveryReference(userId, deliveryId)

            val updates = hashMapOf<String, Any>(
                FIELD_DELIVERY_STATUS to newStatus, // Set the whole status object
                FIELD_DELIVERY_METADATA_UPDATED_AT to actualTimestamp.toString()
            )

            // If marking as completed and a specific completion timestamp is provided (or implied by actualTimestamp)
            // And if the newStatus itself indicates completion.
            if (newStatus.isCompleted == true) {
                // We use actualTimestamp here, which is `timestamp` if provided, or OffsetDateTime.now() otherwise.
                // This ensures completedAt is set consistently.
                updates[FIELD_DELIVERY_TIMES_COMPLETED_AT] = actualTimestamp.toString()

                // ✅ SSOT COMPLIANCE: Stats handled by address-stats-updater cloud function
                // Only update essential metadata - all counting is handled server-side
                val userRef = getUserDocumentReference(firestore, userId)
                batch.update(userRef, mapOf(
                    FIELD_PROFILE_USAGE_LAST_UPDATE to actualTimestamp.toString(),
                    FIELD_PROFILE_USAGE_STATS_LAST_USAGE_DATE to actualTimestamp.toString(),
                    FIELD_PROFILE_METADATA_UPDATED_AT to actualTimestamp.toString(),
                    FIELD_PROFILE_VERSION to FieldValue.increment(1)
                ))
            }

            batch.update(deliveryRef, updates)
            Log.d(TAG, "Batch update prepared for delivery $deliveryId for user $userId with new status: ${newStatus.state}")
        }

        try {
            batch.commit().await()
            Log.i(TAG, "Successfully batch updated status for ${deliveryIds.size} deliveries for user $userId.")
        } catch (e: FirebaseFirestoreException) {
            Log.e(TAG, "FirestoreException during batch status update for user $userId.", e)
            throw e
        } catch (e: Exception) {
            Log.e(TAG, "Generic error during batch status update for user $userId.", e)
            throw e // Re-throw other exceptions too
        }
    }

    /**
     * Atomically deletes a list of delivery documents using a Firestore WriteBatch.
     * This method does NOT handle related statistics updates (e.g., user profile delivery count).
     * Such updates should be handled by the caller if necessary, potentially in a separate transaction
     * or by calling another method in this manager.
     *
     * @param userId The ID of the user.
     * @param deliveryIds A list of delivery document IDs to delete.
     * @throws IllegalArgumentException if userId or deliveryIds are invalid.
     * @throws FirebaseFirestoreException if the batch write fails.
     */
    suspend fun batchDeleteDeliveriesTransaction(
        userId: String,
        deliveryIds: List<String>
    ): Unit = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for batch delete.")
        }
        if (deliveryIds.isEmpty()) {
            Log.w(TAG, "batchDeleteDeliveriesTransaction called with empty list of delivery IDs for user $userId.")
            return@withContext // Nothing to do
        }

        // Convert to use a Firestore Transaction for atomicity with stat updates
        try {
            firestore.runTransaction { transaction ->
                val userProfileRef = getUserDocumentReference(firestore, userId)
                val userProfileSnapshot = transaction.get(userProfileRef)
                var validDeletionsCount = 0

                for (deliveryId in deliveryIds) {
                    if (deliveryId.isBlank()) {
                        Log.w(TAG, "Skipping blank deliveryId in batchDeleteDeliveriesTransaction for user $userId")
                        continue
                    }
                    // It's important to also fetch the delivery if we need to check its state (e.g. isCompleted)
                    // before decrementing stats. For now, we assume all deletions contribute to stat changes.
                    // If only *completed* deliveries should decrement stats, each delivery needs to be fetched.
                    // For simplicity and based on the original prompt, this version decrements for all listed IDs.

                    val deliveryRef = deliveryRemoteDataSource.getDeliveryReference(userId, deliveryId)
                    transaction.delete(deliveryRef)
                    validDeletionsCount++
                    Log.d(TAG, "Transaction: Prepared delete for delivery $deliveryId for user $userId.")
                }

                // Stats handled by address-stats-updater cloud function
                null // Return null for successful transaction
            }.await()
            Log.i(TAG, "Successfully batch deleted $deliveryIds.size deliveries and updated stats for user $userId.")
        } catch (e: FirebaseFirestoreException) {
            Log.e(TAG, "FirestoreException during batch delete transaction for user $userId.", e)
            throw e
        } catch (e: Exception) {
            Log.e(TAG, "Generic error during batch delete transaction for user $userId.", e)
            throw e
        }
    }

    /**
     * Atomically re-associates a delivery with a new address and updates statistics for both the original and new addresses.
     *
     * @param userId The ID of the user.
     * @param deliveryId The ID of the delivery document to update.
     * @param newDeliveryData The complete, new state of the delivery's data to be saved. This should include the new addressId in its reference field.
     * @param originalAddressId The ID of the address from which the delivery is being disassociated.
     * @param newAddressId The ID of the address to which the delivery is being newly associated.
     * @return Unit
     * @throws IllegalArgumentException if any ID is blank or if original and new address IDs are the same (as this transaction is for re-association between two different addresses).
     * @throws FirebaseFirestoreException if the delivery or other critical documents are not found or parsing fails.
     */
    suspend fun reassociateDeliveryAddressTransaction(
        userId: String,
        deliveryId: String,
        newDeliveryData: DeliveryDto.DeliveryData,
        originalAddressId: String,
        newAddressId: String
    ): Unit = withContext(ioDispatcher) {
        if (userId.isBlank() || deliveryId.isBlank() || originalAddressId.isBlank() || newAddressId.isBlank()) {
            throw IllegalArgumentException("User ID, Delivery ID, Original Address ID, and New Address ID cannot be blank for re-association.")
        }
        // This transaction is specifically for moving a delivery between two *different* addresses.
        // If they are the same, a simpler update operation on the delivery (and potentially one address) should be used.
        // However, if the intent is just to update the delivery data AND its addressId field (even if same),
        // then the check below might be too strict. For now, assuming distinct re-association.
        if (originalAddressId == newAddressId) { // If address IDs are same, just update delivery data if provided
             Log.i(TAG, "Original Address ID and New Address ID are the same. Updating delivery data only.")
             // Update the delivery document with new data
             firestore.runTransaction { transaction ->
                val deliveryRef = deliveryRemoteDataSource.getDeliveryReference(userId, deliveryId)
                transaction.update(deliveryRef, FIELD_DELIVERY_DATA, newDeliveryData)
                Log.d(TAG, "Transaction: Updated Delivery $deliveryId with new data (address ID unchanged).")
                null
             }.await()
             return@withContext // Exit as re-association part is skipped.
        }

        val now = OffsetDateTime.now()

        firestore.runTransaction { transaction ->
            val deliveryRef = deliveryRemoteDataSource.getDeliveryReference(userId, deliveryId)

            // 1. Read the current Delivery to get its old data for stat decrement (if necessary, but not used in current logic below)
            val deliverySnapshot = transaction.get(deliveryRef)
            if (!deliverySnapshot.exists()) {
                throw FirebaseFirestoreException("Delivery $deliveryId not found for re-association.", FirebaseFirestoreException.Code.NOT_FOUND)
            }

            // 2. Update the delivery document with newDeliveryData
            transaction.update(deliveryRef, FIELD_DELIVERY_DATA, newDeliveryData)
            Log.d(TAG, "Transaction: Updated Delivery $deliveryId with new data.")

            // 3. Update originalAddressRef stats if originalAddressId is different from newAddressId
            if (originalAddressId != newAddressId) {
                val originalAddressRef = getUserAddressReference(firestore, userId, originalAddressId)
                val originalAddressDocSnapshot = transaction.get(originalAddressRef)

                // Stats handled by address-stats-updater cloud function

                // 4. Update newAddressRef stats
                val newAddressRef = getUserAddressReference(firestore, userId, newAddressId)
                val newAddressDocSnapshot = transaction.get(newAddressRef)

                // Stats handled by address-stats-updater cloud function
            }

            // Stats handled by address-stats-updater cloud function

            null // Firestore transaction success
        }.await()

        Log.i(TAG, "Delivery $deliveryId re-associated from $originalAddressId to $newAddressId successfully via transaction.")
    }

    /**
     * Performs the initial setup for a new user by creating their profile document
     * and a default address document in a single transaction.
     *
     * @param userId The ID of the user for whom to perform setup.
     * @param defaultUserProfile The default User_profile object to save.
     * @param defaultAddress The default Address object to save.
     * @throws FirebaseFirestoreException if the transaction fails.
     */
    suspend fun performInitialSetupTransaction(
        userId: String,
        defaultUserProfile: User_profile,
        defaultAddress: Address
    ) {
        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for initial setup transaction.")
        }
        Log.d(TAG, "Beginning initial setup transaction for user $userId.")

        // Prepare Address SSoT model for saving (convert to Map)
        // Assuming AddressModelTransformations has a similar prepareForSave method
        // This is a placeholder, exact implementation depends on AddressModelTransformations
        // val addressDataMap = addressModelTransformations.prepareAddressForSave(defaultAddress)

        firestore.runTransaction { transaction ->
            // Save the default user profile
            // Save the default user profile using direct Firestore operations
            val userRef = getUserDocumentReference(firestore, userId)
            transaction.set(userRef, defaultUserProfile)
            Log.d(TAG, "Initial setup transaction: Default user profile prepared for save for user $userId.")

            // Save the default address using direct Firestore operations
            val addressRef = getUserAddressReference(firestore, userId, defaultAddress.id)
            // TODO: Need to convert domain Address to AddressData for Firestore storage
            // For now, creating a basic map structure
            val addressDataMap = mapOf(
                "userId" to defaultAddress.userId,
                "fullAddress" to defaultAddress.fullAddress,
                "normalizedAddress" to defaultAddress.normalizedAddress,
                "placeId" to defaultAddress.placeId,
                "isDefault" to defaultAddress.isDefault
            )
            transaction.set(addressRef, mapOf("addressData" to addressDataMap))
            Log.d(TAG, "Initial setup transaction: Default address prepared for save for user $userId, address ID: ${defaultAddress.id}.")

            null // Firestore transaction requires a return value, null for success if no data needs to be returned.
        }.await()
        Log.i(TAG, "Successfully completed initial setup transaction for user $userId.")
    }

    // ===== SIMPLIFIED ADDRESS ASSOCIATION BUSINESS LOGIC =====
    // Moved from DeliveryRepositoryImpl.kt to follow architectural pattern where transaction managers contain complex business logic

    /**
     * Simplified delivery address association update.
     * Moved from DeliveryRepositoryImpl.kt to follow architectural pattern where transaction managers contain complex business logic.
     *
     * @param deliveryId The ID of the delivery to update.
     * @param originalAddressId The original address ID.
     * @param newActualAddressId The new address ID to associate.
     * @param updatedDeliveryData The updated delivery data.
     * @return Result indicating success or failure.
     */
    suspend fun updateDeliveryAddressAssociationSimple(
        deliveryId: String,
        originalAddressId: String,
        newActualAddressId: String,
        updatedDeliveryData: com.autogratuity.domain.model.DeliveryDetails
    ): com.autogratuity.data.model.Result<Unit> = withContext(ioDispatcher) {
        try {
            if (deliveryId.isBlank() || originalAddressId.isBlank() || newActualAddressId.isBlank()) {
                return@withContext com.autogratuity.data.model.Result.Error(IllegalArgumentException("Delivery ID, originalAddressId, and newActualAddressId cannot be blank"))
            }

            Log.d(TAG, "updateDeliveryAddressAssociationSimple: Updating address association for delivery $deliveryId from $originalAddressId to $newActualAddressId")

            if (originalAddressId == newActualAddressId) {
                Log.i(TAG, "updateDeliveryAddressAssociationSimple: Original and new address IDs are the same, just ensuring addressId field is set")
                // Simple case - just return success as no change needed
                return@withContext com.autogratuity.data.model.Result.Success(Unit)
            }

            // For different addresses, use the existing complex transaction method
            val userId = updatedDeliveryData.userId
            if (userId.isBlank()) {
                return@withContext com.autogratuity.data.model.Result.Error(IllegalArgumentException("User ID cannot be blank"))
            }

            updateDeliveryAddressAssociationTransaction(
                userId = userId,
                deliveryId = deliveryId,
                originalAddressId = originalAddressId,
                newAddressId = newActualAddressId
            )

            Log.i(TAG, "updateDeliveryAddressAssociationSimple: Successfully updated address association for delivery $deliveryId")
            com.autogratuity.data.model.Result.Success(Unit)

        } catch (e: Exception) {
            Log.e(TAG, "updateDeliveryAddressAssociationSimple: Unexpected error for delivery $deliveryId", e)
            com.autogratuity.data.model.Result.Error(e)
        }
    }

    /**
     * Simplified delivery update and address reassociation.
     * Moved from DeliveryRepositoryImpl.kt to follow architectural pattern where transaction managers contain complex business logic.
     *
     * @param deliveryDocId The delivery document ID.
     * @param deliveryData The delivery data to update.
     * @param originalAddressId The original address ID (optional).
     * @param newActualAddressId The new address ID (optional).
     * @return Result indicating success or failure.
     */
    suspend fun updateDeliveryAndReassociateAddressSimple(
        deliveryDocId: String,
        deliveryData: com.autogratuity.domain.model.DeliveryDetails,
        originalAddressId: String?,
        newActualAddressId: String?
    ): com.autogratuity.data.model.Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "updateDeliveryAndReassociateAddressSimple: Updating delivery $deliveryDocId with address reassociation")

            // If we're just updating the delivery without changing address association
            if (originalAddressId == null || newActualAddressId == null || originalAddressId == newActualAddressId) {
                Log.d(TAG, "updateDeliveryAndReassociateAddressSimple: No address change, just updating delivery")
                // Simple update case - return success as the actual repository update would be handled by the calling repository
                com.autogratuity.data.model.Result.Success(Unit)
            } else {
                // Full address reassociation needed
                Log.d(TAG, "updateDeliveryAndReassociateAddressSimple: Full address reassociation from $originalAddressId to $newActualAddressId")

                updateDeliveryAddressAssociationSimple(
                    deliveryId = deliveryDocId,
                    originalAddressId = originalAddressId,
                    newActualAddressId = newActualAddressId,
                    updatedDeliveryData = deliveryData
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "updateDeliveryAndReassociateAddressSimple: Unexpected error for delivery $deliveryDocId", e)
            com.autogratuity.data.model.Result.Error(e)
        }
    }
}
