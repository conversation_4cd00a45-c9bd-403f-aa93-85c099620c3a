// Auto-generated from SubscriptionRepository.kt
// IMPORTS FROM EXISTING GENERATED MODELS (not recreated)

import type { Result } from '../types/Result';
import type {
  Delivery as TsDeliveryInterface,
  Address as TsAddressInterface,
  User_profile as TsUserProfileInterface
} from '../../models/generated/delivery.schema';
import type {
  DeliveryStats as TsDeliveryStatsInterface
} from '../../models/generated/address.schema';

/**
 * TypeScript adapter interface generated from SubscriptionRepository.kt
 * Maintains identical contract to Kotlin implementation
 * Uses existing generated models from schemas
 */
export interface SubscriptionRepositoryAdapter {
  getCurrentUserSubscription(): Result<UserSubscription?>;
  getUserSubscription(userId: string): Result<UserSubscription?>;
  updateUserSubscription(userId: string, subscription: UserSubscription): Promise<Result<void>>;
  createDefaultSubscription(userId: string): Result<UserSubscription>;
  observeCurrentUserSubscription(): Flow<Result<UserSubscription?>>;
  observeUserSubscription(userId: string): Flow<Result<UserSubscription?>>;
  isProUser(): Result<boolean>;
  observeIsProUser(): Flow<boolean>;
  isSubscriptionExpired(): Result<boolean>;
  isLifetimeSubscription(): Result<boolean>;
  getSubscriptionLevel(): Result<string>;
  getSubscriptionExpiryDate(): Result<Date?>;
  upgradeSubscription(durationMonths: number, paymentDetails: Map<string): Promise<Result<void>>;
  cancelSubscription(immediate: boolean): Promise<Result<void>>;
  verifySubscription(): Promise<Result<void>>;
  isTrialAvailable(): Result<boolean>;
  observeTrialAvailable(): Flow<boolean>;
  startTrial(trialDurationDays: number): Promise<Result<void>>;
  getSubscriptionHistory(): Result<Map<string, Any[]>>;
  validateSubscription(subscription: UserSubscription): Promise<Result<void>>;
  handleBillingSystemUpdate(userId: string, billingData: Map<string): Promise<Result<void>>;
  clearCache(): Promise<Result<void>>;
  clearCache(userId: string): Promise<Result<void>>;
  invalidateCache(userId: string): Promise<Result<void>>;
  prefetchCriticalData(): Promise<Result<void>>;
  getCacheMetrics(): Result<Map<string, Any>>;
  initialize(): Promise<Result<void>>;
  cleanup(): Promise<Result<void>>;
}