// Auto-generated from AddressStats.kt
// TypeScript equivalent of Android domain.model.AddressStats

/**
 * Domain model generated from Kotlin AddressStats
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface AddressStats {
  deliveryCount: number = 0L;
  tipCount: number = 0L;
  totalTips: number = 0.0;
  averageTipAmount: number = 0.0;
  highestTip: number = 0.0;
  pendingCount: number = 0L;
  averageTimeMinutes: number = 0.0;
  lastDeliveryTimestamp?: Date? = null;
  tipRate: number;
  hasDeliveries: boolean;
  hasTips: boolean;
}