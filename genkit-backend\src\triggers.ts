import * as z from 'zod';
import { runFlow } from '@genkit-ai/flow';
import { onDocumentWritten, FirestoreEvent, Change } from 'firebase-functions/v2/firestore';
import { onCall } from 'firebase-functions/v2/https';
import { DocumentSnapshot } from 'firebase-admin/firestore';
// Import Firebase Storage trigger
import { onObjectFinalized, StorageEvent } from 'firebase-functions/v2/storage';

// Import flows from their new locations (add .js extension for compiled output)
import { parseImportDataWithLogging } from './flows/import-parsing.js';
// Import the GCS processing flow and its input schema
import { processUploadedGeoJsonFile, GcsEventDataInputSchema } from './flows/process-uploaded-geojson.js';

// Corrected import statement for address stats updater
import { updateAddressDeliveryStatsFlow, AddressStatsUpdateInputSchema } from './flows/address-stats-updater.js';
// Google API flows removed - unused functionality
// Import manual DND override flow
import { setManualAddressDndOverrideFlow, SetManualAddressDndInputSchema } from './flows/set-manual-address-dnd-override.js';

// ✅ FIRESTORE-ONLY: User preferences change trigger (Redis removed)
import { onUserPreferencesChange } from './flows/user-preferences-cache-invalidation.js';
// ✅ NEW: Smart coordination triggers
import {
  onDeliveryChangeSmartCoordination,
  onAddressManualDndChangeCoordination
} from './flows/smart-coordination-triggers.js';

// ✅ UTILITY: Helper function to get nested object values safely
function getNestedValue(obj: any, path: string): any {
  if (!obj || !path) return undefined;
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

// NEW TRIGGER for address-stats-updater
export const onDeliveryWrittenUpdateAddressStats = onDocumentWritten(
  'users/{userId}/user_deliveries/{deliveryId}',
  async (event: FirestoreEvent<Change<DocumentSnapshot> | undefined, { userId: string, deliveryId: string }>) => {
    console.log(`TRIGGER FIRED: onDeliveryWrittenUpdateAddressStats - Event ID: ${event.id}, UserID: ${event.params.userId}, DeliveryID: ${event.params.deliveryId}`);
    const specificUserId = event.params.userId;
    const specificDeliveryId = event.params.deliveryId;
    const logPrefix = `[OnDeliveryWrittenUpdateAddressStats] UserID: ${specificUserId}, DeliveryID: ${specificDeliveryId} -`;

    const deliverySnapshotAfter = event.data?.after;
    const deliverySnapshotBefore = event.data?.before;

    let addressId: string | null = null;
    let operationType: 'create' | 'update' | 'delete' = 'update';

    if (deliverySnapshotAfter && deliverySnapshotAfter.exists) { // Create or Update
      const deliveryData = deliverySnapshotAfter.data()?.deliveryData;
      const beforeData = deliverySnapshotBefore?.exists ? deliverySnapshotBefore.data()?.deliveryData : null;

      // ✅ LAYER 1: Skip updates made by the stats flow itself (existing protection)
      const updatedByStatsFlow = deliveryData?.metadata?.updatedByStatsFlow === true;
      if (updatedByStatsFlow) {
        console.log(`${logPrefix} Update was made by stats flow (updatedByStatsFlow=true). Skipping to prevent infinite loop.`);
        return;
      }

      // ✅ LAYER 2: Only trigger on user-relevant field changes (surgical precision)
      const userEditableFields = [
        'amounts.tipAmount',
        'status.isCompleted',
        'reference.addressId',
        'amounts.orderTotal',
        'amounts.deliveryFee',
        'metadata.deliveryDate'
      ];

      const hasUserChanges = userEditableFields.some(fieldPath => {
        const beforeValue = getNestedValue(beforeData, fieldPath);
        const afterValue = getNestedValue(deliveryData, fieldPath);
        return beforeValue !== afterValue;
      });

      const isEdit = deliverySnapshotBefore && deliverySnapshotBefore.exists;

      if (isEdit && !hasUserChanges) {
        console.log(`${logPrefix} No user-editable fields changed. Skipping address stats update.`);
        return;
      }

      // ✅ ENHANCED LOGGING: Show what actually changed
      if (hasUserChanges) {
        const changes = userEditableFields
          .map(fieldPath => {
            const beforeValue = getNestedValue(beforeData, fieldPath);
            const afterValue = getNestedValue(deliveryData, fieldPath);
            return beforeValue !== afterValue ? `${fieldPath}: ${beforeValue}→${afterValue}` : null;
          })
          .filter(Boolean);
        console.log(`${logPrefix} User-editable fields changed: ${changes.join(', ')}. Processing stats update.`);
      }

      // Prioritize reference.addressId, then addressId, then address.id
      addressId = deliveryData?.reference?.addressId || deliveryData?.addressId || deliveryData?.address?.id;
      operationType = deliverySnapshotBefore && deliverySnapshotBefore.exists ? 'update' : 'create';

      const isCompleted = deliveryData?.status?.isCompleted === true;
      const tipAmount = deliveryData?.amounts?.tipAmount;
      console.log(`${logPrefix} Delivery ${operationType}d. AddressID from 'after' snapshot: ${addressId}, isCompleted: ${isCompleted}, tipAmount: ${tipAmount}`);
    } else if (deliverySnapshotBefore && deliverySnapshotBefore.exists) { // Delete
      const deliveryDataBefore = deliverySnapshotBefore.data()?.deliveryData;
      addressId = deliveryDataBefore?.reference?.addressId || deliveryDataBefore?.addressId || deliveryDataBefore?.address?.id;
      operationType = 'delete';
      console.log(`${logPrefix} Delivery deleted. AddressID from 'before' snapshot: ${addressId}`);
    } else {
      console.log(`${logPrefix} Event with no identifiable before or after snapshot data relevant for stats. Skipping.`);
      return;
    }
    
    if (!addressId) {
      console.error(`${logPrefix} Could not determine addressId from delivery document. Skipping stats update.`);
      return;
    }

    // Corrected input schema usage
    const flowInput: z.infer<typeof AddressStatsUpdateInputSchema> = {
      userId: specificUserId,
      addressId: addressId,
    };

    // Corrected flow name in runFlow call
    runFlow(updateAddressDeliveryStatsFlow, flowInput)
      .then(() => {
        console.log(`${logPrefix} runFlow(updateAddressDeliveryStatsFlow) invoked successfully for Op: ${operationType}. Input: ${JSON.stringify(flowInput)}`);
      })
      .catch((flowError: any) => {
        console.error(`${logPrefix} runFlow(updateAddressDeliveryStatsFlow) invocation failed for Op: ${operationType}:`, flowError.message, flowError.stack);
      });
  }
);

// NEW TRIGGER for address DND state changes
export const onAddressDndStateChange = onDocumentWritten(
  'users/{userId}/user_addresses/{addressId}',
  async (event: FirestoreEvent<Change<DocumentSnapshot> | undefined, { userId: string, addressId: string }>) => {
    console.log(`TRIGGER FIRED: onAddressDndStateChange - Event ID: ${event.id}, UserID: ${event.params.userId}, AddressID: ${event.params.addressId}`);
    const specificUserId = event.params.userId;
    const specificAddressId = event.params.addressId;
    const logPrefix = `[OnAddressDndStateChange] UserID: ${specificUserId}, AddressID: ${specificAddressId} -`;

    const addressSnapshotAfter = event.data?.after;
    const addressSnapshotBefore = event.data?.before;

    // Only process if the document exists after the change
    if (!addressSnapshotAfter || !addressSnapshotAfter.exists) {
      console.log(`${logPrefix} Address document deleted, skipping DND processing`);
      return;
    }

    // Check if manualDndState changed (normalize undefined to null for comparison)
    const beforeManualDndState = addressSnapshotBefore?.data()?.addressData?.flags?.manualDndState ?? null;
    const afterManualDndState = addressSnapshotAfter.data()?.addressData?.flags?.manualDndState ?? null;

    // ✅ FIXED: Only trigger on actual manualDndState changes to prevent infinite loops
    // Removed overly broad conditions that would re-trigger when stats updater writes back
    const shouldTriggerStatsUpdate =
      !addressSnapshotBefore || // New document
      beforeManualDndState !== afterManualDndState; // Actual manualDndState change only

    if (shouldTriggerStatsUpdate) {
      console.log(`${logPrefix} Triggering address stats update due to DND state change`);

      const flowInput: z.infer<typeof AddressStatsUpdateInputSchema> = {
        userId: specificUserId,
        addressId: specificAddressId,
      };

      runFlow(updateAddressDeliveryStatsFlow, flowInput)
        .then(() => {
          console.log(`${logPrefix} runFlow(updateAddressDeliveryStatsFlow) invoked successfully for DND state change. Input: ${JSON.stringify(flowInput)}`);
        })
        .catch((flowError: any) => {
          console.error(`${logPrefix} runFlow(updateAddressDeliveryStatsFlow) invocation failed for DND state change:`, flowError.message, flowError.stack);
        });
    } else {
      console.log(`${logPrefix} No DND state change detected, skipping stats update`);
    }
  }
);

// Correcting trigger definition based on linter feedback
export const onGeoJsonFileFinalized = onObjectFinalized(
  'autogratuity-me.firebasestorage.app', // Pass bucket name directly
  async (event: StorageEvent) => { // Use StorageEvent type
    // Access properties from the nested event.data object
    const bucket = event.data.bucket; // Bucket is usually here
    const file = event.data.name;     // File path/name is usually here
    const logPrefix = `[OnGeoJsonFileFinalized F:${file}] -`;
    console.log(`${logPrefix} Triggered for bucket ${bucket}.`);

    // Check if the file path matches the expected pattern
    const GCS_USER_ID_PATH_REGEX = /^user-uploads\/([^\/]+)\/[^\/]+\.json$/i; 
    if (!file || !GCS_USER_ID_PATH_REGEX.test(file)) {
      console.log(`${logPrefix} File path ${file} does not match expected pattern user-uploads/{userId}/*.json. Skipping.`);
      return;
    }

    // Construct the input object for the Genkit flow
    const flowInput: z.infer<typeof GcsEventDataInputSchema> = {
      bucket: bucket,
      file: file, 
    };

    // Validate input before calling flow
    const validation = GcsEventDataInputSchema.safeParse(flowInput);
    if (!validation.success) {
      console.error(`${logPrefix} Invalid GCS event data constructed:`, validation.error.flatten());
      return; 
    }

    try {
      console.log(`${logPrefix} Invoking runFlow(processUploadedGeoJsonFile)... Input: ${JSON.stringify(validation.data)}`);
      await runFlow(processUploadedGeoJsonFile, validation.data);
      console.log(`${logPrefix} runFlow(processUploadedGeoJsonFile) completed successfully.`);
    } catch (flowError: any) {
      console.error(`${logPrefix} runFlow(processUploadedGeoJsonFile) failed:`, flowError.message, flowError.stack);
    }
  }
);

// --- Callable Function ---

// Callable Function for Testing the parseImportDataWithLogging flow
export const testParseImportFlow = onCall(async (request) => {
  const rawText = request.data?.text;
  const logPrefix = `[testParseImportFlow] -`;

  if (typeof rawText !== 'string') {
    console.error(`${logPrefix} Invalid input: 'text' field missing or not a string.`, request.data);
    return { error: "Invalid input: 'text' field missing or not a string." };
  }

  console.log(`${logPrefix} Received text: "${rawText}"`);
  console.log(`${logPrefix} Calling runFlow(parseImportDataWithLogging)...`);

  try {
    // Use the statically imported parseImportDataWithLogging
    const result = await runFlow(parseImportDataWithLogging, rawText);
    console.log(`${logPrefix} runFlow(parseImportDataWithLogging) completed. Result:`, JSON.stringify(result));
    return { success: true, result: result };
  } catch (flowError: any) {
    console.error(`${logPrefix} Error running flow parseImportDataWithLogging:`, flowError.message);
    return { success: false, error: `Flow execution failed: ${flowError.message}` };
  }
});



// Google API callable functions removed - unused functionality

// ✅ FIRESTORE-ONLY: User preferences change trigger export (Redis removed)
export { onUserPreferencesChange };

// ✅ NEW: Export smart coordination triggers
export {
  onDeliveryChangeSmartCoordination,
  onAddressManualDndChangeCoordination
};

// Callable Function for Manual DND Override
export const setManualAddressDndOverride = onCall(async (request) => {
  const logPrefix = `[setManualAddressDndOverride] -`;
  console.log(`${logPrefix} Received request:`, JSON.stringify({
    userId: request.data?.userId,
    addressId: request.data?.addressId,
    desiredState: request.data?.desiredState
  }));

  // Validate input against schema
  const validation = SetManualAddressDndInputSchema.safeParse(request.data);
  if (!validation.success) {
    console.error(`${logPrefix} Invalid input:`, validation.error.flatten());
    return {
      status: 'Error',
      message: `Invalid input: ${validation.error.flatten().fieldErrors}`
    };
  }

  try {
    console.log(`${logPrefix} Calling runFlow(setManualAddressDndOverrideFlow)...`);
    const result = await runFlow(setManualAddressDndOverrideFlow, validation.data);
    console.log(`${logPrefix} runFlow(setManualAddressDndOverrideFlow) completed. Status:`, result.status);
    return result;
  } catch (flowError: any) {
    console.error(`${logPrefix} Error running flow setManualAddressDndOverrideFlow:`, flowError.message);
    return {
      status: 'Error',
      message: `Flow execution failed: ${flowError.message}`
    };
  }
});
