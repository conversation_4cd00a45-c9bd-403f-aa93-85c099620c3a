package com.autogratuity.data.util

import android.util.Log
import com.autogratuity.debug.ClarityArchitectureMonitor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlin.time.Duration.Companion.seconds
import kotlin.time.TimeSource

/**
 * KOIN-compatible Session Manager for preventing duplicate monitoring sessions.
 * Integrates with existing ClarityArchitectureMonitor and RequestDeduplicationManager.
 */
@ExperimentalCoroutinesApi
class SessionManager(
    private val clarityArchitectureMonitor: ClarityArchitectureMonitor,
    private val requestDeduplicationManager: RequestDeduplicationManager,
    private val applicationScope: CoroutineScope
) {
    private val TAG = "SessionManager"
    private val activeSessionMutex = Mutex()
    private var currentSession: MonitoringSession? = null
    
    data class MonitoringSession(
        val sessionId: String,
        val userId: String,
        val startTime: Long,
        val isActive: Boolean = true
    )
    
    suspend fun getOrCreateSession(userId: String): MonitoringSession = activeSessionMutex.withLock {
        // Use RequestDeduplicationManager to prevent duplicate session creation
        val sessionKey = "session_creation_$userId"

        val session = requestDeduplicationManager.deduplicateRequest(
            key = sessionKey,
            timeout = 3.seconds,
            operation = {
                // Check if we have an active session for this user
                currentSession?.let { session ->
                    if (session.isActive && session.userId == userId) {
                        // Reuse existing session if it's less than 5 seconds old
                        val age = System.currentTimeMillis() - session.startTime
                        if (age < 5000) {
                            Log.d(TAG, "Reusing existing session: ${session.sessionId} (age: ${age}ms)")
                            return@deduplicateRequest session
                        }
                    }
                }

                // Close previous session if exists
                currentSession?.let { session ->
                    clarityArchitectureMonitor.endSession()
                    Log.d(TAG, "Closed previous session: ${session.sessionId}")
                }

                // Create new session
                val newSession = MonitoringSession(
                    sessionId = "${userId}_${System.currentTimeMillis()}",
                    userId = userId,
                    startTime = System.currentTimeMillis()
                )

                currentSession = newSession

                // Start monitoring session and cache warming
                clarityArchitectureMonitor.startSession(newSession.userId)
                Log.i(TAG, "Created new session: ${newSession.sessionId}")

                newSession
            }
        )

        return@withLock session ?: run {
            Log.w(TAG, "Session creation was deduplicated but returned null, falling back to current session")
            currentSession ?: throw IllegalStateException("Failed to create or retrieve session for user: $userId")
        }
    }
    
    fun endCurrentSession() {
        currentSession?.let { session ->
            clarityArchitectureMonitor.endSession()
            currentSession = null
            Log.i(TAG, "Session ended: ${session.sessionId}")
        }
    }
    
    fun getCurrentSession(): MonitoringSession? = currentSession
} 