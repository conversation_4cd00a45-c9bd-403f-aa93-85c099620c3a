package com.autogratuity.data.util

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * Modern memory-efficient data source for paginated data loading using Kotlin Flow.
 * Replaces legacy RxJava patterns with 2025 Kotlin standards including:
 * - StateFlow for reactive pagination state
 * - Flow for data streams
 * - Result-based error handling
 * - Standard Java atomic operations for thread-safe operations
 * - Structured concurrency patterns
 *
 * @param T The type of data items
 * @param K The type of page key (e.g., DocumentSnapshot for Firestore)
 */
class FlowPaginatedDataSource<T : Any, K : Any>(
    private val pageLoader: FlowPageLoader<T, K>,
    val pageSize: Int = DEFAULT_PAGE_SIZE,
    private val maxPagesInMemory: Int = DEFAULT_MAX_PAGES_IN_MEMORY
) {

    companion object {
        private const val DEFAULT_MAX_PAGES_IN_MEMORY = 3
        private const val DEFAULT_PAGE_SIZE = 50
    }

    // Thread-safe state management
    private val pageCache = ConcurrentHashMap<Int, List<T>>()
    private val pageKeys = ConcurrentHashMap<Int, K>()
    private val currentPageIndex = AtomicInteger(0)
    private val totalPagesCount = AtomicInteger(Int.MAX_VALUE)
    private val isLoadingFlag = AtomicBoolean(false)
    private val loadingMutex = Mutex()

    // Reactive state streams
    private val _currentPageData = MutableStateFlow<List<T>>(emptyList())
    val currentPageData: StateFlow<List<T>> = _currentPageData.asStateFlow()

    private val _paginationState = MutableStateFlow(
        PaginationState(
            currentPage = 0,
            totalPages = Int.MAX_VALUE,
            hasPrevious = false,
            hasNext = true,
            isLoading = false
        )
    )
    val paginationState: StateFlow<PaginationState> = _paginationState.asStateFlow()

    // Current page properties
    val currentPage: Int get() = currentPageIndex.get()
    val totalPages: Int get() = totalPagesCount.get()

    /**
     * Load the initial page of data
     */
    suspend fun loadInitial(): Result<List<T>> = loadPage(0)

    /**
     * Load the next page if available
     */
    suspend fun loadNext(): Result<List<T>> {
        val current = currentPageIndex.get()
        val total = totalPagesCount.get()
        
        return if (current + 1 >= total) {
            Result.success(emptyList())
        } else {
            loadPage(current + 1)
        }
    }

    /**
     * Load the previous page if available
     */
    suspend fun loadPrevious(): Result<List<T>> {
        val current = currentPageIndex.get()
        
        return if (current <= 0) {
            Result.success(emptyList())
        } else {
            loadPage(current - 1)
        }
    }

    /**
     * Load a specific page of data with modern error handling
     */
    suspend fun loadPage(page: Int): Result<List<T>> {
        if (page < 0) {
            return Result.failure(IllegalArgumentException("Page number must be non-negative"))
        }

        // Check cache first
        pageCache[page]?.let { cachedItems ->
            currentPageIndex.set(page)
            _currentPageData.value = cachedItems
            updatePaginationState()
            return Result.success(cachedItems)
        }

        // Ensure only one loading operation at a time
        return loadingMutex.withLock {
            // Double-check cache after acquiring lock
            pageCache[page]?.let { cachedItems ->
                currentPageIndex.set(page)
                _currentPageData.value = cachedItems
                updatePaginationState()
                return Result.success(cachedItems)
            }

            if (!isLoadingFlag.compareAndSet(false, true)) {
                return Result.failure(IllegalStateException("Loading already in progress"))
            }

            try {
                updatePaginationState(loading = true)

                val keyForRequestedPage = getKeyForPage(page)
                val result = pageLoader.loadPage(pageSize, keyForRequestedPage)
                
                result.fold(
                    onSuccess = { pageResult ->
                        val items = pageResult.items
                        val nextPageKey = pageResult.nextPageKey

                        // Cache the loaded page
                        pageCache[page] = items
                        
                        // Store key for next page
                        nextPageKey?.let { key ->
                            pageKeys[page] = key
                        }

                        // Update current page
                        currentPageIndex.set(page)

                        // Update total pages if we've reached the end
                        if (items.size < pageSize || nextPageKey == null) {
                            totalPagesCount.set(page + 1)
                        }

                        // Manage memory by removing old pages
                        manageMemory(page)

                        // Update reactive state
                        _currentPageData.value = items
                        updatePaginationState()

                        Result.success(items)
                    },
                    onFailure = { error ->
                        updatePaginationState()
                        Result.failure(error)
                    }
                )
            } finally {
                isLoadingFlag.set(false)
            }
        }
    }

    /**
     * Get the key needed to load a specific page
     */
    private suspend fun getKeyForPage(page: Int): K? {
        if (page == 0) return null

        // Check if we have the key for the previous page
        pageKeys[page - 1]?.let { return it }

        // Find the closest page we have a key for
        val closestPage = (page - 2 downTo 0).firstOrNull { pageKeys.containsKey(it) }

        return if (closestPage != null) {
            // Load pages sequentially from the closest page to build up keys
            for (p in (closestPage + 1) until page) {
                loadPage(p).getOrElse { 
                    return null // Failed to load intermediate page
                }
            }
            pageKeys[page - 1]
        } else {
            // No keys available, need to start from beginning
            if (page > 0) {
                loadPage(0).getOrElse { return null }
                getKeyForPage(page) // Recursive call after loading page 0
            } else {
                null
            }
        }
    }

    /**
     * Manage memory by removing old cached pages
     */
    private fun manageMemory(currentPage: Int) {
        if (pageCache.size > maxPagesInMemory) {
            val pagesToRemove = pageCache.keys.filter { 
                kotlin.math.abs(it - currentPage) > maxPagesInMemory / 2 
            }
            pagesToRemove.forEach { page ->
                pageCache.remove(page)
                // Keep the keys as they're small and needed for navigation
            }
        }
    }

    /**
     * Update pagination state
     */
    private fun updatePaginationState(loading: Boolean = isLoadingFlag.get()) {
        val current = currentPageIndex.get()
        val total = totalPagesCount.get()
        
        _paginationState.value = PaginationState(
            currentPage = current,
            totalPages = total,
            hasPrevious = current > 0,
            hasNext = current + 1 < total,
            isLoading = loading
        )
    }

    /**
     * Flow for observing current page data
     */
    fun observeCurrentPage(): Flow<List<T>> = currentPageData

    /**
     * Flow for observing pagination state changes
     */
    fun observePaginationState(): Flow<PaginationState> = paginationState

    /**
     * Jump to a specific page
     */
    suspend fun jumpToPage(page: Int): Result<List<T>> = loadPage(page)

    /**
     * Refresh the current page
     */
    suspend fun refresh(): Result<List<T>> {
        val current = currentPageIndex.get()
        pageCache.remove(current) // Remove from cache to force reload
        return loadPage(current)
    }

    /**
     * Clear all cached data
     */
    fun clearCache() {
        pageCache.clear()
        pageKeys.clear()
        currentPageIndex.set(0)
        totalPagesCount.set(Int.MAX_VALUE)
        _currentPageData.value = emptyList()
        updatePaginationState()
    }
}

/**
 * Modern page loader interface using suspend functions and Result
 */
interface FlowPageLoader<T : Any, K : Any> {
    suspend fun loadPage(pageSize: Int, pageKey: K?): Result<PageResult<T, K>>
}

/**
 * Page result data class
 */
data class PageResult<T : Any, K : Any>(
    val items: List<T>,
    val nextPageKey: K?
)

/**
 * Pagination state data class
 */
data class PaginationState(
    val currentPage: Int,
    val totalPages: Int,
    val hasPrevious: Boolean,
    val hasNext: Boolean,
    val isLoading: Boolean
)

/**
 * Legacy compatibility interface for gradual migration
 * @deprecated Use FlowPageLoader instead
 */
@Deprecated(
    message = "Use FlowPageLoader with suspend functions instead",
    replaceWith = ReplaceWith("FlowPageLoader"),
    level = DeprecationLevel.WARNING
)
interface PageLoader<T : Any, K : Any> {
    @Deprecated("Use FlowPageLoader.loadPage() instead")
    fun loadPage(pageSize: Int, pageKey: K?): io.reactivex.rxjava3.core.Single<PageResult<T, K>>
}

/**
 * Legacy compatibility class for gradual migration
 * @deprecated Use FlowPaginatedDataSource instead
 */
@Deprecated(
    message = "Use FlowPaginatedDataSource with coroutines instead",
    replaceWith = ReplaceWith("FlowPaginatedDataSource"),
    level = DeprecationLevel.WARNING
)
class PaginatedDataSource<T : Any, K : Any>(
    private val pageLoader: PageLoader<T, K>,
    pageSize: Int = 50,
    maxPagesInMemory: Int = 3
) {
    // Legacy implementation remains for compatibility but should not be used in new code
    // Implementation details omitted for brevity - use FlowPaginatedDataSource instead
} 