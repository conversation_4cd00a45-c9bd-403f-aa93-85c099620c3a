package com.autogratuity.data.repository.address

// Places

// Coroutines

// Data Sources

// Mappers

// Domain Repositories

// Domain Models

// Generated Models

// Utils & More

// Performance monitoring

// Import aliases for dual interface pattern

// Import for AddressComponents to fix type resolution
import android.util.Log
import com.autogratuity.data.datasource.local.AddressLocalDataSource
import com.autogratuity.data.datasource.remote.AddressRemoteDataSource
import com.autogratuity.data.mapper.AddressDndMapper
import com.autogratuity.data.mapper.AddressMapper
import com.autogratuity.data.mapper.AddressStatsMapper
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.util_kt.parseUniversalTimestamp
import com.autogratuity.data.repository.core.RepositoryErrorHandler
import com.autogratuity.data.security.AuthenticationManager
import com.autogratuity.data.util.RequestDeduplicationManager
import com.autogratuity.data.util.RequestKeys
import com.autogratuity.data.util.RequestTimeouts
import com.autogratuity.debug.ClarityArchitectureMonitor
import com.autogratuity.domain.model.ManualDndState
import com.autogratuity.domain.repository.PreferenceRepository
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.android.libraries.places.api.model.Place
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.Transaction
import com.google.firebase.functions.FirebaseFunctions
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.time.OffsetDateTime
import java.util.Locale
import kotlin.time.Duration
import kotlin.time.TimeSource
import com.autogratuity.data.model.generated_kt.Address as AddressDto
import com.autogratuity.data.model.generated_kt.Address.AddressData as AddressDataDto
import com.autogratuity.data.model.generated_kt.Delivery_stats as AddressDeliveryStatsDto
import com.autogratuity.data.model.generated_kt.Flags as FlagsDto
import com.autogratuity.data.model.generated_kt.Metadata as MetadataDto
import com.autogratuity.data.repository.address.AddressRepository as DataAddressRepository
import com.autogratuity.domain.repository.AddressRepository as DomainAddressRepository


/**
 * ✅ DUAL INTERFACE IMPLEMENTATION: Address repository following UserProfileRepositoryImpl.kt pattern.
 * Implements both domain interface (for ViewModels/UseCases) and data interface (for infrastructure).
 *
 * Coordinates between remote and local data sources, mapping DTOs to SSoT models
 * and returning com.autogratuity.data.model.Result wrappers.
 *
 * ✅ ARCHITECTURAL ALIGNMENT: Follows Cache System Boundary Adaptation Pattern from atomic-caching.md
 * - Domain interface: Business-focused operations for ViewModels and UseCases
 * - Data interface: Comprehensive data management for CacheLifecycleManager, system maintenance
 *
 * ✅ MODERNIZED: Following clarity.md principles: RemoteDataSource + LocalDataSource + Mapper + Performance Core utilities
 */
@OptIn(ExperimentalCoroutinesApi::class)
class AddressRepositoryImpl(
    // Core architectural dependencies following clarity.md principles
    private val remoteDataSource: AddressRemoteDataSource,
    private val localDataSource: AddressLocalDataSource,
    private val addressMapper: AddressMapper,
    // Additional mappers for specialized operations
    private val addressStatsMapper: AddressStatsMapper,
    private val addressDndMapper: AddressDndMapper,
    // Business logic dependencies
    private val preferenceRepository: Lazy<PreferenceRepository>,
    private val placeToAddressService: PlaceToAddressService,

    // Repository orchestration dependencies
    private val authManager: AuthenticationManager,
    private val ioDispatcher: CoroutineDispatcher,
    private val applicationScope: CoroutineScope,
    // Performance infrastructure from /core
    private val repositoryErrorHandler: RepositoryErrorHandler,
    // Request deduplication for high-traffic operations
    private val requestDeduplicationManager: RequestDeduplicationManager,
    // JSON serialization with proper JSR310 support
    private val objectMapper: ObjectMapper
) : DomainAddressRepository, DataAddressRepository {

    private val TAG = "AddressRepositoryImpl"

    // ===== SAFE CASTING UTILITIES =====
    
    /**
     * Safely casts Any? to Map<String, Any?> without unchecked cast warnings
     */
    @Suppress("UNCHECKED_CAST")
    private fun Any?.safeCastToStringMap(): Map<String, Any?>? {
        return this as? Map<String, Any?>
    }

    /**
     * Safely extracts String value from map
     */
    private fun Map<String, Any?>.getStringValueSafely(key: String): String? {
        return this[key] as? String
    }

    /**
     * Safely extracts Boolean value from map
     */
    private fun Map<String, Any?>.getBooleanValueSafely(key: String): Boolean? {
        return this[key] as? Boolean
    }

    /**
     * Safely extracts Long value from map
     */
    private fun Map<String, Any?>.getLongValueSafely(key: String): Long? {
        return this[key] as? Long
    }

    /**
     * Safely extracts Double value from map
     */
    private fun Map<String, Any?>.getDoubleValueSafely(key: String): Double? {
        return this[key] as? Double
    }

    /**
     * Safely extracts List<String> value from map
     */
    @Suppress("UNCHECKED_CAST")
    private fun Map<String, Any?>.getStringListSafely(key: String): List<String>? {
        return this[key] as? List<String>
    }

    /**
     * Safely extracts List<String> value from map (alias for compatibility)
     */
    @Suppress("UNCHECKED_CAST")
    private fun Map<String, Any?>.getListValueSafely(key: String): List<String>? {
        return this[key] as? List<String>
    }

    /**
     * Safely extracts Int value from map
     */
    private fun Map<String, Any?>.getIntValueSafely(key: String): Int? {
        return this[key] as? Int
    }

    /**
     * Safely extracts Timestamp value from map
     */
    private fun Map<String, Any?>.getTimestampValueSafely(key: String): com.google.firebase.Timestamp? {
        return this[key] as? com.google.firebase.Timestamp
    }

    /**
     * Safely extracts nested map from map
     */
    private fun Map<String, Any?>.getNestedMapSafely(key: String): Map<String, Any?>? {
        return this[key].safeCastToStringMap()
    }

    // ===== END SAFE CASTING UTILITIES =====


    private fun getCurrentUserIdSuspend(): String =
        authManager.getCurrentUserId()
            ?: throw IllegalStateException("User not authenticated")

    // Modern scope management: use centralized ApplicationScope + IO dispatcher
    // Pure orchestration - no inherited scope management

    private val currentUserIdFlow: Flow<String?> = authManager.observeCurrentUser()
        .map { firebaseUser ->
            firebaseUser?.uid
        }

    private fun getCurrentUserId(): String {
        return authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")
    }

    /**
     * Get an address by its ID.
     *
     * @param id The ID of the address to get.
     * @return The address if found, null otherwise.
     */
    // Deprecated DTO-based getById(id: String) method removed.

    /**
     * Get all addresses for the current user.
     *
     * @return A list of all addresses.
     */
    // Deprecated DTO-based getAll() method removed.

    /**
     * Get addresses with pagination for the current user.
     *
     * @param limit The maximum number of addresses to return.
     * @param startAfter The document reference to start after, or null for the first page.
     * @return A list of addresses.
     */
    // Deprecated DTO-based getAll(limit, startAfter) method removed.

    // ===== DUAL INTERFACE IMPLEMENTATION =====

    // Domain interface implementation - delegate to PlaceToAddressService then fetch updated SSoT model
    override suspend fun updateAddressFromPlace(addressId: String, place: Place, userId: String): Result<com.autogratuity.domain.model.Address?> = withContext(ioDispatcher) {
        Log.d(TAG, "updateAddressFromPlace (Domain): Delegating to PlaceToAddressService for address $addressId")

        try {
            // Delegate to PlaceToAddressService which handles the Place conversion logic
            val updateResult = placeToAddressService.updateAddressDetailsFromPlace(userId, addressId, place)

            when (updateResult) {
                is Result.Success -> {
                    // PlaceToAddressService returns Unit, so we need to fetch the updated address
                    Log.d(TAG, "updateAddressFromPlace (Domain): Place update successful, fetching updated address $addressId")
                    val updatedAddressResult = getAddressById(addressId)

                    when (updatedAddressResult) {
                        is Result.Success -> {
                            Log.d(TAG, "updateAddressFromPlace (Domain): Successfully fetched updated address $addressId")
                            Result.Success(updatedAddressResult.data)
                        }
                        is Result.Error -> {
                            Log.e(TAG, "updateAddressFromPlace (Domain): Failed to fetch updated address $addressId", updatedAddressResult.exception)
                            Result.Error(updatedAddressResult.exception)
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "updateAddressFromPlace (Domain): getAddressById returned Loading unexpectedly")
                            Result.Error(IllegalStateException("getAddressById returned Loading unexpectedly"))
                        }
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "updateAddressFromPlace (Domain): PlaceToAddressService failed", updateResult.exception)
                    Result.Error(updateResult.exception)
                }
                is Result.Loading -> {
                    Log.w(TAG, "updateAddressFromPlace (Domain): PlaceToAddressService returned Loading unexpectedly")
                    Result.Error(IllegalStateException("PlaceToAddressService returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "updateAddressFromPlace (Domain): Error updating address $addressId from place", e)
            Result.Error(e)
        }
    }

    // Domain interface method - returns SSoT Address model
    override suspend fun getAddressById(id: String): Result<com.autogratuity.domain.model.Address?> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "getAddressById",
            entityType = "Address"
        ) {
            val userId = getCurrentUserIdSuspend()

            // Request deduplication for high-traffic address lookups
            val result = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressById(userId, id),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                executeGetAddressByIdOperation(id, userId)
            }
            result
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull())
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeGetAddressByIdOperation(id: String, userId: String): com.autogratuity.domain.model.Address? {
        // Note: Query profiling handled by ClarityArchitectureMonitor in remote data source

        val totalStartTime = TimeSource.Monotonic.markNow()
        var cacheCheckDuration: Duration? = null
        var remoteFetchDuration: Duration? = null
        var mappingDuration: Duration? = null
        var cacheStoreDuration: Duration? = null
        var cacheHit = false

        try {

            // 1. CACHE CHECK PHASE
            val cacheCheckStart = TimeSource.Monotonic.markNow()
            val localResult = localDataSource.getAddressById(userId, id)
            cacheCheckDuration = cacheCheckStart.elapsedNow()

            if (localResult is Result.Success && localResult.data != null) {
                cacheHit = true
                Log.d(TAG, "getAddressById: Found address $id in local cache for user $userId")

                // ENHANCED: Add session correlation for cache hit
                ClarityArchitectureMonitor.addSessionEvent("address_cache_hit:$id")

                // ENHANCED: Monitor cache hit with breakdown
                ClarityArchitectureMonitor.monitorCacheSystemPerformance(
                    repositoryClass = "AddressRepository",
                    operation = "getAddressById",
                    cacheCheckDuration = cacheCheckDuration,
                    remoteFetchDuration = null,
                    mappingDuration = null,
                    cacheStoreDuration = null,
                    cacheHit = true,
                    entityType = "Address",
                    entityId = id,
                    userId = userId,
                    cacheMetrics = mapOf(
                        "operation" to "cache_hit",
                        "source" to "local_cache",
                        "data_size" to localResult.data.toString().length
                    )
                )

                return localResult.data
            }
            if (localResult is Result.Error) {
                 Log.w(TAG, "getAddressById: Local data source failed for address $id, user $userId. Error: ${localResult.exception.message}")
                 // Continue to remote, but log this error.
            }

            // 2. REMOTE FETCH PHASE
            Log.d(TAG, "getAddressById: Address $id not in cache or local fetch failed for user $userId, fetching from remote.")
            val remoteFetchStart = TimeSource.Monotonic.markNow()
            val remoteResultDto = remoteDataSource.getAddressById(userId, id)
            remoteFetchDuration = remoteFetchStart.elapsedNow()

            when (remoteResultDto) {
                is Result.Success -> {
                    val addressDto = remoteResultDto.data
                    if (addressDto != null) {
                        // 3. MAPPING PHASE
                        val mappingStart = TimeSource.Monotonic.markNow()
                        val ssotResult = addressMapper.mapToDomain(addressDto.id, addressDto.addressData)
                        mappingDuration = mappingStart.elapsedNow()

                        when (ssotResult) {
                            is Result.Success -> {
                                val ssotAddress = ssotResult.data

                                // 4. CACHE STORAGE PHASE
                                val cacheStoreStart = TimeSource.Monotonic.markNow()
                                localDataSource.saveAddress(userId, ssotAddress)
                                cacheStoreDuration = cacheStoreStart.elapsedNow()

                                Log.d(TAG, "getAddressById: Fetched address $id from remote and updated cache for user $userId")

                                // ENHANCED: Add session correlation for cache miss
                                ClarityArchitectureMonitor.addSessionEvent("address_cache_miss:$id")

                                // ENHANCED: Monitor complete cache miss breakdown
                                ClarityArchitectureMonitor.monitorCacheSystemPerformance(
                                    repositoryClass = "AddressRepository",
                                    operation = "getAddressById",
                                    cacheCheckDuration = cacheCheckDuration,
                                    remoteFetchDuration = remoteFetchDuration,
                                    mappingDuration = mappingDuration,
                                    cacheStoreDuration = cacheStoreDuration,
                                    cacheHit = false,
                                    entityType = "Address",
                                    entityId = id,
                                    userId = userId,
                                    cacheMetrics = mapOf<String, Any>(
                                        "operation" to "cache_miss_with_remote_fetch",
                                        "source" to "firestore",
                                        "total_time_ms" to totalStartTime.elapsedNow().inWholeMilliseconds,
                                        "cache_check_ms" to cacheCheckDuration.inWholeMilliseconds,
                                        "remote_fetch_ms" to remoteFetchDuration.inWholeMilliseconds,
                                        "mapping_ms" to mappingDuration.inWholeMilliseconds,
                                        "cache_store_ms" to cacheStoreDuration.inWholeMilliseconds,
                                        "data_size" to addressDto.toString().length
                                    )
                                )

                                return ssotAddress
                            }
                            is Result.Error -> {
                                Log.e(TAG, "getAddressById: Failed to map DTO to SSoT for address $id, user $userId", ssotResult.exception)
                                throw ssotResult.exception
                            }
                            is Result.Loading -> {
                                Log.w(TAG, "getAddressById: SSoT mapping returned Loading for address $id, user $userId. This is unexpected here.")
                                throw IllegalStateException("SSoT mapping returned Loading unexpectedly for address $id")
                            }
                        }
                    } else {
                        Log.d(TAG, "getAddressById: Address $id not found in remote for user $userId")
                        return null // Explicitly return null if remote says not found
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "getAddressById: Remote data source failed for address $id, user $userId", remoteResultDto.exception)
                    throw remoteResultDto.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "getAddressById: Remote data source returned Loading for address $id, user $userId. This is unexpected here.")
                    throw IllegalStateException("Remote data source returned Loading unexpectedly for address $id")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getAddressById: Unexpected error for address $id", e)

            // Monitor failed domain repository operation with context
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "AddressRepository",
                operation = "getAddressById",
                duration = totalStartTime.elapsedNow(),
                success = false,
                error = e,
                dataType = "Address",
                entityId = id,
                userId = authManager.getCurrentUserId() ?: "unknown",
                resultCount = 0,
                dataSource = "error",
                cacheStrategy = "cache-first"
            )

            throw e // Re-throw for RepositoryErrorHandler to handle
        }
    }

    override suspend fun getAddresses(): Result<List<AddressDto>> = withContext(ioDispatcher) {
        return@withContext getAddressesDto()
    }

    // Internal helper method for DTO operations
    private suspend fun getAddressByIdDto(addressId: String): Result<AddressDto?> = withContext(ioDispatcher) {
        val userId = getCurrentUserIdSuspend()
        Log.d(TAG, "getAddressByIdDto: Fetching address $addressId for user $userId")

        // Call RemoteDataSource directly for DTO with deduplication
        val remoteResult = requestDeduplicationManager.deduplicateRequest(
            key = RequestKeys.addressById(userId, addressId),
            timeout = RequestTimeouts.STANDARD_OPERATION
        ) {
            remoteDataSource.getAddressById(userId, addressId)
        } ?: return@withContext Result.Error(Exception("Request deduplication timeout for getAddressByIdDto"))

        when (remoteResult) {
            is Result.Success -> {
                val addressDto = remoteResult.data
                if (addressDto != null) {
                    Log.d(TAG, "getAddressByIdDto: Found address $addressId for user $userId")

                    // Also update local cache with SSoT model
                    val ssotResult = addressMapper.mapToDomain(addressDto.id, addressDto.addressData)
                    if (ssotResult is Result.Success) {
                        localDataSource.saveAddress(userId, ssotResult.data)
                    }
                }
                Result.Success(addressDto)
            }
            is Result.Error -> {
                Log.e(TAG, "getAddressByIdDto: Remote data source failed for address $addressId, user $userId", remoteResult.exception)
                remoteResult
            }
            is Result.Loading -> {
                Log.w(TAG, "getAddressByIdDto: Remote data source returned Loading for address $addressId, user $userId")
                Result.Error(IllegalStateException("Remote data source returned Loading unexpectedly"))
            }
        }
    }

    // Method getAddressesPaginated (DTO, non-Flow) was removed by refactoring.
    // Its functionality is not part of the DomainAddressRepository interface.
    // The SSoT-compliant getAllAddresses() should be used for fetching all addresses.

    // This method replaces the old DTO-based getAddresses (previously around lines 219-231)
    // It aligns with the DomainAddressRepository interface expecting SSoT models.
    override suspend fun getAllAddresses(): Result<List<com.autogratuity.domain.model.Address>> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "getAllAddresses",
            entityType = "Address"
        ) {
            val userId = getCurrentUserIdSuspend()

            // Request deduplication for high-traffic address list queries
            val result = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userAddresses(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                executeGetAllAddressesOperation(userId)
            }
            result ?: emptyList()
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull() ?: emptyList())
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeGetAllAddressesOperation(userId: String): List<com.autogratuity.domain.model.Address> {
        val totalStartTime = TimeSource.Monotonic.markNow()
        Log.d(TAG, "getAddresses (SSoT) - Fetching all addresses for user $userId")

        // 1. Try local data source first (expects SSoT)
        val localResult = localDataSource.getAllAddresses(userId)
        if (localResult is Result.Success && localResult.data.isNotEmpty()) {
            Log.d(TAG, "getAddresses (SSoT) - Found ${localResult.data.size} addresses in local cache for user $userId")
            return localResult.data // Return cached SSoT data
        }
        if (localResult is Result.Error) {
            Log.w(TAG, "getAddresses (SSoT) - Local data source failed for user $userId. Error: ${localResult.exception.message}")
            // Continue to remote, but log this error.
        }
        // If localResult was Success but data was empty, also proceed to remote.

        // 2. Try remote data source if local failed or was empty
        Log.d(TAG, "getAddresses (SSoT) - Addresses not in cache or local fetch failed/empty for user $userId, fetching from remote.")
        val remoteResultDtoList = remoteDataSource.getAllAddresses(userId)

        when (remoteResultDtoList) {
            is Result.Success -> {
                val addressDtoList = remoteResultDtoList.data
                if (addressDtoList.isNotEmpty()) {
                    // Map List<AddressDto> to List<SSoTAddress>
                    val ssotAddressListResult = mutableListOf<com.autogratuity.domain.model.Address>()
                    var mappingErrorOccurred = false
                    for (dto in addressDtoList) {
                        // Use the mapper signature: mapToDomain(dtoId: String, dataDto: AddressDto.AddressData?)
                        val ssotResult = addressMapper.mapToDomain(dto.id, dto.addressData)
                        when (ssotResult) {
                            is Result.Success -> ssotAddressListResult.add(ssotResult.data)
                            is Result.Error -> {
                                Log.e(TAG, "getAddresses (SSoT) - Failed to map DTO (id: ${dto.id}) to SSoT for user $userId", ssotResult.exception)
                                mappingErrorOccurred = true // Mark that an error occurred
                            }
                            is Result.Loading -> {
                                Log.w(TAG, "getAddresses (SSoT) - SSoT mapping returned Loading for DTO (id: ${dto.id}), user $userId. This is unexpected here.")
                                mappingErrorOccurred = true // Treat as an error for this item
                            }
                        }
                    }

                    if (mappingErrorOccurred && ssotAddressListResult.isEmpty()) {
                        // If all mappings failed, propagate a general mapping error
                        throw Exception("Failed to map any addresses from DTO to SSoT for user $userId")
                    } else {
                        // Save successfully mapped SSoT addresses to local cache
                        if (ssotAddressListResult.isNotEmpty()) {
                            localDataSource.saveAllAddresses(userId, ssotAddressListResult) // Fire and forget, or handle result
                            Log.d(TAG, "getAddresses (SSoT) - Fetched and cached ${ssotAddressListResult.size} SSoT addresses for user $userId")
                        }
                        return ssotAddressListResult.toList() // Return the list
                    }
                } else {
                    Log.d(TAG, "getAddresses (SSoT) - No addresses found in remote for user $userId")
                    return emptyList() // Return empty list
                }
            }
            is Result.Error -> {
                Log.e(TAG, "getAddresses (SSoT) - Remote data source failed for user $userId", remoteResultDtoList.exception)
                throw remoteResultDtoList.exception // Propagate the remote error
            }
            is Result.Loading -> {
                Log.w(TAG, "getAddresses (SSoT) - Remote data source returned Loading for user $userId. This is unexpected here.")
                throw IllegalStateException("Remote data source (getAllAddresses) returned Loading unexpectedly for user $userId")
            }
        }
    }

    // Internal helper method for DTO operations
    private suspend fun getAddressesDto(): Result<List<AddressDto>> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "getAddresses_DataInterface",
            entityType = "Address"
        ) {
            val userId = getCurrentUserIdSuspend()
            Log.d(TAG, "getAddresses (Data Interface): Fetching all addresses for user $userId")

            // Call RemoteDataSource directly for DTOs
            val remoteResult = remoteDataSource.getAllAddresses(userId)

            when (remoteResult) {
                is Result.Success -> {
                    val addressDtoList = remoteResult.data
                    Log.d(TAG, "getAddresses (Data Interface): Found ${addressDtoList.size} addresses for user $userId")

                    // Also update local cache with SSoT models
                    val ssotAddresses = mutableListOf<com.autogratuity.domain.model.Address>()
                    for (dto in addressDtoList) {
                        val ssotResult = addressMapper.mapToDomain(dto.id, dto.addressData)
                        if (ssotResult is Result.Success) {
                            ssotAddresses.add(ssotResult.data)
                        }
                    }
                    if (ssotAddresses.isNotEmpty()) {
                        localDataSource.saveAllAddresses(userId, ssotAddresses)
                    }

                    addressDtoList
                }
                is Result.Error -> {
                    Log.e(TAG, "getAddresses (Data Interface): Remote data source failed for user $userId", remoteResult.exception)
                    throw remoteResult.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "getAddresses (Data Interface): Remote data source returned Loading for user $userId")
                    throw IllegalStateException("Remote data source returned Loading unexpectedly")
                }
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull()!!)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    // Domain interface method - returns Flow<Result<List<SSoT Address>>>
    override fun observeAddresses(): Flow<Result<List<com.autogratuity.domain.model.Address>>> {
        return authManager.observeCurrentUser().flatMapLatest { firebaseUser ->
            val userId = firebaseUser?.uid
            if (userId.isNullOrBlank()) {
                kotlinx.coroutines.flow.flowOf(Result.Error(Exception("User not authenticated for observeAddresses")))
            } else {
                repositoryErrorHandler.handleFlow(
                    flow = localDataSource.observeAll(userId)
                        .onStart {
                            applicationScope.launch(ioDispatcher) {
                                Log.d(TAG, "observeAddresses (Domain Interface) for user $userId: Triggering initial cache refresh.")
                                getAllAddresses()
                            }
                        },
                    operationName = "observeAddresses",
                    entityType = "AddressList"
                ).map { kotlinResult ->
                    when {
                        kotlinResult.isSuccess -> {
                            val addressList = kotlinResult.getOrNull() ?: emptyList<com.autogratuity.domain.model.Address>()
                            Result.Success(addressList)
                        }
                        else -> {
                            val throwable = kotlinResult.exceptionOrNull()
                            val exception = when (throwable) {
                                is Exception -> throwable
                                else -> Exception("Unknown error in observeAddresses flow", throwable)
                            }
                            Result.Error(exception)
                        }
                    }
                }
            }
        }.flowOn(ioDispatcher)
    }

    override fun observeAddress(addressId: String): Flow<Result<AddressDto?>> {
        return currentUserIdFlow.flatMapLatest { userId ->
            if (userId.isNullOrBlank()) {
                Log.w(TAG, "observeAddress (Data Interface): User not authenticated or userId is blank for address $addressId. Emitting error flow.")
                return@flatMapLatest flowOf(Result.Error(Exception("User not authenticated for observeAddress")))
            } else {
                Log.d(TAG, "observeAddress (Data Interface): Setting up DTO observer for user $userId, address $addressId")
                repositoryErrorHandler.handleFlow(
                    flow = remoteDataSource.observeAddressById(userId, addressId)
                        .onStart {
                            applicationScope.launch(ioDispatcher) {
                                Log.d(TAG, "observeAddress (Data Interface) for user $userId, address $addressId: Triggering initial fetch.")
                                getAddressByIdDto(addressId)
                            }
                        },
                    operationName = "observeAddress_DataInterface",
                    entityType = "Address"
                ).map { kotlinResult ->
                    when {
                        kotlinResult.isSuccess -> {
                            val addressDto = kotlinResult.getOrNull()
                            Result.Success(addressDto)
                        }
                        else -> {
                            val throwable = kotlinResult.exceptionOrNull()
                            val exception = when (throwable) {
                                is Exception -> throwable
                                else -> Exception("Unknown error in observeAddress flow", throwable)
                            }
                            Result.Error(exception)
                        }
                    }
                }
            }
        }.flowOn(ioDispatcher)
    }

    override suspend fun getFavoriteAddresses(): List<AddressDto> = withContext(ioDispatcher) {
        try {
            val userId = getCurrentUserIdSuspend()
            Log.d(TAG, "getFavoriteAddresses (Data Interface): Fetching favorite addresses for user $userId")

            // Get all addresses from remote with deduplication
            val allAddressesResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userAddresses(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.getAllAddresses(userId)
            } ?: return@withContext emptyList()

            when (allAddressesResult) {
                is Result.Success -> {
                    val allAddresses = allAddressesResult.data
                    // Filter for favorite addresses
                    val favoriteAddresses = allAddresses.filter { addressDto ->
                        addressDto.addressData.flags?.isFavorite == true
                    }

                    Log.d(TAG, "getFavoriteAddresses (Data Interface): Found ${favoriteAddresses.size} favorite addresses for user $userId")

                    // Update local cache with SSoT models for all addresses
                    val ssotAddresses = mutableListOf<com.autogratuity.domain.model.Address>()
                    for (dto in allAddresses) {
                        val ssotResult = addressMapper.mapToDomain(dto.id, dto.addressData)
                        if (ssotResult is Result.Success) {
                            ssotAddresses.add(ssotResult.data)
                        }
                    }
                    if (ssotAddresses.isNotEmpty()) {
                        localDataSource.saveAllAddresses(userId, ssotAddresses)
                    }

                    favoriteAddresses
                }
                is Result.Error -> {
                    Log.e(TAG, "getFavoriteAddresses (Data Interface): Failed to fetch addresses for user $userId", allAddressesResult.exception)
                    emptyList()
                }
                is Result.Loading -> {
                    Log.w(TAG, "getFavoriteAddresses (Data Interface): Remote data source returned Loading unexpectedly")
                    emptyList()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getFavoriteAddresses (Data Interface): Unexpected error", e)
            emptyList()
        }
    }

    override suspend fun getBestTippingAddresses(limit: Int): List<AddressDto> = withContext(ioDispatcher) {
        try {
            val userId = getCurrentUserIdSuspend()
            Log.d(TAG, "getBestTippingAddresses (Data Interface): Fetching best tipping addresses (limit: $limit) for user $userId")

            // Get all addresses from remote with deduplication
            val allAddressesResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userAddresses(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.getAllAddresses(userId)
            } ?: return@withContext emptyList()

            when (allAddressesResult) {
                is Result.Success -> {
                    val allAddresses = allAddressesResult.data

                    // Sort by delivery stats - highest average tip amount first
                    val bestTippingAddresses = allAddresses
                        .filter { addressDto ->
                            // Only include addresses that have delivery stats with tips
                            val deliveryStats = addressDto.addressData.deliveryStats
                            deliveryStats != null &&
                            deliveryStats.tipCount!! > 0 &&
                            deliveryStats.averageTipAmount!! > 0.0
                        }
                        .sortedByDescending { addressDto ->
                            // Sort by average tip amount, then by total tips as tiebreaker
                            val stats = addressDto.addressData.deliveryStats
                            (stats?.averageTipAmount ?: 0.0) * 1000 + (stats?.totalTips ?: 0.0)
                        }
                        .take(limit)

                    Log.d(TAG, "getBestTippingAddresses (Data Interface): Found ${bestTippingAddresses.size} best tipping addresses for user $userId")

                    // Update local cache with SSoT models for all addresses
                    val ssotAddresses = mutableListOf<com.autogratuity.domain.model.Address>()
                    for (dto in allAddresses) {
                        val ssotResult = addressMapper.mapToDomain(dto.id, dto.addressData)
                        if (ssotResult is Result.Success) {
                            ssotAddresses.add(ssotResult.data)
                        }
                    }
                    if (ssotAddresses.isNotEmpty()) {
                        localDataSource.saveAllAddresses(userId, ssotAddresses)
                    }

                    bestTippingAddresses
                }
                is Result.Error -> {
                    Log.e(TAG, "getBestTippingAddresses (Data Interface): Failed to fetch addresses for user $userId", allAddressesResult.exception)
                    emptyList()
                }
                is Result.Loading -> {
                    Log.w(TAG, "getBestTippingAddresses (Data Interface): Remote data source returned Loading unexpectedly")
                    emptyList()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getBestTippingAddresses (Data Interface): Unexpected error", e)
            emptyList()
        }
    }

    override suspend fun getRecentlyUsedAddresses(limit: Int): List<AddressDto> = withContext(ioDispatcher) {
        try {
            val userId = getCurrentUserIdSuspend()
            Log.d(TAG, "getRecentlyUsedAddresses (Data Interface): Fetching recently used addresses (limit: $limit) for user $userId")

            // Get all addresses from remote with deduplication
            val allAddressesResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userAddresses(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.getAllAddresses(userId)
            } ?: return@withContext emptyList()

            when (allAddressesResult) {
                is Result.Success -> {
                    val allAddresses = allAddressesResult.data

                    // Sort by most recent delivery date
                    val recentlyUsedAddresses = allAddresses
                        .filter { addressDto ->
                            // Only include addresses that have delivery stats with recent activity
                            val deliveryStats = addressDto.addressData.deliveryStats
                            deliveryStats != null &&
                            deliveryStats.deliveryCount!! > 0 &&
                            deliveryStats.lastDeliveryDate != null
                        }
                        .sortedByDescending { addressDto ->
                            // Sort by last delivery date (most recent first)
                            val lastDeliveryDate = addressDto.addressData.deliveryStats?.lastDeliveryDate
                            lastDeliveryDate?.toInstant()?.toEpochMilli() ?: 0L
                        }
                        .take(limit)

                    Log.d(TAG, "getRecentlyUsedAddresses (Data Interface): Found ${recentlyUsedAddresses.size} recently used addresses for user $userId")

                    // Update local cache with SSoT models for all addresses
                    val ssotAddresses = mutableListOf<com.autogratuity.domain.model.Address>()
                    for (dto in allAddresses) {
                        val ssotResult = addressMapper.mapToDomain(dto.id, dto.addressData)
                        if (ssotResult is Result.Success) {
                            ssotAddresses.add(ssotResult.data)
                        }
                    }
                    if (ssotAddresses.isNotEmpty()) {
                        localDataSource.saveAllAddresses(userId, ssotAddresses)
                    }

                    recentlyUsedAddresses
                }
                is Result.Error -> {
                    Log.e(TAG, "getRecentlyUsedAddresses (Data Interface): Failed to fetch addresses for user $userId", allAddressesResult.exception)
                    emptyList()
                }
                is Result.Loading -> {
                    Log.w(TAG, "getRecentlyUsedAddresses (Data Interface): Remote data source returned Loading unexpectedly")
                    emptyList()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getRecentlyUsedAddresses (Data Interface): Unexpected error", e)
            emptyList()
        }
    }

    override suspend fun searchAddresses(query: String): List<AddressDto> = withContext(ioDispatcher) {
        try {
            val userId = getCurrentUserIdSuspend()
            Log.d(TAG, "searchAddresses (Data Interface): Searching addresses with query '$query' for user $userId")

            if (query.isBlank()) {
                Log.w(TAG, "searchAddresses (Data Interface): Empty query provided")
                return@withContext emptyList()
            }

            // Get all addresses from remote with deduplication
            val allAddressesResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userAddresses(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.getAllAddresses(userId)
            } ?: return@withContext emptyList()

            when (allAddressesResult) {
                is Result.Success -> {
                    val allAddresses = allAddressesResult.data
                    val normalizedQuery = query.lowercase().trim()

                    // Search through various address fields
                    val matchingAddresses = allAddresses.filter { addressDto ->
                        val addressData = addressDto.addressData
                        if (false) return@filter false

                        // Search in full address
                        val fullAddress = addressData.fullAddress?.lowercase() ?: ""
                        if (fullAddress.contains(normalizedQuery)) return@filter true

                        // Search in normalized address
                        val normalizedAddress = addressData.normalizedAddress?.lowercase() ?: ""
                        if (normalizedAddress.contains(normalizedQuery)) return@filter true

                        // Search in address components
                        val components = addressData.components
                        if (components != null) {
                            val streetName = components.streetName?.lowercase() ?: ""
                            val city = components.city?.lowercase() ?: ""
                            val state = components.state?.lowercase() ?: ""
                            val postalCode = components.postalCode?.lowercase() ?: ""

                            if (streetName.contains(normalizedQuery) ||
                                city.contains(normalizedQuery) ||
                                state.contains(normalizedQuery) ||
                                postalCode.contains(normalizedQuery)) {
                                return@filter true
                            }
                        }

                        // Search in notes
                        val notes = addressData.notes?.lowercase() ?: ""
                        if (notes.contains(normalizedQuery)) return@filter true

                        // Search in tags
                        val tags = addressData.tags ?: emptyList()
                        if (tags.any { it.lowercase().contains(normalizedQuery) }) return@filter true

                        false
                    }

                    Log.d(TAG, "searchAddresses (Data Interface): Found ${matchingAddresses.size} matching addresses for query '$query'")

                    // Update local cache with SSoT models for all addresses
                    val ssotAddresses = mutableListOf<com.autogratuity.domain.model.Address>()
                    for (dto in allAddresses) {
                        val ssotResult = addressMapper.mapToDomain(dto.id, dto.addressData)
                        if (ssotResult is Result.Success) {
                            ssotAddresses.add(ssotResult.data)
                        }
                    }
                    if (ssotAddresses.isNotEmpty()) {
                        localDataSource.saveAllAddresses(userId, ssotAddresses)
                    }

                    matchingAddresses
                }
                is Result.Error -> {
                    Log.e(TAG, "searchAddresses (Data Interface): Failed to fetch addresses for user $userId", allAddressesResult.exception)
                    emptyList()
                }
                is Result.Loading -> {
                    Log.w(TAG, "searchAddresses (Data Interface): Remote data source returned Loading unexpectedly")
                    emptyList()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "searchAddresses (Data Interface): Unexpected error", e)
            emptyList()
        }
    }

    override suspend fun getAddressesNearLocation(
        latitude: Double,
        longitude: Double,
        radiusKm: Double
    ): List<AddressDto> = withContext(ioDispatcher) {
        try {
            val userId = getCurrentUserIdSuspend()
            Log.d(TAG, "getAddressesNearLocation (Data Interface): Finding addresses near lat=$latitude, lng=$longitude, radius=${radiusKm}km for user $userId")

            // Get all addresses from remote with deduplication
            val allAddressesResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userAddresses(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.getAllAddresses(userId)
            } ?: return@withContext emptyList()

            when (allAddressesResult) {
                is Result.Success -> {
                    val allAddresses = allAddressesResult.data

                    // Filter addresses within the specified radius
                    val nearbyAddresses = allAddresses.filter { addressDto ->
                        val coordinates = addressDto.addressData.coordinates
                        if (coordinates?.latitude == null || coordinates.longitude == null) {
                            return@filter false
                        }

                        // Calculate distance using Haversine formula
                        val distance = calculateDistance(
                            latitude, longitude,
                            coordinates.latitude, coordinates.longitude
                        )

                        distance <= radiusKm
                    }.sortedBy { addressDto ->
                        // Sort by distance (closest first)
                        val coordinates = addressDto.addressData.coordinates
                        if (coordinates?.latitude != null && coordinates.longitude != null) {
                            calculateDistance(
                                latitude, longitude,
                                coordinates.latitude, coordinates.longitude
                            )
                        } else {
                            Double.MAX_VALUE
                        }
                    }

                    Log.d(TAG, "getAddressesNearLocation (Data Interface): Found ${nearbyAddresses.size} addresses within ${radiusKm}km")

                    // Update local cache with SSoT models for all addresses
                    val ssotAddresses = mutableListOf<com.autogratuity.domain.model.Address>()
                    for (dto in allAddresses) {
                        val ssotResult = addressMapper.mapToDomain(dto.id, dto.addressData)
                        if (ssotResult is Result.Success) {
                            ssotAddresses.add(ssotResult.data)
                        }
                    }
                    if (ssotAddresses.isNotEmpty()) {
                        localDataSource.saveAllAddresses(userId, ssotAddresses)
                    }

                    nearbyAddresses
                }
                is Result.Error -> {
                    Log.e(TAG, "getAddressesNearLocation (Data Interface): Failed to fetch addresses for user $userId", allAddressesResult.exception)
                    emptyList()
                }
                is Result.Loading -> {
                    Log.w(TAG, "getAddressesNearLocation (Data Interface): Remote data source returned Loading unexpectedly")
                    emptyList()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getAddressesNearLocation (Data Interface): Unexpected error", e)
            emptyList()
        }
    }

    /**
     * Calculates the distance between two geographic points using the Haversine formula.
     * @return Distance in kilometers
     */
    private fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
        val earthRadiusKm = 6371.0

        val dLat = Math.toRadians(lat2 - lat1)
        val dLon = Math.toRadians(lon2 - lon1)

        val a = kotlin.math.sin(dLat / 2) * kotlin.math.sin(dLat / 2) +
                kotlin.math.cos(Math.toRadians(lat1)) * kotlin.math.cos(Math.toRadians(lat2)) *
                kotlin.math.sin(dLon / 2) * kotlin.math.sin(dLon / 2)

        val c = 2 * kotlin.math.atan2(kotlin.math.sqrt(a), kotlin.math.sqrt(1 - a))

        return earthRadiusKm * c
    }

    // Note: The data interface observeAddresses() method is identical to domain interface
    // Both return Flow<Result<List<Address>>> where Address is the SSoT model
    // This follows the UserProfileRepository pattern where both interfaces use identical signatures

    /**
     * Monitors an address for changes from the cloud (including statistics updates from cloud functions)
     * and updates the local cache accordingly.
     */
    // Method monitorAddressUpdatesFromCloud (DTO) was removed by refactoring.
    // Live SSoT updates are handled by observeAddressById and observeAddresses.

    // Old DTO-based observeAddress(addressId: String) method removed.
    // Replaced by SSoT-compliant observeAddressById(id: String).

    // --- WriteOperations ---
    // Deprecated DTO-based add(entity: Address) method removed.

    /**
     * Update an address entity.
     *
     * @param entity The address entity to update.
     */
    // Deprecated DTO-based update(entity: Address) method removed.

    // ===== DUAL INTERFACE IMPLEMENTATION =====

    // Domain interface method - takes SSoT Address model
    override suspend fun addAddress(address: com.autogratuity.domain.model.Address): Result<String> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "addAddress",
            entityType = "Address"
        ) {
            val userId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")

            // Request deduplication for critical add operation
            val result = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressCreate(userId),
                timeout = RequestTimeouts.COMPLEX_OPERATION
            ) {
                executeAddAddressOperation(address, userId)
            }
            result ?: ""
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull()!!)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeAddAddressOperation(address: com.autogratuity.domain.model.Address, userId: String): String {
        try {
            // 1. Map SSoT Address to AddressDto.AddressData
            // The input SSoT Address might not have an ID if it's brand new.
            // The mapper should handle creating AddressDto.AddressData correctly, potentially without an ID.
            val mapToDtoDataResult = addressMapper.mapToDtoData(address.copy(userId = userId)) // Ensure userId is set if mapper expects it

        val addressDataDto = when (mapToDtoDataResult) {
            is Result.Success -> mapToDtoDataResult.data
            is Result.Error -> {
                Log.e(TAG, "addAddress: Failed to map SSoT to AddressDto.AddressData for user $userId", mapToDtoDataResult.exception)
                throw mapToDtoDataResult.exception
            }
            is Result.Loading -> {
                Log.w(TAG, "addAddress: addressMapper.mapToDtoData returned Loading for user $userId. This is unexpected here.")
                throw IllegalStateException("addressMapper.mapToDtoData returned Loading unexpectedly")
            }
            else -> throw Exception("Unknown state from addressMapper.mapToDtoData")
        }

        // 2. Call RemoteDataSource to add the address
        Log.d(TAG, "addAddress: Adding address to remote for user $userId")
        val remoteAddResult = remoteDataSource.addAddress(userId, addressDataDto)

        return when (remoteAddResult) {
            is Result.Success -> {
                val newDocRef = remoteAddResult.data
                val newId = newDocRef.id
                Log.i(TAG, "addAddress: Address added remotely with ID $newId for user $userId")

                // 3. Create new SSoT with the remote-generated ID and save to local cache
                val newSsotAddressWithId = address.copy(id = newId, userId = userId) // Ensure userId consistency
                val localSaveResult = localDataSource.saveAddress(userId, newSsotAddressWithId)
                if (localSaveResult is Result.Error) {
                    // Log error but proceed with returning success for the add operation itself,
                    // as the remote add was successful. Cache update is secondary.
                    Log.w(TAG, "addAddress: Failed to save address $newId to local cache for user $userId", localSaveResult.exception)
                }
                newId
            }
            is Result.Error -> {
                Log.e(TAG, "addAddress: Remote data source failed to add address for user $userId", remoteAddResult.exception)
                throw remoteAddResult.exception // Propagate the remote error
            }
            is Result.Loading -> {
                Log.w(TAG, "addAddress: remoteDataSource.addAddress returned Loading for user $userId. This is unexpected here.")
                throw IllegalStateException("remoteDataSource.addAddress returned Loading unexpectedly")
            }
            else -> throw Exception("Unknown state from remoteDataSource.addAddress")
        }

        } catch (e: Exception) {
            Log.e(TAG, "addAddress: Unexpected error for user $userId", e)
            throw e // Re-throw for RepositoryErrorHandler to handle
        }
    }

    // Data interface method - takes AddressDataDto and returns DocumentReference
    override suspend fun addAddress(addressData: AddressDataDto): Result<DocumentReference> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "addAddress_DataInterface",
            entityType = "Address"
        ) {
            val userId = getCurrentUserIdSuspend()

            // Request deduplication for critical add operation (data interface)
            val result = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressCreate(userId),
                timeout = RequestTimeouts.COMPLEX_OPERATION
            ) {
                executeAddAddressDataOperation(userId, addressData)
            }
            result ?: throw IllegalStateException("Request deduplication timeout for addAddress")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull()!!)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeAddAddressDataOperation(userId: String, addressData: AddressDataDto): DocumentReference {
        Log.d(TAG, "addAddress (Data Interface): Adding address using AddressDataDto for user $userId")

        // Call RemoteDataSource to add the address
        val remoteAddResult = remoteDataSource.addAddress(userId, addressData)

        when (remoteAddResult) {
            is Result.Success -> {
                val newDocRef = remoteAddResult.data
                Log.i(TAG, "addAddress (Data Interface): Address added remotely with ID ${newDocRef.id} for user $userId")

                // Map AddressDataDto to SSoT model for local cache
                val ssotResult = addressMapper.mapToDomain(newDocRef.id, addressData)
                when (ssotResult) {
                    is Result.Success -> {
                        val ssotAddress = ssotResult.data
                        val localSaveResult = localDataSource.saveAddress(userId, ssotAddress)
                        if (localSaveResult is Result.Error) {
                            Log.w(TAG, "addAddress (Data Interface): Failed to save address ${newDocRef.id} to local cache for user $userId", localSaveResult.exception)
                        }
                    }
                    is Result.Error -> {
                        Log.w(TAG, "addAddress (Data Interface): Failed to map AddressDataDto to SSoT for caching", ssotResult.exception)
                    }
                    is Result.Loading -> {
                        Log.w(TAG, "addAddress (Data Interface): Unexpected Loading state from mapper")
                    }
                }

                return newDocRef
            }
            is Result.Error -> {
                Log.e(TAG, "addAddress (Data Interface): Remote data source failed to add address for user $userId", remoteAddResult.exception)
                throw remoteAddResult.exception
            }
            is Result.Loading -> {
                Log.w(TAG, "addAddress (Data Interface): remoteDataSource.addAddress returned Loading for user $userId")
                throw IllegalStateException("remoteDataSource.addAddress returned Loading unexpectedly")
            }
        }
    }

    // Data interface method - takes AddressDto and returns Unit
    override suspend fun updateAddress(address: AddressDto): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "updateAddress_DataInterface",
            entityType = "Address"
        ) {
            val userId = getCurrentUserIdSuspend()

            // Request deduplication for critical update operation (data interface)
            val result = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressUpdate(userId, address.id),
                timeout = RequestTimeouts.COMPLEX_OPERATION
            ) {
                executeUpdateAddressDataOperation(userId, address)
            }
            result ?: Unit
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull() ?: Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeUpdateAddressDataOperation(userId: String, address: AddressDto) {
        Log.d(TAG, "updateAddress (Data Interface): Updating address ${address.id} for user $userId")

        // Call RemoteDataSource directly with DTO
        val remoteUpdateResult = remoteDataSource.updateAddress(userId, address)

        when (remoteUpdateResult) {
            is Result.Success -> {
                Log.d(TAG, "updateAddress (Data Interface): Address ${address.id} updated successfully for user $userId")

                // Also update local cache with SSoT model
                val ssotResult = addressMapper.mapToDomain(address.id, address.addressData)
                if (ssotResult is Result.Success) {
                    localDataSource.saveAddress(userId, ssotResult.data)
                }
                Unit
            }
            is Result.Error -> {
                Log.e(TAG, "updateAddress (Data Interface): Failed to update address ${address.id} for user $userId", remoteUpdateResult.exception)
                throw remoteUpdateResult.exception
            }
            is Result.Loading -> {
                Log.w(TAG, "updateAddress (Data Interface): Remote data source returned Loading for address ${address.id}")
                throw IllegalStateException("Remote data source returned Loading unexpectedly")
            }
        }
    }

    // Domain interface method - takes domain Address and returns Unit
    override suspend fun updateAddress(address: com.autogratuity.domain.model.Address): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "updateAddress",
            entityType = "Address"
        ) {
            val userId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")

            if (address.id.isBlank()) {
                throw IllegalArgumentException("Address ID cannot be blank for update")
            }

            // Request deduplication for critical update operation
            val result = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressUpdate(userId, address.id),
                timeout = RequestTimeouts.COMPLEX_OPERATION
            ) {
                executeUpdateAddressOperation(address, userId)
            }
            result ?: Unit
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeUpdateAddressOperation(address: com.autogratuity.domain.model.Address, userId: String) {
        try {
            // 1. Map SSoT Address to full AddressDto
            val mapToDtoResult = addressMapper.mapToDto(address.copy(userId = userId)) // Ensure userId consistency if mapper/DTO needs it

        val addressDto = when (mapToDtoResult) {
            is Result.Success -> mapToDtoResult.data
            is Result.Error -> {
                Log.e(TAG, "updateAddress: Failed to map SSoT to AddressDto for user $userId, address ${address.id}", mapToDtoResult.exception)
                throw mapToDtoResult.exception
            }
            is Result.Loading -> {
                Log.w(TAG, "updateAddress: addressMapper.mapToDto returned Loading for user $userId, address ${address.id}. This is unexpected here.")
                throw IllegalStateException("addressMapper.mapToDto returned Loading unexpectedly")
            }
            else -> throw Exception("Unknown state from addressMapper.mapToDto")
        }

        // 2. Call RemoteDataSource to update the address
        Log.d(TAG, "updateAddress: Updating address ${address.id} in remote for user $userId")
        val remoteUpdateResult = remoteDataSource.updateAddress(userId, addressDto)

        when (remoteUpdateResult) {
            is Result.Success -> {
                Log.i(TAG, "updateAddress: Address ${address.id} updated remotely for user $userId")
                // 3. Save the updated SSoT address to local cache
                // The input `address` is the new desired state.
                val localSaveResult = localDataSource.saveAddress(userId, address.copy(userId = userId))
                if (localSaveResult is Result.Error) {
                    Log.w(TAG, "updateAddress: Failed to update address ${address.id} in local cache for user $userId", localSaveResult.exception)
                    // Log error but proceed with returning success for the update operation itself.
                }
                Unit
            }
            is Result.Error -> {
                Log.e(TAG, "updateAddress: Remote data source failed to update address ${address.id} for user $userId", remoteUpdateResult.exception)
                throw remoteUpdateResult.exception // Propagate the remote error
            }
            is Result.Loading -> {
                Log.w(TAG, "updateAddress: remoteDataSource.updateAddress returned Loading for user $userId, address ${address.id}. This is unexpected here.")
                throw IllegalStateException("remoteDataSource.updateAddress returned Loading unexpectedly")
            }
            else -> throw Exception("Unknown state from remoteDataSource.updateAddress")
        }

        } catch (e: Exception) {
            Log.e(TAG, "updateAddress: Unexpected error for address ${address.id}, user $userId", e)
            throw e // Re-throw for RepositoryErrorHandler to handle
        }
    }

    override suspend fun deleteAddress(addressId: String): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "deleteAddress",
            entityType = "Address"
        ) {
            val userId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")

            if (addressId.isBlank()) {
                throw IllegalArgumentException("Address ID cannot be blank for delete")
            }

            // Request deduplication for critical delete operation
            val result = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressDelete(userId, addressId),
                timeout = RequestTimeouts.COMPLEX_OPERATION
            ) {
                executeDeleteAddressOperation(addressId, userId)
            }
            result ?: Unit
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeDeleteAddressOperation(addressId: String, userId: String) {
        try {
            // 1. Call RemoteDataSource to delete the address
            Log.d(TAG, "deleteAddress: Deleting address $addressId from remote for user $userId")
            val remoteDeleteResult = remoteDataSource.deleteAddress(userId, addressId)

        when (remoteDeleteResult) {
            is Result.Success -> {
                Log.i(TAG, "deleteAddress: Address $addressId deleted remotely for user $userId")
                // 2. Delete address from local cache
                val localDeleteResult = localDataSource.deleteAddress(userId, addressId)
                if (localDeleteResult is Result.Error) {
                    Log.w(TAG, "deleteAddress: Failed to delete address $addressId from local cache for user $userId", localDeleteResult.exception)
                    // Log error but proceed with returning success for the delete operation itself.
                }
                Unit
            }
            is Result.Error -> {
                Log.e(TAG, "deleteAddress: Remote data source failed to delete address $addressId for user $userId", remoteDeleteResult.exception)
                // If remote failed, no need to attempt local delete of something that might not have been deleted remotely.
                throw remoteDeleteResult.exception // Propagate the remote error
            }
            is Result.Loading -> {
                Log.w(TAG, "deleteAddress: remoteDataSource.deleteAddress returned Loading for user $userId, address $addressId. This is unexpected here.")
                throw IllegalStateException("remoteDataSource.deleteAddress returned Loading unexpectedly")
            }
            else -> throw Exception("Unknown state from remoteDataSource.deleteAddress")
        }

        } catch (e: Exception) {
            Log.e(TAG, "deleteAddress: Unexpected error for address $addressId, user $userId", e)
            throw e // Re-throw for RepositoryErrorHandler to handle
        }
    }

    override suspend fun updateAddressFlags(
        addressId: String,
        flagsToUpdate: Map<String, Any>
    ): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "updateAddressFlags",
            entityType = "Address"
        ) {
            val userId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")

            if (addressId.isBlank()) {
                throw IllegalArgumentException("Address ID cannot be blank for flag update")
            }

            // Request deduplication for flag update operations
            val result = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressFlagsUpdate(userId, addressId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                executeUpdateAddressFlagsOperation(addressId, flagsToUpdate, userId)
            }
            result ?: Unit
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeUpdateAddressFlagsOperation(addressId: String, flagsToUpdate: Map<String, Any>, userId: String) {
        Log.d(TAG, "updateAddressFlags: Updating flags for address $addressId, user $userId")

        // Call RemoteDataSource to update the flags
        val remoteUpdateResult = remoteDataSource.updateAddressFields(userId, addressId, flagsToUpdate)

        when (remoteUpdateResult) {
            is Result.Success -> {
                Log.d(TAG, "updateAddressFlags: Flags updated successfully for address $addressId")

                // Update local cache by fetching the updated address
                val currentAddressResult = getAddressById(addressId)
                if (currentAddressResult is Result.Success && currentAddressResult.data != null) {
                    // The getAddressById call will automatically update the local cache
                    Log.d(TAG, "updateAddressFlags: Local cache refreshed for address $addressId")
                } else {
                    Log.w(TAG, "updateAddressFlags: Failed to refresh local cache for address $addressId after flag update")
                }
                Unit
            }
            is Result.Error -> {
                Log.e(TAG, "updateAddressFlags: Remote update failed for address $addressId", remoteUpdateResult.exception)
                throw remoteUpdateResult.exception
            }
            is Result.Loading -> {
                Log.w(TAG, "updateAddressFlags: Remote data source returned Loading unexpectedly for address $addressId")
                throw IllegalStateException("Remote data source returned Loading unexpectedly")
            }
        }
    }

    // --- Other AddressRepository specific methods ---

    // DTO-based findAddressByDetails(addressData: AddressData) removed.
    // Will be replaced by SSoT version: findAddressByDetails(addressDetails: com.autogratuity.domain.model.AddressComponents): Result<com.autogratuity.domain.model.Address?>

    // ✅ IMPLEMENTED: SSoT version of findAddressByNormalizedAddress
    override suspend fun findAddressByNormalizedAddress(normalizedAddress: String): Result<com.autogratuity.domain.model.Address?> = withContext(ioDispatcher) {
        val handlerResult = repositoryErrorHandler.handleSuspendFunction<com.autogratuity.domain.model.Address?>(
            operationName = "findAddressByNormalizedAddress",
            entityType = "Address"
        ) {
            val userId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")

            if (normalizedAddress.isBlank()) {
                throw IllegalArgumentException("Normalized address cannot be blank")
            }

            // Request deduplication for address search operations
            val result = requestDeduplicationManager.deduplicateRequest(
                key = "users/$userId/addresses/search/${normalizedAddress.hashCode()}", // Custom key for search operations
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                executeFindAddressByNormalizedAddressOperation(normalizedAddress, userId)
            }
            result
        }

        return@withContext when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull())
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error from repositoryErrorHandler in findAddressByNormalizedAddress", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeFindAddressByNormalizedAddressOperation(normalizedAddress: String, userId: String): com.autogratuity.domain.model.Address? {
        Log.d(TAG, "findAddressByNormalizedAddress (SSoT) - Searching for normalized address: $normalizedAddress for user $userId")

        // 1. Try local data source first
        val localResult = localDataSource.findAddressByNormalizedAddress(userId, normalizedAddress)
        if (localResult is Result.Success && localResult.data != null) {
            Log.d(TAG, "findAddressByNormalizedAddress: Found address in local cache for user $userId")
            return localResult.data // Return DomainAddress?
        }
        if (localResult is Result.Error) {
            Log.w(TAG, "findAddressByNormalizedAddress: Local data source failed for user $userId. Error: ${localResult.exception.message}")
            // Continue to remote, but log this error.
        }

        // 2. Try remote data source
        Log.d(TAG, "findAddressByNormalizedAddress: Checking remote for normalized address: $normalizedAddress, user $userId")
        val remoteResultDto = remoteDataSource.findAddressByNormalizedAddress(userId, normalizedAddress)

        when (remoteResultDto) {
            is Result.Success -> {
                val addressDto = remoteResultDto.data
                if (addressDto != null) {
                    val ssotResult = addressMapper.mapToDomain(addressDto.id, addressDto.addressData)
                    when (ssotResult) {
                        is Result.Success -> {
                            val ssotAddress = ssotResult.data
                            localDataSource.saveAddress(userId, ssotAddress)
                            Log.d(TAG, "findAddressByNormalizedAddress: Found address in remote and updated cache for user $userId")
                            return ssotAddress // Return DomainAddress
                        }
                        is Result.Error -> {
                            Log.e(TAG, "findAddressByNormalizedAddress: Failed to map DTO to SSoT for user $userId", ssotResult.exception)
                            throw ssotResult.exception
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "findAddressByNormalizedAddress: SSoT mapping returned Loading for user $userId. This is unexpected here.")
                            throw IllegalStateException("SSoT mapping returned Loading unexpectedly")
                        }
                    }
                } else {
                    Log.d(TAG, "findAddressByNormalizedAddress: Address not found in remote for normalized address: $normalizedAddress, user $userId")
                    return null // Return null (DomainAddress?)
                }
            }
            is Result.Error -> {
                Log.e(TAG, "findAddressByNormalizedAddress: Remote data source failed for user $userId", remoteResultDto.exception)
                throw remoteResultDto.exception
            }
            is Result.Loading -> {
                Log.w(TAG, "findAddressByNormalizedAddress: remoteDataSource returned Loading for user $userId. This is unexpected here.")
                throw IllegalStateException("remoteDataSource.findAddressByNormalizedAddress returned Loading unexpectedly")
            }
        }
    }

    // Note: Removed conflicting data interface method for findAddressByNormalizedAddress
    // The dual interface pattern requires identical method signatures
    // This will be addressed when the data interface is updated to use SSoT models

    override suspend fun findAddressByDetails(addressData: com.autogratuity.data.model.generated_kt.Address.AddressData): com.autogratuity.data.model.generated_kt.Address? = withContext(ioDispatcher) {
        try {
            val userId = getCurrentUserIdSuspend()
            Log.d(TAG, "findAddressByDetails (Data Interface): Searching for address by details for user $userId")

            // Use the normalized address for lookup since that's the most reliable field
            val normalizedAddress = addressData.normalizedAddress
            if (normalizedAddress.isNullOrBlank()) {
                Log.w(TAG, "findAddressByDetails: Normalized address is blank, cannot search")
                return@withContext null
            }

            // Delegate to the existing findAddressByNormalizedAddress method for DTOs
            val result = remoteDataSource.findAddressByNormalizedAddress(userId, normalizedAddress)

            when (result) {
                is Result.Success -> {
                    Log.d(TAG, "findAddressByDetails: Found address by normalized address: $normalizedAddress")
                    // Return the DTO directly since this is the data interface
                    result.data
                }
                is Result.Error -> {
                    Log.e(TAG, "findAddressByDetails: Error finding address by details", result.exception)
                    null
                }
                is Result.Loading -> {
                    Log.w(TAG, "findAddressByDetails: Remote data source returned Loading unexpectedly")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "findAddressByDetails: Unexpected error", e)
            null
        }
    }

    // DTO-based updateAddressFlags(addressId: String, flagsToUpdate: Map<String, Any>) removed.
    // Will be replaced by SSoT version: updateAddressFlags(addressId: String, flags: com.autogratuity.domain.model.Flags): Result<Unit>

    override suspend fun setAddressFavorite(addressId: String, isFavorite: Boolean): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = authManager.getCurrentUserId() ?: return@withContext Result.Error(Exception("User not authenticated"))
            Log.d(TAG, "setAddressFavorite (SSoT) - Setting favorite=$isFavorite for address $addressId, user $userId")

            val fieldPath = "addressData.flags.isFavorite"
            val updateFields: Map<String, Any> = mapOf(fieldPath to isFavorite)

            val remoteUpdateResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressFavoriteUpdate(userId, addressId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.updateAddressFields(userId, addressId, updateFields)
            } ?: return@withContext Result.Error(Exception("Request deduplication timeout for setAddressFavorite"))

            if (remoteUpdateResult is Result.Error) {
                Log.e(TAG, "setAddressFavorite (SSoT) - Remote update failed for address $addressId, user $userId", remoteUpdateResult.exception)
                return@withContext remoteUpdateResult
            }

            Log.d(TAG, "setAddressFavorite (SSoT) - Remote update successful for $addressId. Updating local SSoT cache.")
            val currentSsotAddressResult = getAddressById(addressId)

            return@withContext when (currentSsotAddressResult) {
                is Result.Success -> {
                    val currentSsotAddress = currentSsotAddressResult.data
                    if (currentSsotAddress != null) {
                        val updatedSsotAddress = currentSsotAddress.copy(
                            flags = currentSsotAddress.flags?.copy(isFavorite = isFavorite)
                                ?: com.autogratuity.domain.model.Flags(isFavorite = isFavorite) // Use SSoT Flags
                        )
                        val localSaveResult = localDataSource.saveAddress(userId, updatedSsotAddress)
                        if (localSaveResult is Result.Error) {
                            Log.w(TAG, "setAddressFavorite (SSoT) - Local cache update failed for $addressId after remote success.", localSaveResult.exception)
                        }
                        Result.Success(Unit)
                    } else {
                        Log.e(TAG, "setAddressFavorite (SSoT) - Address $addressId not found after remote update for cache refresh.")
                        Result.Error(Exception("Address $addressId not found after remote update"))
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "setAddressFavorite (SSoT) - Failed to fetch address $addressId for cache update after remote success.", currentSsotAddressResult.exception)
                    Result.Error(Exception("Failed to refresh local cache for $addressId after remote update: ${currentSsotAddressResult.exception.message}"))
                }
                is Result.Loading -> { // Added Loading case for exhaustiveness
                    Log.w(TAG, "setAddressFavorite (SSoT) - Unexpected Loading state when fetching address $addressId for cache update.")
                    Result.Error(IllegalStateException("Unexpected Loading state for $addressId during cache update"))
                }
            }
        } catch (e: Exception) {
            val userId = getCurrentUserIdSuspend()
            Log.e(TAG, "setAddressFavorite (SSoT) - Unexpected error for address $addressId, user $userId", e)
            Result.Error(e)
        }
    }

    // DTO-based setAddressDoNotDeliver method removed.
    // Will be replaced by SSoT version: setAddressDnd(addressId: String, dndEnabled: Boolean): Result<Unit>

    override suspend fun setAddressVerified(addressId: String, isVerified: Boolean): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = authManager.getCurrentUserId() ?: return@withContext Result.Error(Exception("User not authenticated"))
            Log.d(TAG, "setAddressVerified (SSoT) - Setting verified=$isVerified for address $addressId, user $userId")

            val fieldPath = "addressData.flags.isVerified"
            val updateFields: Map<String, Any> = mapOf(fieldPath to isVerified)

            val remoteUpdateResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressVerifiedUpdate(userId, addressId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.updateAddressFields(userId, addressId, updateFields)
            } ?: return@withContext Result.Error(Exception("Request deduplication timeout for setAddressVerified"))

            if (remoteUpdateResult is Result.Error) {
                Log.e(TAG, "setAddressVerified (SSoT) - Remote update failed for address $addressId, user $userId", remoteUpdateResult.exception)
                return@withContext remoteUpdateResult
            }

            Log.d(TAG, "setAddressVerified (SSoT) - Remote update successful for $addressId. Updating local SSoT cache.")
            val currentSsotAddressResult = getAddressById(addressId)

            return@withContext when (currentSsotAddressResult) {
                is Result.Success -> {
                    val currentSsotAddress = currentSsotAddressResult.data
                    if (currentSsotAddress != null) {
                        val updatedSsotAddress = currentSsotAddress.copy(
                            flags = currentSsotAddress.flags?.copy(isVerified = isVerified)
                                ?: com.autogratuity.domain.model.Flags(isVerified = isVerified) // Use SSoT Flags
                        )
                        val localSaveResult = localDataSource.saveAddress(userId, updatedSsotAddress)
                        if (localSaveResult is Result.Error) {
                            Log.w(TAG, "setAddressVerified (SSoT) - Local cache update failed for $addressId after remote success.", localSaveResult.exception)
                        }
                        Result.Success(Unit)
                    } else {
                        Log.e(TAG, "setAddressVerified (SSoT) - Address $addressId not found after remote update for cache refresh.")
                        Result.Error(Exception("Address $addressId not found after remote update"))
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "setAddressVerified (SSoT) - Failed to fetch address $addressId for cache update after remote success.", currentSsotAddressResult.exception)
                    Result.Error(Exception("Failed to refresh local cache for $addressId after remote update: ${currentSsotAddressResult.exception.message}"))
                }
                is Result.Loading -> { // Added Loading case for exhaustiveness
                    Log.w(TAG, "setAddressVerified (SSoT) - Unexpected Loading state when fetching address $addressId for cache update.")
                    Result.Error(IllegalStateException("Unexpected Loading state for $addressId during cache update"))
                }
            }
        } catch (e: Exception) {
            val userId = getCurrentUserIdSuspend()
            Log.e(TAG, "setAddressVerified (SSoT) - Unexpected error for address $addressId, user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun setAddressAccessIssues(addressId: String, hasAccessIssues: Boolean): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = authManager.getCurrentUserId() ?: return@withContext Result.Error(Exception("User not authenticated"))
            Log.d(TAG, "setAddressAccessIssues (SSoT) - Setting hasAccessIssues=$hasAccessIssues for address $addressId, user $userId")

            val fieldPath = "addressData.flags.hasAccessIssues" // Firestore field path
            val updateFields: Map<String, Any> = mapOf(fieldPath to hasAccessIssues)

            val remoteUpdateResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressAccessIssuesUpdate(userId, addressId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.updateAddressFields(userId, addressId, updateFields)
            } ?: return@withContext Result.Error(Exception("Request deduplication timeout for setAddressAccessIssues"))

            if (remoteUpdateResult is Result.Error) {
                Log.e(TAG, "setAddressAccessIssues (SSoT) - Remote update failed for address $addressId, user $userId", remoteUpdateResult.exception)
                return@withContext remoteUpdateResult
            }

            Log.d(TAG, "setAddressAccessIssues (SSoT) - Remote update successful for $addressId. Updating local SSoT cache.")
            // Fetch the updated SSoT model to ensure local cache consistency
            val currentSsotAddressResult = getAddressById(addressId) // This is our SSoT compliant getAddressById

            return@withContext when (currentSsotAddressResult) {
                is Result.Success -> {
                    val currentSsotAddress = currentSsotAddressResult.data
                    if (currentSsotAddress != null) {
                        val updatedSsotAddress = currentSsotAddress.copy(
                            flags = currentSsotAddress.flags?.copy(hasAccessIssues = hasAccessIssues)
                                ?: com.autogratuity.domain.model.Flags(hasAccessIssues = hasAccessIssues) // Use SSoT Flags
                        )
                        val localSaveResult = localDataSource.saveAddress(userId, updatedSsotAddress)
                        if (localSaveResult is Result.Error) {
                            Log.w(TAG, "setAddressAccessIssues (SSoT) - Local cache update failed for $addressId after remote success.", localSaveResult.exception)
                            // Still return Success for the main operation as remote was successful
                        }
                        Result.Success(Unit)
                    } else {
                        Log.e(TAG, "setAddressAccessIssues (SSoT) - Address $addressId not found after remote update for cache refresh.")
                        Result.Error(Exception("Address $addressId not found after remote update to refresh cache"))
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "setAddressAccessIssues (SSoT) - Failed to fetch address $addressId for cache update after remote success.", currentSsotAddressResult.exception)
                    // Return an error indicating cache refresh failure but remote was likely okay
                    Result.Error(Exception("Failed to refresh local cache for $addressId after remote update: ${currentSsotAddressResult.exception.message}"))
                }
                is Result.Loading -> { // Added Loading case for exhaustiveness
                    Log.w(TAG, "setAddressAccessIssues (SSoT) - Unexpected Loading state when fetching address $addressId for cache update.")
                    Result.Error(IllegalStateException("Unexpected Loading state for $addressId during cache update"))
                }
            }
        } catch (e: Exception) {
            val userId = getCurrentUserIdSuspend()
            Log.e(TAG, "setAddressAccessIssues (SSoT) - Unexpected error for address $addressId, user $userId", e)
            Result.Error(e) // Catch-all
        }
    }

    // This method's signature in AddressRepository interface is: suspend fun updateAddressNotes(addressId: String, notes: String?)
    // Modernized: Aligned with AddressRepository interface returning Result<Unit>
    override suspend fun updateAddressNotes(addressId: String, notes: String?): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = authManager.getCurrentUserId() ?: return@withContext Result.Error(Exception("User not authenticated"))
            Log.d(TAG, "updateAddressNotes (SSoT) - Updating notes for address $addressId, user $userId")

            val fieldPath = "addressData.notes" // Firestore field path
            val updateFields = mutableMapOf<String, Any>()
            if (notes != null) {
                updateFields[fieldPath] = notes
            } else {
                // To delete the field, Firestore typically requires FieldValue.delete().
                // Since updateAddressFields takes Map<String, Any>, we cannot pass FieldValue.delete() directly.
                // This means passing null to updateAddressNotes will result in the notes field NOT being part of this update call.
                // If AddressRemoteDataSourceImpl has special handling for a specific value to mean delete, that should be used.
                // Otherwise, a separate method or an update to updateAddressFields signature is needed for explicit deletion.
                // For now, if notes is null, this map might be empty or only contain other fields if this method were to update more.
                // If the update call proceeds with an empty map for `updateFields` and `remoteDataSource.updateAddressFields`
                // attempts to update with an empty map, Firestore might throw an error or do nothing.
                // It's safer to only call update if there's something to update or ensure remote handles empty map gracefully.
                // However, the current structure calls updateAddressFields regardless.
                // If notes is the *only* field potentially updated here, and it's null, updateFields will be empty.
            }

            val remoteUpdateResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressNotesUpdate(userId, addressId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.updateAddressFields(userId, addressId, updateFields.toMap())
            } ?: return@withContext Result.Error(Exception("Request deduplication timeout for updateAddressNotes"))

            if (remoteUpdateResult is Result.Error) {
                Log.e(TAG, "updateAddressNotes (SSoT) - Remote update failed for address $addressId, user $userId", remoteUpdateResult.exception)
                return@withContext remoteUpdateResult
            }

            Log.d(TAG, "updateAddressNotes (SSoT) - Remote update successful for $addressId. Updating local SSoT cache.")
            // Fetch the updated SSoT model to ensure local cache consistency
            val currentSsotAddressResult = getAddressById(addressId) // SSoT compliant getAddressById

            return@withContext when (currentSsotAddressResult) {
                is Result.Success -> {
                    val currentSsotAddress = currentSsotAddressResult.data
                    if (currentSsotAddress != null) {
                        // Update the notes field in the SSoT model
                        val updatedSsotAddress = currentSsotAddress.copy(notes = notes)
                        val localSaveResult = localDataSource.saveAddress(userId, updatedSsotAddress)
                        if (localSaveResult is Result.Error) {
                            Log.w(TAG, "updateAddressNotes (SSoT) - Local cache update failed for $addressId after remote success.", localSaveResult.exception)
                            // Still return Success for the main operation as remote was successful
                        }
                        Result.Success(Unit)
                    } else {
                        Log.e(TAG, "updateAddressNotes (SSoT) - Address $addressId not found after remote update for cache refresh.")
                        Result.Error(Exception("Address $addressId not found after remote update to refresh cache"))
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "updateAddressNotes (SSoT) - Failed to fetch address $addressId for cache update after remote success.", currentSsotAddressResult.exception)
                    Result.Error(Exception("Failed to refresh local cache for $addressId after remote update: ${currentSsotAddressResult.exception.message}"))
                }
                is Result.Loading -> { // Added Loading case for exhaustiveness
                    Log.w(TAG, "updateAddressNotes (SSoT) - Unexpected Loading state when fetching address $addressId for cache update.")
                    Result.Error(IllegalStateException("Unexpected Loading state for $addressId during cache update"))
                }
            }
        } catch (e: Exception) {
            val userId = getCurrentUserIdSuspend()
            Log.e(TAG, "updateAddressNotes (SSoT) - Unexpected error for address $addressId, user $userId", e)
            Result.Error(e) // Catch-all
        }
    }

    override suspend fun updateAddressDeliveryStats(
        addressId: String,
        deliveryStats: AddressDeliveryStatsDto
    ) {
        TODO("Not yet implemented")
    }

    /**
     * Normalizes an address string for consistent lookup and comparison.
     * This removes common variations in formatting, punctuation, and abbreviations.
     *
     * @param addressString The address string to normalize
     * @return The normalized address string
     */
    override fun normalizeAddress(addressString: String): String {
        if (addressString.isBlank()) return ""

        return addressString
            .lowercase(Locale.getDefault()) // Convert to lowercase
            .trim() // Remove leading/trailing whitespace
            .replace("\\s+".toRegex(), " ") // Normalize spaces (collapse multiple spaces)
            .replace("[,.;:#/\\-_]".toRegex(), " ") // Replace punctuation with spaces
            .replace("\\s+".toRegex(), " ") // Collapse multiple spaces again
            // Replace common abbreviations
            .replace(" street| str| st ", " st ")
            .replace(" road| rd ", " rd ")
            .replace(" avenue| ave ", " ave ")
            .replace(" boulevard| blvd ", " blvd ")
            .replace(" drive| dr ", " dr ")
            .replace(" lane| ln ", " ln ")
            .replace(" court| ct ", " ct ")
            .replace(" circle| cir ", " cir ")
            .replace(" apartment| apt ", " apt ")
            .replace(" suite| ste ", " ste ")
            .replace(" north| n ", " n ")
            .replace(" south| s ", " s ")
            .replace(" east| e ", " e ")
            .replace(" west| w ", " w ")
            .replace(" northeast| ne ", " ne ")
            .replace(" northwest| nw ", " nw ")
            .replace(" southeast| se ", " se ")
            .replace(" southwest| sw ", " sw ")
            .trim() // Final trim
    }

    /**
     * Parses an address string into components (street, city, state, etc.)
     * Implements both domain and data interface requirements.
     *
     * @param addressString The full address string to parse
     * @return AddressComponents DTO (satisfies data interface requirement)
     */
    override fun parseAddressComponents(addressString: String): com.autogratuity.domain.model.AddressComponents {
        Log.d(TAG, "parseAddressComponents - Parsing address: $addressString")

        val componentsMap = mutableMapOf<String, String?>()

        try {
            val parts = addressString.split(",").map { it.trim() }

            if (parts.size >= 3) {
                // Format: "123 Main St, Anytown, CA 12345"
                val streetAddress = parts[0]
                val city = parts[1]
                val stateZip = parts[2].split(" ").filter { it.isNotBlank() }

                componentsMap["streetAddress1"] = streetAddress
                componentsMap["city"] = city

                if (stateZip.isNotEmpty()) {
                    componentsMap["state"] = stateZip[0]
                }

                if (stateZip.size >= 2) {
                    componentsMap["postalCode"] = stateZip[1]
                }
            } else if (parts.size == 2) {
                // Format: "123 Main St, Any-town CA 12345"
                componentsMap["streetAddress1"] = parts[0]

                val cityStateZip = parts[1].split(" ").filter { it.isNotBlank() }
                if (cityStateZip.isNotEmpty()) {
                    componentsMap["city"] = cityStateZip[0]
                }

                if (cityStateZip.size >= 2) {
                    componentsMap["state"] = cityStateZip[1]
                }

                if (cityStateZip.size >= 3) {
                    componentsMap["postalCode"] = cityStateZip[2]
                }
            } else {
                // If we can't parse properly, just use the whole string as street address
                componentsMap["streetAddress1"] = addressString
            }
        } catch (e: Exception) {
            Log.e(TAG, "parseAddressComponents - Error parsing address", e)
            // On error, just use the whole string as street address
            componentsMap["streetAddress1"] = addressString
        }

        // Convert the map to SSoT AddressComponents (domain model)
        return com.autogratuity.domain.model.AddressComponents(
            streetNumber = componentsMap["streetNumber"],
            streetName = componentsMap["streetAddress1"],
            city = componentsMap["city"],
            state = componentsMap["state"],
            postalCode = componentsMap["postalCode"],
            country = componentsMap["country"]
        )
    }

    override suspend fun createAddressFromPlace(
        place: Place,
        userId: String
    ): Result<AddressDto> {
        TODO("Not yet implemented")
    }

    override suspend fun updateAddressFromPlace(
        addressId: String,
        place: Place
    ): Result<Unit> = withContext(ioDispatcher) {
        Log.d(TAG, "updateAddressFromPlace (Data Interface): Updating address $addressId from place")

        try {
            val userId = getCurrentUserIdSuspend()

            // Delegate to PlaceToAddressService which handles the Place conversion logic
            val updateResult = placeToAddressService.updateAddressDetailsFromPlace(userId, addressId, place)

            when (updateResult) {
                is Result.Success -> {
                    Log.d(TAG, "updateAddressFromPlace (Data Interface): Successfully updated address $addressId")
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    Log.e(TAG, "updateAddressFromPlace (Data Interface): Failed to update address $addressId", updateResult.exception)
                    Result.Error(updateResult.exception)
                }
                is Result.Loading -> {
                    Log.w(TAG, "updateAddressFromPlace (Data Interface): PlaceToAddressService returned Loading unexpectedly")
                    Result.Error(IllegalStateException("PlaceToAddressService returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "updateAddressFromPlace (Data Interface): Error updating address $addressId from place", e)
            Result.Error(e)
        }
    }

    override fun findOrCreateAddressTransactional(
        addressData: AddressDataDto,
        userId: String,
        transaction: Transaction?
    ): String {
        TODO("Not yet implemented")
    }

    /**
     * Geocodes an address to obtain geographic coordinates.
     * In this implementation, we're not making actual geocoding API calls
     * but simulating the behavior for consistency with the interface.
     *
     * @param address The address to geocode
     * @return The address with coordinates populated
     */
    override suspend fun geocodeAddress(address: com.autogratuity.domain.model.Address): com.autogratuity.domain.model.Address = withContext(ioDispatcher) {
        // Accessing SSoT Address properties directly
        val streetAddress = address.components?.streetName ?: "" // Corrected: access via components
        val city = address.components?.city ?: "" // Corrected: access via components
        Log.d(TAG, "geocodeAddress (SSoT) - Geocoding address: $streetAddress, $city")

        // Check if the SSoT address already has coordinates
        if (address.coordinates?.latitude != null && address.coordinates.longitude != null) {
            Log.d(TAG, "geocodeAddress (SSoT) - SSoT Address already has coordinates, returning as-is")
            return@withContext address
        }

        // In a real implementation, you would call a geocoding service here
        // and then update the coordinates of the SSoT model.
        // For example: val newCoords = geocodingService.geocode(streetAddress, city)
        // if (newCoords != null) {
        //     return@withContext address.copy(coordinates = com.autogratuity.data.model.generated_kt.Coordinates(newCoords.lat, newCoords.lon))
        // }
        Log.w(TAG, "geocodeAddress (SSoT) - Real geocoding service not implemented. This would call Google Maps or other geocoding API.")

        // Since it's a placeholder, return the original SSoT address
        return@withContext address
    }

    /**
     * Sets an address as the default address for the current user.
     *
     * @param addressId The ID of the address to set as default
     */


    /**
     * Gets the default address for the current user.
     *
     * @return The default address
     * @throws NoSuchElementException if no default address is found
     */
    override suspend fun getDefaultAddress(): Result<com.autogratuity.domain.model.Address?> = withContext(ioDispatcher) {
        Log.d(TAG, "getDefaultAddress (SSoT) - Fetching default address")
        try {
            val defaultAddressId = try {
                val result = preferenceRepository.value.getDefaultAddressId()
                when (result) {
                    is Result.Success -> result.data
                    is Result.Error -> {
                        Log.w(TAG, "getDefaultAddress (SSoT) - Could not get default address ID from preferences.", result.exception)
                        null
                    }
                    is Result.Loading -> {
                        Log.w(TAG, "getDefaultAddress (SSoT) - Unexpected Loading state from getDefaultAddressId")
                        null
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "getDefaultAddress (SSoT) - Could not get default address ID from preferences.", e)
                null // Treat as no default ID set
            }

            if (defaultAddressId.isNullOrBlank()) {
                Log.d(TAG, "getDefaultAddress (SSoT) - No default address ID found in preferences.")
                return@withContext Result.Success(null)
            }

            Log.d(TAG, "getDefaultAddress (SSoT) - Default address ID from preferences: $defaultAddressId. Fetching SSoT Address.")
            // Use the SSoT-compliant getAddressById, which handles local/remote and mapping
            val addressResult = getAddressById(defaultAddressId)

            return@withContext when (addressResult) {
                is Result.Success -> {
                    if (addressResult.data != null) {
                        // Check if the fetched address is actually marked as default in its data (optional, but good for consistency)
                        // For now, if an ID is set in prefs, we trust it refers to the intended default.
                        Log.d(TAG, "getDefaultAddress (SSoT) - Successfully fetched SSoT Address for ID: $defaultAddressId")
                        Result.Success(addressResult.data)
                    } else {
                        Log.w(TAG, "getDefaultAddress (SSoT) - Address for default ID $defaultAddressId not found. Clearing from prefs.")
                        // If the address for the stored ID doesn't exist, clear the preference
                        try {
                            val clearResult = preferenceRepository.value.setDefaultAddressId("") // Set to empty string to clear
                            if (clearResult is Result.Error) {
                                Log.e(TAG, "getDefaultAddress (SSoT) - Failed to clear stale default address ID $defaultAddressId from prefs", clearResult.exception)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "getDefaultAddress (SSoT) - Failed to clear stale default address ID $defaultAddressId from prefs", e)
                        }
                        Result.Success<com.autogratuity.domain.model.Address?>(null) // Ensure type if null
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "getDefaultAddress (SSoT) - Error fetching address for default ID $defaultAddressId", addressResult.exception)
                    Result.Error(addressResult.exception) // Propagate the error
                }
                is Result.Loading -> { // Ensure Loading case for exhaustiveness
                    Log.w(TAG, "getDefaultAddress (SSoT) - Unexpected Loading state for address ID $defaultAddressId")
                    Result.Error(IllegalStateException("getDefaultAddress returned Loading unexpectedly for ID $defaultAddressId"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getDefaultAddress (SSoT) - Unexpected error", e)
            Result.Error(e)
        }
    }

    /**
     * Sets an address as the default address for the current user.
     *
     * @param addressId The ID of the address to set as default
     */
    override suspend fun setDefaultAddress(addressId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = authManager.getCurrentUserId() ?: return@withContext Result.Error(Exception("User not authenticated"))

            if (addressId.isBlank()) {
                return@withContext Result.Error(IllegalArgumentException("New default Address ID cannot be blank"))
            }

            // 1. Get the current default address ID from preferences
            val oldDefaultId = try {
                val result = preferenceRepository.value.getDefaultAddressId()
                when (result) {
                    is Result.Success -> result.data
                    is Result.Error -> {
                        Log.w(TAG, "setDefaultAddress: Could not get old default address ID from preferences for user $userId", result.exception)
                        null
                    }
                    is Result.Loading -> {
                        Log.w(TAG, "setDefaultAddress: Unexpected Loading state from getDefaultAddressId")
                        null
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "setDefaultAddress: Could not get old default address ID from preferences for user $userId", e)
                null // Proceed as if no old default was set
            }

            // 2. Unset the old default address in remote and local, if applicable
            if (!oldDefaultId.isNullOrBlank() && oldDefaultId != addressId) {
                Log.d(TAG, "setDefaultAddress: Unsetting old default address $oldDefaultId for user $userId")
                val unsetOldDefaultRemoteResult = requestDeduplicationManager.deduplicateRequest(
                    key = RequestKeys.addressSetDefault(userId, oldDefaultId),
                    timeout = RequestTimeouts.STANDARD_OPERATION
                ) {
                    remoteDataSource.updateAddressFields(userId, oldDefaultId, mapOf<String, Any>("addressData.isDefault" to false))
                } ?: Result.Error(Exception("Request deduplication timeout for unset old default"))
                if (unsetOldDefaultRemoteResult is Result.Error) {
                    Log.e(TAG, "setDefaultAddress: Failed to unset old default address $oldDefaultId in remote for user $userId", unsetOldDefaultRemoteResult.exception)
                    // Don't fail the whole operation yet, but log it. The new default setting is more critical.
                    // Alternatively, could return this error if unsetting the old is mandatory for consistency.
                }
                // Invalidate local cache for the old default. A specific update is better if available.
                // For now, fetching it after remote update and re-saving is an option.
                val oldAddressDtoResult = requestDeduplicationManager.deduplicateRequest(
                    key = RequestKeys.addressById(userId, oldDefaultId),
                    timeout = RequestTimeouts.STANDARD_OPERATION
                ) {
                    remoteDataSource.getAddressById(userId, oldDefaultId)
                } ?: Result.Error(Exception("Request deduplication timeout for old default address fetch"))
                if (oldAddressDtoResult is Result.Success && oldAddressDtoResult.data != null) {
                    val oldSsotResult = addressMapper.mapToDomain(oldAddressDtoResult.data.id, oldAddressDtoResult.data.addressData)
                    if (oldSsotResult is Result.Success) {
                        localDataSource.saveAddress(userId, oldSsotResult.data)
                    } // else log mapping error for cache update
                } // else log error fetching old default for cache update
            }

            // 3. Set the new address as default in remote
            Log.d(TAG, "setDefaultAddress: Setting new default address $addressId for user $userId")
            val setNewDefaultRemoteResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressSetDefault(userId, addressId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.updateAddressFields(userId, addressId, mapOf<String, Any>("addressData.isDefault" to true))
            } ?: return@withContext Result.Error(Exception("Request deduplication timeout for set new default"))

            return@withContext when (setNewDefaultRemoteResult) {
                is Result.Success -> {
                    Log.i(TAG, "setDefaultAddress: New default address $addressId set successfully in remote for user $userId")
                    // 4. Update preferences
                    try {
                        val prefResult = preferenceRepository.value.setDefaultAddressId(addressId)
                        if (prefResult is Result.Error) {
                            Log.e(TAG, "setDefaultAddress: Failed to update preferences for new default address $addressId, user $userId", prefResult.exception)
                            // This is a significant issue if it fails, as local state becomes inconsistent with remote.
                            // Consider how to handle this - potentially try to revert remote change or flag for sync.
                            // For now, the remote operation succeeded, so we return success for the core task.
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "setDefaultAddress: Failed to update preferences for new default address $addressId, user $userId", e)
                        // This is a significant issue if it fails, as local state becomes inconsistent with remote.
                        // Consider how to handle this - potentially try to revert remote change or flag for sync.
                        // For now, the remote operation succeeded, so we return success for the core task.
                    }

                    // 5. Update local cache for the new default address
                    val newAddressDtoResult = requestDeduplicationManager.deduplicateRequest(
                        key = RequestKeys.addressById(userId, addressId),
                        timeout = RequestTimeouts.STANDARD_OPERATION
                    ) {
                        remoteDataSource.getAddressById(userId, addressId)
                    } ?: Result.Error(Exception("Request deduplication timeout for new default address fetch"))
                    if (newAddressDtoResult is Result.Success && newAddressDtoResult.data != null) {
                        val newSsotResult = addressMapper.mapToDomain(newAddressDtoResult.data.id, newAddressDtoResult.data.addressData)
                        if (newSsotResult is Result.Success) {
                            localDataSource.saveAddress(userId, newSsotResult.data)
                        } // else log mapping error for cache update
                    } // else log error fetching new default for cache update

            Result.Success(Unit)
                }
                is Result.Error -> {
                    Log.e(TAG, "setDefaultAddress: Failed to set new default address $addressId in remote for user $userId", setNewDefaultRemoteResult.exception)
                    setNewDefaultRemoteResult // Propagate the error
                }
                is Result.Loading -> {
                    Log.w(TAG, "setDefaultAddress: remoteDataSource.updateAddress returned Loading for user $userId, address $addressId. This is unexpected here.")
                    Result.Error(IllegalStateException("remoteDataSource.updateAddress returned Loading unexpectedly"))
                }
                else -> Result.Error(Exception("Unknown error setting new default address $addressId in remote"))
            }

        } catch (e: Exception) {
            Log.e(TAG, "setDefaultAddress: Unexpected error for address $addressId, user", e)
            Result.Error(e)
        }
    }

    /**
     * Imports a list of addresses from a data source (e.g., CSV).
     *
     * @param addresses List of maps where each map represents an address with appropriate types
     * @return The number of addresses successfully imported
     */
    override suspend fun importAddresses(addresses: List<Map<String, Any>>): Result<Int> = withContext(ioDispatcher) {
        val userId = try {
            authManager.getCurrentUserId() ?: return@withContext Result.Error(Exception("User not authenticated for importAddresses"))
        } catch (e: IllegalStateException) {
            return@withContext Result.Error(e)
        }

        Log.d(TAG, "importAddresses (SSoT) - Importing ${addresses.size} addresses for user $userId")
        var successCount = 0
        val importErrors = mutableListOf<String>()

        addresses.forEach { addressMap ->
            try {
                // 1. Convert map to SSoT com.autogratuity.domain.model.Address
                // This is a simplified conversion. A more robust solution might involve a dedicated mapper or utility.
                val componentsMap = addressMap["components"].safeCastToStringMap() ?: emptyMap()
                // Ensure fullAddressString is derived correctly, prefer specific field if available
                val fullAddressFromMap = addressMap.getStringValueSafely("fullAddress")
                val streetFromComponents = componentsMap.getStringValueSafely("streetAddress1") ?: componentsMap.getStringValueSafely("streetName")
                val cityFromComponents = componentsMap.getStringValueSafely("city")
                val constructedFullAddress = listOfNotNull(streetFromComponents, cityFromComponents).joinToString(", ")

                val fullAddressString = fullAddressFromMap ?: constructedFullAddress

                val ssotComponents = com.autogratuity.domain.model.AddressComponents(
                    streetNumber = componentsMap.getStringValueSafely("streetNumber"), // Might be part of streetName
                    streetName = streetFromComponents,
                    city = cityFromComponents,
                    state = componentsMap.getStringValueSafely("state"),
                    postalCode = componentsMap.getStringValueSafely("postalCode"),
                    country = componentsMap.getStringValueSafely("country")
                )

                val coordinatesMap = addressMap["coordinates"].safeCastToStringMap() ?: emptyMap()
                val latitude = coordinatesMap.getDoubleValueSafely("latitude")
                val longitude = coordinatesMap.getDoubleValueSafely("longitude")
                val ssotCoordinates = if (latitude != null && longitude != null) {
                    com.autogratuity.domain.model.Coordinates(
                        latitude = latitude,
                        longitude = longitude
                    )
                } else null

                val flagsMap = addressMap["flags"].safeCastToStringMap() ?: emptyMap()
                // This is com.autogratuity.data.model.generated_kt.Flags (FlagsDto) - used if needed by other DTOs, but not directly for SSoT Address flags
                val dtoFlagsForOtherUses = FlagsDto( // DTO Flags, does NOT have isDefault
                    isFavorite = flagsMap.getBooleanValueSafely("isFavorite") == true,
                    isVerified = flagsMap.getBooleanValueSafely("isVerified") == true,
                    doNotDeliver = flagsMap.getBooleanValueSafely("doNotDeliver") == true,
                    dndSource = flagsMap.getStringValueSafely("dndSource"),
                    hasAccessIssues = flagsMap.getBooleanValueSafely("hasAccessIssues") == true,
                    manualDndState = flagsMap.getStringValueSafely("manualDndState")
                )

                // This is com.autogratuity.domain.model.Flags (SSoT Flags model)
                val ssotDomainFlags = com.autogratuity.domain.model.Flags( // SSoT domain Flags
                    isFavorite = flagsMap.getBooleanValueSafely("isFavorite") == true,
                    isVerified = flagsMap.getBooleanValueSafely("isVerified") == true,
                    doNotDeliver = flagsMap.getBooleanValueSafely("doNotDeliver") == true,
                    dndSource = flagsMap.getStringValueSafely("dndSource"),
                    hasAccessIssues = flagsMap.getBooleanValueSafely("hasAccessIssues") == true,
                    manualDndState = ManualDndState.fromString(flagsMap.getStringValueSafely("manualDndState"))
                    // isDefault parameter is correctly NOT here for domain.model.Flags
                )

                val now = OffsetDateTime.now()
                // Ensure metadataFromMap is treated as Map<String, Any?>
                val metadataFromMap = addressMap["metadata"].safeCastToStringMap() ?: emptyMap()
                val source = metadataFromMap.getStringValueSafely("source") ?: "manual_import"

                 val ssotMetadata = MetadataDto(
                    createdAt = parseUniversalTimestamp(metadataFromMap["createdAt"]) ?: now,
                    updatedAt = parseUniversalTimestamp(metadataFromMap["updatedAt"]) ?: now,
                    source = source,
                    version = metadataFromMap.getLongValueSafely("version") ?: 1L,
                    importId = metadataFromMap.getStringValueSafely("importId"),
                    importedAt = parseUniversalTimestamp(metadataFromMap["importedAt"]),
                    captureId = metadataFromMap.getStringValueSafely("captureId"),
                    customData = null // Temporarily set to null to isolate L653 error
                )


                val newSsotAddress = com.autogratuity.domain.model.Address(
                    id = "", // Will be generated by remoteDataSource via addAddress
                    userId = userId,
                    fullAddress = fullAddressString, // Uses fullAddress property from DTO structure
                    normalizedAddress = normalizeAddress(fullAddressString), // Use existing normalizeAddress
                    placeId = addressMap.getStringValueSafely("placeId"),
                    notes = addressMap.getStringValueSafely("notes"),
                    tags = addressMap.getListValueSafely("tags"),
                    components = ssotComponents,
                    coordinates = ssotCoordinates,
                    isDefault = addressMap.getBooleanValueSafely("isDefault") == true, // Added missing isDefault parameter
                    orderIds = addressMap.getListValueSafely("orderIds"), // Added orderIds
                    searchTerms = addressMap.getListValueSafely("searchTerms"), // Added searchTerms
                    searchFields = null, // Added missing searchFields parameter
                    deliveryStats = null, // Assuming deliveryStats are not part of initial import from map
                    flags = ssotDomainFlags, // Assign the SSoT domain Flags model
                    metadata = ssotMetadata,
                    platform = null // TODO: Map platform if available
                )

                // 2. Add the SSoT address using the SSoT-compliant repository method
                val addResult = <EMAIL>(newSsotAddress) // This is the SSoT addAddress

                if (addResult is Result.Success) {
                    Log.d(TAG, "importAddresses (SSoT) - Successfully imported SSoT address, new ID: ${addResult.data}")
                    successCount++
                } else if (addResult is Result.Error) {
                    val errorMsg = "Error importing address from map $addressMap: ${addResult.exception.message}"
                    Log.e(TAG, errorMsg, addResult.exception)
                    importErrors.add(errorMsg)
                }

            } catch (e: Exception) {
                val errorMsg = "Exception processing address map $addressMap: ${e.message}"
                Log.e(TAG, errorMsg, e)
                importErrors.add(errorMsg)
                // Continue with next address instead of failing the whole batch
            }
        }

        // Removed: addressCacheSystem.invalidateAddressesCache(userId)
        // Caching is handled by individual addAddress -> localDataSource.saveAddress calls.
        // If a full list refresh is needed, a dedicated localDataSource.clearUserAddresses(userId) would be better.

        if (importErrors.isNotEmpty()) {
            Log.w(TAG, "importAddresses (SSoT) - Completed with $successCount successes and ${importErrors.size} errors.")
            // Optionally, return a more detailed result if partial success with errors needs to be communicated
            return@withContext Result.Error(Exception("Import completed with ${importErrors.size} errors. First error: ${importErrors.first()}"))
        }

        Log.i(TAG, "importAddresses (SSoT) - Successfully imported $successCount/${addresses.size} SSoT addresses for user $userId")
        return@withContext Result.Success<Int>(successCount) // Explicit type argument for Int

    }

    /**
     * Finds an existing address based on Place ID and user ID, or creates a new one
     * if it doesn't exist.
     *
     * @param place The Place object from Places SDK.
     * @param userId The ID of the user for whom to find/create the address.
     * @return Result holding the found or newly created SSoT [com.autogratuity.domain.model.Address].
     */
    override suspend fun findOrCreateAddressFromPlace(place: Place, userId: String): Result<com.autogratuity.domain.model.Address> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "findOrCreateAddressFromPlace",
            entityType = "Address"
        ) {
            // Request deduplication for place-based address operations
            val result = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressFromPlace(userId, place.id ?: "unknown"),
                timeout = RequestTimeouts.COMPLEX_OPERATION
            ) {
                executeFindOrCreateAddressFromPlaceOperation(place, userId)
            }
            result ?: throw IllegalStateException("Request deduplication timeout for findOrCreateAddressFromPlace")
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(handlerResult.getOrNull()!!)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeFindOrCreateAddressFromPlaceOperation(place: Place, userId: String): com.autogratuity.domain.model.Address {
        Log.d(TAG, "findOrCreateAddressFromPlace (SSoT) - Looking up or creating address for Place ID: ${place.id}, User ID: $userId")

        try {
            val dtoAddressResult = placeToAddressService.findOrCreateAddressFromPlace(place, userId, userId)

        when (dtoAddressResult) {
            is Result.Success -> {
                val dtoAddress = dtoAddressResult.data // This is DTO com.autogratuity.data.model.generated_kt.Address
                // Map DTO to SSoT
                val ssotResult = addressMapper.mapToDomain(dtoAddress.id, dtoAddress.addressData)

                return when (ssotResult) {
                    is Result.Success -> {
                        val ssotAddress = ssotResult.data // This is SSoT com.autogratuity.domain.model.Address
                        // Save the SSoT address to localDataSource
                        val cacheResult = localDataSource.saveAddress(userId, ssotAddress)
                        if (cacheResult is Result.Error) {
                            Log.w(TAG, "findOrCreateAddressFromPlace (SSoT) - Failed to cache SSoT address ${ssotAddress.id}", cacheResult.exception)
                            // Proceed with success as the core operation succeeded
                        }
                        Log.d(TAG, "findOrCreateAddressFromPlace (SSoT) - Successfully found/created and mapped SSoT address: ${ssotAddress.id}")
                        ssotAddress
                    }
                    is Result.Error -> {
                        Log.e(TAG, "findOrCreateAddressFromPlace (SSoT) - Failed to map DTO address to SSoT for Place ID: ${place.id}", ssotResult.exception)
                        throw ssotResult.exception
                    }
                    is Result.Loading -> {
                        Log.w(TAG, "findOrCreateAddressFromPlace (SSoT) - SSoT mapping returned Loading for DTO (id: ${dtoAddressResult.data.id}), user $userId. This is unexpected here.")
                        throw IllegalStateException("SSoT mapping returned Loading unexpectedly for Place ID: ${place.id}")
                    }
                    // No explicit Loading case for ssotResult as mapToDomain doesn't return it, but defensive else could be added.
                    // For now, relying on ssotResult being Success or Error.
                }
            }
            is Result.Error -> {
                Log.e(TAG, "findOrCreateAddressFromPlace (SSoT) - placeToAddressService returned error for Place ID: ${place.id}", dtoAddressResult.exception)
                throw dtoAddressResult.exception // Propagate the error
            }
            is Result.Loading -> {
                Log.e(TAG, "findOrCreateAddressFromPlace (SSoT) - placeToAddressService returned Loading unexpectedly for Place ID: ${place.id}")
                throw IllegalStateException("placeToAddressService returned Loading unexpectedly for Place ID: ${place.id}")
            }
        }
        } catch (e: Exception) {
            Log.e(TAG, "findOrCreateAddressFromPlace (SSoT) - Error finding or creating address from Place ID: ${place.id}", e)
            throw e // Re-throw for RepositoryErrorHandler to handle
        }
    }

    /**
     * Gets all addresses that have valid coordinate data.
     *
     * @return Result holding a list of SSoT [com.autogratuity.domain.model.Address] with coordinates.
     */
    override suspend fun getAddressesWithCoordinates(): Result<List<com.autogratuity.domain.model.Address>> = withContext(ioDispatcher) {
        val userId = try {
            authManager.getCurrentUserId() ?: return@withContext Result.Error(Exception("User not authenticated for getAddressesWithCoordinates"))
        } catch (e: IllegalStateException) {
            return@withContext Result.Error(e)
        }
        Log.d(TAG, "getAddressesWithCoordinates (SSoT) - Fetching addresses with coordinates for user $userId")

        try {
            // 1. Fetch all DTO addresses from remoteDataSource with deduplication
            val remoteResultDtoList = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.userAddresses(userId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.getAllAddresses(userId)
            } ?: return@withContext Result.Error(Exception("Request deduplication timeout for getAddressesWithCoordinates"))

            when (remoteResultDtoList) {
                is Result.Success -> {
                    val addressDtoList = remoteResultDtoList.data
                    if (addressDtoList.isEmpty()) {
                        Log.d(TAG, "getAddressesWithCoordinates (SSoT) - No addresses found in remote for user $userId")
                        return@withContext Result.Success(emptyList())
                    }

                    // 2. Map List<AddressDto> to List<SSoTAddress>
                    val ssotAddressList = mutableListOf<com.autogratuity.domain.model.Address>()
                    var mappingErrorOccurred = false
                    for (dto in addressDtoList) {
                        val ssotResult = addressMapper.mapToDomain(dto.id, dto.addressData)
                        when (ssotResult) {
                            is Result.Success -> ssotAddressList.add(ssotResult.data)
                            is Result.Error -> {
                                Log.e(TAG, "getAddressesWithCoordinates (SSoT) - Failed to map DTO (id: ${dto.id}) to SSoT for user $userId", ssotResult.exception)
                                mappingErrorOccurred = true
                            }
                            is Result.Loading -> {
                                Log.w(TAG, "getAddressesWithCoordinates (SSoT) - SSoT mapping returned Loading for DTO (id: ${dto.id}), user $userId. This is unexpected here.")
                                mappingErrorOccurred = true // Treat as an error for this item
                            }
                        }
                    }

                    if (mappingErrorOccurred && ssotAddressList.isEmpty()) {
                        return@withContext Result.Error(Exception("Failed to map any addresses from DTO to SSoT for getAddressesWithCoordinates for user $userId"))
                    }

                    // 3. Filter SSoT addresses that have valid coordinates
                    val addressesWithCoords = ssotAddressList.filter { ssotAddr ->
                        ssotAddr.coordinates?.latitude != null && ssotAddr.coordinates.longitude != null
                    }

                    // 4. Optionally, update local cache with all fetched & mapped SSoT addresses (not just the filtered ones)
                    // This ensures the cache is populated with everything we got from remote, even if not returned by this specific function call.
                    if (ssotAddressList.isNotEmpty()) {
                        localDataSource.saveAllAddresses(userId, ssotAddressList.toList()) // Explicitly call toList() to ensure immutable List type if required
                        Log.d(TAG, "getAddressesWithCoordinates (SSoT) - Cached ${ssotAddressList.size} SSoT addresses for user $userId")
                    }

                    Log.d(TAG, "getAddressesWithCoordinates (SSoT) - Found ${addressesWithCoords.size} SSoT addresses with coordinates for user $userId")
                    return@withContext Result.Success(addressesWithCoords)
                }
                is Result.Error -> {
                    Log.e(TAG, "getAddressesWithCoordinates (SSoT) - Remote data source failed for user $userId", remoteResultDtoList.exception)
                    return@withContext remoteResultDtoList // Propagate the remote error
                }
                is Result.Loading -> {
                    Log.w(TAG, "getAddressesWithCoordinates (SSoT) - Remote data source returned Loading for user $userId. This is unexpected here.")
                    return@withContext Result.Error(IllegalStateException("Remote data source (getAllAddresses) returned Loading unexpectedly for user $userId"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getAddressesWithCoordinates (SSoT) - Unexpected error for user $userId: ${e.message}", e)
            return@withContext Result.Error(e)
        }
    }

    // Duplicate updateAddressFromPlace method removed - already implemented above at line 219

    /**
     * DEPRECATED: This method directly uses Firestore which violates clean architecture.
     * It should use RemoteDataSource instead. Commenting out to maintain architectural compliance.
     *
     * Finds an existing address matching the provided data or creates a new one
     * within the given Firestore transaction.
     *
     * @param transaction The Firestore transaction to perform operations within.
     * @param userId The ID of the user.
     * @param addressData The raw address data (components) to find or create.
     * @param addressSource A string indicating the origin of this address creation attempt (e.g., "DELIVERY_ASSOCIATION").
     * @return The document ID of the found or newly created address.
     */
    /*
    fun findOrCreateAddressTransactional(
        transaction: Transaction,
        userId: String,
        addressData: AddressDataDto,
        addressSource: String
    ): String {
        Log.d(TAG, "findOrCreateAddressTransactional - Looking up or creating address for user $userId")

        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for findOrCreateAddressTransactional")
        }

        try {
            // 1. Extract normalized address for lookup
            val addressMap = addressDataToMap(addressData) // CORRECTED: Use utility function
            val fullAddress = addressMap?.get("fullAddress") as? String ?: ""

            // Use the normalizeAddress function which doesn't require suspend
            val normalizedAddress = addressMap?.get("normalizedAddress") as? String
                ?: normalizeAddress(fullAddress)

            if (normalizedAddress.isBlank()) {
                throw IllegalArgumentException("Cannot find or create an address with a blank normalized address")
            }

            // 2. Check if there's an existing ID specified in the address data
            val existingIdFromData = addressMap?.get("id") as? String
            if (!existingIdFromData.isNullOrBlank()) {
                // Try to get the address document using the transaction
                val addressDocRef = firestore.collection("users").document(userId).collection("addresses").document(existingIdFromData)
                val docSnapshot = transaction.get(addressDocRef)

                if (docSnapshot.exists()) {
                    Log.d(TAG, "findOrCreateAddressTransactional - Found existing address with provided ID: $existingIdFromData")
                    return existingIdFromData
                }
            }

            // 3. We need to check for an existing address with the same normalized address
            // This part is problematic within a transaction if it involves non-transactional reads or complex queries.
            // Commenting out the problematic query block for now.
            /*
            val userAddressesCollection = firestore.collection("users").document(userId).collection("addresses") // CORRECTED: use firestore
            val query = userAddressesCollection.whereEqualTo("addressData.normalizedAddress", normalizedAddress).limit(1)

            val querySnapshot = runBlocking(Dispatchers.IO) { // This runBlocking is problematic in a transaction
                query.get().await()
            }

            if (!querySnapshot.isEmpty) {
                val existingId = querySnapshot.documents[0].id
                Log.d(TAG, "findOrCreateAddressTransactional - Found existing address with normalized address: $existingId")
                return existingId
            }
            */

            // 4. No existing address found (or query part skipped), create a new one
            Log.d(TAG, "findOrCreateAddressTransactional - Creating new address (or assuming creation due to skipped query)")

            val newAddressRef = firestore.collection("users").document(userId).collection("addresses").document()
            val newAddressId = newAddressRef.id

            val now = OffsetDateTime.now()

            // Corrected DTO Construction based on typical DTO structures and AddressDataDto fields
            val componentsDto = addressData.components // Assumed to be correct type from input AddressDataDto
            val coordinatesDto = addressData.coordinates // Assumed to be correct type from input AddressDataDto

            // SearchFields: Input AddressDataDto has 'searchFields: Address.SearchFields?'.
            // Address.SearchFields DTO typically has 'searchTerms: List<String>?, normalizedKey: String?'
            val searchFieldsDto = addressData.searchFields?.let { sf -> // sf is AddressDto.SearchFields from input
                AddressDto.SearchFields(
                    searchTerms = sf.searchTerms,
                    normalizedKey = sf.normalizedKey
                )
            } ?: addressMap?.get("searchFields")?.let { // Fallback if input addressData.searchFields is null
                 val sfMap = it as? Map<String, Any?>
                 AddressDto.SearchFields(
                    searchTerms = sfMap?.get("searchTerms") as? List<String>,
                    normalizedKey = sfMap?.get("normalizedKey") as? String
                 )
            }

            // DeliveryStats: Input AddressDataDto has 'deliveryStats: Delivery_stats?'.
            // Delivery_stats DTO structure from DtoUtils.kt hint:
            // deliveryCount, tipCount, totalTips, highestTip, pendingCount, averageTimeMinutes, lastDeliveryDate, averageTipAmount, lastDeliveryTimestamp
            val deliveryStatsDto = addressData.deliveryStats?.let { ds -> // ds is Delivery_stats from input
                com.autogratuity.data.model.generated_kt.Delivery_stats(
                    deliveryCount = ds.deliveryCount,
                    tipCount = ds.tipCount,
                    totalTips = ds.totalTips,
                    highestTip = ds.highestTip,
                    pendingCount = ds.pendingCount,
                    averageTimeMinutes = ds.averageTimeMinutes,
                    lastDeliveryDate = ds.lastDeliveryDate,
                    averageTipAmount = ds.averageTipAmount,
                    lastDeliveryTimestamp = ds.lastDeliveryTimestamp
                )
            } ?: addressMap?.get("deliveryStats")?.let { // Fallback if input addressData.deliveryStats is null
                val dsMap = it as? Map<String, Any?>
                com.autogratuity.data.model.generated_kt.Delivery_stats(
                    deliveryCount = dsMap?.get("deliveryCount") as? Long,
                    tipCount = dsMap?.get("tipCount") as? Long,
                    totalTips = dsMap?.get("totalTips") as? Double,
                    highestTip = dsMap?.get("highestTip") as? Double,
                    pendingCount = dsMap?.get("pendingCount") as? Long,
                    averageTimeMinutes = dsMap?.get("averageTimeMinutes") as? Double,
                    lastDeliveryDate = parseUniversalTimestamp(dsMap?.get("lastDeliveryDate")),
                    averageTipAmount = dsMap?.get("averageTipAmount") as? Double,
                    lastDeliveryTimestamp = parseUniversalTimestamp(dsMap?.get("lastDeliveryTimestamp"))
                )
            }

            // Flags: Input AddressDataDto has 'flags: Flags?'.
            // FlagsDto typically has: isFavorite, isVerified, doNotDeliver, dndSource, hasAccessIssues, manualDndState, isDefault
            val flagsDto = (addressData.flags ?: FlagsDto()).copy(
                isFavorite = addressData.flags?.isFavorite == true,
                isVerified = addressData.flags?.isVerified == true,
                doNotDeliver = addressData.flags?.doNotDeliver == true,
                dndSource = addressData.flags?.dndSource,
                hasAccessIssues = addressData.flags?.hasAccessIssues == true,
                manualDndState = addressData.flags?.manualDndState
            )

            // Metadata: Input AddressDataDto has 'metadata: Metadata?'.
            // MetadataDto typically has: createdAt, updatedAt, version, source, lastSync, importId, lastTriggeredAt, importedAt, customData
            val incomingMetadata = addressData.metadata
            val metadataDto = MetadataDto(
                createdAt = incomingMetadata?.createdAt ?: now,
                updatedAt = now, // Always set/update this to current time for creation/update
                version = (incomingMetadata?.version ?: 0L) + 1L, // Increment version or start at 1
                source = incomingMetadata?.source ?: addressSource, // Use incoming source or the provided addressSource
                importId = incomingMetadata?.importId,
                importedAt = incomingMetadata?.importedAt,
                captureId = incomingMetadata?.captureId,
                customData = incomingMetadata?.customData // Assuming incomingMetadata.customData is already correct type or null
            )

            val newAddressDataDto = com.autogratuity.data.model.generated_kt.Address.AddressData(
                userId = userId,
                fullAddress = addressData.fullAddress,
                normalizedAddress = normalizedAddress,
                placeId = addressData.placeId,
                isDefault = addressData.isDefault, // Explicitly passing from input AddressDataDto
                notes = addressData.notes,
                tags = addressData.tags,
                orderIds = addressData.orderIds,
                searchTerms = addressData.searchTerms,
                components = componentsDto,
                coordinates = coordinatesDto,
                searchFields = addressData.searchFields, // Explicitly passing from input AddressDataDto
                deliveryStats = deliveryStatsDto,
                flags = flagsDto,
                metadata = metadataDto,
                platform = addressData.platform
            )

            val newAddressDto = AddressDto(
                id = newAddressId,
                addressData = newAddressDataDto
            )

            val addressToSet = mapOf(
                "id" to newAddressDto.id,
                "addressData" to addressDataToMap(newAddressDto.addressData) // Use utility here too
            )

            transaction.set(newAddressRef, addressToSet)

            Log.d(TAG, "findOrCreateAddressTransactional - Created new address with ID: $newAddressId")
            return newAddressId
        } catch (e: Exception) {
            Log.e(TAG, "findOrCreateAddressTransactional - Error finding or creating address", e)
            throw e
        }
    }
    */





    // ✅ FIXED: SSOT Compliant - Use dedicated manual override function with quota enforcement
    override suspend fun setAddressDnd(addressId: String, dndEnabled: Boolean): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.handleSuspendFunction(
            operationName = "setAddressDnd",
            entityType = "Address"
        ) {
            val userId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")

            // Request deduplication for critical DND operations
            val result = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressDndUpdate(userId, addressId),
                timeout = RequestTimeouts.COMPLEX_OPERATION
            ) {
                executeSetAddressDndOperation(addressId, dndEnabled, userId)
            }
            result ?: Unit
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        return@withContext when {
            handlerResult.isSuccess -> Result.Success(Unit)
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    private suspend fun executeSetAddressDndOperation(addressId: String, dndEnabled: Boolean, userId: String) {
        Log.d(TAG, "setAddressDnd (QUOTA ENFORCED) - Calling manual DND override function for address $addressId, user $userId, desired state: $dndEnabled")

        // ✅ CRITICAL FIX: Call setManualAddressDndOverride function instead of direct Firestore update
        // This enforces freemium quotas and handles all DND logic properly
        val desiredState = if (dndEnabled) "FORCE_DND" else "FORCE_ALLOW"

        try {
            val functions = FirebaseFunctions.getInstance()
            val data = hashMapOf(
                "userId" to userId,
                "addressId" to addressId,
                "desiredState" to desiredState
            )

            val result = functions
                .getHttpsCallable("setManualAddressDndOverride")
                .call(data)
                .await()

            // Handle response from cloud function
            val response = result.data as? Map<*, *>
            val status = response?.get("status") as? String
            val message = response?.get("message") as? String

            if (status == "Error") {
                Log.e(TAG, "setAddressDnd (QUOTA ENFORCED) - Cloud function returned error: $message")
                // Check if it's a quota exceeded error
                if (message?.contains("limit", ignoreCase = true) == true ||
                    message?.contains("quota", ignoreCase = true) == true) {
                    throw Exception("Manual DND limit reached. Upgrade to Pro for unlimited manual overrides.")
                }
                throw Exception(message ?: "Failed to update DND settings")
            }

            Log.d(TAG, "setAddressDnd (QUOTA ENFORCED) - Successfully called setManualAddressDndOverride for $addressId: $message")
        } catch (e: Exception) {
            Log.e(TAG, "setAddressDnd (QUOTA ENFORCED) - Failed to call cloud function for $addressId", e)
            throw e
        }

        // ✅ ATOMIC CACHING ARCHITECTURE: Cache management handled by LocalDataSource/CacheSystem
        // Repository does pure orchestration only - no direct cache manipulation
        // The cloud function has updated Firestore, cache will be refreshed automatically
        // through the reactive observation system when the address is next accessed

        Log.d(TAG, "setAddressDnd (QUOTA ENFORCED) - Cloud function completed successfully for $addressId")
        Unit
    }

    // Modernized: Aligned with AddressRepository interface returning Result<Unit>
    override suspend fun updateAddressTags(addressId: String, tags: List<String>): Result<Unit> = withContext(ioDispatcher) {
        try {
            val userId = authManager.getCurrentUserId() ?: return@withContext Result.Error(Exception("User not authenticated"))
            Log.d(TAG, "updateAddressTags (SSoT) - Updating tags for address $addressId, user $userId")

            val fieldPath = "addressData.tags" // Firestore field path
            val updateFields: Map<String, Any> = mapOf(fieldPath to tags)

            val remoteUpdateResult = requestDeduplicationManager.deduplicateRequest(
                key = RequestKeys.addressTagsUpdate(userId, addressId),
                timeout = RequestTimeouts.STANDARD_OPERATION
            ) {
                remoteDataSource.updateAddressFields(userId, addressId, updateFields)
            } ?: return@withContext Result.Error(Exception("Request deduplication timeout for updateAddressTags"))

            if (remoteUpdateResult is Result.Error) {
                Log.e(TAG, "updateAddressTags (SSoT) - Remote update failed for address $addressId, user $userId", remoteUpdateResult.exception)
                return@withContext remoteUpdateResult
            }

            Log.d(TAG, "updateAddressTags (SSoT) - Remote update successful for $addressId. Updating local SSoT cache.")
            val currentSsotAddressResult = getAddressById(addressId) // SSoT compliant getAddressById

            return@withContext when (currentSsotAddressResult) {
                is Result.Success -> {
                    val currentSsotAddress = currentSsotAddressResult.data
                    if (currentSsotAddress != null) {
                        val updatedSsotAddress = currentSsotAddress.copy(tags = tags) // Update SSoT model
                        val localSaveResult = localDataSource.saveAddress(userId, updatedSsotAddress)
                        if (localSaveResult is Result.Error) {
                            Log.w(TAG, "updateAddressTags (SSoT) - Local cache update failed for $addressId after remote success.", localSaveResult.exception)
                        }
                        Result.Success(Unit)
                    } else {
                        Log.e(TAG, "updateAddressTags (SSoT) - Address $addressId not found after remote update for cache refresh.")
                        Result.Error(Exception("Address $addressId not found after remote update to refresh cache"))
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "updateAddressTags (SSoT) - Failed to fetch address $addressId for cache update after remote success.", currentSsotAddressResult.exception)
                    Result.Error(Exception("Failed to refresh local cache for $addressId after remote update: ${currentSsotAddressResult.exception.message}"))
                }
                is Result.Loading -> { // Added Loading case for exhaustiveness
                    Log.w(TAG, "updateAddressTags (SSoT) - Unexpected Loading state when fetching address $addressId for cache update.")
                    Result.Error(IllegalStateException("Unexpected Loading state for $addressId during cache update"))
                }
            }
        } catch (e: Exception) {
            val userId = getCurrentUserIdSuspend()
            Log.e(TAG, "updateAddressTags (SSoT) - Unexpected error for address $addressId, user $userId", e)
            Result.Error(e) // Catch-all
        }
    }

    // TODO: updateAddressDeliveryStats needs to be aligned similarly if it's in the interface.

    // ===== PAGINATION INTEGRATION =====
    
    // Note: createAddressPageLoader is typically part of a factory or DI setup, 
    // not usually directly in the repository like this if paginatedDataSource is injected.
    // Assuming paginatedDataSource is correctly provided via DI.

    // Note: observePaginatedAddresses, loadInitialAddressPage, and loadNextAddressPage are not part 
    // of the AddressRepository interface and have been removed. If pagination is needed,
    // these methods should be added to the interface first or moved to a separate pagination service.

    // loadInitialAddressPage removed - not part of AddressRepository interface

    // loadNextAddressPage removed - not part of AddressRepository interface

    override fun observeAddressById(addressId: String): Flow<Result<com.autogratuity.domain.model.Address?>> {
        return currentUserIdFlow.flatMapLatest { userId ->
            if (userId.isNullOrBlank()) {
                Log.w(TAG, "observeAddressById (SSoT): User not authenticated or userId is blank for address $addressId. Emitting error flow.")
                return@flatMapLatest flowOf(Result.Error(Exception("User not authenticated for observeAddressById")))
            } else {
                Log.d(TAG, "observeAddressById (SSoT): Setting up SSoT observer for user $userId, address $addressId")
                repositoryErrorHandler.handleFlow(
                    flow = localDataSource.observeById(userId, addressId)
                        .onStart {
                            applicationScope.launch(ioDispatcher) {
                                Log.d(TAG, "observeAddressById (SSoT) for user $userId, address $addressId: Triggering initial cache refresh.")
                                getAddressById(addressId) // This should be SSoT compliant getAddressById
                            }
                        },
                    operationName = "observeAddressById",
                    entityType = "Address"
                ).map { kotlinResult ->
                    when {
                        kotlinResult.isSuccess -> Result.Success(kotlinResult.getOrNull())
                        else -> {
                            val throwable = kotlinResult.exceptionOrNull()
                            val exception = when (throwable) {
                                is Exception -> throwable
                                else -> Exception("Unknown error in observeAddressById flow", throwable)
                            }
                            Result.Error(exception)
                        }
                    }
                }
            }
        }.flowOn(ioDispatcher)
    }

    override fun observeDefaultAddress(): Flow<Result<com.autogratuity.domain.model.Address?>> {
        return authManager.observeCurrentUser().flatMapLatest { firebaseUser ->
            val userId = firebaseUser?.uid
            if (userId.isNullOrBlank()) {
                return@flatMapLatest kotlinx.coroutines.flow.flowOf(Result.Error(Exception("User not authenticated for observeDefaultAddress")))
            }
            else {
                return@flatMapLatest preferenceRepository.value.observeDefaultAddressId()
                    .flatMapLatest { defaultAddressId: String? ->
                        if (defaultAddressId.isNullOrBlank()) {
                            return@flatMapLatest kotlinx.coroutines.flow.flowOf(Result.Success(null))
                        }
                        else {
                            repositoryErrorHandler.handleFlow(
                                flow = localDataSource.observeById(userId, defaultAddressId)
                                    .onStart {
                                        applicationScope.launch(ioDispatcher) {
                                            Log.d(TAG, "observeDefaultAddress for user $userId, address $defaultAddressId: Triggering cache refresh.")
                                            getAddressById(defaultAddressId) // SSoT compliant
                                        }
                                    },
                                operationName = "observeDefaultAddress.localDataSource",
                                entityType = "Address"
                            ).map { kotlinResult ->
                                when {
                                    kotlinResult.isSuccess -> Result.Success(kotlinResult.getOrNull())
                                    else -> {
                                        val throwable = kotlinResult.exceptionOrNull()
                                        val exception = when (throwable) {
                                            is Exception -> throwable
                                            else -> Exception("Unknown error observing default address ID $defaultAddressId", throwable)
                                        }
                                        Result.Error(exception)
                                    }
                                }
                            }
                        }
                    }
            }
        }.flowOn(ioDispatcher)
    }

    // ====== OBSERVE OPERATIONS ======
    // These methods provide reactive observation of address data
    // and are not required to be overridden. Removing them to fix compilation errors.

    // findAddressByPlaceId removed - not part of AddressRepository interface
    // This method has been removed as it's not part of the AddressRepository interface.
    // If needed, it should be added to the interface first or moved to a separate service.

    // Duplicate findAddressByDetails method removed - already implemented above at line 1132



    // ===== REPOSITORY LIFECYCLE IMPLEMENTATION (INFRASTRUCTURE FOCUS) =====

    override suspend fun initialize(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.i(TAG, "initialize: Initializing AddressRepository")

            // Repository initialization - setup any required state
            // LocalDataSource and AddressCacheSystem are already initialized via DI

            Log.d(TAG, "initialize: AddressRepository initialized successfully")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "initialize: Error initializing AddressRepository", e)
            Result.Error(e)
        }
    }

    override suspend fun prefetchCriticalData(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.i(TAG, "prefetchCriticalData: Prefetching critical address data")

            // Prefetch default address for faster access
            val defaultAddressResult = getDefaultAddress()

            when (defaultAddressResult) {
                is Result.Success -> {
                    if (defaultAddressResult.data != null) {
                        Log.d(TAG, "prefetchCriticalData: Successfully prefetched default address: ${defaultAddressResult.data.id}")
                    } else {
                        Log.d(TAG, "prefetchCriticalData: No default address found to prefetch")
                    }
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    Log.e(TAG, "prefetchCriticalData: Error prefetching default address", defaultAddressResult.exception)
                    // Don't fail the entire operation if default address prefetch fails
                    Result.Success(Unit)
                }
                is Result.Loading -> {
                    Log.w(TAG, "prefetchCriticalData: getDefaultAddress returned Loading unexpectedly")
                    Result.Success(Unit)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "prefetchCriticalData: Error during critical data prefetch", e)
            Result.Error(e)
        }
    }

    override suspend fun cleanup(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.i(TAG, "cleanup: Cleaning up AddressRepository resources")

            // Repository cleanup - no direct cache management
            // Cache cleanup is handled by CacheLifecycleManager per atomic-caching.md

            Log.d(TAG, "cleanup: AddressRepository cleanup completed")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "cleanup: Error cleaning up AddressRepository", e)
            Result.Error(e)
        }
    }

    // ===== IMPORT/EXPORT IMPLEMENTATION (INFRASTRUCTURE FOCUS) =====

    override suspend fun exportUserData(userId: String, format: String): Result<String> = withContext(ioDispatcher) {
        try {
            Log.i(TAG, "exportUserData: Exporting address data for user $userId in format $format")

            // Get all addresses for the user
            val addressesResult = getAllAddresses()

            when (addressesResult) {
                is Result.Success -> {
                    val addresses = addressesResult.data

                    // Convert to exportable format using ObjectMapper
                    val exportData = when (format.lowercase()) {
                        "json" -> {
                            val addressMaps = addresses.map { address ->
                                objectMapper.convertValue(address, Map::class.java) as Map<*, *>
                            }
                            objectMapper.writeValueAsString(mapOf(
                                "userId" to userId,
                                "exportedAt" to java.time.OffsetDateTime.now().toString(),
                                "addresses" to addressMaps
                            ))
                        }
                        else -> {
                            Log.w(TAG, "exportUserData: Unsupported format $format, defaulting to JSON")
                            val addressMaps = addresses.map { address ->
                                objectMapper.convertValue(address, Map::class.java) as Map<*, *>
                            }
                            objectMapper.writeValueAsString(mapOf(
                                "userId" to userId,
                                "exportedAt" to OffsetDateTime.now().toString(),
                                "addresses" to addressMaps
                            ))
                        }
                    }

                    Log.d(TAG, "exportUserData: Successfully exported ${addresses.size} addresses for user $userId")
                    Result.Success(exportData)
                }
                is Result.Error -> {
                    Log.e(TAG, "exportUserData: Failed to get addresses for user $userId", addressesResult.exception)
                    Result.Error(addressesResult.exception)
                }
                is Result.Loading -> {
                    Log.w(TAG, "exportUserData: getAllAddresses returned Loading for user $userId")
                    Result.Error(IllegalStateException("getAllAddresses returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "exportUserData: Error exporting data for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun importUserData(userId: String, data: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.i(TAG, "importUserData: Importing address data for user $userId")

            @Suppress("UNCHECKED_CAST")
            val addressesData = data["addresses"] as? List<Map<String, Any>>
                ?: return@withContext Result.Error(IllegalArgumentException("Invalid import data: missing 'addresses' field"))

            var successCount = 0
            var errorCount = 0

            for ((index, addressMap) in addressesData.withIndex()) {
                try {
                    // Convert map to Address domain model
                    val address = objectMapper.convertValue(addressMap, com.autogratuity.domain.model.Address::class.java)

                    // Save the address via repository (which handles DTO mapping and remote storage)
                    val addResult = addAddress(address)
                    when (addResult) {
                        is Result.Success -> {
                            successCount++
                            Log.d(TAG, "importUserData: Successfully imported address ${index + 1}/${addressesData.size}")
                        }
                        is Result.Error -> {
                            errorCount++
                            Log.e(TAG, "importUserData: Failed to import address ${index + 1}/${addressesData.size}", addResult.exception)
                        }
                        is Result.Loading -> {
                            errorCount++
                            Log.w(TAG, "importUserData: Unexpected Loading state for address ${index + 1}/${addressesData.size}")
                        }
                    }
                } catch (e: Exception) {
                    errorCount++
                    Log.e(TAG, "importUserData: Error processing address ${index + 1}/${addressesData.size}", e)
                }
            }

            Log.i(TAG, "importUserData: Import completed for user $userId. Success: $successCount, Errors: $errorCount")
            if (errorCount > 0 && successCount == 0) {
                Result.Error(Exception("Failed to import any addresses for user $userId"))
            } else {
                Result.Success(Unit)
            }
        } catch (e: Exception) {
            Log.e(TAG, "importUserData: Error importing data for user $userId", e)
            Result.Error(e)
        }
    }

    // ===== BACKUP AND RECOVERY IMPLEMENTATION (INFRASTRUCTURE FOCUS) =====

    override suspend fun createUserBackup(userId: String): Result<Map<String, Any>> = withContext(ioDispatcher) {
        try {
            Log.i(TAG, "createUserBackup: Creating backup for user $userId")

            // Get all addresses for the user
            val addressesResult = getAllAddresses()

            when (addressesResult) {
                is Result.Success -> {
                    val addresses = addressesResult.data

                    // Convert addresses to serializable maps
                    val addressMaps = addresses.map { address ->
                        objectMapper.convertValue(address, Map::class.java) as Map<*, *>
                    }

                    val backup = mapOf(
                        "userId" to userId,
                        "backupCreatedAt" to java.time.OffsetDateTime.now().toString(),
                        "version" to 1L,
                        "addresses" to addressMaps,
                        "addressCount" to addresses.size
                    )

                    Log.d(TAG, "createUserBackup: Successfully created backup with ${addresses.size} addresses for user $userId")
                    Result.Success(backup)
                }
                is Result.Error -> {
                    Log.e(TAG, "createUserBackup: Failed to get addresses for user $userId", addressesResult.exception)
                    Result.Error(addressesResult.exception)
                }
                is Result.Loading -> {
                    Log.w(TAG, "createUserBackup: getAllAddresses returned Loading for user $userId")
                    Result.Error(IllegalStateException("getAllAddresses returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "createUserBackup: Error creating backup for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun restoreUserBackup(userId: String, backup: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.i(TAG, "restoreUserBackup: Restoring backup for user $userId")

            // Validate backup format
            val backupUserId = backup["userId"] as? String
            if (backupUserId != userId) {
                return@withContext Result.Error(IllegalArgumentException("Backup userId ($backupUserId) does not match target userId ($userId)"))
            }

            // Note: Cache clearing is handled by CacheLifecycleManager per atomic-caching.md
            // Repository should not directly manage cache operations

            // Import the backup data
            val importResult = importUserData(userId, backup)

            when (importResult) {
                is Result.Success -> {
                    Log.d(TAG, "restoreUserBackup: Successfully restored backup for user $userId")
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    Log.e(TAG, "restoreUserBackup: Failed to import backup data for user $userId", importResult.exception)
                    importResult
                }
                is Result.Loading -> {
                    Log.w(TAG, "restoreUserBackup: importUserData returned Loading for user $userId")
                    Result.Error(IllegalStateException("importUserData returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "restoreUserBackup: Error restoring backup for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun migrateUserData(userId: String, fromVersion: Long, toVersion: Long): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.i(TAG, "migrateUserData: Migrating data for user $userId from version $fromVersion to $toVersion")

            if (fromVersion == toVersion) {
                Log.d(TAG, "migrateUserData: No migration needed - versions are the same")
                return@withContext Result.Success(Unit)
            }

            // For now, implement basic migration by fetching fresh data
            // In the future, this could include version-specific migration logic
            when {
                fromVersion < toVersion -> {
                    Log.d(TAG, "migrateUserData: Performing forward migration by fetching fresh data")
                    // Use repository's standard data fetching which handles caching appropriately
                    val refreshResult = getAllAddresses()
                    when (refreshResult) {
                        is Result.Success -> {
                            Log.d(TAG, "migrateUserData: Successfully refreshed ${refreshResult.data.size} addresses")
                            Result.Success(Unit)
                        }
                        is Result.Error -> {
                            Log.e(TAG, "migrateUserData: Failed to refresh data during migration", refreshResult.exception)
                            refreshResult.let { Result.Error(it.exception) }
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "migrateUserData: getAllAddresses returned Loading unexpectedly")
                            Result.Error(IllegalStateException("getAllAddresses returned Loading during migration"))
                        }
                    }
                }
                else -> {
                    Log.w(TAG, "migrateUserData: Backward migration not supported (from $fromVersion to $toVersion)")
                    Result.Error(IllegalArgumentException("Backward migration not supported"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "migrateUserData: Error migrating data for user $userId", e)
            Result.Error(e)
        }
    }

    companion object {
        // TODO: This key should ideally be shared with PreferenceRepositoryImpl or defined in a common constants file.
        private const val KEY_DEFAULT_ADDRESS_ID = "default_address_id_key_placeholder"
    }
}