package com.autogratuity.data.model

import java.util.concurrent.atomic.AtomicInteger

/**
 * Helper class to track statistics during data processing or import operations.
 */
class ProcessStats {
    private val addedCount = AtomicInteger(0)
    private val updatedCount = AtomicInteger(0)
    private val skippedCount = AtomicInteger(0)
    private val failedCount = AtomicInteger(0)

    fun incrementAdded() {
        addedCount.incrementAndGet()
    }

    fun incrementUpdated() {
        updatedCount.incrementAndGet()
    }

    fun incrementSkipped() {
        skippedCount.incrementAndGet()
    }

    fun incrementFailed() {
        failedCount.incrementAndGet()
    }

    fun getAddedCount(): Int {
        return addedCount.get()
    }

    fun getUpdatedCount(): Int {
        return updatedCount.get()
    }

    fun getSkippedCount(): Int {
        return skippedCount.get()
    }

    fun getFailedCount(): Int {
        return failedCount.get()
    }

    override fun toString(): String {
        return "ProcessStats{" +
                "added=" + addedCount.get() +
                ", updated=" + updatedCount.get() +
                ", skipped=" + skippedCount.get() +
                ", failed=" + failedCount.get() +
                '}'
    }
}