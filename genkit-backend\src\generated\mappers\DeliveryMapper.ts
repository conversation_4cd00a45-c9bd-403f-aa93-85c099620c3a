// Auto-generated from DeliveryMapper.kt
import { Result } from '../types/Result';
import { Delivery } from '../models/domain/Delivery';

/**
 * Business logic mapper generated from Kotlin DeliveryMapper
 */
export class DeliveryMapper {
  calculateTipPercentage([object Object],[object Object]): number? { {
    // TODO: Port business logic from Kotlin DeliveryMapper.calculateTipPercentage
    throw new Error('calculateTipPercentage not yet implemented');
  }

  roundTo2DecimalPlaces([object Object]): number { {
    // TODO: Port business logic from Kotlin DeliveryMapper.roundTo2DecimalPlaces
    throw new Error('roundTo2DecimalPlaces not yet implemented');
  }

  createDefaultDelivery([object Object]): Delivery { {
    // TODO: Port business logic from Kotlin DeliveryMapper.createDefaultDelivery
    throw new Error('createDefaultDelivery not yet implemented');
  }

  ensureValidReference([object Object]): Delivery { {
    // TODO: Port business logic from Kotlin DeliveryMapper.ensureValidReference
    throw new Error('ensureValidReference not yet implemented');
  }

  dateToOffsetDateTime([object Object]): Date? { {
    // TODO: Port business logic from Kotlin DeliveryMapper.dateToOffsetDateTime
    throw new Error('dateToOffsetDateTime not yet implemented');
  }

  offsetDateTimeToDate([object Object]): Date? { {
    // TODO: Port business logic from Kotlin DeliveryMapper.offsetDateTimeToDate
    throw new Error('offsetDateTimeToDate not yet implemented');
  }

  transformUsageStatsToDeliveryStats([object Object]): Delivery_stats { {
    // TODO: Port business logic from Kotlin DeliveryMapper.transformUsageStatsToDeliveryStats
    throw new Error('transformUsageStatsToDeliveryStats not yet implemented');
  }

  transformUsageStatsToDeliveryStats([object Object]): Delivery_stats { {
    // TODO: Port business logic from Kotlin DeliveryMapper.transformUsageStatsToDeliveryStats
    throw new Error('transformUsageStatsToDeliveryStats not yet implemented');
  }

  mapDeliveryStatsToDomain([object Object],[object Object]): DeliveryStats { {
    // TODO: Port business logic from Kotlin DeliveryMapper.mapDeliveryStatsToDomain
    throw new Error('mapDeliveryStatsToDomain not yet implemented');
  }

  createDefaultDeliveryStatsLocal(): Delivery_stats { {
    // TODO: Port business logic from Kotlin DeliveryMapper.createDefaultDeliveryStatsLocal
    throw new Error('createDefaultDeliveryStatsLocal not yet implemented');
  }

  calculateTipPercentageFromAmounts([object Object],[object Object]): number { {
    // TODO: Port business logic from Kotlin DeliveryMapper.calculateTipPercentageFromAmounts
    throw new Error('calculateTipPercentageFromAmounts not yet implemented');
  }

  createDefaultDeliveryDto([object Object]): DeliveryDto { {
    // TODO: Port business logic from Kotlin DeliveryMapper.createDefaultDeliveryDto
    throw new Error('createDefaultDeliveryDto not yet implemented');
  }

  transformUsageStatsToDeliveryStatsFromStats([object Object]): Delivery_stats? { {
    // TODO: Port business logic from Kotlin DeliveryMapper.transformUsageStatsToDeliveryStatsFromStats
    throw new Error('transformUsageStatsToDeliveryStatsFromStats not yet implemented');
  }

  transformUsageStatsToDeliveryStatsMap([object Object]): Map<string, Delivery_stats> { {
    // TODO: Port business logic from Kotlin DeliveryMapper.transformUsageStatsToDeliveryStatsMap
    throw new Error('transformUsageStatsToDeliveryStatsMap not yet implemented');
  }

  ensureValidReferenceDto([object Object]): DeliveryDto { {
    // TODO: Port business logic from Kotlin DeliveryMapper.ensureValidReferenceDto
    throw new Error('ensureValidReferenceDto not yet implemented');
  }

  getStartOfDay([object Object]): Date { {
    // TODO: Port business logic from Kotlin DeliveryMapper.getStartOfDay
    throw new Error('getStartOfDay not yet implemented');
  }

  getEndOfDay([object Object]): Date { {
    // TODO: Port business logic from Kotlin DeliveryMapper.getEndOfDay
    throw new Error('getEndOfDay not yet implemented');
  }

  getStartAndEndOfDayLocal([object Object]): Pair<Date, Date> { {
    // TODO: Port business logic from Kotlin DeliveryMapper.getStartAndEndOfDayLocal
    throw new Error('getStartAndEndOfDayLocal not yet implemented');
  }

  getLastWeekDateRange(): Pair<Date, Date> { {
    // TODO: Port business logic from Kotlin DeliveryMapper.getLastWeekDateRange
    throw new Error('getLastWeekDateRange not yet implemented');
  }

  getLastMonthDateRange(): Pair<Date, Date> { {
    // TODO: Port business logic from Kotlin DeliveryMapper.getLastMonthDateRange
    throw new Error('getLastMonthDateRange not yet implemented');
  }

  validateTipAmount([object Object]): boolean { {
    // TODO: Port business logic from Kotlin DeliveryMapper.validateTipAmount
    throw new Error('validateTipAmount not yet implemented');
  }

  validateDelivery([object Object]): Result<SingleValidationResult> { {
    // TODO: Port business logic from Kotlin DeliveryMapper.validateDelivery
    throw new Error('validateDelivery not yet implemented');
  }

  calculateDeliveryStatsFromDtos([object Object]): Delivery_stats { {
    // TODO: Port business logic from Kotlin DeliveryMapper.calculateDeliveryStatsFromDtos
    throw new Error('calculateDeliveryStatsFromDtos not yet implemented');
  }
}