// Auto-generated from UserSubscription.kt
// TypeScript equivalent of Android domain.model.UserSubscription

/**
 * Domain model generated from Kotlin UserSubscription
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface UserSubscription {
  status?: string | null;
  level?: string | null;
  isActive?: boolean | null;
  startDate?: Date | null;
  expiryDate?: Date | null;
  isLifetime?: boolean | null;
  provider?: string | null;
  orderId?: string | null;
  verification?: VerificationDetails? // Contains PII field 'error';
}