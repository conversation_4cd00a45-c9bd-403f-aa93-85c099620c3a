package com.autogratuity.data.mapper

import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.debug.ClarityArchitectureMonitor
import com.autogratuity.domain.model.AppConfig
import com.autogratuity.domain.model.NotificationPatterns
import kotlinx.coroutines.ExperimentalCoroutinesApi
import java.time.OffsetDateTime
import javax.inject.Inject
import javax.inject.Singleton
import com.autogratuity.data.model.generated_kt.App_config as AppConfigDto
import com.autogratuity.data.model.generated_kt.Notification_patterns as NotificationPatternsDto

/**
 * ConfigMapper handles DTO↔SSoT transformation and business logic for config domain.
 *
 * Responsibilities (per clarity.md):
 * - DTO↔SSoT mapping (App_config ↔ AppConfig, Notification_patterns ↔ NotificationPatterns)
 * - Business logic and calculations
 * - Default creation logic
 * - Configuration value extraction logic
 *
 * Follows the delivery domain pattern for mappers.
 */
@ExperimentalCoroutinesApi
@Singleton
class ConfigMapper @Inject constructor() {

    companion object {
        private const val TAG = "ConfigMapper"
    }

    // ===== APP CONFIG MAPPING =====

    /**
     * Maps App_config DTO to AppConfig SSoT domain model.
     */
    fun mapToDomain(dto: AppConfigDto): Result<AppConfig> {
        val mappingStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        var validationErrors = mutableListOf<String>()
        
        return try {
            Log.d(TAG, "CONVERSION: AppConfig DTO -> Domain | Version: ${dto.version}")
            Log.d(TAG, "  Input: versions=[${dto.versions.minimum}, ${dto.versions.recommended}, ${dto.versions.latest}]")
            Log.d(TAG, "  Features: sync=${dto.features.useNewSyncSystem} | offline=${dto.features.enableOfflineMode} | analytics=${dto.features.enableAnalytics}")
            Log.d(TAG, "  Maintenance: ${dto.maintenance.isInMaintenance}")
            
            // Validate critical fields
            if (dto.version.toString().isBlank()) {
                validationErrors.add("version is null or blank")
            }
            if (dto.versions.minimum.isBlank()) {
                validationErrors.add("minimum version is null or blank")
            }
            if (dto.sync.interval <= 0) {
                validationErrors.add("sync interval must be positive")
            }
            
            val appConfig = AppConfig(
                versions = AppConfig.AppVersions(
                    minimum = dto.versions.minimum,
                    recommended = dto.versions.recommended,
                    latest = dto.versions.latest
                ),
                features = AppConfig.AppFeatures(
                    useNewSyncSystem = dto.features.useNewSyncSystem,
                    enableOfflineMode = dto.features.enableOfflineMode,
                    enableAnalytics = dto.features.enableAnalytics,
                    enableBackgroundSync = dto.features.enableBackgroundSync,
                    enforceVersionCheck = dto.features.enforceVersionCheck
                ),
                limits = AppConfig.AppLimits(
                    freeTier = AppConfig.AppLimits.TierLimits(
                        mappingLimit = dto.limits.freeTier.mappingLimit,
                        importLimit = dto.limits.freeTier.importLimit,
                        exportLimit = dto.limits.freeTier.exportLimit
                    ),
                    proTier = AppConfig.AppLimits.TierLimits(
                        mappingLimit = dto.limits.proTier.mappingLimit,
                        importLimit = dto.limits.proTier.importLimit,
                        exportLimit = dto.limits.proTier.exportLimit
                    )
                ),
                sync = AppConfig.AppSync(
                    interval = dto.sync.interval,
                    backgroundInterval = dto.sync.backgroundInterval,
                    maxBatchSize = dto.sync.maxBatchSize,
                    conflictStrategy = dto.sync.conflictStrategy
                ),
                maintenance = AppConfig.AppMaintenance(
                    isInMaintenance = dto.maintenance.isInMaintenance,
                    maintenanceMessage = dto.maintenance.maintenanceMessage,
                    estimatedEndTime = dto.maintenance.estimatedEndTime
                ),
                updatedAt = dto.updatedAt,
                version = dto.version,
                customData = dto.customData?.let { convertCustomDataToMap(it) }
            )
            
            val mappingDuration = mappingStartTime.elapsedNow()
            
            // 📊 COMPREHENSIVE BEFORE/AFTER STATE LOGGING
            val inputState = mapOf(
                "dto_version" to dto.version,
                "dto_min_version" to dto.versions.minimum,
                "dto_latest_version" to dto.versions.latest,
                "dto_maintenance_mode" to dto.maintenance.isInMaintenance,
                "dto_features_count" to 5, // useNewSyncSystem, enableOfflineMode, etc.
                "dto_customData_size" to dto.customData?.toString()?.length
            )
            
            val outputState = mapOf(
                "domain_version" to appConfig.version,
                "domain_min_version" to appConfig.versions.minimum,
                "domain_latest_version" to appConfig.versions.latest,
                "domain_maintenance_mode" to appConfig.maintenance.isInMaintenance,
                "domain_features_count" to 5,
                "domain_customData_size" to appConfig.customData?.toString()?.length
            )
            
            val fieldTransformations = listOf(
                "versions: NESTED_MAPPING",
                "features: FEATURE_FLAGS_MAPPING",
                "limits: TIER_LIMITS_MAPPING", 
                "maintenance: STATUS_MAPPING",
                "customData: JSON_CONVERSION"
            )
            
            val businessLogicApplied = listOf(
                "validation",
                "version_check", 
                "feature_flags_processing",
                "limits_enforcement",
                "maintenance_status"
            )

            Log.d(TAG, "=== CONFIG MAPPING TRANSFORMATION ===")
            Log.d(TAG, "Input DTO State: $inputState")
            Log.d(TAG, "Output Domain State: $outputState")
            Log.d(TAG, "Field Transformations: $fieldTransformations")
            Log.d(TAG, "Business Logic Applied: $businessLogicApplied")
            Log.d(TAG, "Mapping Duration: ${mappingDuration.inWholeMilliseconds}ms")
            
            // 🔍 SESSION CORRELATION: Add mapping success to session
            ClarityArchitectureMonitor.addSessionEvent("config_mapping_success:${dto.version}")
            
            // ENHANCED: Monitor successful DTO to Domain conversion
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "ConfigMapper",
                entityType = "AppConfig",
                duration = mappingDuration,
                success = true,
                piiFieldsProcessed = 0, // No PII in config
                validationErrors = validationErrors,
                fieldMismatches = emptyList(),
                entityId = "app_config",
                userId = null, // Config is global
                dataSize = dto.toString().length,
                fieldsTransformed = fieldTransformations.size,
                businessLogicApplied = businessLogicApplied,
                cacheUpdated = false
            )
            
            Log.d(TAG, "CONVERSION SUCCESS: AppConfig DTO -> Domain | Version: ${dto.version}")
            Log.d(TAG, "  Output: validation_errors=${validationErrors.size} | duration=${mappingDuration.inWholeMilliseconds}ms")
            Log.d(TAG, "  Domain size: ${appConfig.toString().length} bytes")
            Log.d(TAG, "  Business logic: validation, version_check, feature_flags, limits_enforcement")
            
            Result.Success(appConfig)
        } catch (e: Exception) {
            val mappingDuration = mappingStartTime.elapsedNow()
            
            // 🔍 SESSION CORRELATION: Add mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("config_mapping_failure:${dto.version}:exception")
            
            // ENHANCED: Monitor mapping error
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "ConfigMapper",
                entityType = "AppConfig",
                duration = mappingDuration,
                success = false,
                piiFieldsProcessed = 0,
                validationErrors = validationErrors + "Mapping error: ${e.message}",
                fieldMismatches = emptyList(),
                error = e,
                entityId = "app_config",
                userId = null,
                dataSize = dto.toString().length,
                businessLogicApplied = listOf("mapping_failed")
            )
            
            Log.e(TAG, "CONVERSION FAILED: AppConfig DTO -> Domain | Version: ${dto.version} | Mapping error", e)
            Log.e(TAG, "  Validation errors: $validationErrors")
            Log.e(TAG, "  Duration: ${mappingDuration.inWholeMilliseconds}ms")
            
            Result.Error(e)
        }
    }

    /**
     * Maps AppConfig SSoT domain model to App_config DTO.
     */
    fun mapToDto(domain: AppConfig): Result<AppConfigDto> {
        return try {
            val dto = AppConfigDto(
                versions = AppConfigDto.Versions(
                    minimum = domain.versions.minimum,
                    recommended = domain.versions.recommended,
                    latest = domain.versions.latest
                ),
                features = AppConfigDto.Features(
                    useNewSyncSystem = domain.features.useNewSyncSystem,
                    enableOfflineMode = domain.features.enableOfflineMode,
                    enableAnalytics = domain.features.enableAnalytics,
                    enableBackgroundSync = domain.features.enableBackgroundSync,
                    enforceVersionCheck = domain.features.enforceVersionCheck
                ),
                limits = AppConfigDto.Limits(
                    freeTier = AppConfigDto.TierLimits(
                        mappingLimit = domain.limits.freeTier.mappingLimit,
                        importLimit = domain.limits.freeTier.importLimit,
                        exportLimit = domain.limits.freeTier.exportLimit
                    ),
                    proTier = AppConfigDto.TierLimits(
                        mappingLimit = domain.limits.proTier.mappingLimit,
                        importLimit = domain.limits.proTier.importLimit,
                        exportLimit = domain.limits.proTier.exportLimit
                    )
                ),
                sync = AppConfigDto.Sync(
                    interval = domain.sync.interval,
                    backgroundInterval = domain.sync.backgroundInterval,
                    maxBatchSize = domain.sync.maxBatchSize,
                    conflictStrategy = domain.sync.conflictStrategy
                ),
                maintenance = AppConfigDto.Maintenance(
                    isInMaintenance = domain.maintenance.isInMaintenance,
                    maintenanceMessage = domain.maintenance.maintenanceMessage,
                    estimatedEndTime = domain.maintenance.estimatedEndTime
                ),
                updatedAt = domain.updatedAt,
                version = domain.version,
                customData = domain.customData?.let { convertMapToCustomData(it) }
            )
            Result.Success(dto)
        } catch (e: Exception) {
            Log.e(TAG, "Error mapping AppConfig domain to App_config DTO", e)
            Result.Error(e)
        }
    }

    // ===== NOTIFICATION PATTERNS MAPPING =====

    /**
     * Maps Notification_patterns DTO to NotificationPatterns SSoT domain model.
     */
    fun mapToDomain(dto: NotificationPatternsDto): Result<NotificationPatterns> {
        return try {
            val patterns = NotificationPatterns(
                extractors = NotificationPatterns.NotificationExtractors(
                    shipt = NotificationPatterns.NotificationExtractors.ShiptExtractor(
                        orderId = dto.extractors.shipt.orderId,
                        tipAmount = dto.extractors.shipt.tipAmount
                    )
                ),
                patterns = NotificationPatterns.PlatformPatterns(
                    doordash = dto.patterns.doordash,
                    shipt = dto.patterns.shipt,
                    ubereats = dto.patterns.ubereats
                ),
                updatedAt = dto.updatedAt,
                version = dto.version
            )
            Result.Success(patterns)
        } catch (e: Exception) {
            Log.e(TAG, "Error mapping Notification_patterns DTO to NotificationPatterns domain", e)
            Result.Error(e)
        }
    }

    /**
     * Maps NotificationPatterns SSoT domain model to Notification_patterns DTO.
     */
    fun mapToDto(domain: NotificationPatterns): Result<NotificationPatternsDto> {
        return try {
            val dto = NotificationPatternsDto(
                extractors = NotificationPatternsDto.Extractors(
                    shipt = NotificationPatternsDto.ShiptExtractor(
                        orderId = domain.extractors.shipt.orderId,
                        tipAmount = domain.extractors.shipt.tipAmount
                    )
                ),
                patterns = NotificationPatternsDto.Patterns(
                    doordash = domain.patterns.doordash,
                    shipt = domain.patterns.shipt,
                    ubereats = domain.patterns.ubereats
                ),
                updatedAt = domain.updatedAt,
                version = domain.version
            )
            Result.Success(dto)
        } catch (e: Exception) {
            Log.e(TAG, "Error mapping NotificationPatterns domain to Notification_patterns DTO", e)
            Result.Error(e)
        }
    }

    // ===== BUSINESS LOGIC METHODS =====

    /**
     * Extract configuration value from AppConfig domain model.
     */
    fun getConfigValue(appConfig: AppConfig, key: String, defaultValue: String): String {
        return when (key) {
            "latestVersion" -> appConfig.versions.latest
            "recommendedVersion" -> appConfig.versions.recommended
            "minimumVersion" -> appConfig.versions.minimum
            "maintenanceMessage" -> appConfig.maintenance.maintenanceMessage ?: defaultValue
            "conflictStrategy" -> appConfig.sync.conflictStrategy
            else -> {
                (appConfig.customData?.get(key) as? String) ?: defaultValue
            }
        }
    }

    /**
     * Extract boolean configuration value from AppConfig domain model.
     */
    fun getConfigBoolean(appConfig: AppConfig, key: String, defaultValue: Boolean): Boolean {
        return when (key) {
            "useNewSyncSystem" -> appConfig.features.useNewSyncSystem
            "enableOfflineMode" -> appConfig.features.enableOfflineMode
            "enableAnalytics" -> appConfig.features.enableAnalytics
            "enableBackgroundSync" -> appConfig.features.enableBackgroundSync
            "enforceVersionCheck" -> appConfig.features.enforceVersionCheck
            "isInMaintenance" -> appConfig.maintenance.isInMaintenance
            else -> {
                (appConfig.customData?.get(key) as? Boolean) ?: defaultValue
            }
        }
    }

    /**
     * Validates application configuration.
     */
    fun validateAppConfig(appConfig: AppConfig): Result<Unit> {
        return try {
            // Validate versions
            if (appConfig.versions.minimum.isBlank() ||
                appConfig.versions.recommended.isBlank() ||
                appConfig.versions.latest.isBlank()) {
                return Result.Error(IllegalArgumentException("Version fields cannot be blank"))
            }

            // Validate sync configuration
            if (appConfig.sync.interval <= 0 || appConfig.sync.backgroundInterval <= 0) {
                return Result.Error(IllegalArgumentException("Sync intervals must be positive"))
            }

            if (appConfig.sync.maxBatchSize <= 0) {
                return Result.Error(IllegalArgumentException("Max batch size must be positive"))
            }

            // Validate limits
            if (appConfig.limits.freeTier.mappingLimit < 0 || appConfig.limits.proTier.mappingLimit < 0) {
                return Result.Error(IllegalArgumentException("Mapping limits cannot be negative"))
            }

            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error validating AppConfig", e)
            Result.Error(e)
        }
    }

    // ===== DEFAULT CREATION METHODS =====

    /**
     * Create default AppConfig domain model.
     */
    fun createDefaultAppConfig(): Result<AppConfig> {
        return try {
            val defaultConfig = AppConfig(
                versions = AppConfig.AppVersions(
                    minimum = "1.0.0",
                    recommended = "1.0.0",
                    latest = "1.0.0"
                ),
                features = AppConfig.AppFeatures(
                    useNewSyncSystem = true,
                    enableOfflineMode = true,
                    enableAnalytics = true,
                    enableBackgroundSync = true,
                    enforceVersionCheck = false
                ),
                limits = AppConfig.AppLimits(
                    freeTier = AppConfig.AppLimits.TierLimits(
                        mappingLimit = 100L,
                        importLimit = 10L,
                        exportLimit = 5L
                    ),
                    proTier = AppConfig.AppLimits.TierLimits(
                        mappingLimit = 1000L,
                        importLimit = 100L,
                        exportLimit = 50L
                    )
                ),
                sync = AppConfig.AppSync(
                    interval = 300L, // 5 minutes
                    backgroundInterval = 900L, // 15 minutes
                    maxBatchSize = 50L,
                    conflictStrategy = "server_wins"
                ),
                maintenance = AppConfig.AppMaintenance(
                    isInMaintenance = false,
                    maintenanceMessage = null,
                    estimatedEndTime = null
                ),
                updatedAt = OffsetDateTime.now(), // ✅ CRITICAL FIX: Set proper timestamp - null breaks Firestore queries
                version = 1L,
                customData = mapOf(
                    // Address Enhancement Configuration
                    "address_enhancement_enabled" to true, // ✅ ENABLED: Enable Google Address Validation API
                    "enhance_manual_orders" to true,
                    "enhance_delivery_edits" to true,
                    "enhance_address_edits" to true
                )
            )
            Result.Success(defaultConfig)
        } catch (e: Exception) {
            Log.e(TAG, "Error creating default AppConfig", e)
            Result.Error(e)
        }
    }

    /**
     * Create default NotificationPatterns domain model.
     */
    fun createDefaultNotificationPatterns(): Result<NotificationPatterns> {
        return try {
            val defaultPatterns = NotificationPatterns(
                extractors = NotificationPatterns.NotificationExtractors(
                    shipt = NotificationPatterns.NotificationExtractors.ShiptExtractor(
                        orderId = "Order #(\\d+)",
                        tipAmount = "\\$(\\d+\\.\\d{2})"
                    )
                ),
                patterns = NotificationPatterns.PlatformPatterns(
                    doordash = listOf(
                        "DoorDash.*tip.*\\$(\\d+\\.\\d{2})",
                        "You received.*\\$(\\d+\\.\\d{2}).*tip"
                    ),
                    shipt = listOf(
                        "Shipt.*tip.*\\$(\\d+\\.\\d{2})",
                        "Order #\\d+.*\\$(\\d+\\.\\d{2})"
                    ),
                    ubereats = listOf(
                        "Uber Eats.*tip.*\\$(\\d+\\.\\d{2})",
                        "Trip.*tip.*\\$(\\d+\\.\\d{2})"
                    )
                ),
                updatedAt = OffsetDateTime.now(),
                version = 1L
            )
            Result.Success(defaultPatterns)
        } catch (e: Exception) {
            Log.e(TAG, "Error creating default NotificationPatterns", e)
            Result.Error(e)
        }
    }

    // ===== HELPER METHODS =====

    /**
     * Convert App_config.CustomData to Map<String, Any>.
     */
    private fun convertCustomDataToMap(customData: AppConfigDto.CustomData): Map<String, Any> {
        // Since CustomData is an open class, we need to handle it carefully
        // For now, return empty map - this can be enhanced based on actual usage
        return emptyMap()
    }

    /**
     * Convert Map<String, Any> to App_config.CustomData.
     */
    private fun convertMapToCustomData(map: Map<String, Any>): AppConfigDto.CustomData? {
        // Since CustomData is an open class, we need to handle it carefully
        // For now, return null to avoid Firestore serialization issues
        return null // Fix: Don't instantiate empty CustomData class
    }
}