package com.autogratuity.data.model.state

sealed class RepositoryInitializationStatus {
    object NotStarted : RepositoryInitializationStatus()
    data class InProgress(val completedTasks: Int, val totalTasks: Int, val currentTaskMessage: String? = null) : RepositoryInitializationStatus()
    object Complete : RepositoryInitializationStatus()
    data class PartiallyComplete(val successfulTasks: List<String>, val failedTasks: Map<String, Throwable>) : RepositoryInitializationStatus()
    data class Failed(val error: Throwable, val message: String? = null) : RepositoryInitializationStatus()
} 