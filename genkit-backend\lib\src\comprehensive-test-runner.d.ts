interface TriggerTestResult {
    triggerName: string;
    testName: string;
    success: boolean;
    result?: any;
    error?: string;
    duration: number;
}
declare class FirestoreTriggerSimulator {
    private results;
    private firestore;
    runTriggerSimulations(): Promise<TriggerTestResult[]>;
    private simulateDeliveryWrittenTriggers;
    private simulateUserPreferencesChangeTrigger;
    private simulateGcsFileTrigger;
    private runTriggerTest;
    private printTriggerSummary;
}
declare class ComprehensiveTestRunner {
    runAllTests(): Promise<void>;
    private printOverallSummary;
}
export { ComprehensiveTestRunner, FirestoreTriggerSimulator };
