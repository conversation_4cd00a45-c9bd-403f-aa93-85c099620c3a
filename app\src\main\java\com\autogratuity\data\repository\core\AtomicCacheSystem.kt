package com.autogratuity.data.repository.core

import kotlinx.atomicfu.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable // Added for @Serializable annotation
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes
import kotlin.time.TimeSource
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Modern cache entry with comprehensive metadata and TTL support
 * following 2025 Kotlin standards with atomic operations
 */
data class CacheEntry<V>(
    val value: V,
    val timestamp: Instant,
    val expirationTime: Instant?,
    val metadata: Map<String, Any> = emptyMap(),
    val updateCount: Int = 1,
    val accessCount: Long = 0,
    val lastAccessed: Instant = timestamp,
    val source: CacheSource = CacheSource.MANUAL
) {
    fun isExpired(now: Instant = Clock.System.now()): Boolean =
        expirationTime?.let { it <= now } == true
        
    fun updateWith(
        newValue: V, 
        newMetadata: Map<String, Any> = emptyMap(),
        newSource: CacheSource = CacheSource.UPDATE
    ): CacheEntry<V> = copy(
        value = newValue,
        timestamp = Clock.System.now(),
        metadata = metadata + newMetadata,
        updateCount = updateCount + 1,
        lastAccessed = Clock.System.now(),
        source = newSource
    )
    
    fun accessed(): CacheEntry<V> = copy(
        accessCount = accessCount + 1,
        lastAccessed = Clock.System.now()
    )
}

/**
 * Cache entry source tracking for analytics and debugging
 */
enum class CacheSource {
    MANUAL,          // Manually added
    FIRESTORE,       // From Firestore listener
    CLOUD_FUNCTION,  // From cloud function trigger
    PREFETCH,        // From prefetch operation
    UPDATE,          // From update operation
    MERGE            // From merge operation
}

/**
 * Cache operation metrics for monitoring and optimization
 */
@Serializable
data class CacheMetrics(
    val hits: Long = 0L,
    val misses: Long = 0L,
    val puts: Long = 0L,
    val removes: Long = 0L,
    val expiredCleanups: Long = 0L,
    val totalOperations: Long = 0L
) {
    val hitRate: Double get() = if (totalOperations > 0) hits.toDouble() / totalOperations else 0.0
    val missRate: Double get() = if (totalOperations > 0) misses.toDouble() / totalOperations else 0.0
}

/**
 * Interface for domain-specific cache systems following the repository helper pattern
 */
interface DomainCacheSystem<K, V> {
    suspend fun get(key: K): V?
    suspend fun getWithMetadata(key: K): Pair<V, Map<String, Any>>?
    suspend fun put(key: K, value: V, ttl: Duration? = null, metadata: Map<String, Any> = emptyMap(), source: CacheSource = CacheSource.MANUAL)
    suspend fun update(key: K, value: V, ttl: Duration? = null, metadata: Map<String, Any> = emptyMap())
    suspend fun remove(key: K): V?
    suspend fun clear()
    suspend fun clearExpired()
    fun observe(key: K): Flow<V?>
    fun observeAll(): Flow<Map<K, V>>
    suspend fun getMetrics(): CacheMetrics
    suspend fun invalidateByPredicate(predicate: suspend (key: K, metadata: Map<String, Any>) -> Boolean)
}

/**
 * Modern atomic cache system implementation following 2025 Kotlin standards
 * with comprehensive features for enterprise-grade caching
 */
@Singleton
open class AtomicCacheSystem<K, V> @Inject constructor(
    private val timeSource: TimeSource = TimeSource.Monotonic
) : DomainCacheSystem<K, V> {
    
    // Atomic reference to ensure thread-safety with optimal performance
    private val cache = atomic<Map<K, CacheEntry<V>>>(emptyMap())
    
    // Atomic metrics tracking with explicit types
    // ✅ FIXED: Changed atomic(0L) to atomic(0) to resolve KSP compilation errors
    private val hits = atomic(0)
    private val misses = atomic(0)
    private val puts = atomic(0)
    private val removes = atomic(0)
    private val expiredCleanups = atomic(0)
    
    // MutableStateFlow to signal cache updates for reactive programming
    private val _cacheUpdates = MutableStateFlow(0)
    val cacheUpdates: StateFlow<Int> = _cacheUpdates.asStateFlow()
    
    // Configuration
    protected open val defaultTtl: Duration = 30.minutes
    protected open val maxCacheSize: Int = 1000
    protected open val enableMetrics: Boolean = true
    
    override suspend fun get(key: K): V? {
        val now = Clock.System.now()
        return cache.value[key]?.let { entry ->
            if (!entry.isExpired(now)) {
                // Update access tracking atomically with explicit types
                cache.update { currentCache: Map<K, CacheEntry<V>> ->
                    currentCache.plus(key to entry.accessed())
                }
                if (enableMetrics) hits.incrementAndGet()
                entry.value
            } else {
                // Lazily remove expired entries
                remove(key)
                if (enableMetrics) {
                    misses.incrementAndGet()
                    expiredCleanups.incrementAndGet()
                }
                null
            }
        } ?: run {
            if (enableMetrics) misses.incrementAndGet()
            null
        }
    }
    
    override suspend fun getWithMetadata(key: K): Pair<V, Map<String, Any>>? {
        val now = Clock.System.now()
        return cache.value[key]?.let { entry ->
            if (!entry.isExpired(now)) {
                // Update access tracking atomically with explicit types
                cache.update { currentCache: Map<K, CacheEntry<V>> ->
                    currentCache.plus(key to entry.accessed())
                }
                if (enableMetrics) hits.incrementAndGet()
                entry.value to entry.metadata
            } else {
                remove(key)
                if (enableMetrics) {
                    misses.incrementAndGet()
                    expiredCleanups.incrementAndGet()
                }
                null
            }
        } ?: run {
            if (enableMetrics) misses.incrementAndGet()
            null
        }
    }
    
    override suspend fun put(
        key: K, 
        value: V, 
        ttl: Duration?, 
        metadata: Map<String, Any>,
        source: CacheSource
    ) {
        // Enforce cache size limits with LRU eviction
        if (cache.value.size >= maxCacheSize) {
            evictLeastRecentlyUsed()
        }
        
        cache.update { currentCache: Map<K, CacheEntry<V>> ->
            val newEntry = CacheEntry(
                value = value,
                timestamp = Clock.System.now(),
                expirationTime = (ttl ?: defaultTtl).let { Clock.System.now() + it },
                metadata = metadata,
                source = source
            )
            currentCache.plus(key to newEntry)
        }
        
        if (enableMetrics) puts.incrementAndGet()
        notifyCacheUpdated()
    }
    
    override suspend fun update(key: K, value: V, ttl: Duration?, metadata: Map<String, Any>) {
        cache.update { currentCache: Map<K, CacheEntry<V>> ->
            val existingEntry = currentCache[key]
            if (existingEntry != null) {
                val newEntry = existingEntry.updateWith(value, metadata)
                    .let { entry ->
                        if (ttl != null) {
                            entry.copy(expirationTime = Clock.System.now() + ttl)
                        } else {
                            entry
                        }
                    }
                currentCache.plus(key to newEntry)
            } else {
                // Key doesn't exist, create a new entry
                currentCache.plus(key to CacheEntry(
                    value = value,
                    timestamp = Clock.System.now(),
                    expirationTime = (ttl ?: defaultTtl).let { Clock.System.now() + it },
                    metadata = metadata,
                    source = CacheSource.UPDATE
                ))
            }
        }
        
        if (enableMetrics) puts.incrementAndGet()
        notifyCacheUpdated()
    }
    
    override suspend fun remove(key: K): V? {
        var removedValue: V? = null
        cache.update { currentCache: Map<K, CacheEntry<V>> ->
            removedValue = currentCache[key]?.value
            currentCache.minus(key)
        }
        
        if (removedValue != null) {
            if (enableMetrics) removes.incrementAndGet()
            notifyCacheUpdated()
        }
        return removedValue
    }
    
    override suspend fun clear() {
        val previousSize = cache.value.size
        if (previousSize > 0) {
            cache.update { _: Map<K, CacheEntry<V>> -> emptyMap<K, CacheEntry<V>>() }
            if (enableMetrics) removes.addAndGet(previousSize)
            notifyCacheUpdated()
        }
    }
    
    override suspend fun clearExpired() {
        val now = Clock.System.now()
        cache.update { currentCache: Map<K, CacheEntry<V>> ->
            currentCache.filterValues { !it.isExpired(now) }
        }
        notifyCacheUpdated()
    }
    
    override suspend fun invalidateByPredicate(predicate: suspend (key: K, metadata: Map<String, Any>) -> Boolean) {
        val toRemove = mutableListOf<K>()
        
        for ((key, entry) in cache.value) {
            if (predicate(key, entry.metadata)) {
                toRemove.add(key)
            }
        }
        
        if (toRemove.isNotEmpty()) {
            cache.update { currentCache: Map<K, CacheEntry<V>> ->
                toRemove.fold(currentCache) { acc, key -> acc.minus(key) }
            }
            if (enableMetrics) removes.addAndGet(toRemove.size)
            notifyCacheUpdated()
        }
    }
    
    // Flow for observing specific keys
    override fun observe(key: K): Flow<V?> = cacheUpdates
        .map { get(key) }
        .distinctUntilChanged()
    
    // Flow for observing all cache entries
    override fun observeAll(): Flow<Map<K, V>> = cacheUpdates
        .map { cache.value.mapValues { (_, entry) -> entry.value } }
        .distinctUntilChanged()
    
    override suspend fun getMetrics(): CacheMetrics {
        val totalOps = hits.value.toLong() + misses.value.toLong() // Ensure Long operation
        return CacheMetrics(
            hits = hits.value.toLong(),
            misses = misses.value.toLong(),
            puts = puts.value.toLong(),
            removes = removes.value.toLong(),
            expiredCleanups = expiredCleanups.value.toLong(),
            totalOperations = totalOps
        )
    }
    
    /**
     * Evict the least recently used entry when cache is full
     */
    private suspend fun evictLeastRecentlyUsed() {
        val lruKey = cache.value.entries
            .minByOrNull { it.value.lastAccessed }
            ?.key
        
        lruKey?.let { remove(it) }
    }
    
    private fun notifyCacheUpdated() {
        _cacheUpdates.value = _cacheUpdates.value + 1
    }
    
    /**
     * Get cache size for monitoring
     */
    fun size(): Int = cache.value.size
    
    /**
     * Get detailed cache statistics for monitoring and debugging
     */
    suspend fun getDetailedStats(): Map<String, Any> {
        val metrics = getMetrics()
        return mapOf(
            "size" to size(),
            "maxSize" to maxCacheSize,
            "metrics" to metrics,
            "defaultTtl" to defaultTtl.toString(),
            "enableMetrics" to enableMetrics
        )
    }
} 