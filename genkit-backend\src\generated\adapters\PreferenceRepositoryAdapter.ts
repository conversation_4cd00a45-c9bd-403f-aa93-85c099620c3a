// Auto-generated from PreferenceRepository.kt
// IMPORTS FROM EXISTING GENERATED MODELS (not recreated)

import type { Result } from '../types/Result';
import type {
  Delivery as TsDeliveryInterface,
  Address as TsAddressInterface,
  User_profile as TsUserProfileInterface
} from '../../models/generated/delivery.schema';
import type {
  DeliveryStats as TsDeliveryStatsInterface
} from '../../models/generated/address.schema';

/**
 * TypeScript adapter interface generated from PreferenceRepository.kt
 * Maintains identical contract to Kotlin implementation
 * Uses existing generated models from schemas
 */
export interface PreferenceRepositoryAdapter {
  getCurrentUserPreferences(): Result<UserPreferences?>;
  getUserPreferences(userId: string): Result<UserPreferences?>;
  updateUserPreferences(userId: string, preferences: UserPreferences): Promise<Result<void>>;
  createDefaultPreferences(userId: string): Result<UserPreferences>;
  observeCurrentUserPreferences(): Flow<Result<UserPreferences?>>;
  observeUserPreferences(userId: string): Flow<Result<UserPreferences?>>;
  getThemePreference(): Result<string>;
  setThemePreference(theme: string): Promise<Result<void>>;
  observeThemePreference(): Flow<string>;
  getNotificationsEnabled(): Result<boolean>;
  setNotificationsEnabled(enabled: boolean): Promise<Result<void>>;
  observeNotificationsEnabled(): Flow<boolean>;
  isDataCollectionOptedIn(): Result<boolean>;
  setDataCollectionOptedIn(optedIn: boolean): Promise<Result<void>>;
  observeDataCollectionOptedIn(): Flow<boolean>;
  getDndSettings(): Result<DndDetails?>;
  updateDndSettings(dndDetails: DndDetails): Promise<Result<void>>;
  setDndEnabled(enabled: boolean): Promise<Result<void>>;
  setDndTipThreshold(threshold: number): Promise<Result<void>>;
  setDndComparisonType(comparisonType: string): Promise<Result<void>>;
  observeDndSettings(): Flow<DndDetails?>;
  isOnboardingCompleted(): Result<boolean>;
  setOnboardingCompleted(completed: boolean): Promise<Result<void>>;
  getDefaultAddressId(): Result<string>;
  setDefaultAddressId(addressId: string): Promise<Result<void>>;
  observeDefaultAddressId(): Flow<string>;
  isProUser(): Flow<Result<boolean>>;
  getDndMarkingsUsedCount(): Result<number>;
  getMaxDndMarkings(): Result<number>;
  getAutoCapturedOrdersCount(): Result<number>;
  setPreferenceSettings(settings: Map<string): Promise<Result<void>>;
  validatePreferences(preferences: UserPreferences): Promise<Result<void>>;
  resetToDefaults(): Promise<Result<void>>;
  clearCache(): Promise<Result<void>>;
  clearCache(userId: string): Promise<Result<void>>;
  prefetchCriticalData(): Promise<Result<void>>;
  initialize(): Promise<Result<void>>;
  cleanup(): Promise<Result<void>>;
}