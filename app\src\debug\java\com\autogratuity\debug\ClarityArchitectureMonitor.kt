package com.autogratuity.debug

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.async
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.coroutines.awaitAll
import kotlin.time.TimeSource
import kotlin.time.Duration
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import com.autogratuity.domain.repository.UserRepository
import com.autogratuity.domain.repository.SubscriptionRepository
import com.autogratuity.domain.repository.AddressRepository
import com.autogratuity.domain.repository.DeliveryRepository
import com.autogratuity.data.util.CacheWarmingManager
import kotlinx.coroutines.ExperimentalCoroutinesApi

/**
 * 🔍 CLARITY ARCHITECTURE MONITORING INSTRUMENTATION
 *
 * Provides zero-impact monitoring hooks for Clarity Architecture components.
 * Automatically tracks SSOT/DTO alignment, Firestore operations, and component interactions.
 *
 * Usage: Add monitoring calls to your existing components without changing business logic.
 */
@ExperimentalCoroutinesApi
object ClarityArchitectureMonitor : KoinComponent {

    private const val TAG = "CLARITY_MONITOR"
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // Repository dependencies for cache warming
    private val userRepository: UserRepository by inject()
    private val subscriptionRepository: SubscriptionRepository by inject()
    private val addressRepository: AddressRepository by inject()
    private val deliveryRepository: DeliveryRepository by inject()

    // ✅ ENHANCED: Use CacheWarmingManager for coordinated cache warming
    private val cacheWarmingManager: CacheWarmingManager by inject()

    // 🎛️ PERFORMANCE CONTROL: Smart monitoring with minimal overhead
    var isEnabled = true
        private set

    // 🚀 PERFORMANCE GATES: Only monitor what matters
    // 🔇 UI JANK LOG SPAM REDUCTION: Changed from FULL to CRITICAL_ONLY to reduce tedious UI jank logging
    private var monitoringLevel = MonitoringLevel.CRITICAL_ONLY
    private var lastMonitoringCheck = 0L
    private val monitoringCheckInterval = 5000L // 5 seconds

    enum class MonitoringLevel {
        OFF,           // No monitoring
        CRITICAL_ONLY, // Only errors, violations, and slow operations (>500ms) - NO UI JANK SPAM
        SMART,         // Adaptive based on performance impact
        FULL           // Everything (use sparingly) - INCLUDES UI JANK SPAM
    }

    // 📊 SAMPLING: Reduce overhead with intelligent sampling
    private var sampleRate = 1.0f // Monitor 100% of operations for debugging
    private val criticalOperations = setOf(
        "viewmodel_init", "firestore_write", "mapper_violation", "koin_injection",
        "cache_miss_slow", "stateflow_chain", "dependency_injection"
    )

    // SESSION CORRELATION: Track operations within user sessions
    private val sessionOperations = mutableMapOf<String, MutableList<String>>()
    private var currentSessionId: String? = null

    // PERFORMANCE BASELINES: Track operation performance over time
    private val operationBaselines = mutableMapOf<String, Long>() // operation -> baseline time in ms

    fun enable() { isEnabled = true }
    fun disable() { isEnabled = false }

    // SESSION MANAGEMENT: Track user sessions for correlation with cache warming
    fun startSession(userId: String) {
        currentSessionId = "${userId}_${System.currentTimeMillis()}"
        sessionOperations[currentSessionId!!] = mutableListOf()

        // ✅ ATOMIC CACHING INTEGRATION: Monitor app state and use safe accessors
        scope.launch {
            try {
                val app = com.autogratuity.AutogratuityApp.instance
                if (app != null) {
                    // Monitor repository initialization state
                    app.repositoryInitializedState.collect { isReady ->
                        Log.d(TAG, "SESSION: Repository initialization state: $isReady for session $currentSessionId")
                        if (isReady) {
                            // Start UI performance monitoring when repositories are ready
                            app.startUIPerformanceMonitoring()
                            Log.d(TAG, "SESSION: UI performance monitoring started for session $currentSessionId")
                        }
                    }

                    // Test safe accessors for system health monitoring
                    val authManager = app.getAuthManagerSafe()
                    val encryptionUtils = app.getEncryptionUtilsSafe()
                    val firebaseAuth = app.getFirebaseAuthSafe()
                    val cacheManager = app.getCacheLifecycleManagerSafe()

                    Log.d(TAG, "SESSION: System components verified for session $currentSessionId")
                    Log.d(TAG, "- AuthManager: ${authManager.javaClass.simpleName}")
                    Log.d(TAG, "- EncryptionUtils: ${encryptionUtils.javaClass.simpleName}")
                    Log.d(TAG, "- FirebaseAuth: ${firebaseAuth.javaClass.simpleName}")
                    Log.d(TAG, "- CacheLifecycleManager: ${cacheManager.javaClass.simpleName}")

                    // Get cache metrics for session baseline
                    val metrics = app.getAllCacheMetrics()
                    Log.d(TAG, "SESSION: Cache metrics baseline for $currentSessionId: $metrics")
                    SimpleClarityDashboard.logEvent("[SESSION_START] Cache metrics: $metrics")
                }
            } catch (e: Exception) {
                Log.w(TAG, "Could not monitor app state during session start", e)
            }
        }

        // ENHANCED: Trigger intelligent cache warming for critical data
        triggerCacheWarming(userId)

        Log.i(TAG, "SESSION STARTED: $currentSessionId with cache warming and app state monitoring")
        SimpleClarityDashboard.startSession(currentSessionId!!)
    }

    private fun triggerCacheWarming(userId: String) {
        if (!isEnabled) return
        scope.launch {
            val warmingStartTime = TimeSource.Monotonic.markNow()

            try {
                Log.i(TAG, "CACHE WARMING: Starting coordinated cache warming for user $userId")
                SimpleClarityDashboard.logEvent("[CACHE_WARMING] Starting coordinated cache warming for user: $userId")

                // ✅ ENHANCED: Use CacheWarmingManager for coordinated cache warming
                val warmingResult = cacheWarmingManager.warmCriticalData(userId)

                val warmingDuration = warmingStartTime.elapsedNow()

                // Format results for logging
                val warmingResults = listOf(
                    "user_profile:${formatWarmingStatus(warmingResult.userProfile)}",
                    "subscription:${formatWarmingStatus(warmingResult.subscription)}",
                    "addresses:${formatWarmingStatus(warmingResult.addresses)}",
                    "deliveries:${formatWarmingStatus(warmingResult.deliveries)}"
                )

                // Determine overall success
                val criticalSuccess = warmingResult.userProfile is CacheWarmingManager.WarmingStatus.Success &&
                                   warmingResult.subscription is CacheWarmingManager.WarmingStatus.Success

                // Log cache warming completion with detailed results
                SimpleClarityDashboard.logCacheWarming(
                    userId = userId,
                    cacheSystemName = "CacheWarmingManager",
                    operationsCount = 4, // user, subscription, addresses, deliveries
                    durationMs = warmingResult.totalTimeMs
                )

                // Track as operation correlation for session analysis
                monitorOperationCorrelation(
                    triggerOperation = "session_start",
                    relatedOperations = listOf("user_profile_warming", "subscription_warming", "addresses_warming", "deliveries_warming"),
                    totalDuration = warmingDuration,
                    success = criticalSuccess
                )

                Log.i(TAG, "CACHE WARMING: Completed for user $userId in ${warmingResult.totalTimeMs}ms")
                Log.i(TAG, "CACHE WARMING: Results - ${warmingResults.joinToString(", ")}")
                SimpleClarityDashboard.logEvent("[CACHE_WARMING] Completed for user $userId: ${warmingResults.joinToString(", ")}")

            } catch (e: Exception) {
                Log.e(TAG, "CACHE WARMING: Failed for user $userId", e)
                SimpleClarityDashboard.logEvent("[CACHE_WARMING] Failed for user $userId: ${e.message}")
            }
        }
    }

    private fun formatWarmingStatus(status: CacheWarmingManager.WarmingStatus): String {
        return when (status) {
            is CacheWarmingManager.WarmingStatus.Success -> "success:${status.durationMs}ms"
            is CacheWarmingManager.WarmingStatus.Failure -> "error:${status.durationMs}ms"
            is CacheWarmingManager.WarmingStatus.Timeout -> "timeout:${status.durationMs}ms"
            is CacheWarmingManager.WarmingStatus.Skipped -> "skipped"
        }
    }

    fun endSession() {
        currentSessionId?.let { sessionId ->
            val operations = sessionOperations[sessionId]

            // ✅ ATOMIC CACHING INTEGRATION: Get final cache metrics for session analysis
            scope.launch {
                try {
                    val app = com.autogratuity.AutogratuityApp.instance
                    if (app != null) {
                        val finalMetrics = app.getAllCacheMetrics()
                        Log.i(TAG, "SESSION ENDED: $sessionId with ${operations?.size ?: 0} operations")
                        Log.i(TAG, "SESSION FINAL METRICS: $finalMetrics")
                        SimpleClarityDashboard.logEvent("[SESSION_END] Final cache metrics: $finalMetrics")
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Could not get final cache metrics for session end", e)
                }
            }

            // Keep last 3 sessions for analysis
            if (sessionOperations.size > 3) {
                sessionOperations.remove(sessionOperations.keys.first())
            }
        }
        currentSessionId = null
    }

    private fun addToCurrentSession(operation: String) {
        currentSessionId?.let { sessionId ->
            sessionOperations[sessionId]?.add(operation)
        }
    }

    // PUBLIC API FOR EASY INTEGRATION

    /**
     * Public wrapper for logging events - delegates to SimpleClarityDashboard
     */
    fun logEvent(message: String) {
        SimpleClarityDashboard.logEvent(message)
    }

    /**
     * Public wrapper for adding session events
     */
    fun addSessionEvent(event: String) {
        addToCurrentSession(event)
        SimpleClarityDashboard.addSessionEvent(event)
    }

    /**
     * ✅ ATOMIC CACHING INTEGRATION: Comprehensive system health monitoring
     * Uses all AutogratuityApp monitoring methods and state properties
     */
    fun performSystemHealthCheck() {
        if (!isEnabled) return
        scope.launch {
            try {
                val app = com.autogratuity.AutogratuityApp.instance
                if (app != null) {
                    Log.i(TAG, "SYSTEM HEALTH CHECK: Starting comprehensive monitoring")

                    // Monitor all app state properties
                    val repositoryState = app.repositoryInitializedState.value
                    val userSessionState = app.userSessionComponentReadyState.value
                    val coreDataState = app.coreUserDataReadyState.value

                    Log.i(TAG, "SYSTEM STATE:")
                    Log.i(TAG, "- Repository Initialized: $repositoryState")
                    Log.i(TAG, "- User Session Ready: $userSessionState")
                    Log.i(TAG, "- Core Data Ready: $coreDataState")

                    // Test all safe accessor methods
                    val authManager = app.getAuthManagerSafe()
                    val encryptionUtils = app.getEncryptionUtilsSafe()
                    val firebaseAuth = app.getFirebaseAuthSafe()
                    val cacheManager = app.getCacheLifecycleManagerSafe()

                    Log.i(TAG, "SYSTEM COMPONENTS:")
                    Log.i(TAG, "- AuthManager: ${authManager.javaClass.simpleName}")
                    Log.i(TAG, "- EncryptionUtils: ${encryptionUtils.javaClass.simpleName}")
                    Log.i(TAG, "- FirebaseAuth: ${firebaseAuth.javaClass.simpleName}")
                    Log.i(TAG, "- CacheLifecycleManager: ${cacheManager.javaClass.simpleName}")

                    // Get comprehensive cache metrics
                    val cacheMetrics = app.getAllCacheMetrics()
                    Log.i(TAG, "CACHE METRICS: $cacheMetrics")

                    // ✅ ENHANCED STATS LOGGING: Break down cache metrics for better debugging
                    logEnhancedCacheMetrics(cacheMetrics)

                    // Start UI performance monitoring if not already started
                    app.startUIPerformanceMonitoring()
                    Log.i(TAG, "UI PERFORMANCE MONITORING: Started/Verified")

                    // Log to dashboard for visualization
                    SimpleClarityDashboard.logEvent("[SYSTEM_HEALTH] Repository:$repositoryState UserSession:$userSessionState CoreData:$coreDataState")
                    SimpleClarityDashboard.logEvent("[SYSTEM_HEALTH] Cache metrics: $cacheMetrics")

                    Log.i(TAG, "SYSTEM HEALTH CHECK: Completed successfully")
                } else {
                    Log.w(TAG, "SYSTEM HEALTH CHECK: AutogratuityApp instance not available")
                }
            } catch (e: Exception) {
                Log.e(TAG, "SYSTEM HEALTH CHECK: Failed", e)
                SimpleClarityDashboard.logEvent("[SYSTEM_HEALTH] ERROR: ${e.message}")
            }
        }
    }

    fun setMonitoringLevel(level: MonitoringLevel) {
        monitoringLevel = level
        sampleRate = when (level) {
            MonitoringLevel.OFF -> 0f
            MonitoringLevel.CRITICAL_ONLY -> 0.05f // 5%
            MonitoringLevel.SMART -> 0.1f // 10%
            MonitoringLevel.FULL -> 1f // 100%
        }
    }

    /**
     * 🎯 SMART DECISION: Should we monitor this operation?
     */
    private fun shouldMonitor(operation: String, durationMs: Long = 0): Boolean {
        if (!isEnabled) return false

        return when (monitoringLevel) {
            MonitoringLevel.OFF -> false
            MonitoringLevel.CRITICAL_ONLY -> {
                // Only monitor critical operations or slow ones
                operation in criticalOperations || durationMs > 500
            }
            MonitoringLevel.SMART -> {
                // Smart sampling with bias toward important operations
                if (operation in criticalOperations) true
                else if (durationMs > 100) true // Always monitor slow operations
                else Math.random() < sampleRate
            }
            MonitoringLevel.FULL -> true
        }
    }

    // 🏗️ ARCHITECTURE LAYER MONITORING

    /**
     * Monitor Repository operations (Data Layer) with enhanced context
     */
    fun monitorRepositoryOperation(
        repositoryClass: String,
        operation: String,
        duration: Duration,
        success: Boolean,
        error: Throwable? = null,
        cacheHit: Boolean = false,
        dataType: String = "unknown",
        entityId: String? = null,
        userId: String? = null,
        resultCount: Int? = null,
        dataSource: String? = null,
        dataSize: Int? = null,
        queryDetails: String? = null,
        transformationTime: Duration? = null,
        cacheStrategy: String? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            // Build comprehensive operation description
            val details = buildString {
                append("$operation($dataType)")
                entityId?.let { append(" ID:$it") }
                userId?.let { append(" User:$it") }
                resultCount?.let { append(" Count:$it") }
                dataSize?.let { append(" Size:${it}bytes") }
                dataSource?.let { append(" Source:$it") }
                queryDetails?.let { append(" Query:$it") }
                cacheStrategy?.let { append(" Strategy:$it") }
                transformationTime?.let { append(" Transform:${it.inWholeMilliseconds}ms") }
                if (cacheHit) append(" [CACHE_HIT]") else append(" [CACHE_MISS]")
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "data",
                component = repositoryClass,
                operation = details,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )

            if (cacheHit) {
                SimpleClarityDashboard.logCacheHit()
            } else {
                SimpleClarityDashboard.logCacheMiss()
            }
        }
    }

    /**
     * Monitor ViewModel operations (Presentation Layer) with enhanced UI performance context
     */
    fun monitorViewModelOperation(
        viewModelClass: String,
        operation: String,
        duration: Duration,
        success: Boolean,
        error: Throwable? = null,
        stateFlowUpdated: Boolean = false,
        dependenciesInjected: Int = 0,
        repositoriesUsed: List<String> = emptyList(),
        dataLoaded: String? = null,
        uiState: String? = null,
        errorDetails: String? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            val operationKey = "$viewModelClass.$operation"
            addToCurrentSession(operationKey)

            // Build detailed ViewModel operation description
            val details = buildString {
                append(operation)
                if (dependenciesInjected > 0) append(" Deps:$dependenciesInjected")
                if (repositoriesUsed.isNotEmpty()) append(" Repos:[${repositoriesUsed.joinToString(",")}]")
                dataLoaded?.let { append(" Data:$it") }
                uiState?.let { append(" State:$it") }
                if (stateFlowUpdated) append(" [STATEFLOW_UPDATED]")
                errorDetails?.let { append(" Error:$it") }
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "presentation",
                component = viewModelClass,
                operation = details,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )

            // UI Performance Analysis
            val threshold = when (operation) {
                "init" -> 50L
                "loadData", "refreshData" -> 100L
                "updateState" -> 20L
                else -> 30L
            }

            if (duration.inWholeMilliseconds > threshold) {
                Log.w(TAG, "SLOW ViewModel Operation: $viewModelClass.$operation took ${duration.inWholeMilliseconds}ms (threshold: ${threshold}ms)")
                if (repositoriesUsed.isNotEmpty()) {
                    Log.w(TAG, "   Repositories involved: ${repositoriesUsed.joinToString(", ")}")
                }
            }

            if (!success || error != null) {
                Log.e(TAG, "ViewModel Operation Failed: $viewModelClass.$operation", error)
                errorDetails?.let {
                    Log.e(TAG, "   Error Details: $it")
                }
            }
        }
    }

    /**
     * Monitor UseCase operations (Domain Layer)
     */
    fun monitorUseCaseOperation(
        useCaseClass: String,
        operation: String,
        duration: Duration,
        success: Boolean,
        error: Throwable? = null,
        repositoriesUsed: List<String> = emptyList()
    ) {
        if (!isEnabled) return
        scope.launch {
            SimpleClarityDashboard.logArchitectureEvent(
                layer = "domain",
                component = useCaseClass,
                operation = operation,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )
        }
    }

    // 🔥 FIRESTORE OPERATION MONITORING

    /**
     * Monitor Firestore read operations with detailed document context
     */
    fun monitorFirestoreRead(
        collection: String,
        documentId: String?,
        duration: Duration,
        success: Boolean,
        dataSizeBytes: Int = 0,
        error: Throwable? = null,
        fullPath: String? = null,
        queryType: String = "GET", // GET, QUERY, LISTEN
        queryFilters: List<String> = emptyList(),
        resultCount: Int? = null,
        userId: String? = null,
        cacheSource: String? = null // SERVER, CACHE, DEFAULT
    ) {
        if (!isEnabled) return
        scope.launch {
            // Build detailed Firestore read description
            val pathInfo = fullPath ?: if (documentId != null) "$collection/$documentId" else collection
            val details = buildString {
                append("$queryType:$pathInfo")
                userId?.let { append(" User:$it") }
                resultCount?.let { append(" Results:$it") }
                if (dataSizeBytes > 0) append(" Size:${dataSizeBytes}bytes")
                cacheSource?.let { append(" Source:$it") }
                if (queryFilters.isNotEmpty()) append(" Filters:[${queryFilters.joinToString(",")}]")
            }

            SimpleClarityDashboard.logFirestoreOperation(
                operation = "READ",
                collection = details,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )

            // Log detailed Firestore path information
            Log.i(TAG, "FIRESTORE READ: $details (${duration.inWholeMilliseconds}ms)")

            if (!success || error != null) {
                Log.e(TAG, "FIRESTORE READ FAILED: $pathInfo", error)
            }

            if (duration.inWholeMilliseconds > 1000) {
                Log.w(TAG, "SLOW FIRESTORE READ: $pathInfo took ${duration.inWholeMilliseconds}ms")
            }
        }
    }

    /**
     * Monitor Firestore write operations with detailed document context
     */
    fun monitorFirestoreWrite(
        collection: String,
        documentId: String?,
        duration: Duration,
        success: Boolean,
        dataSizeBytes: Int = 0,
        error: Throwable? = null,
        fullPath: String? = null,
        writeType: String = "SET", // SET, UPDATE, DELETE, BATCH, TRANSACTION
        fieldsUpdated: List<String> = emptyList(),
        userId: String? = null,
        documentData: Map<String, Any?>? = null,
        transactionId: String? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            // Build detailed Firestore write description
            val pathInfo = fullPath ?: if (documentId != null) "$collection/$documentId" else "$collection/[auto-id]"
            val details = buildString {
                append("$writeType:$pathInfo")
                userId?.let { append(" User:$it") }
                if (dataSizeBytes > 0) append(" Size:${dataSizeBytes}bytes")
                if (fieldsUpdated.isNotEmpty()) append(" Fields:[${fieldsUpdated.joinToString(",")}]")
                transactionId?.let { append(" Tx:$it") }
                documentData?.let { data ->
                    val fieldCount = data.size
                    append(" DataFields:$fieldCount")
                    // Log first few field names for debugging (without values for privacy)
                    val fieldNames = data.keys.take(3).joinToString(",")
                    if (fieldNames.isNotEmpty()) append(" [$fieldNames${if (data.size > 3) "..." else ""}]")
                }
            }

            SimpleClarityDashboard.logFirestoreOperation(
                operation = "WRITE",
                collection = details,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )

            // Log detailed Firestore write information
            Log.i(TAG, "FIRESTORE WRITE: $details (${duration.inWholeMilliseconds}ms)")

            if (!success || error != null) {
                Log.e(TAG, "FIRESTORE WRITE FAILED: $pathInfo", error)
            }

            if (duration.inWholeMilliseconds > 2000) {
                Log.w(TAG, "SLOW FIRESTORE WRITE: $pathInfo took ${duration.inWholeMilliseconds}ms")
            }
        }
    }

    /**
     * Monitor Firestore query operations
     */
    fun monitorFirestoreQuery(
        collection: String,
        queryDescription: String,
        duration: Duration,
        success: Boolean,
        resultCount: Int = 0,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            SimpleClarityDashboard.logFirestoreOperation(
                operation = "QUERY",
                collection = collection,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )
        }
    }

    // 🎯 VIEWMODEL PERFORMANCE MONITORING

    /**
     * Monitor ViewModel initialization and lifecycle performance
     */
    fun monitorViewModelInit(
        viewModelClass: String,
        duration: Duration,
        success: Boolean,
        dependenciesCount: Int = 0,
        stateFlowsInitialized: Int = 0,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            SimpleClarityDashboard.logArchitectureEvent(
                layer = "presentation",
                component = viewModelClass,
                operation = "init",
                durationMs = duration.inWholeMilliseconds,
                success = success
            )

            if (duration.inWholeMilliseconds > 100) {
                Log.w(TAG, "SLOW ViewModel Init: $viewModelClass took ${duration.inWholeMilliseconds}ms")
            }

            if (!success || error != null) {
                Log.e(TAG, "❌ ViewModel Init Failed: $viewModelClass", error)
            }
        }
    }

    /**
     * Monitor StateFlow operations and performance
     */
    fun monitorStateFlowOperation(
        viewModelClass: String,
        stateFlowName: String,
        operation: String, // "collect", "emit", "combine", "transform"
        duration: Duration,
        success: Boolean,
        subscriberCount: Int = 0,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            SimpleClarityDashboard.logArchitectureEvent(
                layer = "presentation",
                component = "$viewModelClass.$stateFlowName",
                operation = operation,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )

            if (duration.inWholeMilliseconds > 50) {
                Log.w(TAG, "SLOW StateFlow $operation: $viewModelClass.$stateFlowName took ${duration.inWholeMilliseconds}ms")
            }
        }
    }

    /**
     * Monitor Fragment lifecycle and UI performance
     */
    fun monitorFragmentLifecycle(
        fragmentClass: String,
        lifecycle: String, // "onCreate", "onCreateView", "onViewCreated", "onStart", "onResume", "onPause", "onStop", "onDestroyView", "onDestroy"
        duration: Duration,
        success: Boolean,
        viewBindingTime: Duration? = null,
        observerSetupTime: Duration? = null,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            SimpleClarityDashboard.logArchitectureEvent(
                layer = "presentation",
                component = fragmentClass,
                operation = lifecycle,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )

            // Flag slow lifecycle operations
            val threshold = when (lifecycle) {
                "onCreateView" -> 50L
                "onViewCreated" -> 100L
                "onStart", "onResume" -> 30L
                else -> 20L
            }

            if (duration.inWholeMilliseconds > threshold) {
                Log.w(TAG, "SLOW Fragment $lifecycle: $fragmentClass took ${duration.inWholeMilliseconds}ms")
                viewBindingTime?.let {
                    Log.w(TAG, "   ViewBinding: ${it.inWholeMilliseconds}ms")
                }
                observerSetupTime?.let {
                    Log.w(TAG, "   Observer Setup: ${it.inWholeMilliseconds}ms")
                }
            }
        }
    }

    // �🔄 SSOT/DTO MAPPING MONITORING

    /**
     * Monitor DTO to SSOT mapping operations with enhanced transformation context
     */
    fun monitorDtoToSsotMapping(
        mapperClass: String,
        entityType: String,
        duration: Duration,
        success: Boolean,
        piiFieldsProcessed: Int = 0,
        validationErrors: List<String> = emptyList(),
        fieldMismatches: List<String> = emptyList(),
        error: Throwable? = null,
        entityId: String? = null,
        userId: String? = null,
        dataSize: Int? = null,
        encryptionTime: Duration? = null,
        fieldsTransformed: Int = 0,
        businessLogicApplied: List<String> = emptyList(),
        cacheUpdated: Boolean = false
    ) {
        if (!isEnabled) return
        scope.launch {
            val issues = validationErrors.size + fieldMismatches.size

            // Build comprehensive mapping description
            val details = buildString {
                append("toSsot($entityType)")
                entityId?.let { append(" ID:$it") }
                userId?.let { append(" User:$it") }
                dataSize?.let { append(" Size:${it}bytes") }
                if (fieldsTransformed > 0) append(" Fields:$fieldsTransformed")
                if (piiFieldsProcessed > 0) append(" PII:$piiFieldsProcessed")
                encryptionTime?.let { append(" Decrypt:${it.inWholeMilliseconds}ms") }
                if (businessLogicApplied.isNotEmpty()) append(" Logic:[${businessLogicApplied.joinToString(",")}]")
                if (cacheUpdated) append(" [CACHE_UPDATED]")
                if (issues > 0) append(" Issues:$issues")
            }

            SimpleClarityDashboard.logSsotDtoMapping(
                mapper = mapperClass,
                operation = details,
                entity = entityType,
                durationMs = duration.inWholeMilliseconds,
                success = success,
                issues = issues
            )

            if (!success || fieldMismatches.isNotEmpty() || validationErrors.isNotEmpty()) {
                Log.w(TAG, "SSOT/DTO Alignment Issue in $mapperClass for $entityType:")
                if (fieldMismatches.isNotEmpty()) {
                    Log.w(TAG, "   Field Mismatches: ${fieldMismatches.joinToString(", ")}")
                }
                if (validationErrors.isNotEmpty()) {
                    Log.w(TAG, "   Validation Errors: ${validationErrors.joinToString(", ")}")
                }
                if (error != null) {
                    Log.w(TAG, "   Exception: ${error.message}", error)
                }
            }
        }
    }

    /**
     * Monitor SSOT to DTO mapping operations
     */
    fun monitorSsotToDtoMapping(
        mapperClass: String,
        entityType: String,
        duration: Duration,
        success: Boolean,
        piiFieldsProcessed: Int = 0,
        validationErrors: List<String> = emptyList(),
        fieldMismatches: List<String> = emptyList(),
        error: Throwable? = null,
        entityId: String? = null,
        userId: String? = null,
        dataSize: Int? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            val issues = validationErrors.size + fieldMismatches.size

            // Build detailed mapping description
            val details = buildString {
                append("toDto $entityType")
                entityId?.let { append(" ID:$it") }
                userId?.let { append(" User:$it") }
                dataSize?.let { append(" Size:${it}bytes") }
                if (piiFieldsProcessed > 0) append(" PII:$piiFieldsProcessed")
                if (issues > 0) append(" Issues:$issues")
            }

            SimpleClarityDashboard.logSsotDtoMapping(
                mapper = mapperClass,
                operation = details,
                entity = entityType,
                durationMs = duration.inWholeMilliseconds,
                success = success,
                issues = issues
            )

            if (!success || fieldMismatches.isNotEmpty() || validationErrors.isNotEmpty()) {
                Log.w(TAG, "SSOT/DTO Alignment Issue in $mapperClass for $entityType:")
                if (fieldMismatches.isNotEmpty()) {
                    Log.w(TAG, "   Field Mismatches: ${fieldMismatches.joinToString(", ")}")
                }
                if (validationErrors.isNotEmpty()) {
                    Log.w(TAG, "   Validation Errors: ${validationErrors.joinToString(", ")}")
                }
                if (error != null) {
                    Log.w(TAG, "   Exception: ${error.message}", error)
                }
            }
        }
    }

    // 🎯 CONVENIENCE FUNCTIONS FOR EASY INTEGRATION

    /**
     * 🚀 HIGH-PERFORMANCE: Inline monitoring with minimal overhead
     * Only executes if monitoring is enabled - zero cost when disabled
     */
    internal inline fun <T> monitorIfEnabled(
        layer: String,
        component: String,
        operation: String,
        crossinline block: () -> T
    ): T {
        return if (!isEnabled) {
            block()
        } else {
            val timeSource = TimeSource.Monotonic.markNow()
            var success = true
            try {
                val result = block()
                success = true
                result
            } catch (e: Throwable) {
                success = false
                throw e
            } finally {
                if (isEnabled) { // Double-check in case it was disabled during execution
                    scope.launch {
                        SimpleClarityDashboard.logArchitectureEvent(
                            layer = layer,
                            component = component,
                            operation = operation,
                            durationMs = timeSource.elapsedNow().inWholeMilliseconds,
                            success = success
                        )
                    }
                }
            }
        }
    }

    /**
     * Measure and monitor any operation with automatic timing
     */
    @Suppress("unused")
    fun <T> measureOperation(
        layer: String,
        component: String,
        operation: String,
        block: () -> T
    ): T {
        val timeSource = TimeSource.Monotonic.markNow()
        var success = true
        var error: Throwable? = null

        return try {
            block()
        } catch (e: Throwable) {
            success = false
            error = e
            throw e
        } finally {
            val duration = timeSource.elapsedNow()
            scope.launch {
                SimpleClarityDashboard.logArchitectureEvent(
                    layer = layer,
                    component = component,
                    operation = operation,
                    durationMs = duration.inWholeMilliseconds,
                    success = success
                )
            }
        }
    }

    /**
     * Measure and monitor suspend operations
     */
    @Suppress("unused")
    suspend fun <T> measureSuspendOperation(
        layer: String,
        component: String,
        operation: String,
        block: suspend () -> T
    ): T {
        val timeSource = TimeSource.Monotonic.markNow()
        var success = true
        var error: Throwable? = null

        return try {
            block()
        } catch (e: Throwable) {
            success = false
            error = e
            throw e
        } finally {
            val duration = timeSource.elapsedNow()
            SimpleClarityDashboard.logArchitectureEvent(
                layer = layer,
                component = component,
                operation = operation,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )
        }
    }

    /**
     * Monitor KOIN dependency injection performance
     */
    fun monitorKoinInjection(
        componentClass: String,
        dependencyType: String,
        duration: Duration,
        success: Boolean,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            SimpleClarityDashboard.logArchitectureEvent(
                layer = "injection",
                component = componentClass,
                operation = "inject_$dependencyType",
                durationMs = duration.inWholeMilliseconds,
                success = success
            )

            // 🔇 UI JANK LOG SPAM REDUCTION: Commented out KOIN injection performance logging
            // if (duration.inWholeMilliseconds > 10) {
            //     Log.w(TAG, "SLOW KOIN Injection: $componentClass -> $dependencyType took ${duration.inWholeMilliseconds}ms")
            // }

            // if (!success || error != null) {
            //     Log.e(TAG, "KOIN Injection Failed: $componentClass -> $dependencyType", error)
            // }
        }
    }

    /**
     * Monitor Firestore operations for SSOT/DTO alignment tracking
     */
    fun monitorFirestoreOperation(
        operation: String,
        collection: String,
        duration: Duration,
        success: Boolean,
        error: Throwable? = null,
        documentId: String? = null,
        userId: String? = null,
        dataSize: Int? = null,
        queryDetails: String? = null
    ) {
        if (!isEnabled) return

        scope.launch {
            // Build detailed Firestore operation description
            val details = buildString {
                append("$operation:$collection")
                documentId?.let { append("/doc:$it") }
                userId?.let { append(" User:$it") }
                dataSize?.let { append(" Size:${it}bytes") }
                queryDetails?.let { append(" Query:$it") }
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "data",
                component = "Firestore",
                operation = details,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )

            // Always log slow Firestore operations
            if (duration.inWholeMilliseconds > 500) {
                Log.w(TAG, "SLOW Firestore $operation: $collection took ${duration.inWholeMilliseconds}ms")
            }

            // Always log Firestore errors
            if (!success || error != null) {
                Log.e(TAG, "❌ Firestore $operation Failed: $collection", error)
            }
        }
    }

    /**
     * Monitor cache operations and hit/miss ratios
     */
    fun monitorCacheOperation(
        cacheSystem: String,
        operation: String, // "get", "put", "invalidate", "clear"
        key: String,
        duration: Duration,
        success: Boolean,
        hit: Boolean = false,
        cacheSize: Int = 0,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            SimpleClarityDashboard.logArchitectureEvent(
                layer = "cache",
                component = cacheSystem,
                operation = operation,
                durationMs = duration.inWholeMilliseconds,
                success = success
            )

            if (hit) {
                SimpleClarityDashboard.logCacheHit()
            } else if (operation == "get") {
                SimpleClarityDashboard.logCacheMiss()
            }

            if (duration.inWholeMilliseconds > 5) {
                Log.w(TAG, "🐌 SLOW Cache $operation: $cacheSystem[$key] took ${duration.inWholeMilliseconds}ms")
            }
        }
    }

    /**
     * Monitor architectural violations and compliance
     */
    fun monitorArchitecturalViolation(
        violationType: String, // "PRESENTATION_USES_DTO", "DOMAIN_DEPENDS_ON_DATA", "MAPPER_BYPASSED", "PII_LEAK"
        component: String,
        description: String,
        severity: String = "ERROR" // "WARNING", "ERROR", "CRITICAL"
    ) {
        if (!isEnabled) return
        scope.launch {
            Log.e(TAG, "🚨 ARCHITECTURAL VIOLATION [$severity]: $violationType in $component - $description")

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "violation",
                component = component,
                operation = violationType,
                durationMs = 0,
                success = false
            )
        }
    }

    /**
     * Validate SSOT/DTO field alignment
     */
    @Suppress("unused")
    fun validateFieldAlignment(
        ssotFields: Set<String>,
        dtoFields: Set<String>,
        entityType: String
    ): Pair<List<String>, List<String>> {
        val missingInDto = ssotFields - dtoFields
        val extraInDto = dtoFields - ssotFields

        val mismatches = mutableListOf<String>()
        if (missingInDto.isNotEmpty()) {
            mismatches.addAll(missingInDto.map { "Missing in DTO: $it" })
        }
        if (extraInDto.isNotEmpty()) {
            mismatches.addAll(extraInDto.map { "Extra in DTO: $it" })
        }

        if (mismatches.isNotEmpty()) {
            Log.w(TAG, "🔍 Field alignment issues for $entityType: ${mismatches.joinToString(", ")}")

            // Log as architectural violation if significant mismatches
            if (mismatches.size > 2) {
                monitorArchitecturalViolation(
                    violationType = "SSOT_DTO_MISALIGNMENT",
                    component = entityType,
                    description = "Significant field mismatches: ${mismatches.size} issues",
                    severity = "WARNING"
                )
            }
        }

        return Pair(missingInDto.toList(), extraInDto.toList())
    }

    // 🎯 ENHANCED PERFORMANCE MONITORING FOR SPECIFIC ISSUES

    /**
     * Monitor ViewModel dependency injection performance - TARGETED for heavy injection patterns
     */
    fun monitorViewModelDependencyInjection(
        viewModelClass: String,
        dependenciesCount: Int,
        repositoriesCount: Int,
        injectionDuration: Duration,
        success: Boolean,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            val operationKey = "$viewModelClass.dependency_injection"
            addToCurrentSession(operationKey)

            // ✅ ATOMIC CACHING INTEGRATION: Monitor cache metrics during ViewModel injection
            try {
                val app = com.autogratuity.AutogratuityApp.instance
                if (app != null) {
                    // Use AutogratuityApp cache metrics for monitoring
                    val cacheMetrics = app.getAllCacheMetrics()
                    Log.d(TAG, "Cache metrics during $viewModelClass injection: $cacheMetrics")
                }
            } catch (e: Exception) {
                Log.w(TAG, "Could not get cache metrics during ViewModel injection", e)
            }

            // Build detailed injection description
            val details = buildString {
                append("inject_dependencies")
                append(" Deps:$dependenciesCount")
                append(" Repos:$repositoriesCount")
                if (injectionDuration.inWholeMilliseconds > 20) append(" [SLOW_INJECTION]")
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "presentation",
                component = viewModelClass,
                operation = details,
                durationMs = injectionDuration.inWholeMilliseconds,
                success = success
            )

            // Flag heavy injection patterns
            if (dependenciesCount > 4 || repositoriesCount > 3) {
                Log.w(TAG, "HEAVY INJECTION: $viewModelClass has $dependenciesCount deps ($repositoriesCount repos)")
            }

            if (injectionDuration.inWholeMilliseconds > 20) {
                Log.w(TAG, "SLOW INJECTION: $viewModelClass took ${injectionDuration.inWholeMilliseconds}ms")
            }
        }
    }

    /**
     * Monitor StateFlow chain performance - TARGETED for complex reactive chains
     */
    fun monitorStateFlowChain(
        viewModelClass: String,
        chainName: String,
        chainLength: Int,
        emissionDuration: Duration,
        subscriberCount: Int,
        success: Boolean,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            val operationKey = "$viewModelClass.$chainName.stateflow_chain"
            addToCurrentSession(operationKey)

            // Build detailed chain description
            val details = buildString {
                append("stateflow_chain")
                append(" Chain:$chainLength")
                append(" Subs:$subscriberCount")
                if (emissionDuration.inWholeMilliseconds > 30) append(" [SLOW_EMISSION]")
                if (chainLength > 3) append(" [COMPLEX_CHAIN]")
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "presentation",
                component = "$viewModelClass.$chainName",
                operation = details,
                durationMs = emissionDuration.inWholeMilliseconds,
                success = success
            )

            // 🔇 UI JANK LOG SPAM REDUCTION: Commented out verbose StateFlow chain logging
            // Flag complex or slow chains
            // if (chainLength > 3) {
            //     Log.w(TAG, "COMPLEX CHAIN: $viewModelClass.$chainName has $chainLength operators")
            // }

            // if (emissionDuration.inWholeMilliseconds > 30) {
            //     Log.w(TAG, "SLOW EMISSION: $viewModelClass.$chainName took ${emissionDuration.inWholeMilliseconds}ms")
            // }
        }
    }

    /**
     * Monitor cache performance with detailed miss analysis - TARGETED for slow cache operations
     */
    fun monitorCachePerformance(
        repositoryClass: String,
        operation: String,
        cacheHit: Boolean,
        cacheDuration: Duration,
        remoteDuration: Duration? = null,
        transformationDuration: Duration? = null,
        entityType: String,
        entityId: String? = null,
        userId: String? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            val operationKey = "$repositoryClass.$operation.cache_performance"
            addToCurrentSession(operationKey)

            // Build detailed cache performance description
            val details = buildString {
                append("cache_$operation")
                append(" Hit:$cacheHit")
                append(" Cache:${cacheDuration.inWholeMilliseconds}ms")
                remoteDuration?.let { append(" Remote:${it.inWholeMilliseconds}ms") }
                transformationDuration?.let { append(" Transform:${it.inWholeMilliseconds}ms") }
                entityId?.let { append(" ID:$it") }
                if (!cacheHit && (remoteDuration?.inWholeMilliseconds ?: 0) > 1000) append(" [SLOW_REMOTE]")
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "data",
                component = repositoryClass,
                operation = details,
                durationMs = cacheDuration.inWholeMilliseconds + (remoteDuration?.inWholeMilliseconds ?: 0),
                success = true
            )

            // Flag performance issues
            if (!cacheHit && (remoteDuration?.inWholeMilliseconds ?: 0) > 1000) {
                Log.w(TAG, "SLOW CACHE MISS: $repositoryClass.$operation took ${remoteDuration?.inWholeMilliseconds}ms from remote")
            }

            if ((transformationDuration?.inWholeMilliseconds ?: 0) > 50) {
                Log.w(TAG, "SLOW TRANSFORMATION: $repositoryClass.$operation transformation took ${transformationDuration?.inWholeMilliseconds}ms")
            }
        }
    }

    /**
     * Monitor operation correlation within sessions - TARGETED for end-to-end tracing
     */
    fun monitorOperationCorrelation(
        triggerOperation: String,
        relatedOperations: List<String>,
        totalDuration: Duration,
        success: Boolean
    ) {
        if (!isEnabled) return
        scope.launch {
            currentSessionId?.let { sessionId ->
                val details = buildString {
                    append("correlation")
                    append(" Trigger:$triggerOperation")
                    append(" Related:${relatedOperations.size}")
                    append(" Total:${totalDuration.inWholeMilliseconds}ms")
                    if (totalDuration.inWholeMilliseconds > 500) append(" [SLOW_CORRELATION]")
                }

                SimpleClarityDashboard.logArchitectureEvent(
                    layer = "correlation",
                    component = "SessionTracker",
                    operation = details,
                    durationMs = totalDuration.inWholeMilliseconds,
                    success = success
                )

                Log.i(TAG, "OPERATION CORRELATION in $sessionId: $triggerOperation -> ${relatedOperations.joinToString(", ")} (${totalDuration.inWholeMilliseconds}ms)")
            }
        }
    }

    /**
     * Monitor detailed cache system performance breakdown - TARGETED for cache bottleneck analysis
     */
    fun monitorCacheSystemPerformance(
        repositoryClass: String,
        operation: String,
        cacheCheckDuration: Duration?,
        remoteFetchDuration: Duration? = null,
        mappingDuration: Duration? = null,
        cacheStoreDuration: Duration? = null,
        cacheHit: Boolean,
        entityType: String,
        entityId: String? = null,
        userId: String? = null,
        cacheMetrics: Map<String, Any>? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            val operationKey = "$repositoryClass.$operation.cache_breakdown"
            addToCurrentSession(operationKey)

            // Build detailed cache performance breakdown
            val details = buildString {
                append("cache_breakdown")
                append(" Check:${cacheCheckDuration?.inWholeMilliseconds ?: 0}ms")
                remoteFetchDuration?.let { append(" Remote:${it.inWholeMilliseconds}ms") }
                mappingDuration?.let { append(" Map:${it.inWholeMilliseconds}ms") }
                cacheStoreDuration?.let { append(" Store:${it.inWholeMilliseconds}ms") }
                append(" Hit:$cacheHit")
                entityId?.let { append(" ID:$it") }
                userId?.let { append(" User:$it") }

                // Add cache metrics if available
                cacheMetrics?.let { metrics ->
                    metrics["hitRate"]?.let { append(" HitRate:$it") }
                    metrics["totalOps"]?.let { append(" Ops:$it") }
                }

                // Flag performance issues
                val totalTime = (cacheCheckDuration?.inWholeMilliseconds ?: 0) +
                               (remoteFetchDuration?.inWholeMilliseconds ?: 0) +
                               (mappingDuration?.inWholeMilliseconds ?: 0) +
                               (cacheStoreDuration?.inWholeMilliseconds ?: 0)

                if (!cacheHit && (remoteFetchDuration?.inWholeMilliseconds ?: 0) > 1000) append(" [SLOW_REMOTE]")
                if ((mappingDuration?.inWholeMilliseconds ?: 0) > 50) append(" [SLOW_MAPPING]")
                if (totalTime > 500) append(" [SLOW_TOTAL]")
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "cache_system",
                component = repositoryClass,
                operation = details,
                durationMs = (cacheCheckDuration?.inWholeMilliseconds ?: 0) +
                           (remoteFetchDuration?.inWholeMilliseconds ?: 0) +
                           (mappingDuration?.inWholeMilliseconds ?: 0) +
                           (cacheStoreDuration?.inWholeMilliseconds ?: 0),
                success = true
            )

            // 🔇 UI JANK LOG SPAM REDUCTION: Commented out verbose cache breakdown logging
            // Enhanced logging for cache performance analysis
            // Log.i(TAG, "CACHE BREAKDOWN: $repositoryClass.$operation")
            // Log.i(TAG, "  Cache Check: ${cacheCheckDuration?.inWholeMilliseconds ?: 0}ms")
            // remoteFetchDuration?.let { Log.i(TAG, "  Remote Fetch: ${it.inWholeMilliseconds}ms") }
            // mappingDuration?.let { Log.i(TAG, "  DTO->Domain Mapping: ${it.inWholeMilliseconds}ms") }
            // cacheStoreDuration?.let { Log.i(TAG, "  Cache Store: ${it.inWholeMilliseconds}ms") }

            // Flag critical performance issues - Keep only critical slow remote warnings
            if (!cacheHit && (remoteFetchDuration?.inWholeMilliseconds ?: 0) > 2000) {
                Log.w(TAG, "CRITICAL SLOW REMOTE: $repositoryClass.$operation remote fetch took ${remoteFetchDuration?.inWholeMilliseconds}ms")
            }
        }
    }

    /**
     * Monitor AtomicCacheSystem metrics integration - TARGETED for cache health analysis
     */
    fun monitorAtomicCacheMetrics(
        cacheSystemName: String,
        metrics: Map<String, Any>,
        operation: String = "metrics_snapshot"
    ) {
        if (!isEnabled) return
        scope.launch {
            val details = buildString {
                append("cache_metrics")
                metrics["hits"]?.let { append(" Hits:$it") }
                metrics["misses"]?.let { append(" Misses:$it") }
                metrics["hitRate"]?.let { append(" HitRate:$it") }
                metrics["totalOperations"]?.let { append(" TotalOps:$it") }
                metrics["cacheSize"]?.let { append(" Size:$it") }
                metrics["expiredCleanups"]?.let { append(" Cleanups:$it") }

                // Health indicators
                val hitRate = metrics["hitRate"] as? Double ?: 0.0
                when {
                    hitRate > 0.8 -> append(" [HEALTHY]")
                    hitRate > 0.5 -> append(" [MODERATE]")
                    else -> append(" [POOR_PERFORMANCE]")
                }
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "cache_metrics",
                component = cacheSystemName,
                operation = details,
                durationMs = 0,
                success = true
            )

            // Log cache health analysis
            val hitRate = metrics["hitRate"] as? Double ?: 0.0
            if (hitRate < 0.5) {
                Log.w(TAG, "POOR CACHE PERFORMANCE: $cacheSystemName hit rate is ${String.format("%.2f", hitRate * 100)}%")
            }
        }
    }

    /**
     * ✅ ENHANCED CACHE METRICS LOGGING: Break down cache metrics for better debugging
     */
    private fun logEnhancedCacheMetrics(cacheMetrics: Map<String, Any>) {
        Log.d(TAG, "📊 ENHANCED CACHE BREAKDOWN:")

        // Delivery Cache Analysis
        val deliveryMetrics = cacheMetrics["delivery"] as? Map<*, *>
        if (deliveryMetrics != null) {
            val totalTips = deliveryMetrics["totalCachedTips"] as? Double ?: 0.0
            val avgTip = deliveryMetrics["avgTipPerDelivery"] as? Double ?: 0.0
            val completedDeliveries = deliveryMetrics["completedDeliveries"] as? Int ?: 0
            val totalEntries = deliveryMetrics["totalEntries"] as? Int ?: 0
            val hitRate = deliveryMetrics["hitRate"] as? Double ?: 0.0

            Log.d(TAG, "   🚚 DELIVERY CACHE:")
            Log.d(TAG, "      💰 Tips: \$${String.format("%.2f", totalTips)} total, \$${String.format("%.2f", avgTip)} avg")
            Log.d(TAG, "      📦 Deliveries: $completedDeliveries completed, $totalEntries total entries")
            Log.d(TAG, "      🎯 Performance: ${String.format("%.1f", hitRate * 100)}% hit rate")

            // Flag potential issues
            if (totalEntries > 0 && totalTips == 0.0) {
                Log.w(TAG, "      🚨 ISSUE: Deliveries cached but no tips recorded")
            }
            if (completedDeliveries > 0 && avgTip == 0.0) {
                Log.w(TAG, "      🚨 ISSUE: Completed deliveries but average tip is 0")
            }
        }

        // User Profile Cache Analysis
        val userMetrics = cacheMetrics["userProfile"] as? Map<*, *>
        if (userMetrics != null) {
            val totalTips = userMetrics["totalCachedTips"] as? Double ?: 0.0
            val avgTipsPerUser = userMetrics["avgTipsPerUser"] as? Double ?: 0.0
            val totalEntries = userMetrics["totalEntries"] as? Int ?: 0
            val hitRate = userMetrics["hitRate"] as? Double ?: 0.0

            Log.d(TAG, "   👤 USER PROFILE CACHE:")
            Log.d(TAG, "      💰 Tips: \$${String.format("%.2f", totalTips)} total, \$${String.format("%.2f", avgTipsPerUser)} per user")
            Log.d(TAG, "      👥 Users: $totalEntries total entries")
            Log.d(TAG, "      🎯 Performance: ${String.format("%.1f", hitRate * 100)}% hit rate")

            // Flag aggregation issues
            if (totalEntries > 0 && totalTips == 0.0) {
                Log.w(TAG, "      🚨 AGGREGATION ISSUE: Users cached but no aggregated tips")
            }
        }

        // Address Cache Analysis
        val addressMetrics = cacheMetrics["address"] as? Map<*, *>
        if (addressMetrics != null) {
            val avgTipsPerAddress = addressMetrics["avgTipsPerAddress"] as? Double ?: 0.0
            val addressesWithStats = addressMetrics["addressesWithStats"] as? Int ?: 0
            val totalAddresses = addressMetrics["size"] as? Int ?: 0

            Log.d(TAG, "   🏠 ADDRESS CACHE:")
            Log.d(TAG, "      💰 Tips: \$${String.format("%.2f", avgTipsPerAddress)} avg per address")
            Log.d(TAG, "      📍 Addresses: $addressesWithStats with stats, $totalAddresses total")

            // Flag stats aggregation issues
            if (addressesWithStats > 0 && avgTipsPerAddress == 0.0) {
                Log.w(TAG, "      🚨 STATS ISSUE: Addresses with stats but no tip aggregation")
            }
        }

        // Overall Cache Health Summary
        val allHitRates = listOfNotNull(
            deliveryMetrics?.get("hitRate") as? Double,
            userMetrics?.get("hitRate") as? Double,
            addressMetrics?.get("hitRate") as? Double
        )

        if (allHitRates.isNotEmpty()) {
            val avgHitRate = allHitRates.average()
            val healthStatus = when {
                avgHitRate > 0.8 -> "EXCELLENT"
                avgHitRate > 0.6 -> "GOOD"
                avgHitRate > 0.4 -> "MODERATE"
                else -> "POOR"
            }
            Log.d(TAG, "   📈 OVERALL CACHE HEALTH: ${String.format("%.1f", avgHitRate * 100)}% avg hit rate [$healthStatus]")
        }
    }

    // UI PERFORMANCE & JANK DETECTION MONITORING

    /**
     * Monitor UI frame performance and jank detection - TARGETED for UI responsiveness analysis
     */
    fun monitorUIFramePerformance(
        viewModelClass: String,
        operation: String,
        frameDrops: Int = 0,
        skippedFrames: Int = 0,
        uiThreadBlockingDuration: Duration? = null,
        renderingDuration: Duration,
        success: Boolean = true,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            val operationKey = "$viewModelClass.$operation.ui_frame_performance"
            addToCurrentSession(operationKey)

            val details = buildString {
                append("ui_frame_performance")
                append(" Render:${renderingDuration.inWholeMilliseconds}ms")
                if (frameDrops > 0) append(" Drops:$frameDrops")
                if (skippedFrames > 0) append(" Skipped:$skippedFrames")
                uiThreadBlockingDuration?.let { append(" UIBlock:${it.inWholeMilliseconds}ms") }

                // Performance indicators
                when {
                    frameDrops > 5 || skippedFrames > 10 -> append(" [SEVERE_JANK]")
                    frameDrops > 2 || skippedFrames > 5 -> append(" [MODERATE_JANK]")
                    renderingDuration.inWholeMilliseconds > 16 -> append(" [SLOW_RENDER]")
                    else -> append(" [SMOOTH]")
                }
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "ui_performance",
                component = viewModelClass,
                operation = details,
                durationMs = renderingDuration.inWholeMilliseconds,
                success = success
            )

            // 🔇 UI JANK LOG SPAM REDUCTION: Commented out tedious UI jank logging
            // Flag UI performance issues
            // if (frameDrops > 2 || skippedFrames > 5) {
            //     Log.w(TAG, "UI JANK DETECTED: $viewModelClass.$operation - Drops:$frameDrops Skipped:$skippedFrames")
            // }

            // if (uiThreadBlockingDuration != null && uiThreadBlockingDuration.inWholeMilliseconds > 16) {
            //     Log.w(TAG, "UI THREAD BLOCKING: $viewModelClass.$operation blocked UI for ${uiThreadBlockingDuration.inWholeMilliseconds}ms")
            // }
        }
    }

    /**
     * Monitor StateFlow emission performance and chain complexity - TARGETED for reactive performance
     */
    fun monitorStateFlowEmission(
        viewModelClass: String,
        stateFlowName: String,
        emissionDuration: Duration,
        subscriberCount: Int,
        chainLength: Int = 1,
        dataSize: Int = 0,
        success: Boolean = true,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            val operationKey = "$viewModelClass.$stateFlowName.stateflow_emission"
            addToCurrentSession(operationKey)

            val details = buildString {
                append("stateflow_emission")
                append(" Flow:$stateFlowName")
                append(" Emit:${emissionDuration.inWholeMilliseconds}ms")
                append(" Subs:$subscriberCount")
                append(" Chain:$chainLength")
                if (dataSize > 0) append(" Size:${dataSize}bytes")

                // Performance indicators
                when {
                    emissionDuration.inWholeMilliseconds > 50 -> append(" [SLOW_EMISSION]")
                    chainLength > 5 -> append(" [COMPLEX_CHAIN]")
                    subscriberCount > 10 -> append(" [MANY_SUBSCRIBERS]")
                    else -> append(" [EFFICIENT]")
                }
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "reactive_performance",
                component = viewModelClass,
                operation = details,
                durationMs = emissionDuration.inWholeMilliseconds,
                success = success
            )

            // 🔇 UI JANK LOG SPAM REDUCTION: Commented out verbose StateFlow performance logging
            // Flag StateFlow performance issues
            // if (emissionDuration.inWholeMilliseconds > 30) {
            //     Log.w(TAG, "SLOW STATEFLOW EMISSION: $viewModelClass.$stateFlowName took ${emissionDuration.inWholeMilliseconds}ms")
            // }

            // if (chainLength > 5) {
            //     Log.w(TAG, "COMPLEX STATEFLOW CHAIN: $viewModelClass.$stateFlowName has $chainLength operators")
            // }
        }
    }

    /**
     * Monitor loading state performance and user experience impact - TARGETED for loading optimization
     */
    fun monitorLoadingStatePerformance(
        viewModelClass: String,
        operation: String,
        loadingDuration: Duration,
        loadingSteps: List<String> = emptyList(),
        userVisibleDelay: Duration? = null,
        cacheHit: Boolean = false,
        success: Boolean = true,
        error: Throwable? = null
    ) {
        if (!isEnabled) return
        scope.launch {
            val operationKey = "$viewModelClass.$operation.loading_performance"
            addToCurrentSession(operationKey)

            val details = buildString {
                append("loading_performance")
                append(" Op:$operation")
                append(" Load:${loadingDuration.inWholeMilliseconds}ms")
                if (loadingSteps.isNotEmpty()) append(" Steps:[${loadingSteps.joinToString(",")}]")
                userVisibleDelay?.let { append(" UserDelay:${it.inWholeMilliseconds}ms") }
                if (cacheHit) append(" [CACHE_HIT]") else append(" [CACHE_MISS]")

                // User experience indicators
                val totalDelay = userVisibleDelay?.inWholeMilliseconds ?: loadingDuration.inWholeMilliseconds
                when {
                    totalDelay > 3000 -> append(" [POOR_UX]")
                    totalDelay > 1000 -> append(" [SLOW_UX]")
                    totalDelay > 500 -> append(" [MODERATE_UX]")
                    else -> append(" [FAST_UX]")
                }
            }

            SimpleClarityDashboard.logArchitectureEvent(
                layer = "loading_performance",
                component = viewModelClass,
                operation = details,
                durationMs = loadingDuration.inWholeMilliseconds,
                success = success
            )

            // Flag poor loading performance
            val totalDelay = userVisibleDelay?.inWholeMilliseconds ?: loadingDuration.inWholeMilliseconds
            if (totalDelay > 2000) {
                Log.w(TAG, "POOR LOADING UX: $viewModelClass.$operation took ${totalDelay}ms (user-visible)")
            }
        }
    }
}
