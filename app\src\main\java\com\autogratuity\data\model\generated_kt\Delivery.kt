/*
 * Delivery.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

import kotlin.Double

/**
 * Represents a delivery record in Firestore.
 */
data class Delivery(
    /** Firestore Document ID */
    val id: String,
    /** Wrapper for all delivery-specific data. */
    val deliveryData: DeliveryData
) {

    /**
     * Wrapper for all delivery-specific data.
     */
    data class DeliveryData(
        /** The ID of the user associated with this delivery. */
        val userId: String,
        /** The platform-specific order identifier. */
        val orderId: String,
        /** User-provided notes about the delivery. */
        val notes: String? = null,
        /** Simplified address information embedded in the delivery. */
        val address: SimpleAddress,
        /** The status object containing delivery state information. */
        val status: Status?,
        /** Timestamp information related to the delivery lifecycle. */
        val times: Times?,
        /** Monetary amounts related to the delivery. */
        val amounts: Amounts?,
        /** Reference information linking to other entities. */
        val reference: Reference?,
        /** Information about the delivery platform. */
        val platform: Platform?,
        /** List of items in the delivery (placeholder) */
        val items: List<Item>? = null,
        /** Represents metadata commonly stored within Firestore documents. */
        val metadata: Metadata?
    )

    /**
     * Simplified address information embedded in the delivery.
     */
    data class SimpleAddress(
        /** The ID of the canonical Address document this refers to. */
        val id: String,
        /** The full string representation of the address. */
        val fullAddress: String,
        val latitude: Double,
        val longitude: Double,
        /** Google Place ID for this address, if available, at the time of delivery. */
        val placeId: String? = null
    )

    /**
     * Placeholder for item details
     */
    open class Item

}
