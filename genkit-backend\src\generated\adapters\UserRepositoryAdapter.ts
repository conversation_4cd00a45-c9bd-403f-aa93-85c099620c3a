// Auto-generated from UserRepository.kt
// IMPORTS FROM EXISTING GENERATED MODELS (not recreated)

import type { Result } from '../types/Result';
import type {
  Delivery as TsDeliveryInterface,
  Address as TsAddressInterface,
  User_profile as TsUserProfileInterface
} from '../../models/generated/delivery.schema';
import type {
  DeliveryStats as TsDeliveryStatsInterface
} from '../../models/generated/address.schema';
import type { UserProfileData as TsUserProfileData } from '../../models/generated/user_profile.schema';

/**
 * TypeScript adapter interface generated from UserRepository.kt
 * Maintains identical contract to Kotlin implementation
 * Uses existing generated models from schemas
 */
export interface UserRepositoryAdapter {
  getUserById(id: string): Result<User?>;
  getCurrentUser(): Result<User?>;
  addUser(user: User): Promise<Result<void>>;
  updateUser(user: User): Promise<Result<void>>;
  deleteUser(id: string): Promise<Result<void>>;
  observeUserById(id: string): Flow<Result<User?>>;
  observeCurrentUser(): Flow<Result<User?>>;
  updateUserPreferences(userId: string, preferences: UserPreferences): Promise<Result<void>>;
  updateUserSubscription(userId: string, subscription: UserSubscription): Promise<Result<void>>;
  setDefaultAddress(userId: string, addressId: string): Promise<Result<void>>;
  getDefaultAddressSummary(): Result<string?>;
  updateUserDisplayName(newDisplayName: string): Promise<Result<void>>;
  updateUserUsageStats(userId: string, stats: UserUsageStats): Promise<Result<void>>;
  validateUser(user: User): Promise<Result<void>>;
  userExistsByEmail(email: string): Result<boolean>;
  createDefaultUser(userId: string, email: string | null): Result<User>;
  handleUserSignIn(userId: string, authProvider: string): Promise<Result<void>>;
  handleUserSignOut(userId: string): Promise<Result<void>>;
}