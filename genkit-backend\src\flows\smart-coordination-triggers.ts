/**
 * ✅ SMART COORDINATION TRIGGERS
 * 
 * Robust coordination system for stats and DND updates across multiple collections.
 * Handles complex multi-tier logic that works both forward and backward.
 * 
 * Key Features:
 * - Cross-address coordination when orders move between addresses
 * - Dynamic tip status transition detection (pending ↔ confirmed)
 * - Smart debouncing to prevent cascade updates
 * - Version stamping for race condition prevention
 * - Comprehensive delta tracking and user profile coordination
 */

import { onDocumentWritten, FirestoreEvent, Change } from 'firebase-functions/v2/firestore';
import { DocumentSnapshot } from 'firebase-admin/firestore';
import { runFlow } from '@genkit-ai/flow';

// Import the enhanced functions
import { calculateAddressStatsFlow } from './calculate-address-stats.js';
import { evaluateAddressDndFlow } from './evaluate-address-dnd.js';
import { updateUserStatsFlow } from './update-user-stats.js';

// ✅ SMART DEBOUNCING: Prevent cascade updates
interface DebounceEntry {
  lastUpdate: number;
  operationType: string;
  version: number;
}

const debounceCache = new Map<string, DebounceEntry>();
const DEBOUNCE_WINDOW_MS = 2000; // 2 seconds
const MAX_CACHE_SIZE = 1000;

function shouldDebounce(key: string, operationType: string, version?: number): boolean {
  const now = Date.now();
  const entry = debounceCache.get(key);

  if (!entry) {
    debounceCache.set(key, { lastUpdate: now, operationType, version: version || 0 });
    return false;
  }

  // Check if within debounce window and same operation type
  const withinWindow = (now - entry.lastUpdate) < DEBOUNCE_WINDOW_MS;
  const sameOperation = entry.operationType === operationType;
  const sameVersion = version ? entry.version === version : false;

  if (withinWindow && (sameOperation || sameVersion)) {
    return true; // Should debounce
  }

  // Update entry
  entry.lastUpdate = now;
  entry.operationType = operationType;
  entry.version = version || entry.version;

  // Clean cache if too large
  if (debounceCache.size > MAX_CACHE_SIZE) {
    const oldestKey = debounceCache.keys().next().value;
    debounceCache.delete(oldestKey);
  }

  return false;
}

// ✅ SMART COORDINATION: Enhanced delivery change detection
export const onDeliveryChangeSmartCoordination = onDocumentWritten(
  'users/{userId}/user_deliveries/{deliveryId}',
  async (event: FirestoreEvent<Change<DocumentSnapshot> | undefined, { userId: string, deliveryId: string }>) => {
    const logPrefix = `[SmartCoordination][${event.params.userId}][${event.params.deliveryId}]`;
    const startTime = Date.now();
    console.log(`${logPrefix} Smart coordination triggered`);

    try {
      const before = event.data?.before?.data();
      const after = event.data?.after?.data();
      
      if (!after) {
        console.log(`${logPrefix} Document deleted, skipping coordination`);
        return;
      }

      const beforeData = before?.deliveryData;
      const afterData = after.deliveryData;

      // ✅ CROSS-ADDRESS COORDINATION: Detect address changes
      const oldAddressId = beforeData?.reference?.addressId;
      const newAddressId = afterData?.reference?.addressId;
      const addressChanged = oldAddressId && newAddressId && oldAddressId !== newAddressId;

      // ✅ TIP STATUS TRANSITION DETECTION
      const oldTipAmount = beforeData?.amounts?.tipAmount;
      const newTipAmount = afterData?.amounts?.tipAmount;
      const oldIsCompleted = beforeData?.status?.isCompleted ?? false;
      const newIsCompleted = afterData?.status?.isCompleted ?? false;
      
      const tipStatusChanged = detectTipStatusTransition(
        oldTipAmount, newTipAmount, oldIsCompleted, newIsCompleted
      );

      // ✅ ORDER ID IMMUTABILITY CHECK
      const oldOrderId = beforeData?.orderId;
      const newOrderId = afterData?.orderId;
      const orderIdChanged = oldOrderId && newOrderId && oldOrderId !== newOrderId;

      console.log(`${logPrefix} Change analysis:`, {
        addressChanged,
        tipStatusChanged: tipStatusChanged.changed,
        orderIdChanged,
        oldAddressId,
        newAddressId
      });

      // ✅ SMART DEBOUNCING: Check if we should process this change
      const debounceKey = `${event.params.userId}:${newAddressId || oldAddressId}`;
      const operationType = addressChanged ? 'cross_address' :
                           tipStatusChanged.changed ? 'tip_transition' :
                           'standard_update';

      if (shouldDebounce(debounceKey, operationType)) {
        console.log(`${logPrefix} Debouncing ${operationType} - skipping duplicate operation`);
        return;
      }

      // ✅ COORDINATION LOGIC: Handle different change types
      if (addressChanged) {
        await handleCrossAddressCoordination(
          event.params.userId,
          oldAddressId!,
          newAddressId!,
          logPrefix
        );
      } else if (tipStatusChanged.changed) {
        await handleTipStatusTransition(
          event.params.userId,
          newAddressId!,
          tipStatusChanged,
          logPrefix
        );
      } else if (orderIdChanged) {
        console.warn(`${logPrefix} Order ID changed - this should not happen! Old: ${oldOrderId}, New: ${newOrderId}`);
        // Could trigger validation or alert here
      } else {
        // Standard stats update for other changes
        await handleStandardStatsUpdate(
          event.params.userId,
          newAddressId!,
          'delivery_update',
          logPrefix
        );
      }

      const totalDuration = Date.now() - startTime;
      console.log(`${logPrefix} Smart coordination completed in ${totalDuration}ms`);

    } catch (error) {
      const totalDuration = Date.now() - startTime;
      console.error(`${logPrefix} Smart coordination error after ${totalDuration}ms:`, error);
    }
  }
);

// ✅ SMART COORDINATION: Address manual DND changes
export const onAddressManualDndChangeCoordination = onDocumentWritten(
  'users/{userId}/user_addresses/{addressId}',
  async (event: FirestoreEvent<Change<DocumentSnapshot> | undefined, { userId: string, addressId: string }>) => {
    const logPrefix = `[ManualDndCoordination][${event.params.userId}][${event.params.addressId}]`;
    console.log(`${logPrefix} Manual DND coordination triggered`);

    try {
      const before = event.data?.before?.data();
      const after = event.data?.after?.data();

      if (!after) {
        console.log(`${logPrefix} Address deleted, skipping coordination`);
        return;
      }

      // ✅ MANUAL DND STATE CHANGE DETECTION
      const beforeManualState = before?.addressData?.flags?.manualDndState ?? null;
      const afterManualState = after.addressData?.flags?.manualDndState ?? null;
      
      const manualStateChanged = beforeManualState !== afterManualState;

      if (manualStateChanged) {
        console.log(`${logPrefix} Manual DND state changed: ${beforeManualState} → ${afterManualState}`);
        
        // ✅ TRIGGER DND RE-EVALUATION with coordination context
        await runFlow(evaluateAddressDndFlow, {
          userId: event.params.userId,
          addressId: event.params.addressId,
          coordinationContext: {
            previousDndStatus: before?.addressData?.flags?.doNotDeliver ?? false,
            operationType: 'manual_dnd_change',
            triggerSource: 'address_manual_dnd_trigger'
          }
        });
      }

    } catch (error) {
      console.error(`${logPrefix} Manual DND coordination error:`, error);
    }
  }
);

// ✅ HELPER FUNCTIONS

interface TipStatusTransition {
  changed: boolean;
  type: 'pending_to_confirmed' | 'confirmed_to_pending' | 'amount_change' | 'completion_change' | 'none';
  triggersDndEvaluation: boolean;
  isZeroTipConfirmed: boolean;
}

function detectTipStatusTransition(
  oldTipAmount: number | null | undefined,
  newTipAmount: number | null | undefined,
  oldIsCompleted: boolean,
  newIsCompleted: boolean
): TipStatusTransition {
  // Normalize null/undefined to null
  const oldTip = oldTipAmount ?? null;
  const newTip = newTipAmount ?? null;

  // Detect transition types
  const wasPending = oldTip === null && oldIsCompleted;
  const isPending = newTip === null && newIsCompleted;
  const wasConfirmed = oldTip !== null && oldIsCompleted;
  const isConfirmed = newTip !== null && newIsCompleted;

  let type: TipStatusTransition['type'] = 'none';
  let triggersDndEvaluation = false;
  let isZeroTipConfirmed = false;

  if (wasPending && isConfirmed) {
    type = 'pending_to_confirmed';
    triggersDndEvaluation = true;
    isZeroTipConfirmed = newTip === 0;
  } else if (wasConfirmed && isPending) {
    type = 'confirmed_to_pending';
    triggersDndEvaluation = true;
  } else if (wasConfirmed && isConfirmed && oldTip !== newTip) {
    type = 'amount_change';
    triggersDndEvaluation = true;
    isZeroTipConfirmed = newTip === 0;
  } else if (oldIsCompleted !== newIsCompleted) {
    type = 'completion_change';
    triggersDndEvaluation = newIsCompleted;
  }

  return {
    changed: type !== 'none',
    type,
    triggersDndEvaluation,
    isZeroTipConfirmed
  };
}

async function handleCrossAddressCoordination(
  userId: string,
  oldAddressId: string,
  newAddressId: string,
  logPrefix: string
): Promise<void> {
  console.log(`${logPrefix} Handling cross-address coordination: ${oldAddressId} → ${newAddressId}`);

  // ✅ UPDATE BOTH ADDRESSES in parallel
  const [oldAddressResult, newAddressResult] = await Promise.allSettled([
    runFlow(calculateAddressStatsFlow, { 
      userId, 
      addressId: oldAddressId 
    }),
    runFlow(calculateAddressStatsFlow, { 
      userId, 
      addressId: newAddressId 
    })
  ]);

  // ✅ TRIGGER DND EVALUATION for both addresses
  await Promise.allSettled([
    runFlow(evaluateAddressDndFlow, {
      userId,
      addressId: oldAddressId,
      coordinationContext: {
        operationType: 'cross_address_update',
        triggerSource: 'delivery_address_change'
      }
    }),
    runFlow(evaluateAddressDndFlow, {
      userId,
      addressId: newAddressId,
      coordinationContext: {
        operationType: 'cross_address_update',
        triggerSource: 'delivery_address_change'
      }
    })
  ]);

  console.log(`${logPrefix} Cross-address coordination completed`);
}

async function handleTipStatusTransition(
  userId: string,
  addressId: string,
  transition: TipStatusTransition,
  logPrefix: string
): Promise<void> {
  console.log(`${logPrefix} Handling tip status transition: ${transition.type}`);

  // ✅ ALWAYS UPDATE STATS for tip changes
  const statsResult = await runFlow(calculateAddressStatsFlow, { 
    userId, 
    addressId 
  });

  // ✅ TRIGGER DND EVALUATION if needed
  if (transition.triggersDndEvaluation) {
    await runFlow(evaluateAddressDndFlow, {
      userId,
      addressId,
      coordinationContext: {
        operationType: 'tip_status_transition',
        triggerSource: `tip_${transition.type}`
      }
    });
  }

  console.log(`${logPrefix} Tip status transition handling completed`);
}

async function handleStandardStatsUpdate(
  userId: string,
  addressId: string,
  operationType: string,
  logPrefix: string
): Promise<void> {
  console.log(`${logPrefix} Handling standard stats update: ${operationType}`);

  // ✅ CALCULATE STATS with coordination
  const statsResult = await runFlow(calculateAddressStatsFlow, { 
    userId, 
    addressId 
  });

  // ✅ EVALUATE DND if stats require it
  if (statsResult.coordination?.requiresDndEvaluation) {
    await runFlow(evaluateAddressDndFlow, {
      userId,
      addressId,
      coordinationContext: {
        operationType,
        triggerSource: 'stats_calculation'
      }
    });
  }

  console.log(`${logPrefix} Standard stats update completed`);
}
