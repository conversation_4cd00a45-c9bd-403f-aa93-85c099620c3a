package com.autogratuity.data.datasource.local

import android.util.Log
import com.autogratuity.data.repository.subscription.SubscriptionCacheSystem
import com.autogratuity.data.model.Result
import com.autogratuity.domain.model.UserSubscription
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Interface for local data operations related to Subscription.
 * All methods operate with SSoT domain objects (UserSubscription).
 *
 * Follows the delivery domain pattern for local data sources.
 */
interface SubscriptionLocalDataSource {
    suspend fun getUserSubscription(userId: String): Result<UserSubscription?>
    suspend fun saveUserSubscription(userId: String, subscription: UserSubscription): Result<Unit>
    suspend fun updateUserSubscription(userId: String, subscription: UserSubscription): Result<Unit>
    suspend fun deleteUserSubscription(userId: String): Result<Unit>
    suspend fun clearAllSubscriptions(): Result<Unit>
    fun observeById(userId: String): Flow<UserSubscription?>
    suspend fun getCacheMetrics(): Result<Map<String, Any>>
    suspend fun invalidateUserSubscription(userId: String): Result<Unit>
}

/**
 * Implementation of SubscriptionLocalDataSource using SubscriptionCacheSystem.
 * Handles all local subscription operations with SSoT domain models.
 *
 * Follows the delivery domain pattern for local data source implementations.
 */
class SubscriptionLocalDataSourceImpl @Inject constructor(
    private val subscriptionCacheSystem: SubscriptionCacheSystem,
    private val ioDispatcher: CoroutineDispatcher
) : SubscriptionLocalDataSource {

    companion object {
        private const val TAG = "SubscriptionLocalDataSource"
    }

    override suspend fun getUserSubscription(userId: String): Result<UserSubscription?> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "getUserSubscription: Getting subscription for user $userId")
            
            val subscription = subscriptionCacheSystem.get(userId)
            
            Log.d(TAG, "getUserSubscription: Successfully retrieved subscription for user $userId")
            Result.Success(subscription)
        } catch (e: Exception) {
            Log.e(TAG, "getUserSubscription: Error getting subscription for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun saveUserSubscription(userId: String, subscription: UserSubscription): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "saveUserSubscription: Saving subscription for user $userId")
            
            subscriptionCacheSystem.cacheSubscription(userId, subscription)
            
            Log.d(TAG, "saveUserSubscription: Successfully saved subscription for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "saveUserSubscription: Error saving subscription for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun updateUserSubscription(userId: String, subscription: UserSubscription): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "updateUserSubscription: Updating subscription for user $userId")
            
            subscriptionCacheSystem.cacheSubscription(userId, subscription)
            
            Log.d(TAG, "updateUserSubscription: Successfully updated subscription for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "updateUserSubscription: Error updating subscription for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteUserSubscription(userId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "deleteUserSubscription: Deleting subscription for user $userId")
            
            subscriptionCacheSystem.invalidateUserSubscription(userId)
            
            Log.d(TAG, "deleteUserSubscription: Successfully deleted subscription for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "deleteUserSubscription: Error deleting subscription for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun clearAllSubscriptions(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "clearAllSubscriptions: Clearing all cached subscriptions")
            
            subscriptionCacheSystem.clear()
            
            Log.d(TAG, "clearAllSubscriptions: Successfully cleared all subscriptions")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "clearAllSubscriptions: Error clearing all subscriptions", e)
            Result.Error(e)
        }
    }

    override fun observeById(userId: String): Flow<UserSubscription?> {
        Log.d(TAG, "observeUserSubscription: Setting up observation for user $userId")
        return subscriptionCacheSystem.observe(userId)
    }

    override suspend fun getCacheMetrics(): Result<Map<String, Any>> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "getCacheMetrics: Getting subscription cache metrics")
            
            val metrics = subscriptionCacheSystem.getSubscriptionCacheMetrics()
            
            Log.d(TAG, "getCacheMetrics: Successfully retrieved cache metrics")
            Result.Success(metrics)
        } catch (e: Exception) {
            Log.e(TAG, "getCacheMetrics: Error getting cache metrics", e)
            Result.Error(e)
        }
    }

    override suspend fun invalidateUserSubscription(userId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "invalidateUserSubscription: Invalidating subscription for user $userId")
            
            subscriptionCacheSystem.invalidateUserSubscription(userId)
            
            Log.d(TAG, "invalidateUserSubscription: Successfully invalidated subscription for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "invalidateUserSubscription: Error invalidating subscription for user $userId", e)
            Result.Error(e)
        }
    }
}