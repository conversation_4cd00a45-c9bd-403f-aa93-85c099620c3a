/**
 * ✅ DELIVERY ADDRESS VALIDATION
 * 
 * Ensures deliveries can only be created for delivery addresses, not default addresses.
 * Default addresses are shielded from all delivery operations.
 */

import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore } from 'firebase-admin/firestore';

// Initialize Firestore
const db = getFirestore();

// Input/Output schemas
const ValidateDeliveryAddressInputSchema = z.object({
  userId: z.string(),
  addressId: z.string(),
  operationType: z.enum(['delivery_creation', 'delivery_update', 'delivery_move']).optional()
});

const ValidateDeliveryAddressOutputSchema = z.object({
  success: z.boolean(),
  isValid: z.boolean(),
  reason: z.string().optional(),
  addressType: z.enum(['delivery_address', 'default_address', 'not_found']),
  error: z.string().optional()
});

/**
 * ✅ VALIDATE DELIVERY ADDRESS: Ensure address is eligible for delivery operations
 */
export const validateDeliveryAddressFlow = defineFlow(
  {
    name: 'validateDeliveryAddress',
    inputSchema: ValidateDeliveryAddressInputSchema,
    outputSchema: ValidateDeliveryAddressOutputSchema,
  },
  async (input) => {
    const logPrefix = `[ValidateDeliveryAddress][${input.userId}][${input.addressId}]`;
    const operationType = input.operationType || 'delivery_creation';
    
    console.log(`${logPrefix} Validating address for operation: ${operationType}`);

    try {
      // Get address document
      const addressDoc = await db.doc(`users/${input.userId}/user_addresses/${input.addressId}`).get();
      
      if (!addressDoc.exists) {
        console.log(`${logPrefix} Address not found`);
        return {
          success: true,
          isValid: false,
          reason: 'Address not found',
          addressType: 'not_found'
        };
      }

      const addressData = addressDoc.data();
      const isDefaultAddress = addressData?.addressData?.isDefault === true;

      if (isDefaultAddress) {
        console.log(`${logPrefix} ❌ VALIDATION FAILED - Cannot create deliveries to default address`);
        return {
          success: true,
          isValid: false,
          reason: 'Default addresses are protected from delivery operations. They are for map centering and reference only.',
          addressType: 'default_address'
        };
      }

      console.log(`${logPrefix} ✅ VALIDATION PASSED - Address is eligible for delivery operations`);
      return {
        success: true,
        isValid: true,
        reason: 'Address is eligible for delivery operations',
        addressType: 'delivery_address'
      };

    } catch (error) {
      console.error(`${logPrefix} Error validating delivery address:`, error);
      return {
        success: false,
        isValid: false,
        reason: 'Validation error occurred',
        addressType: 'not_found',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
);

/**
 * ✅ HELPER: Get delivery-eligible addresses for a user
 */
export const getDeliveryEligibleAddressesFlow = defineFlow(
  {
    name: 'getDeliveryEligibleAddresses',
    inputSchema: z.object({
      userId: z.string(),
      limit: z.number().optional().default(50)
    }),
    outputSchema: z.object({
      success: z.boolean(),
      addresses: z.array(z.object({
        id: z.string(),
        fullAddress: z.string().optional(),
        isDefault: z.boolean(),
        deliveryCount: z.number().optional()
      })),
      totalCount: z.number(),
      defaultAddressCount: z.number(),
      deliveryAddressCount: z.number(),
      error: z.string().optional()
    })
  },
  async (input) => {
    const logPrefix = `[GetDeliveryEligibleAddresses][${input.userId}]`;
    console.log(`${logPrefix} Getting delivery-eligible addresses`);

    try {
      // Get all addresses for the user
      const addressesSnapshot = await db
        .collection(`users/${input.userId}/user_addresses`)
        .limit(input.limit)
        .get();

      const allAddresses = addressesSnapshot.docs.map(doc => ({
        id: doc.id,
        fullAddress: doc.data().addressData?.fullAddress || 'Unknown Address',
        isDefault: doc.data().addressData?.isDefault === true,
        deliveryCount: doc.data().addressData?.deliveryStats?.deliveryCount || 0
      }));

      // Filter out default addresses for delivery operations
      const deliveryEligibleAddresses = allAddresses.filter(addr => !addr.isDefault);
      const defaultAddresses = allAddresses.filter(addr => addr.isDefault);

      console.log(`${logPrefix} Found ${allAddresses.length} total addresses: ${deliveryEligibleAddresses.length} delivery-eligible, ${defaultAddresses.length} default`);

      return {
        success: true,
        addresses: deliveryEligibleAddresses,
        totalCount: allAddresses.length,
        defaultAddressCount: defaultAddresses.length,
        deliveryAddressCount: deliveryEligibleAddresses.length
      };

    } catch (error) {
      console.error(`${logPrefix} Error getting delivery-eligible addresses:`, error);
      return {
        success: false,
        addresses: [],
        totalCount: 0,
        defaultAddressCount: 0,
        deliveryAddressCount: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
);

/**
 * ✅ HELPER: Check if address has any deliveries (for cleanup decisions)
 */
export const checkAddressDeliveryStatusFlow = defineFlow(
  {
    name: 'checkAddressDeliveryStatus',
    inputSchema: z.object({
      userId: z.string(),
      addressId: z.string()
    }),
    outputSchema: z.object({
      success: z.boolean(),
      hasDeliveries: z.boolean(),
      deliveryCount: z.number(),
      isDefaultAddress: z.boolean(),
      shouldDelete: z.boolean(),
      reason: z.string(),
      error: z.string().optional()
    })
  },
  async (input) => {
    const logPrefix = `[CheckAddressDeliveryStatus][${input.userId}][${input.addressId}]`;
    
    try {
      // Check if address is default
      const addressDoc = await db.doc(`users/${input.userId}/user_addresses/${input.addressId}`).get();
      
      if (!addressDoc.exists) {
        return {
          success: true,
          hasDeliveries: false,
          deliveryCount: 0,
          isDefaultAddress: false,
          shouldDelete: false,
          reason: 'Address not found'
        };
      }

      const isDefaultAddress = addressDoc.data()?.addressData?.isDefault === true;

      // Check for deliveries
      const deliveriesSnapshot = await db
        .collection(`users/${input.userId}/user_deliveries`)
        .where('deliveryData.reference.addressId', '==', input.addressId)
        .limit(1)
        .get();

      const hasDeliveries = !deliveriesSnapshot.empty;
      const deliveryCount = deliveriesSnapshot.size;

      // Determine if address should be deleted
      let shouldDelete = false;
      let reason = '';

      if (isDefaultAddress) {
        shouldDelete = false;
        reason = 'Default address - protected from deletion';
      } else if (hasDeliveries) {
        shouldDelete = false;
        reason = 'Has deliveries - should not be deleted';
      } else {
        shouldDelete = true;
        reason = 'No deliveries and not default - eligible for deletion';
      }

      console.log(`${logPrefix} Status: deliveries=${hasDeliveries}, default=${isDefaultAddress}, shouldDelete=${shouldDelete}`);

      return {
        success: true,
        hasDeliveries,
        deliveryCount,
        isDefaultAddress,
        shouldDelete,
        reason
      };

    } catch (error) {
      console.error(`${logPrefix} Error checking address delivery status:`, error);
      return {
        success: false,
        hasDeliveries: false,
        deliveryCount: 0,
        isDefaultAddress: false,
        shouldDelete: false,
        reason: 'Error occurred during check',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
);
