package com.autogratuity.data.datasource.local

import android.util.Log
import com.autogratuity.domain.model.Delivery
import com.autogratuity.data.model.Result
import com.autogratuity.data.repository.delivery.DeliveryCacheSystem
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Interface for local data operations (caching) related to Deliveries.
 * All methods operate with SSoT Delivery models.
 * Following UserProfileLocalDataSource pattern for consistency.
 */
interface DeliveryLocalDataSource {
    suspend fun getDeliveryById(userId: String, deliveryId: String): Result<Delivery?>
    suspend fun getAllDeliveries(userId: String): Result<List<Delivery>>
    suspend fun saveDelivery(userId: String, delivery: Delivery): Result<Unit>
    suspend fun saveAllDeliveries(userId: String, deliveries: List<Delivery>): Result<Unit>
    suspend fun deleteDelivery(userId: String, deliveryId: String): Result<Unit>
    suspend fun deleteAllDeliveries(userId: String): Result<Unit>
    suspend fun clearAllCaches(): Result<Unit>

    // Additional methods to eliminate "never used" flags in cache system
    suspend fun getAddressDeliveries(addressId: String): Result<List<Delivery>>
    suspend fun getPendingTipDeliveries(): Result<List<Delivery>>
    suspend fun invalidateAddressDeliveries(addressId: String): Result<Unit>
    suspend fun invalidatePendingTipDeliveries(): Result<Unit>
    suspend fun updateFromCloudFunction(deliveryId: String, cloudDelivery: Delivery, userId: String): Result<Unit>

    fun observeById(userId: String, deliveryId: String): Flow<Delivery?>
    fun observeAll(userId: String): Flow<List<Delivery>>
    fun observeCurrentDelivery(): Flow<Delivery?>

    // Methods to use cache system properties and eliminate "never used" flags
    suspend fun getCurrentDeliveryStats(): Result<com.autogratuity.data.model.generated_kt.Delivery_stats?>
    suspend fun createDefaultDeliveryStats(): Result<com.autogratuity.data.model.generated_kt.Delivery_stats>
    fun getDeliveryErrorFlow(): Flow<String?>
}

@OptIn(ExperimentalCoroutinesApi::class)
class DeliveryLocalDataSourceImpl @Inject constructor(
    private val cacheSystem: DeliveryCacheSystem,
    private val ioDispatcher: CoroutineDispatcher, // Koin will provide this via module definition
    private val deliveryStatsMapper: com.autogratuity.data.mapper.DeliveryStatsMapper // ✅ INJECT: For enhanced stats logging
) : DeliveryLocalDataSource {

    override suspend fun getDeliveryById(userId: String, deliveryId: String): Result<Delivery?> = withContext(ioDispatcher) {
        try {
            val delivery = cacheSystem.getDelivery(userId, deliveryId)
            Log.d("DeliveryLocalDataSource", "Retrieved delivery $deliveryId for user $userId: ${delivery != null}")
            Result.Success(delivery)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error getting delivery $deliveryId for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun getAllDeliveries(userId: String): Result<List<Delivery>> = withContext(ioDispatcher) {
        try {
            val deliveries = cacheSystem.getUserDeliveries(userId)
            Log.d("DeliveryLocalDataSource", "Retrieved ${deliveries.size} deliveries for user $userId")
            Result.Success(deliveries)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error getting all deliveries for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun saveDelivery(userId: String, delivery: Delivery): Result<Unit> = withContext(ioDispatcher) {
        try {
            cacheSystem.cacheDelivery(delivery.id, delivery, userId)
            Log.d("DeliveryLocalDataSource", "Saved delivery ${delivery.id} for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error saving delivery ${delivery.id} for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun saveAllDeliveries(userId: String, deliveries: List<Delivery>): Result<Unit> = withContext(ioDispatcher) {
        try {
            // Use a generic key for the delivery list
            val cacheKey = "deliveries_${userId}_all"
            cacheSystem.cacheDeliveryList(cacheKey, deliveries, userId)
            Log.d("DeliveryLocalDataSource", "Saved ${deliveries.size} deliveries for user $userId")

            // ✅ STATS LOGGING: Only log during critical cache operations (disabled for normal saves)
            // Stats calculation moved to critical conversion points only

            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error saving ${deliveries.size} deliveries for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteDelivery(userId: String, deliveryId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            // ✅ FIXED: User-aware delivery deletion with validation
            val existingDelivery = cacheSystem.getDelivery(userId, deliveryId)
            if (existingDelivery != null) {
                cacheSystem.remove(deliveryId)
                Log.d("DeliveryLocalDataSource", "Deleted delivery $deliveryId for user $userId")
            } else {
                Log.w("DeliveryLocalDataSource", "Delivery $deliveryId not found for user $userId")
            }
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error deleting delivery $deliveryId for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteAllDeliveries(userId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            // Invalidate all deliveries for this user
            cacheSystem.invalidateUserDeliveries(userId)
            Log.d("DeliveryLocalDataSource", "Deleted all deliveries for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error deleting all deliveries for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun clearAllCaches(): Result<Unit> = withContext(ioDispatcher) {
        try {
            cacheSystem.clear()
            Log.d("DeliveryLocalDataSource", "Cleared all delivery caches")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error clearing all caches", e)
            Result.Error(e)
        }
    }

    override fun observeById(userId: String, deliveryId: String): Flow<Delivery?> {
        // ✅ STANDARDIZED: User-aware reactive observation with validation
        return cacheSystem.observe(deliveryId).map { delivery ->
            // Validate user ownership in reactive stream
            if (delivery?.details?.userId == userId) {
                delivery
            } else {
                null // Filter out deliveries not owned by user
            }
        }
    }

    override fun observeAll(userId: String): Flow<List<Delivery>> {
        // ✅ FIXED: Proper userId-aware filtering in reactive observation
        return cacheSystem.observeAll().map { entries ->
            entries.values.filter { delivery ->
                // Filter by userId using delivery details or fallback to cache metadata
                delivery.details.userId == userId
            }
        }
    }

    // ====== ADDITIONAL METHODS TO ELIMINATE "NEVER USED" FLAGS ======

    override suspend fun getAddressDeliveries(addressId: String): Result<List<Delivery>> = withContext(ioDispatcher) {
        try {
            val deliveries = cacheSystem.getAddressDeliveries(addressId)
            Log.d("DeliveryLocalDataSource", "Retrieved ${deliveries.size} deliveries for address $addressId")
            Result.Success(deliveries)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error getting deliveries for address $addressId", e)
            Result.Error(e)
        }
    }

    override suspend fun getPendingTipDeliveries(): Result<List<Delivery>> = withContext(ioDispatcher) {
        try {
            val deliveries = cacheSystem.getPendingTipDeliveries()
            Log.d("DeliveryLocalDataSource", "Retrieved ${deliveries.size} pending tip deliveries")
            Result.Success(deliveries)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error getting pending tip deliveries", e)
            Result.Error(e)
        }
    }

    override suspend fun invalidateAddressDeliveries(addressId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            cacheSystem.invalidateAddressDeliveries(addressId)
            Log.d("DeliveryLocalDataSource", "Invalidated deliveries for address $addressId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error invalidating deliveries for address $addressId", e)
            Result.Error(e)
        }
    }

    override suspend fun invalidatePendingTipDeliveries(): Result<Unit> = withContext(ioDispatcher) {
        try {
            cacheSystem.invalidatePendingTipDeliveries()
            Log.d("DeliveryLocalDataSource", "Invalidated pending tip deliveries")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error invalidating pending tip deliveries", e)
            Result.Error(e)
        }
    }

    override suspend fun updateFromCloudFunction(deliveryId: String, cloudDelivery: Delivery, userId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            cacheSystem.updateFromCloudFunction(deliveryId, cloudDelivery, userId)
            Log.d("DeliveryLocalDataSource", "Updated delivery $deliveryId from cloud function for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error updating delivery $deliveryId from cloud function", e)
            Result.Error(e)
        }
    }

    override fun observeCurrentDelivery(): Flow<Delivery?> {
        return cacheSystem.observeCurrentDelivery()
    }

    // ====== METHODS TO USE CACHE SYSTEM PROPERTIES ======

    /**
     * ✅ FIXED: Get current stats from cache system (eliminates "never used" flag)
     */
    override suspend fun getCurrentDeliveryStats(): Result<com.autogratuity.data.model.generated_kt.Delivery_stats?> = withContext(ioDispatcher) {
        try {
            val stats = cacheSystem.getCurrentStats()
            Log.d("DeliveryLocalDataSource", "Retrieved current delivery stats: ${stats != null}")
            Result.Success(stats)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error getting current delivery stats", e)
            Result.Error(e)
        }
    }

    /**
     * ✅ FIXED: Create default delivery stats (eliminates "never used" flag)
     */
    override suspend fun createDefaultDeliveryStats(): Result<com.autogratuity.data.model.generated_kt.Delivery_stats> = withContext(ioDispatcher) {
        try {
            val defaultStats = cacheSystem.createDefaultDeliveryStats()
            Log.d("DeliveryLocalDataSource", "Created default delivery stats")
            Result.Success(defaultStats)
        } catch (e: Exception) {
            Log.e("DeliveryLocalDataSource", "Error creating default delivery stats", e)
            Result.Error(e)
        }
    }

    /**
     * ✅ FIXED: Get delivery error flow (eliminates "never used" flag)
     */
    override fun getDeliveryErrorFlow(): Flow<String?> {
        return cacheSystem.deliveryErrorFlow
    }
}
