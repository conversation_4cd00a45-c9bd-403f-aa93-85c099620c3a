package com.autogratuity.ui.dashboard

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import androidx.core.content.ContextCompat
import com.google.android.gms.maps.model.BitmapDescriptor
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.autogratuity.R
import com.autogratuity.domain.model.Address
import com.autogratuity.domain.model.ManualDndState

/**
 * Manages custom map markers for different address states
 */
class MapMarkerManager(private val context: Context) {
    
    enum class AddressMarkerState {
        PENDING_TRANSPARENT,  // Pending tips (tipAmount = null)
        DND_MANUAL,          // Manual DND override
        DND_RULE_BASED,      // Rule-based DND
        ACTIVE_HIGH_TIPS,    // High tip addresses (>$5 avg)
        ACTIVE_LOW_TIPS,     // Low tip addresses
        NEW_ADDRESS,         // New address, no deliveries
        VERIFIED_GOOD        // Verified good address
    }
    
    private val markerCache = mutableMapOf<AddressMarkerState, BitmapDescriptor>()
    
    /**
     * Get the appropriate marker state for an address
     */
    fun getMarkerStateForAddress(address: Address): AddressMarkerState {
        return when {
            // Pending delivery (tipAmount = null)
            address.deliveryStats?.let { stats ->
                // Check if there are any deliveries with null tipAmount
                stats.deliveryCount ?: 0 > 0 && hasNullTipAmounts(address)
            } == true -> AddressMarkerState.PENDING_TRANSPARENT

            // Manual DND override
            address.flags?.manualDndState == ManualDndState.FORCE_DND -> AddressMarkerState.DND_MANUAL

            // Rule-based DND - using domain model method
            address.isDndEnforced() -> AddressMarkerState.DND_RULE_BASED

            // High tip addresses
            address.deliveryStats?.averageTipAmount ?: 0.0 > 5.0 -> AddressMarkerState.ACTIVE_HIGH_TIPS

            // New address (no deliveries)
            address.deliveryStats?.deliveryCount ?: 0L == 0L -> AddressMarkerState.NEW_ADDRESS

            // Verified good address
            address.flags?.isVerified == true -> AddressMarkerState.VERIFIED_GOOD

            // Default: Low tip addresses
            else -> AddressMarkerState.ACTIVE_LOW_TIPS
        }
    }
    
    /**
     * Get the marker icon for a specific state
     */
    fun getMarkerIcon(state: AddressMarkerState): BitmapDescriptor {
        return markerCache.getOrPut(state) {
            createMarkerIcon(state)
        }
    }
    
    /**
     * Get the marker icon for an address
     */
    fun getMarkerIconForAddress(address: Address): BitmapDescriptor {
        val state = getMarkerStateForAddress(address)
        return getMarkerIcon(state)
    }
    
    /**
     * Create a marker icon for a specific state
     */
    private fun createMarkerIcon(state: AddressMarkerState): BitmapDescriptor {
        val drawableRes = when (state) {
            AddressMarkerState.PENDING_TRANSPARENT -> R.drawable.ic_pending_transparent
            AddressMarkerState.DND_MANUAL -> R.drawable.ic_dnd_marker
            AddressMarkerState.DND_RULE_BASED -> R.drawable.ic_dnd_marker
            AddressMarkerState.ACTIVE_HIGH_TIPS -> R.drawable.ic_high_tip_marker
            AddressMarkerState.ACTIVE_LOW_TIPS -> return BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_YELLOW)
            AddressMarkerState.NEW_ADDRESS -> return BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_BLUE)
            AddressMarkerState.VERIFIED_GOOD -> return BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN)
        }
        
        val drawable = ContextCompat.getDrawable(context, drawableRes)
        return drawable?.let { 
            BitmapDescriptorFactory.fromBitmap(drawableToBitmap(it, 48, 48))
        } ?: BitmapDescriptorFactory.defaultMarker()
    }
    
    /**
     * Convert drawable to bitmap with specified size
     */
    private fun drawableToBitmap(drawable: android.graphics.drawable.Drawable, width: Int, height: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, width, height)
        drawable.draw(canvas)
        return bitmap
    }
    
    /**
     * Check if address has deliveries with null tip amounts (pending)
     * This would need to be implemented based on your data structure
     */
    private fun hasNullTipAmounts(address: Address): Boolean {
        // TODO: Implement logic to check if there are pending deliveries
        // This might require querying the deliveries collection
        return false
    }
    
    /**
     * Clear the marker cache (call when theme changes, etc.)
     */
    fun clearCache() {
        markerCache.clear()
    }
    
    /**
     * Get a description for the marker state (for info windows)
     */
    fun getStateDescription(state: AddressMarkerState): String {
        return when (state) {
            AddressMarkerState.PENDING_TRANSPARENT -> "⏳ Pending Delivery"
            AddressMarkerState.DND_MANUAL -> "🚫 Manual DND"
            AddressMarkerState.DND_RULE_BASED -> "🚫 Rule-based DND"
            AddressMarkerState.ACTIVE_HIGH_TIPS -> "💰 High Tips"
            AddressMarkerState.ACTIVE_LOW_TIPS -> "💵 Active"
            AddressMarkerState.NEW_ADDRESS -> "🆕 New Address"
            AddressMarkerState.VERIFIED_GOOD -> "✅ Verified Good"
        }
    }
}