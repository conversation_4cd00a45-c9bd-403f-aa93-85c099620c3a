// Auto-generated from UserUsage.kt
// TypeScript equivalent of Android domain.model.UserUsage

/**
 * Domain model generated from Kotlin UserUsage
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface UserUsage {
  mappingCount?: number | null;
  deliveryCount?: number | null;
  addressCount?: number | null;
  dndMarkingsUsed?: number | null;
  maxDndMarkings?: number | null;
  autoCapturedOrders?: number | null;
  lastUsageUpdate?: Date | null;
}