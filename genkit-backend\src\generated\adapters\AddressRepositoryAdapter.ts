// Auto-generated from AddressRepository.kt
// IMPORTS FROM EXISTING GENERATED MODELS (not recreated)

import type { Result } from '../types/Result';
import type {
  Delivery as TsDeliveryInterface,
  Address as TsAddressInterface,
  User_profile as TsUserProfileInterface
} from '../../models/generated/delivery.schema';
import type {
  DeliveryStats as TsDeliveryStatsInterface
} from '../../models/generated/address.schema';
import type { AddressData as TsAddressData } from '../../models/generated/address.schema';

/**
 * TypeScript adapter interface generated from AddressRepository.kt
 * Maintains identical contract to Kotlin implementation
 * Uses existing generated models from schemas
 */
export interface AddressRepositoryAdapter {
  getAddressById(id: string): Result<Address?>;
  getAllAddresses(): Result<Address[]>;
  findAddressByNormalizedAddress(normalizedAddress: string): Result<Address?>;
  addAddress(address: Address): Result<string>;
  updateAddress(address: Address): Promise<Result<void>>;
  deleteAddress(id: string): Promise<Result<void>>;
  setDefaultAddress(addressId: string): Promise<Result<void>>;
  observeAddressById(id: string): Flow<Result<Address?>>;
  observeAddresses(): Flow<Result<Address[]>>;
  observeDefaultAddress(): Flow<Result<Address?>>;
  getDefaultAddress(): Result<Address?>;
  importAddresses(addresses: List<Map<string): Result<number>;
  findOrCreateAddressFromPlace(place: Place, userId: string): Result<Address>;
  getAddressesWithCoordinates(): Result<Address[]>;
  updateAddressFromPlace(addressId: string, place: Place, userId: string): Result<Address?>;
  setAddressFavorite(addressId: string, isFavorite: boolean): Promise<Result<void>>;
  setAddressVerified(addressId: string, isVerified: boolean): Promise<Result<void>>;
  setAddressAccessIssues(addressId: string, hasAccessIssues: boolean): Promise<Result<void>>;
  updateAddressNotes(addressId: string, notes: string | null): Promise<Result<void>>;
  setAddressDnd(addressId: string, dndEnabled: boolean): Promise<Result<void>>;
  updateAddressTags(addressId: string, tags: string[]): Promise<Result<void>>;
  normalizeAddress(addressString: string): string;
  parseAddressComponents(addressString: string): AddressComponents;
  geocodeAddress(address: Address): Address;
  prefetchCriticalData(): Promise<Result<void>>;
  initialize(): Promise<Result<void>>;
  cleanup(): Promise<Result<void>>;
}