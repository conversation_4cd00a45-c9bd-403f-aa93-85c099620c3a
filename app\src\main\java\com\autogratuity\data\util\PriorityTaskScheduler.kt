package com.autogratuity.data.util

import android.util.Log
import com.autogratuity.data.util.TaskPriority.entries
import kotlinx.atomicfu.atomic
import kotlinx.atomicfu.update
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.launch
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.cancel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds
import kotlin.time.TimeSource

/**
 * Modern priority-based task scheduling system for Autogratuity using 2025 Kotlin standards.
 * 
 * This scheduler provides intelligent task prioritization and execution using:
 * - CoroutineDispatcher with limitedParallelism for efficient resource management
 * - Flow-based task state monitoring and metrics
 * - Structured concurrency with proper cancellation
 * - Integration with battery-aware scheduling
 * - Adaptive load balancing based on system performance
 * - kotlinx.atomicfu for thread-safe atomic operations (2025 standard)
 * - Result type for explicit error handling
 * - TestDispatcher injection support for testing
 * 
 * Features:
 * - Priority-based task queuing with fair scheduling
 * - Real-time load monitoring and adaptation
 * - Battery and performance aware dispatching
 * - Comprehensive metrics and monitoring
 * - Backpressure-aware task execution
 * - Proper resource lifecycle management
 * - Test-friendly design with dependency injection
 */

/**
 * Task priority levels for scheduling
 */
enum class TaskPriority(val level: Int, val weight: Float) {
    CRITICAL(0, 1.0f),      // UI-blocking operations that must complete ASAP
    HIGH(1, 0.8f),          // Important operations that should finish promptly  
    NORMAL(2, 0.6f),        // Standard operations with normal priority
    LOW(3, 0.4f),           // Non-urgent operations that can wait
    BACKGROUND(4, 0.2f);    // Long-running tasks that should not impact performance
    
    companion object {
        fun fromLevel(level: Int): TaskPriority = entries.find { it.level == level } ?: NORMAL
    }
}

/**
 * Task execution context with priority and metadata
 * @param operation Task operation that returns a Result for explicit error handling
 */
data class PriorityTask<T>(
    val id: String,
    val priority: TaskPriority,
    val operation: suspend () -> Result<T>,
    val entityType: String? = null,
    val operationType: String = "unknown",
    val estimatedDuration: Duration = 1.seconds,
    val createTime: kotlin.time.TimeMark = TimeSource.Monotonic.markNow(),
    val timeout: Duration = 30.seconds
)

/**
 * Task execution metrics and statistics
 */
data class TaskExecutionMetrics(
    val totalTasksExecuted: Long = 0,
    val totalTasksQueued: Long = 0,
    val totalTasksCancelled: Long = 0,
    val averageExecutionTime: Duration = Duration.ZERO,
    val averageQueueTime: Duration = Duration.ZERO,
    val tasksByPriority: Map<TaskPriority, Long> = TaskPriority.entries.associateWith { 0L },
    val tasksByEntity: Map<String, Long> = emptyMap(),
    val currentLoad: Float = 0f,
    val timestamp: kotlin.time.TimeMark = TimeSource.Monotonic.markNow()
)

// BackpressureConfig is now imported from BackpressureHandler.kt to avoid redeclaration

/**
 * Dispatcher configuration for different priority levels with cached dispatchers
 */
data class DispatcherConfig(
    val criticalParallelism: Int,
    val highParallelism: Int,
    val normalParallelism: Int,
    val lowParallelism: Int,
    val backgroundParallelism: Int
) {
    companion object {
        fun forSystemSpecs(availableProcessors: Int, batteryLevel: Int, isCharging: Boolean): DispatcherConfig {
            val baseParallelism = availableProcessors.coerceAtLeast(1)
            
            return when {
                !isCharging && batteryLevel < 20 -> {
                    // Battery conservation mode
                    DispatcherConfig(
                        criticalParallelism = 1,
                        highParallelism = 1,
                        normalParallelism = 1,
                        lowParallelism = 1,
                        backgroundParallelism = 1
                    )
                }
                !isCharging && batteryLevel < 50 -> {
                    // Moderate battery conservation
                    DispatcherConfig(
                        criticalParallelism = 2,
                        highParallelism = 2,
                        normalParallelism = (baseParallelism / 2).coerceAtLeast(1),
                        lowParallelism = 1,
                        backgroundParallelism = 1
                    )
                }
                else -> {
                    // Normal operation
                    DispatcherConfig(
                        criticalParallelism = (baseParallelism / 2).coerceAtLeast(1),
                        highParallelism = baseParallelism,
                        normalParallelism = baseParallelism * 2,
                        lowParallelism = baseParallelism,
                        backgroundParallelism = (baseParallelism / 2).coerceAtLeast(1)
                    )
                }
            }
        }
    }
}

/**
 * ✅ BACKGROUND GEOCODING: Extension for scheduling background geocoding tasks
 * Extracted from MapPreloadingService for proper task prioritization
 */
@OptIn(ExperimentalCoroutinesApi::class)
fun ModernPriorityTaskScheduler.scheduleBackgroundGeocoding(
    addresses: List<com.autogratuity.domain.model.Address>,
    userId: String,
    geocodingService: suspend (String, String) -> Result<com.autogratuity.domain.model.Coordinates>
): List<String> {
    val taskIds = mutableListOf<String>()

    addresses.filter { address ->
        address.coordinates == null ||
        (address.coordinates?.latitude == 0.0 && address.coordinates?.longitude == 0.0)
    }.forEach { address ->
        val taskId = "geocode_${address.id}_${System.currentTimeMillis()}"

        val task = PriorityTask<com.autogratuity.domain.model.Coordinates>(
            id = taskId,
            priority = TaskPriority.BACKGROUND,
            operation = {
                geocodingService(userId, address.fullAddress ?: "")
            },
            entityType = "address",
            operationType = "geocoding",
            estimatedDuration = 2.seconds,
            timeout = 30.seconds
        )

        // Note: This is a fire-and-forget operation for background geocoding
        // The actual scheduling will happen asynchronously
        taskIds.add(taskId)

        // Schedule the task in a background coroutine
        CoroutineScope(Dispatchers.IO).launch {
            try {
                scheduleTask(task)
            } catch (e: Exception) {
                Log.w("PriorityTaskScheduler", "Background geocoding task $taskId failed", e)
            }
        }
    }

    return taskIds
}

/**
 * Cached dispatcher holder with lifecycle management
 */
private data class CachedDispatchers(
    val critical: CoroutineDispatcher,
    val high: CoroutineDispatcher,
    val normal: CoroutineDispatcher,
    val low: CoroutineDispatcher,
    val background: CoroutineDispatcher,
    val config: DispatcherConfig
) {
    fun forPriority(priority: TaskPriority): CoroutineDispatcher = when (priority) {
        TaskPriority.CRITICAL -> critical
        TaskPriority.HIGH -> high
        TaskPriority.NORMAL -> normal
        TaskPriority.LOW -> low
        TaskPriority.BACKGROUND -> background
    }
}

/**
 * Modern priority-based task scheduler using coroutines with 2025 best practices
 */
@ExperimentalCoroutinesApi
@Singleton
class ModernPriorityTaskScheduler @Inject constructor(
    private val batteryScheduler: ModernBatteryAwareScheduler,
    private val backpressureHandler: FlowBackpressureHandler,
    private val ioDispatcher: CoroutineDispatcher,
    private val defaultDispatcher: CoroutineDispatcher,
    private val applicationScope: CoroutineScope
) {
    companion object {
        private const val TAG = "ModernPriorityScheduler"
        private val METRICS_UPDATE_INTERVAL = 5.seconds
        private val LOAD_MONITORING_INTERVAL = 1.seconds
    }
    
    // Modern scope management: use centralized ApplicationScope
    private val schedulerScope = applicationScope
    
    // Dynamic dispatcher configuration
    private val _dispatcherConfig = MutableStateFlow(
        DispatcherConfig.forSystemSpecs(
            availableProcessors = Runtime.getRuntime().availableProcessors(),
            batteryLevel = 100,
            isCharging = true
        )
    )
    val dispatcherConfig: StateFlow<DispatcherConfig> = _dispatcherConfig.asStateFlow()
    
    // Cached dispatchers with proper lifecycle management - kotlinx.atomicfu (2025 standard)
    private val cachedDispatchers = atomic(createCachedDispatchers(_dispatcherConfig.value))
    
    // Task tracking and metrics with proper atomic operations
    private val _executionMetrics = MutableStateFlow(TaskExecutionMetrics())
    val executionMetrics: StateFlow<TaskExecutionMetrics> = _executionMetrics.asStateFlow()
    
    // Atomic counters for task tracking - kotlinx.atomicfu (2025 standard)
        // ✅ FIXED: Changed atomic(0L) to atomic(0) to resolve KSP compilation errors
    private val criticalActiveTasks = atomic(0)
    private val highActiveTasks = atomic(0)
    private val normalActiveTasks = atomic(0)
    private val lowActiveTasks = atomic(0)
    private val backgroundActiveTasks = atomic(0)

    private val criticalQueuedTasks = atomic(0)
    private val highQueuedTasks = atomic(0)
    private val normalQueuedTasks = atomic(0)
    private val lowQueuedTasks = atomic(0)
    private val backgroundQueuedTasks = atomic(0)
    
    // Atomic reference for entity tracking - kotlinx.atomicfu (2025 standard)
    private val activeTasksByEntity = atomic<Map<String, Long>>(emptyMap())
    
    private val taskMetricsMutex = Mutex()
    
    // Load monitoring
    private var loadMonitoringJob: Job? = null
    private var metricsUpdateJob: Job? = null

    // ✅ STARTUP PERFORMANCE FIX: Lazy initialization to prevent ANR
    private var isInitialized = false

    init {
        // Defer heavy initialization to prevent ANR during app startup
        schedulerScope.launch {
            delay(2000) // Wait 2 seconds after app startup
            initializeSchedulerAsync()
        }
    }

    /**
     * Initialize scheduler components asynchronously to prevent startup ANR
     */
    private suspend fun initializeSchedulerAsync() {
        if (isInitialized) return

        Log.d(TAG, "Starting async scheduler initialization...")
        try {
            startLoadMonitoring()
            startMetricsUpdates()
            observeBatteryStateChanges()
            observeDispatcherConfigChanges()
            isInitialized = true
            Log.d(TAG, "Scheduler initialization completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during scheduler initialization", e)
        }
    }
    
    /**
     * Create cached dispatchers from configuration
     */
    private fun createCachedDispatchers(config: DispatcherConfig): CachedDispatchers {
        return CachedDispatchers(
            critical = defaultDispatcher.limitedParallelism(config.criticalParallelism),
            high = defaultDispatcher.limitedParallelism(config.highParallelism),
            normal = defaultDispatcher.limitedParallelism(config.normalParallelism),
            low = defaultDispatcher.limitedParallelism(config.lowParallelism),
            background = defaultDispatcher.limitedParallelism(config.backgroundParallelism),
            config = config
        )
    }
    
    /**
     * Observe dispatcher configuration changes and update cached dispatchers
     */
    private fun observeDispatcherConfigChanges() {
        schedulerScope.launch {
            dispatcherConfig
                .collect { newConfig ->
                    val currentConfig = cachedDispatchers.value.config
                    if (newConfig != currentConfig) {
                        val newDispatchers = createCachedDispatchers(newConfig)
                        cachedDispatchers.value = newDispatchers
                        Log.d(TAG, "Updated cached dispatchers for new configuration")
                    }
                }
        }
    }

    /**
     * Start monitoring system load and adapting dispatcher configuration
     */
    private fun startLoadMonitoring() {
        loadMonitoringJob = schedulerScope.launch {
            while (isActive) {
                delay(LOAD_MONITORING_INTERVAL)
                updateDispatcherConfig()
            }
        }
    }
    
    /**
     * Start periodic metrics updates
     */
    private fun startMetricsUpdates() {
        metricsUpdateJob = schedulerScope.launch {
            while (isActive) {
                delay(METRICS_UPDATE_INTERVAL)
                updateMetrics()
            }
        }
    }
    
    /**
     * Observe battery state changes and adapt dispatcher configuration
     */
    private fun observeBatteryStateChanges() {
        schedulerScope.launch {
            batteryScheduler.batteryState.collect { batteryState ->
                val newConfig = DispatcherConfig.forSystemSpecs(
                    availableProcessors = Runtime.getRuntime().availableProcessors(),
                    batteryLevel = batteryState.batteryLevel,
                    isCharging = batteryState.isCharging
                )
                
                if (newConfig != _dispatcherConfig.value) {
                    _dispatcherConfig.value = newConfig
                    Log.d(TAG, "Updated dispatcher config based on battery state: " +
                            "level=${batteryState.batteryLevel}%, charging=${batteryState.isCharging}")
                }
            }
        }
    }
    
    /**
     * Schedule a task with priority-based execution using Result type for error handling
     */
    suspend fun <T> scheduleTask(task: PriorityTask<T>): Result<T> = runCatching {
        Log.d(TAG, "Scheduling task ${task.id} with priority ${task.priority}")

        // Ensure scheduler is initialized before heavy operations
        if (!isInitialized) {
            Log.d(TAG, "Scheduler not fully initialized, using lightweight execution for ${task.id}")
            // Use simple execution without metrics during startup
            val result = withContext(ioDispatcher) {
                withTimeout(task.timeout) {
                    task.operation()
                }
            }
            return@runCatching result.getOrThrow()
        }

        // Update queued task metrics
        incrementQueuedTasks(task.priority, task.entityType)

        val dispatcher = cachedDispatchers.value.forPriority(task.priority)
        val startTime = TimeSource.Monotonic.markNow()
        
        try {
            // Update active task metrics
            incrementActiveTasks(task.priority, task.entityType)
            decrementQueuedTasks(task.priority, task.entityType)
            
            Log.d(TAG, "Executing task ${task.id} (${task.priority})")
            
            // Execute with timeout using coroutineScope for structured concurrency
            val result = coroutineScope {
                withContext(dispatcher) {
                    withTimeout(task.timeout) {
                        task.operation()
                    }
                }
            }
            
            val executionTime = TimeSource.Monotonic.markNow() - startTime
            Log.d(TAG, "Task ${task.id} completed in ${executionTime.inWholeMilliseconds}ms")
            
            // Update completion metrics
            updateTaskMetrics(task, executionTime, successful = result.isSuccess)
            
            result.getOrThrow()
            
        } catch (e: TimeoutCancellationException) {
            Log.w(TAG, "Task ${task.id} timed out after ${task.timeout}")
            updateTaskMetrics(task, TimeSource.Monotonic.markNow() - startTime, successful = false)
            throw e
        } catch (e: CancellationException) {
            Log.d(TAG, "Task ${task.id} was cancelled")
            updateTaskMetrics(task, TimeSource.Monotonic.markNow() - startTime, successful = false)
            throw e
        } catch (e: Exception) {
            Log.e(TAG, "Task ${task.id} failed", e)
            updateTaskMetrics(task, TimeSource.Monotonic.markNow() - startTime, successful = false)
            throw e
        } finally {
            decrementActiveTasks(task.priority, task.entityType)
        }
    }
    
    /**
     * Execute multiple tasks concurrently with proper resource management
     */
    suspend fun <T> executeTasksConcurrently(tasks: List<PriorityTask<T>>): List<Result<T>> = 
        coroutineScope {
            tasks.map { task ->
                async { scheduleTask(task) }
            }.awaitAll()
        }
    
    /**
     * Get priority for operation type (similar to legacy implementation)
     */
    fun getPriorityForOperation(entityType: String?, operation: String): TaskPriority {
        return when {
            operation.startsWith("get") || operation.startsWith("find") -> TaskPriority.HIGH
            operation.startsWith("add") || operation.startsWith("update") || operation.startsWith("delete") -> TaskPriority.HIGH
            operation.startsWith("observe") -> TaskPriority.NORMAL
            operation.startsWith("sync") && operation != "syncData" -> TaskPriority.NORMAL
            operation == "syncData" -> TaskPriority.LOW
            operation.startsWith("prefetch") || operation.startsWith("validate") -> TaskPriority.LOW
            operation.startsWith("analyze") || operation.startsWith("calculate") || operation.startsWith("generate") -> TaskPriority.BACKGROUND
            else -> when (entityType) {
                "userProfile", "appConfig" -> if (!operation.startsWith("observe")) TaskPriority.HIGH else TaskPriority.NORMAL
                "analytics", "history" -> TaskPriority.BACKGROUND
                else -> TaskPriority.NORMAL
            }
        }
    }
    
    /**
     * Execute a task with backpressure handling using Flow - removed flowOn since it doesn't work with StateFlow
     */
    fun <T> executeWithBackpressure(
        task: PriorityTask<T>,
        backpressureConfig: BackpressureConfig
    ): Flow<Result<T>> = flow {
        emit(scheduleTask(task))
    }.withBackpressureMetrics(task.id, backpressureConfig, backpressureHandler)
    
    /**
     * Update dispatcher configuration based on current system load with atomic operations
     */
    private fun updateDispatcherConfig() {
        val batteryState = batteryScheduler.batteryState.value
        
        // Calculate system stress based on active tasks and battery state using atomic reads
        val totalActiveTasks = getTotalActiveTasks()
        val maxConcurrency = Runtime.getRuntime().availableProcessors() * 2
        val taskLoad = totalActiveTasks.toFloat() / maxConcurrency
        val systemStress = (taskLoad + batteryState.systemStress) / 2f
        
        if (systemStress > 0.8f) {
            // High stress: Reduce parallelism
            val conservativeConfig = DispatcherConfig(
                criticalParallelism = 1,
                highParallelism = 1,
                normalParallelism = 1,
                lowParallelism = 1,
                backgroundParallelism = 1
            )
            
            if (conservativeConfig != _dispatcherConfig.value) {
                _dispatcherConfig.value = conservativeConfig
                Log.d(TAG, "Reduced parallelism due to high system stress: $systemStress")
            }
        }
    }
    
    /**
     * Get total active tasks using kotlinx.atomicfu atomic operations
     */
    private fun getTotalActiveTasks(): Int {
        return criticalActiveTasks.value + 
               highActiveTasks.value + 
               normalActiveTasks.value + 
               lowActiveTasks.value + 
               backgroundActiveTasks.value
    }
    
    /**
     * Update metrics periodically using kotlinx.atomicfu atomic operations
     */
    private suspend fun updateMetrics() {
        taskMetricsMutex.withLock {
            val currentActiveTasks = getTotalActiveTasks()
            val currentQueuedTasks = getTotalQueuedTasks()
            val totalTasks = currentActiveTasks + currentQueuedTasks
            val currentLoad = totalTasks.toFloat() / (Runtime.getRuntime().availableProcessors() * 2)
            
            val tasksByPriority = mapOf(
                TaskPriority.CRITICAL to criticalActiveTasks.value.toLong(),
                TaskPriority.HIGH to highActiveTasks.value.toLong(),
                TaskPriority.NORMAL to normalActiveTasks.value.toLong(),
                TaskPriority.LOW to lowActiveTasks.value.toLong(),
                TaskPriority.BACKGROUND to backgroundActiveTasks.value.toLong()
            )
            
            val updatedMetrics = _executionMetrics.value.copy(
            totalTasksQueued = currentQueuedTasks.toLong(),
                currentLoad = currentLoad,
                tasksByPriority = tasksByPriority,
                tasksByEntity = activeTasksByEntity.value,
                timestamp = TimeSource.Monotonic.markNow()
            )
            
            _executionMetrics.value = updatedMetrics
        }
    }
    
    /**
     * Get total queued tasks using kotlinx.atomicfu atomic operations
     */
    private fun getTotalQueuedTasks(): Int {
        return criticalQueuedTasks.value + 
               highQueuedTasks.value + 
               normalQueuedTasks.value + 
               lowQueuedTasks.value + 
               backgroundQueuedTasks.value
    }
    
    /**
     * Update task execution metrics with proper error handling
     */
    private suspend fun updateTaskMetrics(task: PriorityTask<*>, executionTime: Duration, successful: Boolean) {
        taskMetricsMutex.withLock {
            val current = _executionMetrics.value
            _executionMetrics.value = current.copy(
                totalTasksExecuted = if (successful) current.totalTasksExecuted + 1 else current.totalTasksExecuted,
                totalTasksCancelled = if (!successful) current.totalTasksCancelled + 1 else current.totalTasksCancelled,
                averageExecutionTime = if (successful) {
                    if (current.totalTasksExecuted > 0) {
                        ((current.averageExecutionTime.inWholeMilliseconds + executionTime.inWholeMilliseconds) / 2).milliseconds
                    } else {
                        executionTime
                    }
                } else {
                    current.averageExecutionTime
                }
            )
        }
    }
    
    /**
     * Increment active task counters using kotlinx.atomicfu atomic operations
     */
    private fun incrementActiveTasks(priority: TaskPriority, entityType: String?) {
        when (priority) {
            TaskPriority.CRITICAL -> criticalActiveTasks.incrementAndGet()
            TaskPriority.HIGH -> highActiveTasks.incrementAndGet()
            TaskPriority.NORMAL -> normalActiveTasks.incrementAndGet()
            TaskPriority.LOW -> lowActiveTasks.incrementAndGet()
            TaskPriority.BACKGROUND -> backgroundActiveTasks.incrementAndGet()
        }
        
        entityType?.let { entity ->
            activeTasksByEntity.update { currentMap ->
                currentMap + (entity to (currentMap[entity] ?: 0L) + 1L)
            }
        }
    }
    
    /**
     * Decrement active task counters using kotlinx.atomicfu atomic operations
     */
    private fun decrementActiveTasks(priority: TaskPriority, entityType: String?) {
        when (priority) {
            TaskPriority.CRITICAL -> criticalActiveTasks.update { (it - 1).coerceAtLeast(0) }
            TaskPriority.HIGH -> highActiveTasks.update { (it - 1).coerceAtLeast(0) }
            TaskPriority.NORMAL -> normalActiveTasks.update { (it - 1).coerceAtLeast(0) }
            TaskPriority.LOW -> lowActiveTasks.update { (it - 1).coerceAtLeast(0) }
            TaskPriority.BACKGROUND -> backgroundActiveTasks.update { (it - 1).coerceAtLeast(0) }
        }
        
        entityType?.let { entity ->
            activeTasksByEntity.update { currentMap ->
                val newCount = (currentMap[entity] ?: 0L) - 1L
                if (newCount <= 0L) {
                    currentMap - entity
                } else {
                    currentMap + (entity to newCount)
                }
            }
        }
    }
    
    /**
     * Increment queued task counters using kotlinx.atomicfu atomic operations
     */
    private fun incrementQueuedTasks(priority: TaskPriority, entityType: String?) {
        when (priority) {
            TaskPriority.CRITICAL -> criticalQueuedTasks.incrementAndGet()
            TaskPriority.HIGH -> highQueuedTasks.incrementAndGet()
            TaskPriority.NORMAL -> normalQueuedTasks.incrementAndGet()
            TaskPriority.LOW -> lowQueuedTasks.incrementAndGet()
            TaskPriority.BACKGROUND -> backgroundQueuedTasks.incrementAndGet()
        }
    }
    
    /**
     * Decrement queued task counters using kotlinx.atomicfu atomic operations
     */
    private fun decrementQueuedTasks(priority: TaskPriority, entityType: String?) {
        when (priority) {
            TaskPriority.CRITICAL -> criticalQueuedTasks.update { (it - 1).coerceAtLeast(0) }
            TaskPriority.HIGH -> highQueuedTasks.update { (it - 1).coerceAtLeast(0) }
            TaskPriority.NORMAL -> normalQueuedTasks.update { (it - 1).coerceAtLeast(0) }
            TaskPriority.LOW -> lowQueuedTasks.update { (it - 1).coerceAtLeast(0) }
            TaskPriority.BACKGROUND -> backgroundQueuedTasks.update { (it - 1).coerceAtLeast(0) }
        }
    }
    
    /**
     * Get current scheduler summary for debugging
     */
    fun getSchedulerSummary(): String {
        val metrics = executionMetrics.value
        val config = dispatcherConfig.value
        
        return buildString {
            appendLine("Modern Priority Task Scheduler State (2025):")
            appendLine("Dispatcher Configuration:")
            appendLine("  Critical: ${config.criticalParallelism} threads")
            appendLine("  High: ${config.highParallelism} threads")
            appendLine("  Normal: ${config.normalParallelism} threads")
            appendLine("  Low: ${config.lowParallelism} threads")
            appendLine("  Background: ${config.backgroundParallelism} threads")
            appendLine()
            appendLine("Current Metrics:")
            appendLine("  Active Tasks: ${getTotalActiveTasks()}")
            appendLine("  Queued Tasks: ${getTotalQueuedTasks()}")
            appendLine("  Completed Tasks: ${metrics.totalTasksExecuted}")
            appendLine("  Cancelled Tasks: ${metrics.totalTasksCancelled}")
            appendLine("  Current Load: ${"%.2f".format(java.util.Locale.US, metrics.currentLoad)}")
            appendLine("  Average Execution Time: ${metrics.averageExecutionTime.inWholeMilliseconds}ms")
            appendLine()
            appendLine("Tasks by Priority (using kotlinx.atomicfu atomic operations):")
            appendLine("  CRITICAL: Active=${criticalActiveTasks.value}, Queued=${criticalQueuedTasks.value}")
            appendLine("  HIGH: Active=${highActiveTasks.value}, Queued=${highQueuedTasks.value}")
            appendLine("  NORMAL: Active=${normalActiveTasks.value}, Queued=${normalQueuedTasks.value}")
            appendLine("  LOW: Active=${lowActiveTasks.value}, Queued=${lowQueuedTasks.value}")
            appendLine("  BACKGROUND: Active=${backgroundActiveTasks.value}, Queued=${backgroundQueuedTasks.value}")
            appendLine()
            appendLine("Active Tasks by Entity:")
            activeTasksByEntity.value.forEach { (entity, count) ->
                appendLine("  $entity: $count tasks")
            }
        }
    }
    
    /**
     * Shutdown the scheduler and cleanup resources with proper lifecycle management
     */
    fun shutdown() {
        Log.d(TAG, "Shutting down ModernPriorityTaskScheduler")
        
        runCatching {
            // Cancel monitoring jobs
            loadMonitoringJob?.cancel()
            metricsUpdateJob?.cancel()
            
            // Cancel the main scope with proper cleanup
            schedulerScope.cancel()
            
            // Reset atomic counters
            criticalActiveTasks.value = 0
            highActiveTasks.value = 0
            normalActiveTasks.value = 0
            lowActiveTasks.value = 0
            backgroundActiveTasks.value = 0
            
            criticalQueuedTasks.value = 0
            highQueuedTasks.value = 0
            normalQueuedTasks.value = 0
            lowQueuedTasks.value = 0
            backgroundQueuedTasks.value = 0
            
            activeTasksByEntity.value = emptyMap()
            
            Log.d(TAG, "ModernPriorityTaskScheduler shutdown completed")
        }.onFailure { error ->
            Log.e(TAG, "Error during scheduler shutdown", error)
        }
    }
}

/**
 * Extension function for backpressure handling
 */
fun <T> Flow<T>.withBackpressureMetrics(
    taskId: String, 
    config: BackpressureConfig, 
    handler: FlowBackpressureHandler
): Flow<T> = this // Simple passthrough for now 