package com.autogratuity.data.repository.core

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

/**
 * Modern exception class for Firestore query errors with enhanced error reporting.
 *
 * This class provides standardized error reporting for query building and execution errors
 * with specific error types and detailed error messages to aid debugging.
 * 
 * Enhanced in 2025 with kotlinx.datetime and buildString patterns.
 */
class FirestoreQueryException : RuntimeException {

    /**
     * Enum defining the different types of query errors with enhanced categorization
     */
    enum class QueryErrorType {
        /** Invalid field name used in query */
        INVALID_FIELD,

        /** Invalid comparison operator used */
        INVALID_OPERATOR,

        /** Invalid value type for the specified field */
        INVALID_VALUE_TYPE,

        /** Query contains more clauses than Firestore supports */
        TOO_MANY_CLAUSES,

        /** Query limit exceeds maximum allowed value */
        LIMIT_EXCEEDED,

        /** Multiple order by clauses for the same field */
        DUPLICATE_ORDER_BY,

        /** Invalid order direction */
        INVALID_ORDER_DIRECTION,

        /** General query validation error */
        VALIDATION_ERROR,

        /** Query contains unsupported operation */
        UNSUPPORTED_OPERATION,

        /** Query execution error */
        EXECUTION_ERROR,

        /** Query timeout or performance related error */
        PERFORMANCE_ERROR,

        /** Query index missing or insufficient */
        INDEX_ERROR
    }

    // The specific type of query error
    val errorType: QueryErrorType

    // The field that caused the error (if applicable)
    val fieldName: String?

    // Timestamp when the error occurred
    val timestamp: Instant = Clock.System.now()

    // Additional error context
    val errorContext: Map<String, Any>

    /**
     * Constructor with error message
     *
     * @param message Detailed error message
     */
    constructor(message: String)
            : this(QueryErrorType.VALIDATION_ERROR, message, null as Throwable?)

    /**
     * Constructor with error type and message
     *
     * @param errorType Specific type of query error
     * @param message Detailed error message
     */
    constructor(errorType: QueryErrorType, message: String)
            : this(errorType, message, null as Throwable?)

    /**
     * Constructor with error type, message, and field name
     *
     * @param errorType Specific type of query error
     * @param message Detailed error message
     * @param fieldName Name of the field that caused the error (if applicable)
     */
    constructor(errorType: QueryErrorType, message: String, fieldName: String?)
            : super(formatMessage(errorType, message, fieldName)) {
        this.errorType = errorType
        this.fieldName = fieldName
        this.errorContext = buildMap {
            fieldName?.let { put("field_name", it) }
            put("error_type", errorType.name)
            put("timestamp", timestamp.toString())
        }
    }

    /**
     * Constructor with error type, message, and cause
     *
     * @param errorType Specific type of query error
     * @param message Detailed error message
     * @param cause The original exception that caused this error
     */
    constructor(errorType: QueryErrorType, message: String, cause: Throwable?)
            : super(formatMessage(errorType, message, null), cause) {
        this.errorType = errorType
        this.fieldName = null
        this.errorContext = buildMap {
            put("error_type", errorType.name)
            put("timestamp", timestamp.toString())
            cause?.let { 
                put("cause_type", it::class.simpleName ?: "Unknown")
                put("cause_message", it.message ?: "No message")
            }
        }
    }

    /**
     * Enhanced constructor with full context
     *
     * @param errorType Specific type of query error
     * @param message Detailed error message
     * @param fieldName Name of the field that caused the error (if applicable)
     * @param cause The original exception that caused this error
     * @param additionalContext Additional context information
     */
    constructor(
        errorType: QueryErrorType, 
        message: String, 
        fieldName: String?, 
        cause: Throwable?,
        additionalContext: Map<String, Any> = emptyMap()
    ) : super(formatMessage(errorType, message, fieldName), cause) {
        this.errorType = errorType
        this.fieldName = fieldName
        this.errorContext = buildMap {
            fieldName?.let { put("field_name", it) }
            put("error_type", errorType.name)
            put("timestamp", timestamp.toString())
            cause?.let { 
                put("cause_type", it::class.simpleName ?: "Unknown")
                put("cause_message", it.message ?: "No message")
            }
            putAll(additionalContext)
        }
    }

    /**
     * Get a comprehensive error summary for logging and debugging
     */
    fun getErrorSummary(): String = buildString {
        append("FirestoreQueryException [${errorType.name}]: ")
        append(message ?: "Unknown query error")
        
        if (errorContext.isNotEmpty()) {
            append(" Context: {")
            append(errorContext.entries.joinToString(", ") { "${it.key}=${it.value}" })
            append("}")
        }
        
        cause?.let { append(" Caused by: ${it::class.simpleName}: ${it.message}") }
    }

    /**
     * Convert this exception to a RepositoryException for unified error handling
     */
    fun toRepositoryException(): RepositoryException {
        return when (errorType) {
            QueryErrorType.INVALID_FIELD,
            QueryErrorType.INVALID_OPERATOR,
            QueryErrorType.INVALID_VALUE_TYPE,
            QueryErrorType.VALIDATION_ERROR -> {
                RepositoryException.ValidationError(
                    message ?: "Query validation error",
                    this,
                    fieldName = fieldName,
                    context = errorContext
                )
            }
            
            QueryErrorType.PERFORMANCE_ERROR,
            QueryErrorType.EXECUTION_ERROR -> {
                RepositoryException.TimeoutError(
                    message ?: "Query execution error",
                    this,
                    context = errorContext
                )
            }
            
            QueryErrorType.INDEX_ERROR -> {
                RepositoryException.ConfigurationError(
                    message ?: "Query index configuration error",
                    this,
                    context = errorContext
                )
            }
            
            else -> {
                RepositoryException.DatabaseError(
                    message ?: "Firestore query error",
                    this,
                    operation = "query_execution",
                    context = errorContext
                )
            }
        }
    }

    companion object {
        /**
         * Format a detailed error message with type and field information using buildString
         *
         * @param errorType The type of query error
         * @param message Base error message
         * @param fieldName Name of the field that caused the error (if applicable)
         * @return Formatted error message
         */
        private fun formatMessage(errorType: QueryErrorType, message: String, fieldName: String?): String = buildString {
            append("Firestore query error [${errorType.name}]: ")
            append(message)

            if (fieldName != null && fieldName.isNotEmpty()) {
                append(" (Field: $fieldName)")
            }
        }

        /**
         * Create a FirestoreQueryException for field validation errors
         */
        fun invalidField(fieldName: String, reason: String? = null): FirestoreQueryException {
            val message = buildString {
                append("Invalid field '$fieldName'")
                reason?.let { append(": $it") }
            }
            return FirestoreQueryException(
                QueryErrorType.INVALID_FIELD,
                message,
                fieldName,
                null,
                mapOf("validation_reason" to (reason ?: "unspecified"))
            )
        }

        /**
         * Create a FirestoreQueryException for operator validation errors
         */
        fun invalidOperator(operator: String, fieldName: String? = null): FirestoreQueryException {
            val message = buildString {
                append("Invalid operator '$operator'")
                fieldName?.let { append(" for field '$it'") }
            }
            return FirestoreQueryException(
                QueryErrorType.INVALID_OPERATOR,
                message,
                fieldName,
                null,
                mapOf("operator" to operator)
            )
        }

        /**
         * Create a FirestoreQueryException for value type errors
         */
        fun invalidValueType(
            fieldName: String, 
            expectedType: String, 
            actualType: String, 
            value: Any?
        ): FirestoreQueryException {
            val message = buildString {
                append("Invalid value type for field '$fieldName': ")
                append("expected $expectedType, got $actualType")
                value?.let { append(" (value: $it)") }
            }
            return FirestoreQueryException(
                QueryErrorType.INVALID_VALUE_TYPE,
                message,
                fieldName,
                null,
                mapOf(
                    "expected_type" to expectedType,
                    "actual_type" to actualType,
                    "value" to (value?.toString() ?: "null")
                )
            )
        }

        /**
         * Create a FirestoreQueryException for query complexity errors
         */
        fun tooManyCluses(currentCount: Int, maxAllowed: Int): FirestoreQueryException {
            val message = "Query contains $currentCount clauses, maximum allowed is $maxAllowed"
            return FirestoreQueryException(
                QueryErrorType.TOO_MANY_CLAUSES,
                message,
                null,
                null,
                mapOf(
                    "current_count" to currentCount,
                    "max_allowed" to maxAllowed
                )
            )
        }

        /**
         * Create a FirestoreQueryException for invalid order direction errors
         */
        fun invalidOrderDirection(direction: String, fieldName: String? = null): FirestoreQueryException {
            val message = buildString {
                append("Invalid order direction '$direction'")
                fieldName?.let { append(" for field '$it'") }
                append(". Valid directions are 'ASC' or 'DESC'")
            }
            return FirestoreQueryException(
                QueryErrorType.INVALID_ORDER_DIRECTION,
                message,
                fieldName,
                null,
                mapOf("direction" to direction)
            )
        }

        /**
         * Create a FirestoreQueryException for index-related errors
         */
        fun indexError(description: String, suggestedIndex: String? = null): FirestoreQueryException {
            val message = buildString {
                append("Query index error: ")
                append(description)
                suggestedIndex?.let { append(". Suggested index: $it") }
            }
            return FirestoreQueryException(
                QueryErrorType.INDEX_ERROR,
                message,
                null,
                null,
                buildMap {
                    put("description", description)
                    suggestedIndex?.let { put("suggested_index", it) }
                }
            )
        }
    }
}