import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore, Timestamp, FieldValue, Transaction } from 'firebase-admin/firestore';
import { dndPreferencesCache, ComputedDndSettings } from '../utils/dnd-preferences-cache';

// Import existing types and validation/sanitization functions to ensure consistency
// We will reuse these to avoid duplicating logic and ensure data integrity.
import {
  Address,
  DeliveryStats,
} from '../models/generated/address.schema';
import { Delivery } from '../models/generated/delivery.schema';
import { Status } from '../models/generated/status.schema';

import {
    validateAndCastAddress,
    validateAndCastDelivery,
    evaluateDndForDeliveryData,
    validateAndCastStatus,
} from './address-stats-updater'; // Assuming these can be exported

const db = getFirestore();

// Step 1: Define a clear and comprehensive input schema for the callable function.
// This schema will handle different types of actions through a discriminated union.

const NewDeliveryPayloadSchema = z.object({
  type: z.literal('NEW_DELIVERY'),
  deliveryData: z.any(), // We'll rely on validateAndCastDelivery for the shape
});

const ManualDndPayloadSchema = z.object({
  type: z.literal('MANUAL_DND_OVERRIDE'),
  manualDndState: z.enum(['FORCE_DND', 'FORCE_ALLOW', 'CLEAR']),
});

const RecalculateStatsPayloadSchema = z.object({
    type: z.literal('RECALCULATE_STATS'),
});

const UpdateActionSchema = z.discriminatedUnion('type', [
  NewDeliveryPayloadSchema,
  ManualDndPayloadSchema,
  RecalculateStatsPayloadSchema,
]);

export const ProcessAddressUpdateInputSchema = z.object({
  userId: z.string(),
  addressId: z.string(),
  idempotencyKey: z.string().uuid(),
  action: UpdateActionSchema,
});

export const ProcessAddressUpdateOutputSchema = z.object({
  status: z.enum(['SUCCESS', 'SKIPPED', 'ERROR']),
  message: z.string(),
  addressId: z.string(),
});

// A pure function to calculate the new state. This function is deterministic and testable.
// It takes the current state and an action, and returns the full set of updates.
async function calculateNewState(
    transaction: Transaction,
    userId: string,
    addressId: string,
    action: z.infer<typeof UpdateActionSchema>,
    logPrefix: string
) {
    // 1. READ all necessary documents within the transaction
    const userRef = db.collection('users').doc(userId);
    const addressRef = userRef.collection('user_addresses').doc(addressId);
    const deliveriesQuery = userRef.collection('user_deliveries')
        .where('deliveryData.reference.addressId', '==', addressId)
        .orderBy('deliveryData.metadata.createdAt', 'desc');

    const [userDoc, addressDoc, deliveriesSnapshot, userDndPrefs] = await Promise.all([
        transaction.get(userRef),
        transaction.get(addressRef),
        transaction.get(deliveriesQuery),
        dndPreferencesCache.getUserDndPreferences(userId, logPrefix),
    ]);

    if (!addressDoc.exists) {
        throw new Error(`Address ${addressId} not found for user ${userId}`);
    }

    // 2. PROCESS the action and calculate the new state
    const currentAddress = validateAndCastAddress(addressDoc.data());
    const allDeliveries = deliveriesSnapshot.docs.map(doc => validateAndCastDelivery(doc.data(), doc.id)).filter(Boolean) as Delivery[];

    let newManualDndState = currentAddress?.addressData?.flags?.manualDndState ?? null;

    if (action.type === 'MANUAL_DND_OVERRIDE') {
        newManualDndState = action.manualDndState === 'CLEAR' ? null : action.manualDndState;
    }
    
    if (action.type === 'NEW_DELIVERY') {
        // Here you would add the new delivery to the `allDeliveries` array before recalculating
        const newDelivery = validateAndCastDelivery(action.deliveryData);
        if (newDelivery) {
            allDeliveries.unshift(newDelivery); // Add to the start for processing
        }
    }

    // 3. RECALCULATE stats and DND status using existing logic
    const hasIncompleteHistory = deliveriesSnapshot.size >= 500;
    const allDeliveryData = allDeliveries.map(d => d.deliveryData);
    const dndResult = evaluateDndForDeliveryData(allDeliveryData, userDndPrefs, hasIncompleteHistory);

    let finalDndStatus = dndResult.dnd;
    let finalDndSource = dndResult.reason ? (dndResult.reason === 'EXPLICIT_IMPORT' ? 'RULE_BASED_EXPLICIT_IMPORT' : 'RULE_BASED_USER_PREFERENCES') : null;

    if (newManualDndState === 'FORCE_DND') {
        finalDndStatus = true;
        finalDndSource = 'MANUAL_USER_FORCE_DND';
    } else if (newManualDndState === 'FORCE_ALLOW') {
        finalDndStatus = false;
        finalDndSource = 'MANUAL_USER_FORCE_ALLOW';
    }

    // Recalculate delivery stats
    let deliveryCount = 0;
    let tipCount = 0;
    let pendingCount = 0;
    let totalTips = 0;
    let highestTip: number | null = null;
    let lastDeliveryTimestamp: Timestamp | null = null;

    allDeliveries.forEach(delivery => {
        const deliveryData = delivery.deliveryData;
        deliveryCount++;
        const tipAmount = deliveryData.amounts?.tipAmount ?? null;
        const status = validateAndCastStatus(deliveryData.status);
        if (tipAmount != null && status?.isCompleted) {
            tipCount++;
            totalTips += tipAmount;
            if (highestTip === null || tipAmount > highestTip) {
                highestTip = tipAmount;
            }
        } else {
            pendingCount++;
        }
        const deliveryTimestamp = deliveryData.times?.completedAt ?? deliveryData.metadata?.createdAt;
        if (deliveryTimestamp && (lastDeliveryTimestamp === null || deliveryTimestamp.toMillis() > lastDeliveryTimestamp.toMillis())) {
            lastDeliveryTimestamp = deliveryTimestamp;
        }
    });

    const newStats: DeliveryStats = {
        deliveryCount,
        tipCount,
        totalTips,
        highestTip,
        pendingCount,
        averageTipAmount: tipCount > 0 ? totalTips / tipCount : 0,
        lastDeliveryTimestamp,
    };

    // 4. PREPARE all write operations
    const operations = {
        addressUpdate: {
            'addressData.deliveryStats': newStats,
            'addressData.flags.doNotDeliver': finalDndStatus,
            'addressData.flags.dndSource': finalDndSource,
            'addressData.flags.manualDndState': newManualDndState,
            'addressData.metadata.updatedAt': FieldValue.serverTimestamp(),
        },
        userProfileUpdate: {
            // We would calculate deltas here for atomic increments if needed
            'profileData.metadata.updatedAt': FieldValue.serverTimestamp(),
        },
        newDelivery: action.type === 'NEW_DELIVERY' ? action.deliveryData : null,
    };

    return operations;
}

/**
 * A single, atomic, and idempotent Cloud Function to handle all address-related updates.
 * This function replaces the brittle, event-driven cascade of the previous architecture.
 *
 * It uses a Firestore Transaction to guarantee that all reads and writes are atomic,
 * preventing race conditions and ensuring data consistency across all related collections.
 */
export const processAddressUpdateFlow = defineFlow(
    {
        name: 'processAddressUpdateFlow',
        inputSchema: ProcessAddressUpdateInputSchema,
        outputSchema: ProcessAddressUpdateOutputSchema,
    },
    async (input) => {
        const { userId, addressId, idempotencyKey, action } = input;
        const logPrefix = `[ProcessAddressUpdateFlow IdempotencyKey: ${idempotencyKey}] -`;

        console.log(`${logPrefix} Starting operation for User: ${userId}, Address: ${addressId}, Action: ${action.type}`);

        const idempotencyRef = db.collection('idempotencyKeys').doc(idempotencyKey);

        try {
            await db.runTransaction(async (transaction) => {
                const idempotencyDoc = await transaction.get(idempotencyRef);

                if (idempotencyDoc.exists) {
                    console.warn(`${logPrefix} SKIPPED: Idempotency key already processed.`);
                    throw new Error('IDEMPOTENCY_KEY_PROCESSED');
                }

                console.log(`${logPrefix} TRANSACTION: Idempotency check passed. Executing core logic.`);

                const operations = await calculateNewState(transaction, userId, addressId, action, logPrefix);

                const userRef = db.collection('users').doc(userId);
                const addressRef = userRef.collection('user_addresses').doc(addressId);
                
                transaction.update(addressRef, operations.addressUpdate);
                transaction.update(userRef, operations.userProfileUpdate);

                if (operations.newDelivery) {
                    const newDeliveryRef = userRef.collection('user_deliveries').doc();
                    transaction.set(newDeliveryRef, operations.newDelivery);
                }
                
                transaction.set(idempotencyRef, {
                    processedAt: FieldValue.serverTimestamp(),
                    userId,
                    addressId,
                    actionType: action.type,
                });
            });

            return {
                status: 'SUCCESS' as const,
                message: 'Address update processed successfully.',
                addressId,
            };

        } catch (error: any) {
            if (error.message === 'IDEMPOTENCY_KEY_PROCESSED') {
                return {
                    status: 'SKIPPED' as const,
                    message: 'This operation has already been processed.',
                    addressId,
                };
            }

            console.error(`${logPrefix} ERROR: Transaction failed.`, error);
            return {
                status: 'ERROR' as const,
                message: `An unexpected error occurred: ${error.message}`,
                addressId,
            };
        }
    }
); 