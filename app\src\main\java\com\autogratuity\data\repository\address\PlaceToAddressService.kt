@file:Suppress("DEPRECATION")

package com.autogratuity.data.repository.address

import android.util.Log
import com.autogratuity.data.datasource.remote.AddressRemoteDataSource
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.generated_kt.Address
import com.autogratuity.data.model.generated_kt.Address.Components as AddressComponents
import com.autogratuity.data.model.generated_kt.Coordinates as AddressCoordinates
import com.autogratuity.data.model.generated_kt.Delivery_stats as AddressDeliveryStats
import com.autogratuity.data.model.generated_kt.Flags as AddressFlags
import com.autogratuity.data.model.generated_kt.Metadata
import com.google.android.libraries.places.api.model.Place
import com.fasterxml.jackson.databind.ObjectMapper
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Handles all interactions with the Google Places API (or any other place service).
 * Manages logic for finding or creating addresses based on Place data,
 * including address deduplication and conversion to the application's Address model.
 */
@Singleton
class PlaceToAddressService @Inject constructor(
    private val remoteDataSource: AddressRemoteDataSource,
    private val objectMapper: ObjectMapper
) {
    private val TAG = "PlaceToAddressService"

    /**
     * Normalizes an address string for consistency and comparison.
     *
     * @param addressString The address string to normalize.
     * @return The normalized address string.
     */
    fun normalizeAddress(addressString: String): String {
        return addressString.lowercase(Locale.ROOT)
            .replace(Regex("[^a-z0-9\\s]"), "")
            .replace(Regex("\\s+"), " ")
            .trim()
    }

    /**
     * Parses an address string into components.
     *
     * @param addressString The address string to parse.
     * @return AddressComponents object containing the parsed components.
     */
    fun parseAddressComponents(addressString: String): AddressComponents {
        val streetName = StringBuilder()
        var city: String? = null
        var state: String? = null
        var postalCode: String? = null
        var country: String? = null
        var streetNumVal: String? = null

        if (addressString.isNotEmpty()) {
            val parts = addressString.split(",")
            if (parts.isNotEmpty()) streetName.append(parts[0].trim())
            
            if (parts.size > 1) city = parts[1].trim()
            
            if (parts.size > 2) {
                val stateZipCountryCandidate = parts[2].trim()
                val szParts = stateZipCountryCandidate.split(Regex("\\s+")) 
                if (szParts.isNotEmpty()) state = szParts.firstOrNull { it.length == 2 && it.all(Char::isLetter) }
                
                postalCode = szParts.firstOrNull { it.all(Char::isDigit) && (it.length == 5 || it.length == 9) }
                
                if (szParts.size > 1 && state == null && postalCode == null) {
                    country = stateZipCountryCandidate
                } else if (szParts.lastOrNull() != state && szParts.lastOrNull() != postalCode) {
                    country = szParts.lastOrNull()
                }

                val firstPartStreet = streetName.toString()
                val streetParts = firstPartStreet.split(Regex("\\s+"), 2)
                if (streetParts.isNotEmpty() && streetParts[0].all(Char::isDigit)) {
                    streetNumVal = streetParts[0]
                    if (streetParts.size > 1) {
                        streetName.clear().append(streetParts[1])
                    } else {
                        streetName.clear()
                    }
                }
                return AddressComponents(
                    streetNumber = streetNumVal,
                    streetName = streetName.toString().ifEmpty { null },
                    city = city,
                    state = state,
                    postalCode = postalCode,
                    country = country
                )
            }
            return AddressComponents(
                streetNumber = null,
                streetName = streetName.toString().ifEmpty { null },
                city = city,
                state = null,
                postalCode = null,
                country = null
            )
        }
        return AddressComponents(
            streetNumber = null,
            streetName = null,
            city = null,
            state = null,
            postalCode = null,
            country = null
        )
    }

    /**
     * Finds or creates an address based on a Google Place object.
     *
     * @param place The Google Place object.
     * @param userIdToSet The ID of the user to set as the owner.
     * @param repositoryUserId The ID of the user in the repository context (typically the same as userIdToSet).
     * @return Result holding the found or created DTO [Address] object, or an error.
     */
    suspend fun findOrCreateAddressFromPlace(place: Place, userIdToSet: String, repositoryUserId: String?): Result<Address> {
        if (userIdToSet.isBlank()) {
            return Result.Error(IllegalArgumentException("userIdToSet cannot be empty for findOrCreateAddressFromPlace"))
        }
        
        val userId = repositoryUserId ?: userIdToSet
        val placeId = place.id ?: return Result.Error(IllegalArgumentException("Place ID cannot be null"))

        // Try to find an existing address with this placeId using RemoteDataSource
        // Assuming remoteDataSource will have a method like findAddressByPlaceId that returns Result<Address?>
        // If it doesn't, this part needs adaptation or the method needs to be added to RemoteDataSource.
        // For now, let's simulate this call and proceed assuming it might return null or an error.
        
        // Placeholder: remoteDataSource.findAddressByPlaceId(userId, placeId)
        // Let's assume a more direct approach if findAddressByPlaceId isn't standard:
        // We might need to fetch all and filter, or rely on a specific query method in RemoteDataSource if it exists.
        // For the purpose of this refactor, let's assume we try to create and rely on Firestore rules or a subsequent check for duplicates if absolutely needed,
        // or that findAddressByPlaceId would be the correct method on remoteDataSource.
        // Given the original code did this: var existingAddress = addressFirestoreAccess.findAddressByPlaceId(userId, placeId)
        // We will assume remoteDataSource should provide a similar capability.
        
        // SIMPLIFIED: The original code directly called addressFirestoreAccess.findAddressByPlaceId.
        // For a cleaner SSoT, the remoteDataSource should expose such specific query if needed.
        // If not, this service might have to construct the DTO and try to add it, letting remoteDataSource handle conflicts if any.
        // Let's proceed with the creation path and assume AddressRemoteDataSource would ideally handle "find by place ID or specific unique field".
        // If remoteDataSource.findAddressByPlaceId is not available, this logic is more complex.
        // For now, let's reflect the original flow as best as possible with new dependencies.

        // Let's assume remoteDataSource has findAddressByPlaceId (DTO Address?)
        val findResult = remoteDataSource.findAddressByPlaceId(userId, placeId) // This method needs to exist on remoteDataSource
        
        when (findResult) {
            is Result.Success -> {
                if (findResult.data != null) {
                    Log.d(TAG, "Existing address found for placeId $placeId via remoteDataSource")
                    return Result.Success(findResult.data) // Return existing DTO Address
                }
                // Not found, proceed to create
            }
            is Result.Error -> {
                Log.w(TAG, "Error trying to findAddressByPlaceId $placeId via remoteDataSource, will attempt to create. Error: ${findResult.exception.message}")
                // Decide if we proceed to create or return error. Original code proceeded.
            }
            is Result.Loading -> {
                Log.e(TAG, "findAddressByPlaceId $placeId returned Loading state unexpectedly. Cannot proceed with findOrCreateAddressFromPlace.")
                return Result.Error(IllegalStateException("findAddressByPlaceId returned Loading state unexpectedly during find/create operation for placeId: $placeId"))
            }
        }
        
        // Create new AddressData from Place object (original logic for construction)
        val newAddressDataMap = mutableMapOf<String, Any?>() // ... (original map construction logic remains here)
        newAddressDataMap["userId"] = userIdToSet
        newAddressDataMap["fullAddress"] = place.address
        newAddressDataMap["normalizedAddress"] = normalizeAddress(place.address ?: "")
        newAddressDataMap["placeId"] = placeId
        newAddressDataMap["isDefault"] = false
        newAddressDataMap["notes"] = null
        newAddressDataMap["tags"] = emptyList<String>()
        newAddressDataMap["orderIds"] = emptyList<String>()

        val components = AddressComponents(
            streetNumber = place.addressComponents?.asList()?.find { it.types.contains("street_number") }?.name,
            streetName = place.addressComponents?.asList()?.find { it.types.contains("route") }?.name,
            city = place.addressComponents?.asList()?.find { it.types.containsAll(listOf("locality", "political")) }?.name,
            state = place.addressComponents?.asList()?.find { it.types.containsAll(listOf("administrative_area_level_1", "political")) }?.shortName,
            postalCode = place.addressComponents?.asList()?.find { it.types.contains("postal_code") }?.name,
            country = place.addressComponents?.asList()?.find { it.types.containsAll(listOf("country", "political")) }?.shortName
        )
        val coordinates = place.latLng?.let { AddressCoordinates(latitude = it.latitude, longitude = it.longitude) }
        val searchFields = Address.SearchFields(
            searchTerms = listOfNotNull(place.name, place.address).filter { it.isNotEmpty() },
            normalizedKey = null
        )
        val deliveryStats = AddressDeliveryStats()
        val flags = AddressFlags(isVerified = true)
        val metadata = Metadata(source = "google_places_api")

        val newAddressDataObject = Address.AddressData(
            userId = userIdToSet,
            fullAddress = place.address,
            normalizedAddress = normalizeAddress(place.address ?: ""),
            placeId = placeId,
            isDefault = false,
            notes = null,
            tags = emptyList<String>(),
            orderIds = emptyList<String>(),
            searchTerms = listOfNotNull(place.name, place.address).filter { it.isNotEmpty() },
            components = components,
            coordinates = coordinates,
            searchFields = searchFields,
            deliveryStats = deliveryStats, 
            flags = flags, 
            metadata = metadata
        )
        
        // Add the address to Firestore via RemoteDataSource
        Log.d(TAG, "Adding new address for placeId $placeId via remoteDataSource")
        val addResult = remoteDataSource.addAddress(userId, newAddressDataObject) // Returns Result<DocumentReference>
        
        return when (addResult) {
            is Result.Success -> {
                val newDocRef = addResult.data
                // Fetch the newly created address via RemoteDataSource
                Log.d(TAG, "Fetching newly created address ${newDocRef.id} via remoteDataSource")
                val fetchNewResult = remoteDataSource.getAddressById(userId, newDocRef.id) // Returns Result<Address?>
                when (fetchNewResult) {
                    is Result.Success -> {
                        if (fetchNewResult.data != null) {
                            Result.Success(fetchNewResult.data)
                        } else {
                            Log.e(TAG, "Failed to fetch newly created address with id ${newDocRef.id} after add")
                            Result.Error(IllegalStateException("Failed to fetch newly created address with id ${newDocRef.id}"))
                        }
                    }
                    is Result.Error -> {
                        Log.e(TAG, "Error fetching newly created address ${newDocRef.id}", fetchNewResult.exception)
                        Result.Error(fetchNewResult.exception)
                    }
                    is Result.Loading -> {
                        Log.e(TAG, "getAddressById ${newDocRef.id} returned Loading unexpectedly after add.")
                        Result.Error(IllegalStateException("getAddressById returned Loading unexpectedly for ${newDocRef.id}"))
                    }
                }
            }
            is Result.Error -> {
                Log.e(TAG, "Error adding new address for placeId $placeId", addResult.exception)
                Result.Error(addResult.exception)
            }
            is Result.Loading -> {
                Log.e(TAG, "addAddress for placeId $placeId returned Loading unexpectedly.")
                Result.Error(IllegalStateException("addAddress returned Loading unexpectedly for placeId $placeId"))
            }
        }
    }

    /**
     * Updates an existing address with details from a Google Place.
     *
     * @param userId The ID of the user.
     * @param addressId The ID of the address to update.
     * @param place The Google Place object.
     * @return Result holding [Unit] on success, or an error.
     */
    suspend fun updateAddressDetailsFromPlace(userId: String, addressId: String, place: Place): Result<Unit> {
        if (userId.isBlank()) {
            return Result.Error(IllegalArgumentException("User ID cannot be blank for updateAddressDetailsFromPlace"))
        }
        if (addressId.isBlank()) {
            return Result.Error(IllegalArgumentException("Address ID must not be empty"))
        }
        val placeIdFromGoogle = place.id ?: return Result.Error(IllegalArgumentException("Place ID from Google Place cannot be null"))

        // Get the existing address DTO using RemoteDataSource
        Log.d(TAG, "Fetching existing DTO address $addressId for update via remoteDataSource")
        val existingAddressResult = remoteDataSource.getAddressById(userId, addressId) // Returns Result<Address?>
        
        val existingAddressDto = when (existingAddressResult) {
            is Result.Success -> {
                if (existingAddressResult.data == null) {
                    Log.e(TAG, "Address $addressId not found for update.")
                    return Result.Error(NoSuchElementException("Address $addressId not found for update."))
                }
                existingAddressResult.data
            }
            is Result.Error -> {
                Log.e(TAG, "Error fetching address $addressId for update.", existingAddressResult.exception)
                return Result.Error(existingAddressResult.exception)
            }
            is Result.Loading -> {
                Log.e(TAG, "getAddressById $addressId for update returned Loading unexpectedly.")
                return Result.Error(IllegalStateException("getAddressById returned Loading unexpectedly for $addressId during update"))
            }
        }

        val baseAddressData = existingAddressDto.addressData

        // Update components from Place (original logic)
        val baseComponents = baseAddressData.components
        val updatedComponents = AddressComponents(
            streetNumber = place.addressComponents?.asList()?.find { it.types.contains("street_number") }?.name ?: baseComponents?.streetNumber,
            streetName = place.addressComponents?.asList()?.find { it.types.contains("route") }?.name ?: baseComponents?.streetName,
            city = place.addressComponents?.asList()?.find { it.types.containsAll(listOf("locality", "political")) }?.name ?: baseComponents?.city,
            state = place.addressComponents?.asList()?.find { it.types.containsAll(listOf("administrative_area_level_1", "political")) }?.shortName ?: baseComponents?.state,
            postalCode = place.addressComponents?.asList()?.find { it.types.contains("postal_code") }?.name ?: baseComponents?.postalCode,
            country = place.addressComponents?.asList()?.find { it.types.containsAll(listOf("country", "political")) }?.shortName ?: baseComponents?.country
        )

        // Update coordinates from Place (original logic)
        val updatedCoordinates = place.latLng?.let { 
            AddressCoordinates(latitude = it.latitude, longitude = it.longitude) 
        } ?: baseAddressData.coordinates

        // Update metadata (original logic)
        val baseMetadata = baseAddressData.metadata ?: Metadata()
        val updatedMetadata = baseMetadata.copy(
            source = "google_places_api_update",
            version = (baseMetadata.version ?: 0L) + 1L
        )

        // Set the address as verified (original logic)
        val baseFlags = baseAddressData.flags ?: AddressFlags()
        val updatedFlags = baseFlags.copy(isVerified = true)

        // Prepare the updated address data (original logic)
        val updatedAddressData = Address.AddressData(
            userId = baseAddressData.userId,
            fullAddress = place.address ?: baseAddressData.fullAddress,
            normalizedAddress = normalizeAddress(place.address ?: baseAddressData.fullAddress ?: ""),
            placeId = placeIdFromGoogle,
            isDefault = baseAddressData.isDefault,
            notes = baseAddressData.notes,
            tags = baseAddressData.tags,
            orderIds = baseAddressData.orderIds,
            searchTerms = baseAddressData.searchTerms, 
            components = updatedComponents,
            coordinates = updatedCoordinates,
            searchFields = baseAddressData.searchFields,
            deliveryStats = baseAddressData.deliveryStats,
            flags = updatedFlags,
            metadata = updatedMetadata,
            platform = baseAddressData.platform
        )

        // Create an updated DTO Address object
        val updatedAddressDto = Address(id = addressId, addressData = updatedAddressData)

        // Update in Firestore via RemoteDataSource
        Log.d(TAG, "Updating address $addressId via remoteDataSource")
        val updateResult = remoteDataSource.updateAddress(userId, updatedAddressDto) // Returns Result<Unit>
        
        return when (updateResult) {
            is Result.Success -> {
                Log.d(TAG, "Address details updated from Place for ID: $addressId via remoteDataSource")
                Result.Success(Unit)
            }
            is Result.Error -> {
                Log.e(TAG, "Error updating address $addressId from Place via remoteDataSource", updateResult.exception)
                Result.Error(updateResult.exception)
            }
            is Result.Loading -> {
                Log.e(TAG, "updateAddress $addressId returned Loading unexpectedly.")
                Result.Error(IllegalStateException("updateAddress returned Loading unexpectedly for $addressId"))
            }
        }
    }

    /**
     * Fetches place predictions based on a query string.
     *
     * @param query The user's input query.
     * @return A list of potential place predictions.
     */
    fun getPlacePredictions(query: String): List<String> {
        // TODO: Implement actual call to Google Places Autocomplete API
        // For now, we're returning a simple implementation that would need to be enhanced
        throw NotImplementedError("getPlacePredictions is not yet implemented")
    }

    /**
     * Geocodes an address to get coordinates.
     *
     * @param address The address to geocode.
     * @return The geocoded address with coordinates.
     */
    fun geocodeAddress(address: Address): Address {
        // TODO: Implement geocoding logic using Google Maps Geocoding API
        // For now, return the original address since this requires API calls
        Log.w(TAG, "geocodeAddress not implemented yet, returning original address")
        return address
    }
} 