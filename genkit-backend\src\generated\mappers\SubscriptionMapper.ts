// Auto-generated from SubscriptionMapper.kt
import { Result } from '../types/Result';
import { Delivery } from '../models/domain/Delivery';

/**
 * Business logic mapper generated from Kotlin SubscriptionMapper
 */
export class SubscriptionMapper {
  isSubscriptionExpired([object Object]): boolean { {
    // TODO: Port business logic from Kotlin SubscriptionMapper.isSubscriptionExpired
    throw new Error('isSubscriptionExpired not yet implemented');
  }
}