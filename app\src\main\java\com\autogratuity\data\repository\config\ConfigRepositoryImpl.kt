package com.autogratuity.data.repository.config

// FirestoreRepository and FirestoreRepositoryDependencies removed - no longer needed
import android.util.Log
import com.autogratuity.data.datasource.local.ConfigLocalDataSource
import com.autogratuity.data.datasource.remote.ConfigRemoteDataSource
import com.autogratuity.data.mapper.ConfigMapper
import com.autogratuity.data.model.Result
import com.autogratuity.data.repository.core.RepositoryErrorHandler
import com.autogratuity.data.util.AuthenticationStateCoordinator
// ✅ REMOVED: CacheWarmingManager import removed from domain repositories
import com.autogratuity.data.util.ModernPriorityTaskScheduler
import com.autogratuity.data.util.RequestDeduplicationManager
import com.autogratuity.data.util.SessionManager
import com.autogratuity.data.util.TaskPriority
import com.autogratuity.data.util.PriorityTask
import com.autogratuity.debug.ClarityArchitectureMonitor
import com.autogratuity.domain.model.AppConfig
import com.autogratuity.domain.model.NotificationPatterns
import com.autogratuity.domain.repository.ConfigRepository
import com.autogratuity.data.repository.config.ConfigRepository as ConfigProfileRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.time.Duration
import com.autogratuity.data.util.RequestKeys
import com.autogratuity.data.util.RequestTimeouts
import kotlin.time.Duration.Companion.seconds
import kotlin.time.TimeSource

/**
 * ConfigRepositoryImpl following Clarity Architecture with enterprise-grade infrastructure.
 * Pure orchestration between RemoteDataSource + LocalDataSource + Mapper.
 *
 * Follows the delivery domain pattern exactly:
 * - RemoteDataSource: DTO operations with Firestore
 * - LocalDataSource: SSoT caching operations
 * - Mapper: DTO↔SSoT transformation + business logic
 * - RepositoryImpl: Pure orchestration with Result<T> wrapping
 *
 * ✅ ENHANCED: Full enterprise infrastructure integration following UserProfileRepositoryImpl pattern
 * - Priority task scheduling for critical config operations
 * - Request deduplication for preventing duplicate config fetches
 * - Session management for operation correlation
 * - Cache warming for intelligent data preloading
 * - Authentication state coordination
 * - Comprehensive error handling and monitoring
 * - Validation engine integration
 *
 * ✅ MODERNIZED: Simplified constructor for Constructor DSL compatibility
 */
@OptIn(ExperimentalCoroutinesApi::class)
class ConfigRepositoryImpl(
    // Core architectural dependencies following clarity.md principles
    private val remoteDataSource: ConfigRemoteDataSource,
    private val localDataSource: ConfigLocalDataSource,
    private val configMapper: ConfigMapper,
    // Repository orchestration dependencies
    private val ioDispatcher: CoroutineDispatcher,
    private val applicationScope: CoroutineScope,
    // ✅ STARTUP PERFORMANCE FIX: Lazy infrastructure utilities to prevent ANR
    private val requestDeduplicationManager: Lazy<RequestDeduplicationManager>,
    private val priorityTaskScheduler: Lazy<ModernPriorityTaskScheduler>,
    private val sessionManager: Lazy<SessionManager>,
    // ✅ REMOVED: CacheWarmingManager dependency removed from domain repositories
    private val authStateCoordinator: Lazy<AuthenticationStateCoordinator>,
    // Performance infrastructure
    private val repositoryErrorHandler: Lazy<RepositoryErrorHandler>,
    // JSON serialization with proper JSR310 support
    private val objectMapper: com.fasterxml.jackson.databind.ObjectMapper
) : ConfigRepository, ConfigProfileRepository {

    companion object {
        private const val TAG = "ConfigRepositoryImpl"
    }

    init {
        Log.d(TAG, "Initializing ConfigRepositoryImpl with enterprise infrastructure following Clarity Architecture")
        // Event bus integration simplified
        Log.i(TAG, "ConfigRepositoryImpl initialized successfully with full infrastructure support")
    }

    /**
     * Execute operation with priority task scheduling
     * Following UserProfileRepositoryImpl pattern for consistent task prioritization
     */
    private suspend fun <T> executeWithPriority(
        operation: String,
        entityType: String = "Config",
        entityId: String? = null,
        priority: TaskPriority = TaskPriority.HIGH,
        block: suspend () -> Result<T>
    ): Result<T> {
        val task = PriorityTask<T>(
            id = "${operation}_${entityId ?: "config"}",
            priority = priority,
            operation = {
                // Convert com.autogratuity.data.model.Result to kotlin.Result
                val result = block()
                when (result) {
                    is Result.Success -> kotlin.Result.success(result.data)
                    is Result.Error -> kotlin.Result.failure(result.exception)
                    is Result.Loading -> kotlin.Result.failure(IllegalStateException("Unexpected Loading state"))
                }
            },
            entityType = entityType,
            operationType = operation,
            estimatedDuration = 3.seconds,
            timeout = 15.seconds
        )

        return try {
            val kotlinResult = priorityTaskScheduler.value.scheduleTask(task)
            when {
                kotlinResult.isSuccess -> Result.Success(kotlinResult.getOrThrow())
                else -> Result.Error((kotlinResult.exceptionOrNull() ?: Exception("Unknown error")) as Exception)
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    /**
     * Execute operation with comprehensive profiling and monitoring
     * Following UserProfileRepositoryImpl pattern for consistent performance tracking
     */
    private suspend fun <T> executeWithProfiling(
        operation: String,
        entityId: String? = null,
        block: suspend () -> Result<T>
    ): Result<T> {
        val startTime = TimeSource.Monotonic.markNow()
        val session = sessionManager.value.getCurrentSession()

        Log.d(TAG, "executeWithProfiling: Starting $operation for entity $entityId (session: ${session?.sessionId})")

        return try {
            val result = block()
            val duration = startTime.elapsedNow()

            // Monitor successful operation with comprehensive metrics
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "ConfigRepository",
                operation = operation,
                duration = duration,
                success = result is Result.Success,
                cacheHit = false, // Will be overridden by specific operations
                dataType = "Config",
                entityId = entityId ?: "config",
                userId = null, // Config operations are typically system-wide
                resultCount = if (result is Result.Success) 1 else 0,
                dataSource = "repository",
                dataSize = 0, // Will be overridden by specific operations
                cacheStrategy = "cache-first"
            )

            Log.d(TAG, "executeWithProfiling: Completed $operation in ${duration.inWholeMilliseconds}ms")
            result

        } catch (e: Exception) {
            val duration = startTime.elapsedNow()

            // Monitor failed operation
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "ConfigRepository",
                operation = operation,
                duration = duration,
                success = false,
                error = e,
                dataType = "Config",
                entityId = entityId ?: "config",
                userId = null,
                resultCount = 0,
                dataSource = "error"
            )

            Log.e(TAG, "executeWithProfiling: Failed $operation after ${duration.inWholeMilliseconds}ms", e)
            Result.Error(e)
        }
    }

    /**
     * Enhanced error handling with comprehensive validation and monitoring
     * Following UserProfileRepositoryImpl pattern for consistent error management
     */
    private suspend fun <T> executeWithEnhancedErrorHandling(
        operation: String,
        entityId: String? = null,
        validateInput: suspend () -> Unit = {},
        block: suspend () -> Result<T>
    ): Result<T> {
        val startTime = TimeSource.Monotonic.markNow()
        val session = sessionManager.value.getCurrentSession()

        return try {
            // 1. Input validation phase
            validateInput()

            // 2. Execute operation with profiling
            val result = executeWithProfiling(operation, entityId, block)

            // 3. Result validation
            when (result) {
                is Result.Success -> {
                    Log.d(TAG, "executeWithEnhancedErrorHandling: $operation completed successfully for entity $entityId")
                    result
                }
                is Result.Error -> {
                    Log.w(TAG, "executeWithEnhancedErrorHandling: $operation failed for entity $entityId: ${result.exception.message}")

                    // Enhanced error monitoring with session correlation
                    ClarityArchitectureMonitor.addSessionEvent("error:${operation}:$entityId")
                    result
                }
                is Result.Loading -> {
                    Log.w(TAG, "executeWithEnhancedErrorHandling: $operation returned Loading for entity $entityId")
                    Result.Error(IllegalStateException("Operation returned Loading unexpectedly"))
                }
            }

        } catch (e: IllegalArgumentException) {
            val duration = startTime.elapsedNow()
            Log.e(TAG, "executeWithEnhancedErrorHandling: Input validation failed for $operation", e)

            // Monitor validation errors
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "ConfigRepository",
                operation = operation,
                duration = duration,
                success = false,
                error = e,
                dataType = "Config",
                entityId = entityId ?: "config",
                userId = null,
                resultCount = 0,
                dataSource = "validation_error"
            )

            Result.Error(e)

        } catch (e: IllegalStateException) {
            val duration = startTime.elapsedNow()
            Log.e(TAG, "executeWithEnhancedErrorHandling: State validation failed for $operation", e)

            // Monitor state errors
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "ConfigRepository",
                operation = operation,
                duration = duration,
                success = false,
                error = e,
                dataType = "Config",
                entityId = entityId ?: "config",
                userId = null,
                resultCount = 0,
                dataSource = "state_error"
            )

            Result.Error(e)

        } catch (e: Exception) {
            val duration = startTime.elapsedNow()
            Log.e(TAG, "executeWithEnhancedErrorHandling: Unexpected error in $operation", e)

            // Monitor unexpected errors
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "ConfigRepository",
                operation = operation,
                duration = duration,
                success = false,
                error = e,
                dataType = "Config",
                entityId = entityId ?: "config",
                userId = null,
                resultCount = 0,
                dataSource = "unexpected_error"
            )

            Result.Error(e)
        }
    }

    // ===== CORE APP CONFIG OPERATIONS =====

    /**
     * Get app config using cache-first strategy with enterprise infrastructure
     * ✅ ENHANCED: With priority task scheduling for config-critical operations
     */
    override suspend fun getAppConfig(forceRefresh: Boolean): Result<AppConfig?> = withContext(ioDispatcher) {
        // Execute with HIGH priority for config operations (critical for app functionality)
        executeWithPriority(
            operation = "getAppConfig",
            entityType = "AppConfig",
            entityId = "app_config",
            priority = TaskPriority.HIGH
        ) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "getAppConfig",
            entityType = "AppConfig"
        ) {
            val totalStartTime = TimeSource.Monotonic.markNow()
            var cacheCheckDuration: Duration? = null
            var remoteFetchDuration: Duration? = null
            var mappingDuration: Duration? = null
            var cacheStoreDuration: Duration? = null
            var cacheHit = false

            val session = sessionManager.value.getCurrentSession()

            Log.d(TAG, "getAppConfig: Starting operation forceRefresh=$forceRefresh (session: ${session?.sessionId})")

            // ENHANCED: Add session correlation for config access
            ClarityArchitectureMonitor.addSessionEvent("config_fetch:app_config")

            if (!forceRefresh) {
                // 1. CACHE CHECK PHASE
                val cacheCheckStart = TimeSource.Monotonic.markNow()
                val localResult = localDataSource.getAppConfig()
                cacheCheckDuration = cacheCheckStart.elapsedNow()

                if (localResult is Result.Success && localResult.data != null) {
                    cacheHit = true
                    Log.d(TAG, "getAppConfig: Found AppConfig in local cache (session: ${session?.sessionId})")

                    // ENHANCED: Add session correlation for config cache hit
                    ClarityArchitectureMonitor.addSessionEvent("config_cache_hit:app_config")

                    // ENHANCED: Monitor cache hit with breakdown
                    ClarityArchitectureMonitor.monitorCacheSystemPerformance(
                        repositoryClass = "ConfigRepository",
                        operation = "getAppConfig",
                        cacheCheckDuration = cacheCheckDuration,
                        remoteFetchDuration = null,
                        mappingDuration = null,
                        cacheStoreDuration = null,
                        cacheHit = true,
                        entityType = "AppConfig",
                        entityId = "app_config",
                        userId = null,
                        cacheMetrics = mapOf<String, Any>(
                            "operation" to "cache_hit",
                            "source" to "local_cache",
                            "data_size" to localResult.data.toString().length
                        )
                    )

                    // Monitor successful cache hit
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "ConfigRepository",
                        operation = "getAppConfig",
                        duration = totalStartTime.elapsedNow(),
                        success = true,
                        cacheHit = true,
                        dataType = "AppConfig",
                        entityId = "app_config",
                        userId = null,
                        resultCount = 1,
                        dataSource = "cache",
                        dataSize = localResult.data.toString().length,
                        cacheStrategy = "cache-first"
                    )

                    return@handleSuspendFunction localResult
                }
                if (localResult is Result.Error) {
                    Log.w(TAG, "getAppConfig: Local data source failed for app_config. Error: ${localResult.exception.message}")
                }
            }

            // 2. REMOTE FETCH PHASE WITH REQUEST DEDUPLICATION
            Log.d(TAG, "getAppConfig: AppConfig not in cache, fetching from remote with deduplication")
            val remoteFetchStart = TimeSource.Monotonic.markNow()

            // Use RequestDeduplicationManager to prevent duplicate config fetches
            val remoteResult = requestDeduplicationManager.value.deduplicateRequest(
                key = RequestKeys.appConfig(),
                timeout = RequestTimeouts.STANDARD_OPERATION,
                operation = {
                    remoteDataSource.getAppConfigDto()
                }
            ) ?: throw IllegalStateException("Request deduplication timeout for app_config")

            remoteFetchDuration = remoteFetchStart.elapsedNow()

            when (remoteResult) {
                is Result.Success -> {
                    val dto = remoteResult.data
                    if (dto != null) {
                        // 3. MAPPING PHASE
                        val mappingStart = TimeSource.Monotonic.markNow()
                        val ssotResult = configMapper.mapToDomain(dto)
                        mappingDuration = mappingStart.elapsedNow()

                        when (ssotResult) {
                            is Result.Success -> {
                                val ssotAppConfig = ssotResult.data

                                // 4. CACHE STORAGE PHASE
                                val cacheStoreStart = TimeSource.Monotonic.markNow()
                                localDataSource.saveAppConfig(ssotAppConfig)
                                cacheStoreDuration = cacheStoreStart.elapsedNow()

                                Log.d(TAG, "getAppConfig: Fetched AppConfig from remote and updated cache")

                                // ENHANCED: Add session correlation for config cache miss
                                ClarityArchitectureMonitor.addSessionEvent("config_cache_miss:app_config")

                                // ENHANCED: Monitor complete cache miss breakdown
                                ClarityArchitectureMonitor.monitorCacheSystemPerformance(
                                    repositoryClass = "ConfigRepository",
                                    operation = "getAppConfig",
                                    cacheCheckDuration = cacheCheckDuration,
                                    remoteFetchDuration = remoteFetchDuration,
                                    mappingDuration = mappingDuration,
                                    cacheStoreDuration = cacheStoreDuration,
                                    cacheHit = false,
                                    entityType = "AppConfig",
                                    entityId = "app_config",
                                    userId = null,
                                    cacheMetrics = mapOf<String, Any>(
                                        "operation" to "cache_miss_with_remote_fetch",
                                        "source" to "firestore",
                                        "total_time_ms" to totalStartTime.elapsedNow().inWholeMilliseconds,
                                        "cache_check_ms" to (cacheCheckDuration?.inWholeMilliseconds ?: 0),
                                        "remote_fetch_ms" to remoteFetchDuration.inWholeMilliseconds,
                                        "mapping_ms" to mappingDuration.inWholeMilliseconds,
                                        "cache_store_ms" to cacheStoreDuration.inWholeMilliseconds,
                                        "data_size" to dto.toString().length
                                    )
                                )

                                // Monitor successful remote fetch
                                ClarityArchitectureMonitor.monitorRepositoryOperation(
                                    repositoryClass = "ConfigRepository",
                                    operation = "getAppConfig",
                                    duration = totalStartTime.elapsedNow(),
                                    success = true,
                                    cacheHit = false,
                                    dataType = "AppConfig",
                                    entityId = "app_config",
                                    userId = null,
                                    resultCount = 1,
                                    dataSource = "remote",
                                    dataSize = dto.toString().length,
                                    cacheStrategy = "cache-first"
                                )

                                Result.Success(ssotAppConfig)
                            }
                            is Result.Error -> {
                                Log.e(TAG, "getAppConfig: Failed to map DTO to SSoT for app_config", ssotResult.exception)
                                throw ssotResult.exception
                            }
                            is Result.Loading -> {
                                Log.w(TAG, "getAppConfig: SSoT mapping returned Loading for app_config")
                                throw IllegalStateException("SSoT mapping returned Loading unexpectedly")
                            }
                        }
                    } else {
                        Log.d(TAG, "getAppConfig: No AppConfig found in remote, creating local default")
                        // ✅ FIX: Don't try to write system config - just create local default
                        val defaultResult = configMapper.createDefaultAppConfig()
                        when (defaultResult) {
                            is Result.Success -> {
                                val defaultConfig = defaultResult.data
                                // ✅ FIX: Only save to local cache, not remote system config
                                localDataSource.saveAppConfig(defaultConfig)
                                Log.d(TAG, "getAppConfig: Created and cached local default config")
                                Result.Success(defaultConfig)
                            }
                            is Result.Error -> throw defaultResult.exception
                            is Result.Loading -> throw IllegalStateException("Default creation returned Loading")
                        }
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "getAppConfig: Remote data source failed for app_config", remoteResult.exception)
                    throw remoteResult.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "getAppConfig: Remote data source returned Loading for app_config")
                    throw IllegalStateException("Remote data source returned Loading unexpectedly")
                }
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    null -> Exception("Unknown error - no exception details available")
                    else -> Exception("Unknown error: ${throwable.message}", throwable)
                }
                Result.Error(exception)
            }
        }
        } // Close priority task block
    }

    /**
     * Update app config with enterprise infrastructure
     * ✅ ENHANCED: With priority task scheduling and comprehensive error handling
     */
    override suspend fun updateAppConfig(appConfig: AppConfig): Result<Unit> = withContext(ioDispatcher) {
        // Execute with HIGH priority for config update operations (critical for app functionality)
        executeWithPriority(
            operation = "updateAppConfig",
            entityType = "AppConfig",
            entityId = "app_config",
            priority = TaskPriority.HIGH
        ) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "updateAppConfig",
            entityType = "AppConfig"
        ) {
            val totalStartTime = TimeSource.Monotonic.markNow()
            val session = sessionManager.value.getCurrentSession()

            Log.d(TAG, "updateAppConfig: Starting operation (session: ${session?.sessionId})")

            // Input validation using domain validation
            validateAppConfigDomain(appConfig)

            // ENHANCED: Add session correlation for config update
            ClarityArchitectureMonitor.addSessionEvent("config_update:app_config")

            // Map SSoT to DTO using ConfigMapper
            val dtoResult = configMapper.mapToDto(appConfig)
            when (dtoResult) {
                is Result.Success -> {
                    val dto = dtoResult.data
                    // Save to remote data source
                    val remoteResult = remoteDataSource.saveAppConfigDto(dto)
                    when (remoteResult) {
                        is Result.Success -> {
                            // Update local cache
                            localDataSource.saveAppConfig(appConfig)
                            Log.d(TAG, "updateAppConfig: Successfully updated AppConfig")

                            // Monitor successful update
                            ClarityArchitectureMonitor.monitorRepositoryOperation(
                                repositoryClass = "ConfigRepository",
                                operation = "updateAppConfig",
                                duration = totalStartTime.elapsedNow(),
                                success = true,
                                cacheHit = false,
                                dataType = "AppConfig",
                                entityId = "app_config",
                                userId = null,
                                resultCount = 1,
                                dataSource = "remote",
                                dataSize = dto.toString().length,
                                cacheStrategy = "write-through"
                            )

                            Result.Success(Unit)
                        }
                        is Result.Error -> {
                            Log.e(TAG, "updateAppConfig: Remote save failed", remoteResult.exception)
                            throw remoteResult.exception
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "updateAppConfig: Remote save returned Loading")
                            throw IllegalStateException("Remote save returned Loading unexpectedly")
                        }
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "updateAppConfig: Failed to map SSoT to DTO", dtoResult.exception)
                    throw dtoResult.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "updateAppConfig: Mapper returned Loading")
                    throw IllegalStateException("Mapper returned Loading unexpectedly")
                }
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    null -> Exception("Unknown error - no exception details available")
                    else -> Exception("Unknown error: ${throwable.message}", throwable)
                }
                Result.Error(exception)
            }
        }
        } // Close priority task block
    }

    override suspend fun updateAppConfigFields(fields: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "updateAppConfigFields: Updating fields ${fields.keys}")

            // Delegate to remote data source for field updates
            val remoteResult = remoteDataSource.updateAppConfigFieldsDto(fields)
            return@withContext when (remoteResult) {
                is Result.Success -> {
                    // Invalidate local cache to force refresh on next access
                    localDataSource.deleteAppConfig()
                    Log.d(TAG, "updateAppConfigFields: Successfully updated fields and invalidated cache")
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    Log.e(TAG, "updateAppConfigFields: Remote update failed", remoteResult.exception)
                    remoteResult
                }
                is Result.Loading -> {
                    Log.w(TAG, "updateAppConfigFields: Remote update returned Loading")
                    Result.Error(IllegalStateException("Remote update returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "updateAppConfigFields: Unexpected error", e)
            Result.Error(e)
        }
    }

    /**
     * Initialize ConfigRepository with lightweight initialization
     * ✅ SIMPLIFIED: Removed complex priority scheduling to prevent ANR during startup
     */
    override suspend fun initialize(): Result<Unit> = withContext(ioDispatcher) {
        try {
            val startTime = TimeSource.Monotonic.markNow()
            Log.d(TAG, "initialize: Initializing ConfigRepository (lightweight)")

            // Simple background cache warming - non-blocking
            applicationScope.launch(ioDispatcher) {
                try {
                    prefetchCriticalData()
                    Log.d(TAG, "initialize: Background cache warming completed")
                } catch (e: Exception) {
                    Log.w(TAG, "initialize: Background cache warming failed", e)
                }
            }

            // Monitor successful initialization
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "ConfigRepository",
                operation = "initialize",
                duration = startTime.elapsedNow(),
                success = true,
                cacheHit = false,
                dataType = "ConfigRepository",
                entityId = "config_repository",
                userId = null,
                resultCount = 1,
                dataSource = "initialization",
                cacheStrategy = "lightweight"
            )

            Log.d(TAG, "initialize: ConfigRepository initialized successfully (lightweight)")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "initialize: Error initializing ConfigRepository", e)
            Result.Error(e)
        }
    }

    // ===== NOTIFICATION PATTERNS OPERATIONS =====

    /**
     * Get notification patterns - Domain interface method
     * ✅ TIER 2: HIGH PRIORITY - Notification processing dependency
     */
    override suspend fun getNotificationPatterns(): Result<NotificationPatterns?> = withContext(ioDispatcher) {
        getNotificationPatterns(forceRefresh = false)
    }

    /**
     * Get notification patterns with enterprise infrastructure
     * ✅ TIER 2: HIGH PRIORITY - Enhanced with request deduplication and monitoring
     */
    suspend fun getNotificationPatterns(forceRefresh: Boolean): Result<NotificationPatterns?> = withContext(ioDispatcher) {
        // Execute with HIGH priority for notification pattern operations
        executeWithPriority(
            operation = "getNotificationPatterns",
            entityType = "NotificationPatterns",
            entityId = "notification_patterns",
            priority = TaskPriority.HIGH
        ) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "getNotificationPatterns",
            entityType = "NotificationPatterns"
        ) {
            val totalStartTime = TimeSource.Monotonic.markNow()
            var cacheCheckDuration: Duration? = null
            var remoteFetchDuration: Duration? = null
            var cacheHit = false

            val session = sessionManager.value.getCurrentSession()

            Log.d(TAG, "getNotificationPatterns: Starting operation forceRefresh=$forceRefresh (session: ${session?.sessionId})")

            // ENHANCED: Add session correlation for notification patterns access
            ClarityArchitectureMonitor.addSessionEvent("config_fetch:notification_patterns")

            if (!forceRefresh) {
                // 1. CACHE CHECK PHASE
                val cacheCheckStart = TimeSource.Monotonic.markNow()
                val localResult = localDataSource.getNotificationPatterns()
                cacheCheckDuration = cacheCheckStart.elapsedNow()

                if (localResult is Result.Success && localResult.data != null) {
                    cacheHit = true
                    Log.d(TAG, "getNotificationPatterns: Found NotificationPatterns in local cache (session: ${session?.sessionId})")

                    // ENHANCED: Add session correlation for patterns cache hit
                    ClarityArchitectureMonitor.addSessionEvent("patterns_cache_hit:notification_patterns")

                    // Monitor successful cache hit
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "ConfigRepository",
                        operation = "getNotificationPatterns",
                        duration = totalStartTime.elapsedNow(),
                        success = true,
                        cacheHit = true,
                        dataType = "NotificationPatterns",
                        entityId = "notification_patterns",
                        userId = null,
                        resultCount = 1,
                        dataSource = "cache",
                        dataSize = localResult.data.toString().length,
                        cacheStrategy = "cache-first"
                    )

                    return@handleSuspendFunction localResult
                }
                if (localResult is Result.Error) {
                    Log.w(TAG, "getNotificationPatterns: Local data source failed for notification_patterns. Error: ${localResult.exception.message}")
                }
            }

            // 2. REMOTE FETCH PHASE WITH REQUEST DEDUPLICATION
            Log.d(TAG, "getNotificationPatterns: NotificationPatterns not in cache, fetching from remote with deduplication")
            val remoteFetchStart = TimeSource.Monotonic.markNow()

            // Use RequestDeduplicationManager to prevent duplicate pattern fetches
            val remoteResult = requestDeduplicationManager.value.deduplicateRequest(
                key = RequestKeys.notificationPatterns(),
                timeout = RequestTimeouts.STANDARD_OPERATION,
                operation = {
                    remoteDataSource.getNotificationPatternsDto()
                }
            ) ?: throw IllegalStateException("Request deduplication timeout for notification_patterns")

            remoteFetchDuration = remoteFetchStart.elapsedNow()

            when (remoteResult) {
                is Result.Success -> {
                    val dto = remoteResult.data
                    if (dto != null) {
                        // 3. MAPPING PHASE
                        val mappingStart = TimeSource.Monotonic.markNow()
                        val ssotResult = configMapper.mapToDomain(dto)
                        val mappingDuration = mappingStart.elapsedNow()

                        when (ssotResult) {
                            is Result.Success -> {
                                val ssotPatterns = ssotResult.data

                                // 4. CACHE STORAGE PHASE
                                val cacheStoreStart = TimeSource.Monotonic.markNow()
                                localDataSource.saveNotificationPatterns(ssotPatterns)
                                val cacheStoreDuration = cacheStoreStart.elapsedNow()

                                Log.d(TAG, "getNotificationPatterns: Fetched NotificationPatterns from remote and updated cache")

                                // ENHANCED: Add session correlation for patterns cache miss
                                ClarityArchitectureMonitor.addSessionEvent("patterns_cache_miss:notification_patterns")

                                // Monitor successful remote fetch
                                ClarityArchitectureMonitor.monitorRepositoryOperation(
                                    repositoryClass = "ConfigRepository",
                                    operation = "getNotificationPatterns",
                                    duration = totalStartTime.elapsedNow(),
                                    success = true,
                                    cacheHit = false,
                                    dataType = "NotificationPatterns",
                                    entityId = "notification_patterns",
                                    userId = null,
                                    resultCount = 1,
                                    dataSource = "remote",
                                    dataSize = dto.toString().length,
                                    cacheStrategy = "cache-first"
                                )

                                Result.Success(ssotPatterns)
                            }
                            is Result.Error -> {
                                Log.e(TAG, "getNotificationPatterns: Failed to map DTO to SSoT for notification_patterns", ssotResult.exception)
                                throw ssotResult.exception
                            }
                            is Result.Loading -> {
                                Log.w(TAG, "getNotificationPatterns: SSoT mapping returned Loading for notification_patterns")
                                throw IllegalStateException("SSoT mapping returned Loading unexpectedly")
                            }
                        }
                    } else {
                        Log.d(TAG, "getNotificationPatterns: No NotificationPatterns found in remote, returning null (patterns will be created when user enables tip capture)")
                        // Don't create defaults automatically - let user enable tip capture first
                        Result.Success(null)
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "getNotificationPatterns: Remote data source failed for notification_patterns", remoteResult.exception)
                    throw remoteResult.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "getNotificationPatterns: Remote data source returned Loading for notification_patterns")
                    throw IllegalStateException("Remote data source returned Loading unexpectedly")
                }
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    null -> Exception("Unknown error - no exception details available")
                    else -> Exception("Unknown error: ${throwable.message}", throwable)
                }
                Result.Error(exception)
            }
        }
        } // Close priority task block
    }

    override suspend fun updateNotificationPatterns(patterns: NotificationPatterns): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "updateNotificationPatterns: Updating NotificationPatterns")

            // Map SSoT to DTO using ConfigMapper
            val dtoResult = configMapper.mapToDto(patterns)
            return@withContext when (dtoResult) {
                is Result.Success -> {
                    val dto = dtoResult.data
                    // Save to remote data source
                    val remoteResult = remoteDataSource.saveNotificationPatternsDto(dto)
                    when (remoteResult) {
                        is Result.Success -> {
                            // Update local cache
                            localDataSource.saveNotificationPatterns(patterns)
                            Log.d(TAG, "updateNotificationPatterns: Successfully updated NotificationPatterns")
                            Result.Success(Unit)
                        }
                        is Result.Error -> {
                            Log.e(TAG, "updateNotificationPatterns: Remote save failed", remoteResult.exception)
                            remoteResult
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "updateNotificationPatterns: Remote save returned Loading")
                            Result.Error(IllegalStateException("Remote save returned Loading unexpectedly"))
                        }
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "updateNotificationPatterns: Failed to map SSoT to DTO", dtoResult.exception)
                    dtoResult
                }
                is Result.Loading -> {
                    Log.w(TAG, "updateNotificationPatterns: Mapper returned Loading")
                    Result.Error(IllegalStateException("Mapper returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "updateNotificationPatterns: Unexpected error", e)
            Result.Error(e)
        }
    }

    // ===== OBSERVATION METHODS =====

    /**
     * Observe app config with enterprise infrastructure
     * ✅ TIER 1: CRITICAL - Real-time config updates for UI components
     */
    override fun observeAppConfig(): Flow<Result<AppConfig?>> {
        return localDataSource.observeAppConfig()
            .onStart {
                // ENHANCED: Trigger initial cache population with session correlation
                applicationScope.launch(ioDispatcher) {
                    try {
                        val session = sessionManager.value.getCurrentSession()
                        Log.d(TAG, "observeAppConfig: Triggering initial cache refresh (session: ${session?.sessionId})")

                        // ENHANCED: Add session correlation for config observation
                        ClarityArchitectureMonitor.addSessionEvent("config_observe:app_config")

                        // Use enhanced getAppConfig method
                        getAppConfig(forceRefresh = false)

                        // Monitor observation start
                        ClarityArchitectureMonitor.monitorRepositoryOperation(
                            repositoryClass = "ConfigRepository",
                            operation = "observeAppConfig_start",
                            duration = 0.seconds,
                            success = true,
                            cacheHit = false,
                            dataType = "AppConfig",
                            entityId = "app_config",
                            userId = null,
                            resultCount = 1,
                            dataSource = "observation",
                            cacheStrategy = "reactive"
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "observeAppConfig: Error during initial cache refresh", e)

                        // Monitor observation error
                        ClarityArchitectureMonitor.monitorRepositoryOperation(
                            repositoryClass = "ConfigRepository",
                            operation = "observeAppConfig_start",
                            duration = 0.seconds,
                            success = false,
                            error = e,
                            dataType = "AppConfig",
                            entityId = "app_config",
                            userId = null,
                            resultCount = 0,
                            dataSource = "observation_error"
                        )
                    }
                }
            }
            .catch { e ->
                Log.e(TAG, "observeAppConfig: Error in local data source flow", e)
                emit(Result.Error(e as? Exception ?: Exception("Unknown error in observeAppConfig")))
            }
            .flowOn(ioDispatcher)
    }

    override fun observeNotificationPatterns(): Flow<Result<NotificationPatterns?>> {
        return localDataSource.observeNotificationPatterns()
            .onStart {
                // Trigger initial cache population
                applicationScope.launch(ioDispatcher) {
                    Log.d(TAG, "observeNotificationPatterns: Triggering initial cache refresh")
                    getNotificationPatterns(forceRefresh = false)
                }
            }
            .catch { e ->
                Log.e(TAG, "observeNotificationPatterns: Error in local data source flow", e)
                emit(Result.Error(e as? Exception ?: Exception("Unknown error in observeNotificationPatterns")))
            }
            .flowOn(ioDispatcher)
    }

    // ===== UTILITY METHODS =====



    // ===== EVENT BUS INTEGRATION =====
    // Event bus integration removed for simplicity

    // ===== DOMAIN INTERFACE IMPLEMENTATION =====

    /**
     * Get config value with enterprise infrastructure
     * ✅ TIER 2: HIGH PRIORITY - Feature flags called throughout app
     */
    override suspend fun getConfigValue(key: String, defaultValue: String): Result<String> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "getConfigValue",
            entityType = "ConfigValue"
        ) {
            val startTime = TimeSource.Monotonic.markNow()
            val session = sessionManager.value.getCurrentSession()

            Log.d(TAG, "getConfigValue: Getting value for key=$key, defaultValue=$defaultValue (session: ${session?.sessionId})")

            // ENHANCED: Add session correlation for config value access
            ClarityArchitectureMonitor.addSessionEvent("config_value_access:$key")

            // Input validation
            require(key.isNotBlank()) { "Config key cannot be blank" }
            require(key.length <= 100) { "Config key too long: ${key.length}" }

            // Use RequestDeduplicationManager for frequently accessed config values
            val configResult = requestDeduplicationManager.value.deduplicateRequest(
                key = RequestKeys.configValue(key),
                timeout = RequestTimeouts.QUICK_OPERATION,
                operation = {
                    getAppConfig(forceRefresh = false)
                }
            ) ?: throw IllegalStateException("Request deduplication timeout for config value: $key")

            when (configResult) {
                is Result.Success -> {
                    val config = configResult.data
                    val value = if (config != null) {
                        // Extract config value directly from domain model
                        extractConfigValueFromDomain(config, key, defaultValue)
                    } else {
                        defaultValue
                    }

                    // Monitor successful config value access
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "ConfigRepository",
                        operation = "getConfigValue",
                        duration = startTime.elapsedNow(),
                        success = true,
                        cacheHit = config != null,
                        dataType = "ConfigValue",
                        entityId = key,
                        userId = null,
                        resultCount = 1,
                        dataSource = if (config != null) "config" else "default",
                        cacheStrategy = "cache-first"
                    )

                    Result.Success(value)
                }
                is Result.Error -> {
                    Log.w(TAG, "getConfigValue: Failed to get config for key $key, returning default", configResult.exception)

                    // Monitor fallback to default
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "ConfigRepository",
                        operation = "getConfigValue",
                        duration = startTime.elapsedNow(),
                        success = true, // Still successful since we return default
                        cacheHit = false,
                        dataType = "ConfigValue",
                        entityId = key,
                        userId = null,
                        resultCount = 1,
                        dataSource = "default_fallback"
                    )

                    Result.Success(defaultValue)
                }
                is Result.Loading -> throw IllegalStateException("Unexpected Loading state for config value: $key")
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    null -> Exception("Unknown error - no exception details available")
                    else -> Exception("Unknown error: ${throwable.message}", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    /**
     * Get config boolean with enterprise infrastructure
     * ✅ TIER 2: HIGH PRIORITY - Boolean feature flags called throughout app
     */
    override suspend fun getConfigBoolean(key: String, defaultValue: Boolean): Result<Boolean> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "getConfigBoolean",
            entityType = "ConfigBoolean"
        ) {
            val startTime = TimeSource.Monotonic.markNow()
            val session = sessionManager.value.getCurrentSession()

            Log.d(TAG, "getConfigBoolean: Getting boolean for key=$key, defaultValue=$defaultValue (session: ${session?.sessionId})")

            // ENHANCED: Add session correlation for config boolean access
            ClarityArchitectureMonitor.addSessionEvent("config_boolean_access:$key")

            // Input validation
            require(key.isNotBlank()) { "Config key cannot be blank" }
            require(key.length <= 100) { "Config key too long: ${key.length}" }

            // Use RequestDeduplicationManager for frequently accessed boolean flags
            val configResult = requestDeduplicationManager.value.deduplicateRequest(
                key = "config_boolean:$key",
                timeout = 5.seconds,
                operation = {
                    getAppConfig(forceRefresh = false)
                }
            ) ?: throw IllegalStateException("Request deduplication timeout for config boolean: $key")

            when (configResult) {
                is Result.Success -> {
                    val config = configResult.data
                    val value = if (config != null) {
                        // Extract config boolean directly from domain model
                        extractConfigBooleanFromDomain(config, key, defaultValue)
                    } else {
                        defaultValue
                    }

                    // Monitor successful config boolean access
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "ConfigRepository",
                        operation = "getConfigBoolean",
                        duration = startTime.elapsedNow(),
                        success = true,
                        cacheHit = config != null,
                        dataType = "ConfigBoolean",
                        entityId = key,
                        userId = null,
                        resultCount = 1,
                        dataSource = if (config != null) "config" else "default",
                        cacheStrategy = "cache-first"
                    )

                    Result.Success(value)
                }
                is Result.Error -> {
                    Log.w(TAG, "getConfigBoolean: Failed to get config for key $key, returning default", configResult.exception)

                    // Monitor fallback to default
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "ConfigRepository",
                        operation = "getConfigBoolean",
                        duration = startTime.elapsedNow(),
                        success = true, // Still successful since we return default
                        cacheHit = false,
                        dataType = "ConfigBoolean",
                        entityId = key,
                        userId = null,
                        resultCount = 1,
                        dataSource = "default_fallback"
                    )

                    Result.Success(defaultValue)
                }
                is Result.Loading -> throw IllegalStateException("Unexpected Loading state for config boolean: $key")
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    null -> Exception("Unknown error - no exception details available")
                    else -> Exception("Unknown error: ${throwable.message}", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    /**
     * Increment counter with basic infrastructure
     * ✅ TIER 3: STANDARD - Analytics/usage tracking with basic error handling
     */
    override suspend fun incrementCounter(counterKey: String): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "incrementCounter",
            entityType = "ConfigCounter"
        ) {
            val startTime = TimeSource.Monotonic.markNow()

            Log.d(TAG, "incrementCounter: Incrementing counter for key=$counterKey")

            // Input validation
            require(counterKey.isNotBlank()) { "Counter key cannot be blank" }
            require(counterKey.length <= 50) { "Counter key too long: ${counterKey.length}" }

            // Get current config, increment counter, and save back
            val configResult = getAppConfig(forceRefresh = false)
            when (configResult) {
                is Result.Success -> {
                    val config = configResult.data
                    if (config != null) {
                        // Increment counter directly in domain model
                        val updatedConfig = incrementCounterInDomain(config, counterKey)
                        val updateResult = updateAppConfig(updatedConfig)

                        // Monitor counter increment
                        ClarityArchitectureMonitor.monitorRepositoryOperation(
                            repositoryClass = "ConfigRepository",
                            operation = "incrementCounter",
                            duration = startTime.elapsedNow(),
                            success = updateResult is Result.Success,
                            dataType = "ConfigCounter",
                            entityId = counterKey,
                            userId = null,
                            resultCount = 1,
                            dataSource = "config_update"
                        )

                        updateResult
                    } else {
                        Log.w(TAG, "incrementCounter: No config found, cannot increment counter")
                        throw IllegalStateException("No config found for counter: $counterKey")
                    }
                }
                is Result.Error -> throw configResult.exception
                is Result.Loading -> throw IllegalStateException("Unexpected Loading state for counter: $counterKey")
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    null -> Exception("Unknown error - no exception details available")
                    else -> Exception("Unknown error: ${throwable.message}", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun updateDeviceLastActive(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "updateDeviceLastActive: Updating device last active timestamp")
            
            // Update device last active timestamp in config
            val fields = mapOf("devices.lastActive" to System.currentTimeMillis())
            updateAppConfigFields(fields)
        } catch (e: Exception) {
            Log.e(TAG, "updateDeviceLastActive: Error updating device last active", e)
            Result.Error(e)
        }
    }

    override suspend fun prefetchCriticalData(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "prefetchCriticalData: Prefetching critical config data (lightweight)")

            // Lightweight prefetch - just trigger cache check, don't wait for results
            launch {
                try {
                    getAppConfig(forceRefresh = false)
                } catch (e: Exception) {
                    Log.w(TAG, "prefetchCriticalData: AppConfig prefetch failed", e)
                }
            }

            launch {
                try {
                    getNotificationPatterns(forceRefresh = false)
                } catch (e: Exception) {
                    Log.w(TAG, "prefetchCriticalData: NotificationPatterns prefetch failed", e)
                }
            }

            Log.d(TAG, "prefetchCriticalData: Lightweight prefetch initiated")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "prefetchCriticalData: Error initiating prefetch", e)
            Result.Error(e)
        }
    }

    override suspend fun clearCache(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "clearCache: Clearing all config cache")
            localDataSource.clearAllCaches()
        } catch (e: Exception) {
            Log.e(TAG, "clearCache: Error clearing cache", e)
            Result.Error(e)
        }
    }

    override suspend fun invalidateCache(key: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "invalidateCache: Invalidating cache for key $key")
            
            // For config, we typically clear all cache since it's a single document
            localDataSource.clearAllCaches()
        } catch (e: Exception) {
            Log.e(TAG, "invalidateCache: Error invalidating cache for key $key", e)
            Result.Error(e)
        }
    }

    /**
     * Validate app config with domain validation
     * ✅ TIER 3: STANDARD - Validation helper with domain validation
     */
    override suspend fun validateAppConfig(appConfig: AppConfig): Result<Unit> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "validateAppConfig",
            entityType = "AppConfigValidation"
        ) {
            val startTime = TimeSource.Monotonic.markNow()

            Log.d(TAG, "validateAppConfig: Validating app config with domain validation")

            // Use domain validation for comprehensive validation

            // Also perform domain-specific validation
            validateAppConfigDomain(appConfig)

            // Monitor validation success
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "ConfigRepository",
                operation = "validateAppConfig",
                duration = startTime.elapsedNow(),
                success = true,
                dataType = "AppConfigValidation",
                entityId = "app_config_validation",
                userId = null,
                resultCount = 1,
                dataSource = "validation_engine"
            )

            Result.Success(Unit)
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    null -> Exception("Unknown error - no exception details available")
                    else -> Exception("Unknown error: ${throwable.message}", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    override suspend fun createDefaultAppConfig(): Result<AppConfig> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "createDefaultAppConfig: Creating default app config")
            
            val defaultConfig = createDefaultAppConfigDomain()
            Result.Success(defaultConfig)
        } catch (e: Exception) {
            Log.e(TAG, "createDefaultAppConfig: Error creating default app config", e)
            Result.Error(e)
        }
    }

    override suspend fun createDefaultNotificationPatterns(): Result<NotificationPatterns> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "createDefaultNotificationPatterns: Creating default notification patterns")
            
            val defaultPatterns = createDefaultNotificationPatternsDomain()
            Result.Success(defaultPatterns)
        } catch (e: Exception) {
            Log.e(TAG, "createDefaultNotificationPatterns: Error creating default notification patterns", e)
            Result.Error(e)
        }
    }

    // ===== HELPER METHODS =====

    private fun extractConfigValueFromDomain(config: AppConfig, key: String, defaultValue: String): String {
        return when (key) {
            "versions.minimum" -> config.versions.minimum
            "versions.recommended" -> config.versions.recommended
            "versions.latest" -> config.versions.latest
            "maintenance.message" -> config.maintenance.maintenanceMessage ?: defaultValue
            "sync.conflictStrategy" -> config.sync.conflictStrategy
            else -> {
                // Check custom data
                config.customData?.get(key)?.toString() ?: defaultValue
            }
        }
    }

    private fun extractConfigBooleanFromDomain(config: AppConfig, key: String, defaultValue: Boolean): Boolean {
        return when (key) {
            "features.useNewSyncSystem" -> config.features.useNewSyncSystem
            "features.enableOfflineMode" -> config.features.enableOfflineMode
            "features.enableAnalytics" -> config.features.enableAnalytics
            "features.enableBackgroundSync" -> config.features.enableBackgroundSync
            "features.enforceVersionCheck" -> config.features.enforceVersionCheck
            "maintenance.isInMaintenance" -> config.maintenance.isInMaintenance
            else -> {
                // Check custom data
                config.customData?.get(key)?.let { 
                    when (it) {
                        is Boolean -> it
                        is String -> it.toBoolean()
                        else -> defaultValue
                    }
                } ?: defaultValue
            }
        }
    }

    private fun incrementCounterInDomain(config: AppConfig, counterKey: String): AppConfig {
        val currentValue = config.customData?.get(counterKey) as? Long ?: 0L
        val newCustomData = (config.customData ?: emptyMap()).toMutableMap()
        newCustomData[counterKey] = currentValue + 1
        
        return config.copy(
            customData = newCustomData,
            version = config.version + 1,
            updatedAt = java.time.OffsetDateTime.now()
        )
    }

    private fun validateAppConfigDomain(appConfig: AppConfig) {
        // Basic validation
        require(appConfig.versions.minimum.isNotBlank()) { "Minimum version cannot be blank" }
        require(appConfig.versions.recommended.isNotBlank()) { "Recommended version cannot be blank" }
        require(appConfig.versions.latest.isNotBlank()) { "Latest version cannot be blank" }
        require(appConfig.version >= 0) { "Version must be non-negative" }
    }

    private fun createDefaultAppConfigDomain(): AppConfig {
        return AppConfig(
            versions = AppConfig.AppVersions(
                minimum = "1.0.0",
                recommended = "1.0.0",
                latest = "1.0.0"
            ),
            features = AppConfig.AppFeatures(
                useNewSyncSystem = true,
                enableOfflineMode = false,
                enableAnalytics = true,
                enableBackgroundSync = true,
                enforceVersionCheck = true
            ),
            limits = AppConfig.AppLimits(
                freeTier = AppConfig.AppLimits.TierLimits(
                    mappingLimit = 100L,
                    importLimit = 50L,
                    exportLimit = 25L
                ),
                proTier = AppConfig.AppLimits.TierLimits(
                    mappingLimit = 1000L,
                    importLimit = 500L,
                    exportLimit = 250L
                )
            ),
            sync = AppConfig.AppSync(
                interval = 300000L, // 5 minutes
                backgroundInterval = 900000L, // 15 minutes
                maxBatchSize = 100L,
                conflictStrategy = "client_wins"
            ),
            maintenance = AppConfig.AppMaintenance(
                isInMaintenance = false,
                maintenanceMessage = null,
                estimatedEndTime = null
            ),
            updatedAt = java.time.OffsetDateTime.now(),
            version = 1L,
            customData = mapOf(
                // 🎯 Enable AddressEnhancementService by default
                "address_enhancement_enabled" to true,
                "enhance_manual_orders" to true,
                "enhance_delivery_edits" to true,
                "enhance_address_edits" to true
            )
        )
    }

    private fun createDefaultNotificationPatternsDomain(): NotificationPatterns {
        return NotificationPatterns(
            extractors = NotificationPatterns.NotificationExtractors(
                shipt = NotificationPatterns.NotificationExtractors.ShiptExtractor(
                    orderId = "Order #(\\d+)",
                    tipAmount = "\\$(\\d+\\.\\d{2})"
                )
            ),
            patterns = NotificationPatterns.PlatformPatterns(
                doordash = listOf("DoorDash order completed"),
                shipt = listOf("Your order has been completed", "You received a tip"),
                ubereats = listOf("UberEats order completed")
            ),
            updatedAt = java.time.OffsetDateTime.now(),
            version = 1L
        )
    }

    // ===== DOMAIN INTERFACE CLEANUP =====

    override suspend fun cleanup(): Result<Unit> = withContext(ioDispatcher) {
        try {
            Log.d(TAG, "cleanup: Cleaning up ConfigRepository resources")
            clearCache()
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "cleanup: Error during cleanup", e)
            Result.Error(e)
        }
    }

    // ===== DATA INTERFACE OPERATIONS =====

    /**
     * No-op operation that completes immediately.
     * Used for testing and infrastructure validation.
     */
    override suspend fun noOpSuspending(): Result<Unit> = withContext(ioDispatcher) {
        Log.d(TAG, "noOpSuspending: Executing no-op operation")
        Result.Success(Unit)
    }



}