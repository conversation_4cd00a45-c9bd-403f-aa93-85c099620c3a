import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore } from 'firebase-admin/firestore';

// ✅ ONLY use generated models from schemas
import type {
  Delivery
} from '../models/generated/delivery.schema';

const db = getFirestore();

// Input/Output schemas
const ValidateDeliveryEditInputSchema = z.object({
  userId: z.string(),
  deliveryId: z.string(),
  proposedChanges: z.object({
    orderId: z.string().optional(),
    tipAmount: z.number().nullable().optional(),
    tipStatus: z.enum(['pending', 'received']).optional(),
    addressId: z.string().optional(),
    // Other editable fields
    notes: z.string().optional(),
    platform: z.string().optional()
  })
});

const ValidateDeliveryEditOutputSchema = z.object({
  success: z.boolean(),
  allowed: z.boolean(),
  violations: z.array(z.object({
    field: z.string(),
    reason: z.string(),
    severity: z.enum(['error', 'warning'])
  })),
  error: z.string().optional()
});

/**
 * ✅ FOCUSED FUNCTION: Validate delivery editing constraints
 * 
 * Purpose: Enforce business rules for delivery editing including order ID immutability
 * Uses: Only generated schema models
 * Updates: None (validation only)
 */
export const validateDeliveryEditFlow = defineFlow(
  {
    name: 'validateDeliveryEdit',
    inputSchema: ValidateDeliveryEditInputSchema,
    outputSchema: ValidateDeliveryEditOutputSchema,
  },
  async (input) => {
    const logPrefix = `[ValidateDeliveryEdit][${input.userId}][${input.deliveryId}]`;
    const startTime = Date.now();
    const changeCount = Object.keys(input.proposedChanges).length;
    console.log(`${logPrefix} Validating ${changeCount} proposed changes`);

    try {
      // Fetch current delivery
      const fetchStartTime = Date.now();
      const deliveryDoc = await db.doc(`users/${input.userId}/user_deliveries/${input.deliveryId}`).get();
      const fetchDuration = Date.now() - fetchStartTime;

      console.log(`${logPrefix} Delivery fetch completed in ${fetchDuration}ms - exists: ${deliveryDoc.exists}`);

      if (!deliveryDoc.exists) {
        console.error(`${logPrefix} Delivery document not found`);
        throw new Error('Delivery not found');
      }

      const currentDelivery: Delivery = deliveryDoc.data() as Delivery;
      const violations: Array<{field: string, reason: string, severity: 'error' | 'warning'}> = [];

      console.log(`${logPrefix} Validating changes:`, Object.keys(input.proposedChanges));

      // ✅ CRITICAL BUSINESS RULE: Order ID is IMMUTABLE
      if (input.proposedChanges.orderId !== undefined) {
        const currentOrderId = currentDelivery.deliveryData?.orderId;
        if (input.proposedChanges.orderId !== currentOrderId) {
          violations.push({
            field: 'orderId',
            reason: 'Order ID cannot be changed after delivery creation. Delete and recreate delivery if order ID is incorrect.',
            severity: 'error'
          });
        }
      }

      // ✅ TIP STATUS TRANSITION VALIDATION
      if (input.proposedChanges.tipAmount !== undefined || input.proposedChanges.tipStatus !== undefined) {
        const currentTipAmount = currentDelivery.deliveryData?.amounts?.tipAmount;
        const currentIsCompleted = currentDelivery.deliveryData?.status?.isCompleted ?? false;
        const proposedTipAmount = input.proposedChanges.tipAmount;
        const proposedTipStatus = input.proposedChanges.tipStatus;

        // Validate tip status transitions
        if (proposedTipStatus === 'pending' && proposedTipAmount !== null && proposedTipAmount !== undefined) {
          violations.push({
            field: 'tipAmount',
            reason: 'Cannot set tip amount when status is pending. Set status to received first.',
            severity: 'error'
          });
        }

        if (proposedTipStatus === 'received' && (proposedTipAmount === null || proposedTipAmount === undefined)) {
          violations.push({
            field: 'tipAmount',
            reason: 'Must provide tip amount when status is received.',
            severity: 'error'
          });
        }

        // Warn about tip changes that might affect DND status
        if (currentTipAmount !== proposedTipAmount && currentIsCompleted) {
          violations.push({
            field: 'tipAmount',
            reason: 'Changing tip amount on completed delivery will trigger DND re-evaluation for this address.',
            severity: 'warning'
          });
        }
      }

      // ✅ ADDRESS CHANGE VALIDATION
      if (input.proposedChanges.addressId !== undefined) {
        const currentAddressId = currentDelivery.deliveryData?.reference?.addressId;
        if (input.proposedChanges.addressId !== currentAddressId) {
          violations.push({
            field: 'addressId',
            reason: 'Changing delivery address will trigger stats recalculation for both old and new addresses.',
            severity: 'warning'
          });

          // Check if new address exists
          const newAddressDoc = await db.doc(`users/${input.userId}/user_addresses/${input.proposedChanges.addressId}`).get();
          if (!newAddressDoc.exists) {
            violations.push({
              field: 'addressId',
              reason: 'Target address does not exist. Create address first.',
              severity: 'error'
            });
          }
        }
      }

      // ✅ PLATFORM CHANGE VALIDATION
      if (input.proposedChanges.platform !== undefined) {
        const currentPlatform = currentDelivery.deliveryData?.platform?.name;
        if (input.proposedChanges.platform !== currentPlatform) {
          violations.push({
            field: 'platform',
            reason: 'Changing platform will affect platform breakdown statistics.',
            severity: 'warning'
          });
        }
      }

      const hasErrors = violations.some(v => v.severity === 'error');
      const hasWarnings = violations.some(v => v.severity === 'warning');
      const allowed = !hasErrors;

      const totalDuration = Date.now() - startTime;
      console.log(`${logPrefix} Validation completed in ${totalDuration}ms - allowed: ${allowed}, errors: ${violations.filter(v => v.severity === 'error').length}, warnings: ${violations.filter(v => v.severity === 'warning').length}`);

      return {
        success: true,
        allowed,
        violations
      };

    } catch (error) {
      const totalDuration = Date.now() - startTime;
      console.error(`${logPrefix} Error validating delivery edit after ${totalDuration}ms:`, error);
      return {
        success: false,
        allowed: false,
        violations: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
);

/**
 * ✅ HELPER: Validate tip status transition logic
 */
function validateTipStatusTransition(
  currentTipAmount: number | null | undefined,
  currentIsCompleted: boolean,
  proposedTipAmount: number | null | undefined,
  proposedTipStatus: 'pending' | 'received' | undefined
): {valid: boolean, reason?: string} {
  
  // Current state analysis
  const currentIsPending = currentTipAmount == null && currentIsCompleted;
  const currentIsReceived = currentTipAmount != null && currentIsCompleted;
  
  // Proposed state analysis
  const proposedIsPending = proposedTipStatus === 'pending' || (proposedTipAmount == null && proposedTipStatus !== 'received');
  const proposedIsReceived = proposedTipStatus === 'received' || (proposedTipAmount != null && proposedTipStatus !== 'pending');
  
  // Valid transitions:
  // pending -> received (tip amount gets set)
  // received -> received (tip amount changes)
  // pending -> pending (no change)
  // received -> pending (tip amount cleared - unusual but allowed)
  
  if (proposedIsPending && proposedTipAmount != null) {
    return {valid: false, reason: 'Cannot have tip amount when status is pending'};
  }
  
  if (proposedIsReceived && (proposedTipAmount == null || proposedTipAmount === undefined)) {
    return {valid: false, reason: 'Must have tip amount when status is received'};
  }
  
  return {valid: true};
}
