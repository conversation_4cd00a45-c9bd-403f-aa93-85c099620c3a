// Auto-generated from AppConfig.kt
// TypeScript equivalent of Android domain.model.AppConfig

/**
 * Domain model generated from Kotlin AppConfig
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface AppConfig {
  versions: AppVersions;
  features: AppFeatures;
  limits: AppLimits;
  sync: AppSync;
  maintenance: AppMaintenance;
  updatedAt?: Date? = null;
  version: number;
  customData: Map<string;
  minimum: string;
  recommended: string;
  latest: string;
  useNewSyncSystem: boolean = true;
  enableOfflineMode: boolean = true;
  enableAnalytics: boolean = true;
  enableBackgroundSync: boolean = true;
  enforceVersionCheck: boolean = false;
  freeTier: TierLimits;
  proTier: TierLimits;
  mappingLimit: number;
  importLimit: number;
  exportLimit: number;
  interval: number;
  backgroundInterval: number;
  maxBatchSize: number;
  conflictStrategy: string;
  isInMaintenance: boolean = false;
  maintenanceMessage?: string? = null;
  estimatedEndTime?: Date? = null;
}