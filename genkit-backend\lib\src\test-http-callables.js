"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpCallableTester = void 0;
const app_1 = require("firebase-admin/app");
const admin = __importStar(require("firebase-admin"));
const flow_1 = require("@genkit-ai/flow");
// Import the flows directly instead of the trigger wrappers
const import_parsing_1 = require("./flows/import-parsing");
const set_manual_address_dnd_override_1 = require("./flows/set-manual-address-dnd-override");
// Import schemas for validation
const set_manual_address_dnd_override_2 = require("./flows/set-manual-address-dnd-override");
// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
    (0, app_1.initializeApp)();
}
class HttpCallableTester {
    constructor() {
        this.results = [];
    }
    async runAllHttpTests() {
        console.log('🌐 Starting HTTP Callable Function Testing...\n');
        await this.testParseImportCallable();
        await this.testManualDndOverrideCallable();
        this.printHttpSummary();
        return this.results;
    }
    async testParseImportCallable() {
        console.log('📥 Testing testParseImportFlow Callable...');
        // Test valid input
        await this.runHttpTest('testParseImportFlow', 'Valid Import Text', async () => {
            const testText = `
                Order #12345
                Delivery to: 123 Main St, City, State 12345
                Tip: $5.50
                Platform: Shipt
                Date: 2024-01-15
                `;
            // Test the flow directly
            return await (0, flow_1.runFlow)(import_parsing_1.parseImportDataWithLogging, testText.trim());
        });
        // Test invalid input (empty text)
        await this.runHttpTest('testParseImportFlow', 'Empty Text Input', async () => {
            try {
                return await (0, flow_1.runFlow)(import_parsing_1.parseImportDataWithLogging, '');
            }
            catch (error) {
                return { error: error.message, expected: true };
            }
        });
        // Test malformed text
        await this.runHttpTest('testParseImportFlow', 'Malformed Text Input', async () => {
            const malformedText = 'This is not a valid delivery format';
            return await (0, flow_1.runFlow)(import_parsing_1.parseImportDataWithLogging, malformedText);
        });
    }
    // Google API and Dashboard batch callable testing removed - unused functionality
    async testManualDndOverrideCallable() {
        console.log('🚫 Testing setManualAddressDndOverride Callable...');
        // Test valid DND override
        await this.runHttpTest('setManualAddressDndOverride', 'Valid DND Override (FORCE_DND)', async () => {
            const testInput = {
                userId: 'test-user-123',
                addressId: 'test-address-456',
                desiredState: 'FORCE_DND'
            };
            const validation = set_manual_address_dnd_override_2.SetManualAddressDndInputSchema.safeParse(testInput);
            if (!validation.success) {
                throw new Error(`Invalid test data: ${validation.error.message}`);
            }
            return await (0, flow_1.runFlow)(set_manual_address_dnd_override_1.setManualAddressDndOverrideFlow, validation.data);
        });
        // Test FORCE_ALLOW state
        await this.runHttpTest('setManualAddressDndOverride', 'Valid DND Override (FORCE_ALLOW)', async () => {
            const testInput = {
                userId: 'test-user-123',
                addressId: 'test-address-456',
                desiredState: 'FORCE_ALLOW'
            };
            const validation = set_manual_address_dnd_override_2.SetManualAddressDndInputSchema.safeParse(testInput);
            if (!validation.success) {
                throw new Error(`Invalid test data: ${validation.error.message}`);
            }
            return await (0, flow_1.runFlow)(set_manual_address_dnd_override_1.setManualAddressDndOverrideFlow, validation.data);
        });
        // Test invalid state
        await this.runHttpTest('setManualAddressDndOverride', 'Invalid DND State', async () => {
            const testInput = {
                userId: 'test-user-123',
                addressId: 'test-address-456',
                desiredState: 'INVALID_STATE'
            };
            const validation = set_manual_address_dnd_override_2.SetManualAddressDndInputSchema.safeParse(testInput);
            if (!validation.success) {
                return { error: 'Invalid DND state', expected: true };
            }
            return await (0, flow_1.runFlow)(set_manual_address_dnd_override_1.setManualAddressDndOverrideFlow, validation.data);
        });
        // Test missing required fields
        await this.runHttpTest('setManualAddressDndOverride', 'Missing Required Fields', async () => {
            const testInput = {
                userId: 'test-user-123'
                // Missing addressId and desiredState
            };
            const validation = set_manual_address_dnd_override_2.SetManualAddressDndInputSchema.safeParse(testInput);
            if (!validation.success) {
                return { error: 'Missing required fields', expected: true };
            }
            return await (0, flow_1.runFlow)(set_manual_address_dnd_override_1.setManualAddressDndOverrideFlow, validation.data);
        });
    }
    async runHttpTest(functionName, testName, testFunction) {
        const startTime = Date.now();
        try {
            console.log(`  ⏳ Running: ${testName}...`);
            const result = await testFunction();
            const duration = Date.now() - startTime;
            // Determine if this is a success based on the result structure
            const isSuccess = this.isSuccessfulResult(result);
            this.results.push({
                functionName,
                testName,
                success: isSuccess,
                result,
                duration,
                statusCode: this.extractStatusCode(result)
            });
            const status = isSuccess ? '✅' : '⚠️';
            console.log(`  ${status} ${testName} - ${isSuccess ? 'Success' : 'Expected Error'} (${duration}ms)`);
            if (result && typeof result === 'object') {
                console.log(`    Result: ${JSON.stringify(result, null, 2).substring(0, 200)}...`);
            }
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.results.push({
                functionName,
                testName,
                success: false,
                error: error.message,
                duration
            });
            console.log(`  ❌ ${testName} - Failed: ${error.message} (${duration}ms)`);
        }
        console.log(''); // Add spacing
    }
    isSuccessfulResult(result) {
        if (!result || typeof result !== 'object') {
            return false;
        }
        // Check for expected errors (validation working correctly)
        if (result.expected === true)
            return true;
        // Check for common success indicators
        if (result.success === true)
            return true;
        if (result.status === 'SUCCESS')
            return true;
        if (result.status === 'OK')
            return true;
        // Check for error indicators that are actual failures
        if (result.error && result.expected !== true)
            return false;
        if (result.status === 'ERROR')
            return false;
        if (result.status === 'INVALID_INPUT')
            return false;
        if (result.status === 'INTERNAL_ERROR')
            return false;
        // If no clear indicators, consider it a success if it has meaningful data
        return Object.keys(result).length > 0;
    }
    extractStatusCode(result) {
        if (result && typeof result === 'object') {
            if (result.statusCode)
                return result.statusCode;
            if (result.status === 'SUCCESS' || result.success === true)
                return 200;
            if (result.status === 'INVALID_INPUT')
                return 400;
            if (result.status === 'INTERNAL_ERROR')
                return 500;
        }
        return undefined;
    }
    printHttpSummary() {
        console.log('📋 HTTP Callable Test Summary:');
        console.log('==============================');
        const successful = this.results.filter(r => r.success).length;
        const failed = this.results.filter(r => !r.success).length;
        const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
        console.log(`Total HTTP Tests: ${this.results.length}`);
        console.log(`Successful: ${successful}`);
        console.log(`Failed: ${failed}`);
        console.log(`Total Duration: ${totalDuration}ms`);
        console.log('');
        // Group by function
        const byFunction = this.results.reduce((acc, result) => {
            if (!acc[result.functionName]) {
                acc[result.functionName] = [];
            }
            acc[result.functionName].push(result);
            return acc;
        }, {});
        Object.entries(byFunction).forEach(([functionName, results]) => {
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            console.log(`${functionName}: ${successCount}/${totalCount} tests passed`);
            results.forEach(result => {
                const status = result.success ? '✅' : result.error ? '❌' : '⚠️';
                const statusCode = result.statusCode ? ` [${result.statusCode}]` : '';
                console.log(`  ${status} ${result.testName}${statusCode} (${result.duration}ms)`);
                if (result.error) {
                    console.log(`    Error: ${result.error}`);
                }
            });
            console.log('');
        });
    }
}
exports.HttpCallableTester = HttpCallableTester;
// Main execution function
async function main() {
    const tester = new HttpCallableTester();
    const results = await tester.runAllHttpTests();
    // Exit with error code if any tests failed
    const hasFailures = results.some(r => !r.success);
    process.exit(hasFailures ? 1 : 0);
}
// Run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=test-http-callables.js.map