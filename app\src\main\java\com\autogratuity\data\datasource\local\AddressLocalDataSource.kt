package com.autogratuity.data.datasource.local

import com.autogratuity.domain.model.Address
import com.autogratuity.data.model.Result
import com.autogratuity.data.repository.address.AddressCacheSystem
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Interface for local data operations (caching) related to Addresses.
 * All methods operate with SSoT Address models.
 */
interface AddressLocalDataSource {
    suspend fun getAddressById(userId: String, addressId: String): Result<Address?>
    suspend fun getAllAddresses(userId: String): Result<List<Address>>
    suspend fun findAddressByNormalizedAddress(userId: String, normalizedAddress: String): Result<Address?>
    suspend fun saveAddress(userId: String, address: Address): Result<Unit>
    suspend fun saveAllAddresses(userId: String, addresses: List<Address>): Result<Unit>
    suspend fun deleteAddress(userId: String, addressId: String): Result<Unit>
    suspend fun deleteAllAddresses(userId: String): Result<Unit>
    suspend fun clearAllCaches(): Result<Unit>

    fun observeById(userId: String, addressId: String): Flow<Address?>
    fun observeAll(userId: String): Flow<List<Address>>
}

class AddressLocalDataSourceImpl @Inject constructor(
    private val addressCacheSystem: AddressCacheSystem,
    private val ioDispatcher: CoroutineDispatcher // Koin will provide this via module definition
) : AddressLocalDataSource {

    override suspend fun getAddressById(userId: String, addressId: String): Result<Address?> = withContext(ioDispatcher) {
        try {
            val address = addressCacheSystem.getAddress(userId, addressId)
            Result.Success(address)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun getAllAddresses(userId: String): Result<List<Address>> = withContext(ioDispatcher) {
        try {
            val addresses = addressCacheSystem.getUserAddresses(userId)
            Result.Success(addresses)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }
    
    override suspend fun findAddressByNormalizedAddress(userId: String, normalizedAddress: String): Result<Address?> = withContext(ioDispatcher) {
        try {
            if (userId.isBlank() || normalizedAddress.isBlank()) {
                return@withContext Result.Error(IllegalArgumentException("User ID and normalized address cannot be blank"))
            }
            
            // Get all addresses for the user and search for matching normalized address
            val addresses = addressCacheSystem.getUserAddresses(userId)
            val matchingAddress = addresses.find { address ->
                address.normalizedAddress?.equals(normalizedAddress, ignoreCase = true) == true
            }
            
            Result.Success(matchingAddress)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun saveAddress(userId: String, address: Address): Result<Unit> = withContext(ioDispatcher) {
        try {
            val addressId = address.id
            addressCacheSystem.cacheAddress(addressId, address, userId)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun saveAllAddresses(userId: String, addresses: List<Address>): Result<Unit> = withContext(ioDispatcher) {
        try {
            val cacheKey = "addresses_${userId}_all"
            addressCacheSystem.cacheAddressList(cacheKey, addresses, userId)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun deleteAddress(userId: String, addressId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            addressCacheSystem.invalidateAddressCache(userId, addressId)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun deleteAllAddresses(userId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            addressCacheSystem.invalidateUserAddresses(userId)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun clearAllCaches(): Result<Unit> = withContext(ioDispatcher) {
        try {
            addressCacheSystem.clear()
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override fun observeById(userId: String, addressId: String): Flow<Address?> {
        // ✅ STANDARDIZED: User-aware reactive observation with validation
        return addressCacheSystem.getAddressFlow(userId, addressId)
    }

    override fun observeAll(userId: String): Flow<List<Address>> {
        // ✅ STANDARDIZED: User-aware reactive observation with validation
        return addressCacheSystem.getAddressesFlow(userId)
    }
}