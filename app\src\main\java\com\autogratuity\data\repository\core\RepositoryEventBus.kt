package com.autogratuity.data.repository.core

import android.util.Log
import kotlinx.atomicfu.AtomicRef
import kotlinx.atomicfu.atomic
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Modern event bus for communication between repositories without direct dependencies.
 * This class enables decoupling of repository components while maintaining
 * functional communication through a pub/sub pattern.
 * 
 * Modernized in 2025 to use Kotlin Flow, atomic operations, and structured concurrency.
 */
@Singleton
class ModernRepositoryEventBus @Inject constructor(
    private val applicationScope: CoroutineScope
) {
    private val TAG = "ModernRepositoryEventBus"

    // Buffer capacity for SharedFlow
    private val bufferCapacity = 1000

    // SharedFlow for broadcasting events to all subscribers
    private val _eventFlow = MutableSharedFlow<RepositoryEvent>(
        replay = 0,
        extraBufferCapacity = bufferCapacity // Allow buffering for high-frequency events
    )
    val eventFlow: SharedFlow<RepositoryEvent> = _eventFlow.asSharedFlow()

    // Atomic reference for thread-safe listener management
    private val listeners: AtomicRef<Map<String, List<EventListener>>> = atomic<Map<String, List<EventListener>>>(emptyMap())
    
    // Mutex for thread-safe listener operations
    private val listenerMutex = Mutex()

    /**
     * Event type constants with modern organization.
     */
    object EventType {
        // Repository sources
        const val CONFIG_REPOSITORY = "config_repository"
        const val SYNC_REPOSITORY = "sync_repository"
        const val DELIVERY_REPOSITORY = "delivery_repository"
        const val ADDRESS_REPOSITORY = "address_repository"
        const val PREFERENCE_REPOSITORY = "preference_repository"
        const val SUBSCRIPTION_REPOSITORY = "subscription_repository"
        const val USER_REPOSITORY = "user_repository"

        // Event types with modern categorization
        const val SYNC_OPERATION_COMPLETED = "sync_operation_completed"
        const val SYNC_OPERATION_FAILED = "sync_operation_failed"
        const val SYNC_STATUS_CHANGED = "sync_status_changed"
        const val CONFIG_UPDATED = "config_updated"
        const val PROFILE_UPDATED = "profile_updated"
        const val SUBSCRIPTION_CHANGED = "subscription_changed"
        const val USER_LOGOUT = "user_logout"
        const val CACHE_INVALIDATED = "cache_invalidated"
        const val NETWORK_STATUS_CHANGED = "network_status_changed"
    }

    /**
     * Modern repository event data class with enhanced type safety.
     */
    data class RepositoryEvent(
        val type: String,
        val source: String,
        val data: Map<String, Any> = emptyMap(),
        val timestamp: kotlinx.datetime.Instant = kotlinx.datetime.Clock.System.now(),
        val correlationId: String? = null
    ) {
        /**
         * Get a specific data value with type safety.
         *
         * @param key Data key
         * @param T Expected value type
         * @return Value or null if not found or wrong type
         */
        @Suppress("UNCHECKED_CAST")
        inline fun <reified T> getValue(key: String): T? {
            return try {
                val value = data[key]
                when (value) {
                    null -> null
                    is T -> value
                    else -> {
                        Log.w("ModernRepositoryEventBus", "Type mismatch for key '$key': expected ${T::class.simpleName}, got ${value::class.simpleName}")
                        null
                    }
                }
            } catch (e: Exception) {
                Log.w("ModernRepositoryEventBus", "Error getting event data for key '$key'", e)
                null
            }
        }

        /**
         * Get a specific data value with default fallback.
         *
         * @param key Data key
         * @param defaultValue Default value if key not found or wrong type
         * @return Value or default
         */
        inline fun <reified T> getValueOrDefault(key: String, defaultValue: T): T {
            return getValue<T>(key) ?: defaultValue
        }
    }

    /**
     * Modern functional interface for event listeners.
     */
    fun interface EventListener {
        /**
         * Called when an event is received.
         * This method should be lightweight and non-blocking.
         *
         * @param event The repository event
         */
        suspend fun onEvent(event: RepositoryEvent)
    }

    /**
     * Register a listener for a specific repository with thread safety.
     *
     * @param repositoryType Repository type from EventType constants
     * @param listener Listener to register
     */
    suspend fun register(repositoryType: String, listener: EventListener) {
        listenerMutex.withLock {
            val currentListeners = listeners.value
            val repoListeners = currentListeners[repositoryType] ?: emptyList()
            
            if (listener !in repoListeners) {
                val updatedRepoListeners = repoListeners + listener
                val updatedListeners = currentListeners + (repositoryType to updatedRepoListeners)
                listeners.value = updatedListeners
                
                Log.d(TAG, "Registered listener for repository type: $repositoryType")
            }
        }
    }

    /**
     * Unregister a listener for a specific repository with thread safety.
     *
     * @param repositoryType Repository type from EventType constants
     * @param listener Listener to unregister
     */
    suspend fun unregister(repositoryType: String, listener: EventListener) {
        listenerMutex.withLock {
            val currentListeners = listeners.value
            val repoListeners = currentListeners[repositoryType] ?: return@withLock
            
            val updatedRepoListeners = repoListeners - listener
            val updatedListeners = if (updatedRepoListeners.isEmpty()) {
                currentListeners - repositoryType
            } else {
                currentListeners + (repositoryType to updatedRepoListeners)
            }
            listeners.value = updatedListeners
            
            Log.d(TAG, "Unregistered listener for repository type: $repositoryType")
        }
    }

    /**
     * Post an event to the event bus with modern Flow distribution.
     *
     * @param eventType Event type from EventType constants
     * @param sourceRepository Source repository identifier
     * @param data Event data
     * @param targetRepository Optional target repository for direct delivery
     * @param correlationId Optional correlation ID for event tracking
     */
    suspend fun post(
        eventType: String, 
        sourceRepository: String, 
        data: Map<String, Any>? = null, 
        targetRepository: String? = null,
        correlationId: String? = null
    ) {
        val event = RepositoryEvent(
            type = eventType,
            source = sourceRepository,
            data = data ?: emptyMap(),
            correlationId = correlationId
        )

        // Emit to SharedFlow for all Flow-based subscribers
        val emitResult = _eventFlow.tryEmit(event)
        if (!emitResult) {
            Log.w(TAG, "Failed to emit event to SharedFlow: ${event.type} from ${event.source}")
        }

        // Direct notification to target repository if specified
        if (targetRepository != null) {
            notifyListeners(targetRepository, event)
        } else {
            // Broadcast to all repositories except the source
            val currentListeners = listeners.value
            for (repoType in currentListeners.keys) {
                if (repoType != sourceRepository) {
                    notifyListeners(repoType, event)
                }
            }
        }
    }

    /**
     * Notify all listeners for a specific repository with structured concurrency.
     *
     * @param repositoryType Repository type
     * @param event Event to deliver
     */
    private fun notifyListeners(repositoryType: String, event: RepositoryEvent) {
        val repoListeners = listeners.value[repositoryType] ?: return
        
        // Use structured concurrency for parallel listener notification
        applicationScope.launch {
            repoListeners.forEach { listener ->
                launch {
                    try {
                        listener.onEvent(event)
                    } catch (e: Exception) {
                        Log.e(TAG, buildString {
                            append("Error delivering event to listener: ")
                            append("event=${event.type}, ")
                            append("source=${event.source}, ")
                            append("target=$repositoryType, ")
                            append("error=${e.message}")
                        }, e)
                    }
                }
            }
        }
    }

    // eventFlow property already provides getter - no need for explicit function

    /**
     * Get event statistics for monitoring and debugging.
     *
     * @return Map with listener statistics
     */
    fun getEventStatistics(): Map<String, Any> {
        val currentListeners = listeners.value
        return buildMap {
            put("total_repository_types", currentListeners.size)
            put("total_listeners", currentListeners.values.sumOf { it.size })
            put("repository_listener_counts", currentListeners.mapValues { it.value.size })
            put("buffer_capacity", bufferCapacity)
            put("replay_cache_size", _eventFlow.replayCache.size)
        }
    }

    /**
     * Clear all listeners - for testing purposes only.
     * Uses atomic operation for thread safety.
     */
    @JvmSynthetic
    internal suspend fun clearAllListenersForTesting() {
        listenerMutex.withLock {
            listeners.value = emptyMap()
            Log.d(TAG, "Cleared all listeners for testing")
        }
    }

    /**
     * Legacy API compatibility - deprecated.
     * New code should use getEventFlow() instead.
     */
    @Deprecated(
        message = "Use Flow-based getEventFlow() instead",
        replaceWith = ReplaceWith("getEventFlow()"),
        level = DeprecationLevel.WARNING
    )
    fun getEventSubject(): SharedFlow<RepositoryEvent> {
        Log.w(TAG, "getEventSubject() is deprecated. Use getEventFlow() instead")
        return eventFlow
    }
} 