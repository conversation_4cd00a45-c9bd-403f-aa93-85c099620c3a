package com.autogratuity.data.repository.user

// Performance monitoring

import android.util.Log
import com.autogratuity.data.datasource.local.UserLocalDataSource
import com.autogratuity.data.datasource.remote.UserRemoteDataSource
import com.autogratuity.data.mapper.UserMapper
import com.autogratuity.data.model.Result
import com.autogratuity.data.repository.core.RepositoryErrorHandler
import com.autogratuity.data.security.AuthenticationManager
import com.autogratuity.data.util.ValidationEngine
import com.autogratuity.data.util.RequestDeduplicationManager
import com.autogratuity.data.util.RequestKeys
import com.autogratuity.data.util.RequestTimeouts
import com.autogratuity.data.util.ModernPriorityTaskScheduler
import com.autogratuity.data.util.SessionManager
import com.autogratuity.data.util.AuthenticationStateCoordinator
import com.autogratuity.debug.ClarityArchitectureMonitor
import com.autogratuity.domain.model.User
import com.autogratuity.domain.model.UserPreferences
import com.autogratuity.domain.model.UserSubscription
import com.autogratuity.domain.model.UserUsageStats
import com.autogratuity.domain.repository.AddressRepository
import com.autogratuity.domain.repository.UserRepository
import java.time.OffsetDateTime
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds
import kotlin.time.TimeSource


/**
 * User repository implementation following DeliveryRepositoryImpl reference pattern.
 *
 * Uses FirestoreRepository inheritance with RemoteDataSource + LocalDataSource + Mapper pattern.
 * Extends FirestoreRepository like DeliveryRepositoryImpl for consistency.
 *
 * Key features:
 * - FirestoreRepository inheritance pattern like DeliveryRepositoryImpl
 * - Cache-first strategy: local → remote → cache SSoT results
 * - Result<T> pattern for all operations
 * - SSoT User models throughout
 * - ✅ DUAL INTERFACE IMPLEMENTATION: Implements both domain and data interfaces
 * - Pure orchestration: coordinates RemoteDataSource + LocalDataSource + Mapper only
 *
 * ✅ ATOMIC CACHING INTEGRATION: Implements both UserRepository (domain) and UserProfileRepository (data)
 * following the Cache System Boundary Adaptation Pattern from atomic-caching.md
 */
@OptIn(ExperimentalCoroutinesApi::class)
class UserProfileRepositoryImpl(
    // Core architectural dependencies following clarity.md principles
    private val remoteDataSource: UserRemoteDataSource,
    private val localDataSource: UserLocalDataSource,
    private val userMapper: UserMapper,
    // Repository cross-domain dependencies following clarity.md
    private val addressRepository: AddressRepository,
    // Repository orchestration dependencies
    private val authManager: AuthenticationManager,
    private val ioDispatcher: CoroutineDispatcher,
    private val applicationScope: CoroutineScope,
    // Lazy infrastructure utilities to prevent ANR
    private val requestDeduplicationManager: Lazy<RequestDeduplicationManager>,
    private val priorityTaskScheduler: Lazy<ModernPriorityTaskScheduler>,
    private val sessionManager: Lazy<SessionManager>,
    private val authStateCoordinator: Lazy<AuthenticationStateCoordinator>,
    // Performance infrastructure
    private val repositoryErrorHandler: Lazy<RepositoryErrorHandler>,
    // Data validation infrastructure
    private val validationEngine: Lazy<ValidationEngine>,
    // JSON serialization with proper JSR310 support
    private val objectMapper: com.fasterxml.jackson.databind.ObjectMapper
) : UserRepository, UserProfileRepository {

    companion object {
        private const val TAG = "UserProfileRepositoryImpl"

        /**
         * Helper function to safely convert kotlin.Result to com.autogratuity.data.model.Result
         */
        private fun <T> convertKotlinResultToModelResult(kotlinResult: kotlin.Result<T>): Result<T> {
            return when {
                kotlinResult.isSuccess -> kotlinResult.getOrNull()?.let { Result.Success(it) }
                    ?: Result.Error(Exception("Null result"))
                else -> {
                    val throwable = kotlinResult.exceptionOrNull()
                    val exception = when (throwable) {
                        is Exception -> throwable
                        null -> Exception("Unknown error - no exception details available")
                        else -> Exception("Unknown error: ${throwable.message}", throwable)
                    }
                    Result.Error(exception)
                }
            }
        }
    }

    /**
     * Enhanced authentication helper using AuthenticationStateCoordinator
     * ✅ ENHANCED: Proper handling of null return from waitForAuthentication() to prevent Flow cancellation exceptions
     */
    private suspend fun getCurrentUserIdSuspend(): String {
        Log.d(TAG, "getCurrentUserIdSuspend: Checking authentication state")

        // ✅ ENHANCED: Handle null return from waitForAuthentication() (timeout/cancellation scenarios)
        val authState = authStateCoordinator.value.waitForAuthentication(timeoutMs = 5000)

        return when (authState) {
            is AuthenticationStateCoordinator.AuthReadyState.Authenticated -> {
                Log.d(TAG, "getCurrentUserIdSuspend: Authentication confirmed for user ${authState.userId}")
                authState.userId
            }
            is AuthenticationStateCoordinator.AuthReadyState.Unauthenticated -> {
                Log.e(TAG, "getCurrentUserIdSuspend: User not authenticated")
                throw IllegalStateException("User not authenticated")
            }
            is AuthenticationStateCoordinator.AuthReadyState.AuthenticationInProgress -> {
                Log.e(TAG, "getCurrentUserIdSuspend: Authentication timeout - still in progress")
                throw IllegalStateException("Authentication timeout - authentication still in progress")
            }
            is AuthenticationStateCoordinator.AuthReadyState.Unknown -> {
                Log.e(TAG, "getCurrentUserIdSuspend: Authentication timeout - unknown state")
                throw IllegalStateException("Authentication timeout - unknown authentication state")
            }
            null -> {
                // ✅ CRITICAL FIX: Handle null return from waitForAuthentication() (timeout/cancellation)
                Log.w(TAG, "getCurrentUserIdSuspend: Authentication wait returned null (timeout or cancellation)")
                throw IllegalStateException("Authentication wait timeout - no authentication state available")
            }
        }
    }

    /**
     * Execute operation with priority task scheduling
     * Following DeliveryRepositoryImpl pattern for consistent task prioritization
     */
    private suspend fun <T> executeWithPriority(
        operation: String,
        entityType: String = "User",
        entityId: String? = null,
        priority: com.autogratuity.data.util.TaskPriority = com.autogratuity.data.util.TaskPriority.HIGH,
        block: suspend () -> Result<T>
    ): Result<T> {
        val task = com.autogratuity.data.util.PriorityTask<T>(
            id = "${operation}_${entityId ?: "user"}",
            priority = priority,
            operation = {
                // Convert com.autogratuity.data.model.Result to kotlin.Result
                val result = block()
                when (result) {
                    is Result.Success -> kotlin.Result.success(result.data)
                    is Result.Error -> kotlin.Result.failure(result.exception)
                    is Result.Loading -> kotlin.Result.failure(IllegalStateException("Unexpected Loading state"))
                }
            },
            entityType = entityType,
            operationType = operation,
            estimatedDuration = 5.seconds,
            timeout = 30.seconds
        )

        return try {
            val kotlinResult = priorityTaskScheduler.value.scheduleTask(task)
            when {
                kotlinResult.isSuccess -> Result.Success(kotlinResult.getOrThrow())
                else -> Result.Error((kotlinResult.exceptionOrNull() ?: Exception("Unknown error")) as Exception)
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    /**
     * Execute operation with comprehensive profiling and monitoring
     * Following DeliveryRepositoryImpl pattern for consistent performance tracking
     */
    private suspend fun <T> executeWithProfiling(
        operation: String,
        entityId: String? = null,
        block: suspend () -> Result<T>
    ): Result<T> {
        val startTime = TimeSource.Monotonic.markNow()
        val session = sessionManager.value.getCurrentSession()

        Log.d(TAG, "executeWithProfiling: Starting $operation for entity $entityId (session: ${session?.sessionId})")

        return try {
            val result = block()
            val duration = startTime.elapsedNow()

            // Monitor successful operation with comprehensive metrics
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "UserRepository",
                operation = operation,
                duration = duration,
                success = result is Result.Success,
                cacheHit = false, // Will be overridden by specific operations
                dataType = "User",
                entityId = entityId ?: "unknown",
                userId = entityId ?: "unknown",
                resultCount = if (result is Result.Success) 1 else 0,
                dataSource = "repository",
                dataSize = 0, // Will be overridden by specific operations
                cacheStrategy = "cache-first"
            )

            Log.d(TAG, "executeWithProfiling: Completed $operation in ${duration.inWholeMilliseconds}ms")
            result

        } catch (e: Exception) {
            val duration = startTime.elapsedNow()

            // Monitor failed operation
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "UserRepository",
                operation = operation,
                duration = duration,
                success = false,
                error = e,
                dataType = "User",
                entityId = entityId ?: "unknown",
                userId = entityId ?: "unknown",
                resultCount = 0,
                dataSource = "error"
            )

            Log.e(TAG, "executeWithProfiling: Failed $operation after ${duration.inWholeMilliseconds}ms", e)
            Result.Error(e)
        }
    }

    /**
     * Enhanced error handling with comprehensive validation and monitoring
     * Following DeliveryRepositoryImpl pattern for consistent error management
     */
    private suspend fun <T> executeWithEnhancedErrorHandling(
        operation: String,
        entityId: String? = null,
        validateInput: suspend () -> Unit = {},
        block: suspend () -> Result<T>
    ): Result<T> {
        val startTime = TimeSource.Monotonic.markNow()
        // ✅ SESSION CONTEXT: Capture session for comprehensive logging and correlation across all operation phases
        val session = sessionManager.value.getCurrentSession()

        return try {
            // 1. Input validation phase
            validateInput()

            // 2. Execute operation with profiling
            val result = executeWithProfiling(operation, entityId, block)

            // 3. Result validation
            when (result) {
                is Result.Success -> {
                    Log.d(TAG, "executeWithEnhancedErrorHandling: $operation completed successfully for entity $entityId (session: ${session?.sessionId})")

                    // ✅ SESSION CORRELATION: Add success event with session context
                    ClarityArchitectureMonitor.addSessionEvent("success:${operation}:$entityId")
                    result
                }
                is Result.Error -> {
                    Log.w(TAG, "executeWithEnhancedErrorHandling: $operation failed for entity $entityId: ${result.exception.message} (session: ${session?.sessionId})")

                    // Enhanced error monitoring with session correlation
                    ClarityArchitectureMonitor.addSessionEvent("error:${operation}:$entityId")
                    result
                }
                is Result.Loading -> {
                    Log.w(TAG, "executeWithEnhancedErrorHandling: $operation returned Loading for entity $entityId (session: ${session?.sessionId})")

                    // ✅ SESSION CORRELATION: Add loading state warning with session context
                    ClarityArchitectureMonitor.addSessionEvent("warning:loading_state:${operation}:$entityId")
                    Result.Error(IllegalStateException("Operation returned Loading unexpectedly"))
                }
            }

        } catch (e: IllegalArgumentException) {
            val duration = startTime.elapsedNow()
            Log.e(TAG, "executeWithEnhancedErrorHandling: Input validation failed for $operation (session: ${session?.sessionId})", e)

            // Monitor validation errors
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "UserRepository",
                operation = operation,
                duration = duration,
                success = false,
                error = e,
                dataType = "User",
                entityId = entityId ?: "unknown",
                userId = entityId ?: "unknown",
                resultCount = 0,
                dataSource = "validation_error"
            )

            Result.Error(e)

        } catch (e: IllegalStateException) {
            val duration = startTime.elapsedNow()
            Log.e(TAG, "executeWithEnhancedErrorHandling: State validation failed for $operation (session: ${session?.sessionId})", e)

            // Monitor state errors
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "UserRepository",
                operation = operation,
                duration = duration,
                success = false,
                error = e,
                dataType = "User",
                entityId = entityId ?: "unknown",
                userId = entityId ?: "unknown",
                resultCount = 0,
                dataSource = "state_error"
            )

            Result.Error(e)

        } catch (e: Exception) {
            val duration = startTime.elapsedNow()
            Log.e(TAG, "executeWithEnhancedErrorHandling: Unexpected error in $operation (session: ${session?.sessionId})", e)

            // Monitor unexpected errors
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = "UserRepository",
                operation = operation,
                duration = duration,
                success = false,
                error = e,
                dataType = "User",
                entityId = entityId ?: "unknown",
                userId = entityId ?: "unknown",
                resultCount = 0,
                dataSource = "unexpected_error"
            )

            Result.Error(e)
        }
    }

    // --- Domain Interface Implementation (SSoT User models) ---

    /**
     * Get user by ID using cache-first strategy like AddressRepositoryImpl
     * ✅ ENHANCED: With priority task scheduling for user-critical operations
     */
    override suspend fun getUserById(id: String): Result<User?> = withContext(ioDispatcher) {
        // Execute with CRITICAL priority for user profile fetching (highest priority read operation)
        executeWithPriority(
            operation = "getUserById",
            entityType = "User",
            entityId = id,
            priority = com.autogratuity.data.util.TaskPriority.CRITICAL
        ) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "getUserById",
            entityType = "User"
        ) {
            val totalStartTime = TimeSource.Monotonic.markNow()
            var cacheCheckDuration: Duration? = null
            var remoteFetchDuration: Duration? = null
            var mappingDuration: Duration? = null
            var cacheStoreDuration: Duration? = null
            var cacheHit = false

            val currentUserId = getCurrentUserIdSuspend()
            val session = sessionManager.value.getCurrentSession()

            Log.d(TAG, "getUserById: Starting operation for user $id (session: ${session?.sessionId})")

            if (id != currentUserId) {
                throw IllegalArgumentException("Cannot access profile for different user")
            }

            // 1. CACHE CHECK PHASE
            val cacheCheckStart = TimeSource.Monotonic.markNow()
            val localResult = localDataSource.getUserById(currentUserId, id)
            cacheCheckDuration = cacheCheckStart.elapsedNow()
            
            if (localResult is Result.Success && localResult.data != null) {
                cacheHit = true
                Log.d(TAG, "getUserById: Found user $id in local cache (session: ${session?.sessionId})")

                // ENHANCED: Add session correlation for user cache hit
                ClarityArchitectureMonitor.addSessionEvent("user_cache_hit:$id")

                // ENHANCED: Monitor cache hit with breakdown
                ClarityArchitectureMonitor.monitorCacheSystemPerformance(
                    repositoryClass = "UserRepository",
                    operation = "getUserById",
                    cacheCheckDuration = cacheCheckDuration,
                    remoteFetchDuration = null,
                    mappingDuration = null,
                    cacheStoreDuration = null,
                    cacheHit = true,
                    entityType = "User",
                    entityId = id,
                    userId = currentUserId,
                    cacheMetrics = mapOf<String, Any>(
                        "operation" to "cache_hit",
                        "source" to "local_cache",
                        "data_size" to localResult.data.toString().length
                    )
                )

                // Monitor successful cache hit
                ClarityArchitectureMonitor.monitorRepositoryOperation(
                    repositoryClass = "UserRepository",
                    operation = "getUserById",
                    duration = totalStartTime.elapsedNow(),
                    success = true,
                    cacheHit = true,
                    dataType = "User",
                    entityId = id,
                    userId = currentUserId,
                    resultCount = 1,
                    dataSource = "cache",
                    dataSize = localResult.data.toString().length,
                    cacheStrategy = "cache-first"
                )

                return@handleSuspendFunction localResult
            }
            if (localResult is Result.Error) {
                Log.w(TAG, "getUserById: Local data source failed for user $id. Error: ${localResult.exception.message}")
            }

            // 2. REMOTE FETCH PHASE WITH REQUEST DEDUPLICATION
            Log.d(TAG, "getUserById: User $id not in cache, fetching from remote with deduplication")
            val remoteFetchStart = TimeSource.Monotonic.markNow()

            // Use RequestDeduplicationManager to prevent duplicate user profile fetches
            val remoteResultDto = requestDeduplicationManager.value.deduplicateRequest(
                key = RequestKeys.userProfile(id),
                timeout = RequestTimeouts.STANDARD_OPERATION,
                operation = {
                    remoteDataSource.getUserProfileById(id)
                }
            ) ?: throw IllegalStateException("Request deduplication timeout for user $id")

            remoteFetchDuration = remoteFetchStart.elapsedNow()

            when (remoteResultDto) {
                is Result.Success -> {
                    val userProfileDto = remoteResultDto.data
                    if (userProfileDto != null) {
                        // 3. MAPPING PHASE
                        val mappingStart = TimeSource.Monotonic.markNow()
                        val ssotResult = userMapper.mapToDomain(id, userProfileDto)
                        mappingDuration = mappingStart.elapsedNow()
                        
                        when (ssotResult) {
                            is Result.Success -> {
                                val ssotUser = ssotResult.data
                                
                                // 4. CACHE STORAGE PHASE
                                val cacheStoreStart = TimeSource.Monotonic.markNow()
                                localDataSource.saveUser(id, ssotUser)
                                cacheStoreDuration = cacheStoreStart.elapsedNow()
                                
                                Log.d(TAG, "getUserById: Fetched user $id from remote and updated cache")
                                
                                // ENHANCED: Add session correlation for user cache miss
                                ClarityArchitectureMonitor.addSessionEvent("user_cache_miss:$id")

                                // ENHANCED: Monitor complete cache miss breakdown
                                ClarityArchitectureMonitor.monitorCacheSystemPerformance(
                                    repositoryClass = "UserRepository",
                                    operation = "getUserById",
                                    cacheCheckDuration = cacheCheckDuration,
                                    remoteFetchDuration = remoteFetchDuration,
                                    mappingDuration = mappingDuration,
                                    cacheStoreDuration = cacheStoreDuration,
                                    cacheHit = false,
                                    entityType = "User",
                                    entityId = id,
                                    userId = currentUserId,
                                    cacheMetrics = mapOf<String, Any>(
                                        "operation" to "cache_miss_with_remote_fetch",
                                        "source" to "firestore",
                                        "total_time_ms" to totalStartTime.elapsedNow().inWholeMilliseconds,
                                        "cache_check_ms" to cacheCheckDuration.inWholeMilliseconds,
                                        "remote_fetch_ms" to remoteFetchDuration.inWholeMilliseconds,
                                        "mapping_ms" to mappingDuration.inWholeMilliseconds,
                                        "cache_store_ms" to cacheStoreDuration.inWholeMilliseconds,
                                        "data_size" to userProfileDto.toString().length
                                    )
                                )

                                // Monitor successful remote fetch
                                ClarityArchitectureMonitor.monitorRepositoryOperation(
                                    repositoryClass = "UserRepository",
                                    operation = "getUserById",
                                    duration = totalStartTime.elapsedNow(),
                                    success = true,
                                    cacheHit = false,
                                    dataType = "User",
                                    entityId = id,
                                    userId = currentUserId,
                                    resultCount = 1,
                                    dataSource = "remote",
                                    dataSize = userProfileDto.toString().length,
                                    cacheStrategy = "cache-first"
                                )

                                Result.Success(ssotUser)
                            }
                            is Result.Error -> {
                                Log.e(TAG, "getUserById: Failed to map DTO to SSoT for user $id", ssotResult.exception)
                                throw ssotResult.exception
                            }
                            is Result.Loading -> {
                                Log.w(TAG, "getUserById: SSoT mapping returned Loading for user $id")
                                throw IllegalStateException("SSoT mapping returned Loading unexpectedly")
                            }
                        }
                    } else {
                        Log.d(TAG, "getUserById: User $id not found in remote")
                        Result.Success(null)
                    }
                }
                is Result.Error -> {
                    Log.e(TAG, "getUserById: Remote data source failed for user $id", remoteResultDto.exception)
                    throw remoteResultDto.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "getUserById: Remote data source returned Loading for user $id")
                    throw IllegalStateException("Remote data source returned Loading unexpectedly")
                }
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    null -> Exception("Unknown error - no exception details available")
                    else -> Exception("Unknown error: ${throwable.message}", throwable)
                }
                Result.Error(exception)
            }
        }
        } // Close priority task block
    }

    /**
     * ✅ FIX: Get current authenticated user with enhanced timing handling
     */
    override suspend fun getCurrentUser(): Result<User?> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "getCurrentUser",
            entityType = "User"
        ) {
            val totalStartTime = TimeSource.Monotonic.markNow()

            // ✅ ENHANCED: Use AuthenticationStateCoordinator instead of manual retry logic
            try {
                val currentUserId = getCurrentUserIdSuspend()
                Log.d(TAG, "getCurrentUser: Authentication ready for user $currentUserId")

                val result = getUserById(currentUserId)

                // Monitor domain repository operation with detailed context
                ClarityArchitectureMonitor.monitorRepositoryOperation(
                    repositoryClass = "UserRepository",
                    operation = "getCurrentUser",
                    duration = totalStartTime.elapsedNow(),
                    success = result is Result.Success,
                    cacheHit = result is Result.Success && result.data != null,
                    dataType = "User",
                    entityId = currentUserId,
                    userId = currentUserId,
                    resultCount = if (result is Result.Success && result.data != null) 1 else 0,
                    dataSource = if (result is Result.Success && result.data != null) "cache" else "none",
                    dataSize = if (result is Result.Success && result.data != null) result.data.toString().length else 0,
                    cacheStrategy = "cache-first"
                )

                return@handleSuspendFunction result

            } catch (e: IllegalStateException) {
                // ✅ ENHANCED: Only log authentication errors for actual authentication failures
                val isAuthenticationError = e.message?.contains("authentication", ignoreCase = true) == true ||
                                          e.message?.contains("not authenticated", ignoreCase = true) == true ||
                                          e.message?.contains("timeout", ignoreCase = true) == true

                if (isAuthenticationError) {
                    Log.w(TAG, "getCurrentUser: Authentication not available: ${e.message}")
                } else {
                    // This is likely a Flow cancellation or other non-authentication issue
                    Log.d(TAG, "getCurrentUser: Operation cancelled or interrupted: ${e.message}")
                }

                // Monitor authentication unavailable (this is a valid state)
                ClarityArchitectureMonitor.monitorRepositoryOperation(
                    repositoryClass = "UserRepository",
                    operation = "getCurrentUser",
                    duration = totalStartTime.elapsedNow(),
                    success = true, // This is actually successful - just no authenticated user
                    cacheHit = false,
                    dataType = "User",
                    entityId = "null",
                    userId = "null",
                    resultCount = 0,
                    dataSource = "none",
                    dataSize = 0,
                    cacheStrategy = "cache-first"
                )

                return@handleSuspendFunction Result.Success(null)
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
    }

    /**
     * Add a new user using cache-first strategy like AddressRepositoryImpl
     * ✅ ENHANCED: With priority task scheduling and comprehensive validation
     */
    override suspend fun addUser(user: User): Result<Unit> = withContext(ioDispatcher) {
        // Execute with HIGH priority and enhanced error handling
        executeWithPriority(
            operation = "addUser",
            entityType = "User",
            entityId = user.id,
            priority = com.autogratuity.data.util.TaskPriority.HIGH
        ) {
            executeWithEnhancedErrorHandling(
                operation = "addUser",
                entityId = user.id,
                validateInput = {
                    // Comprehensive input validation
                    if (user.id.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                    if (user.email?.isBlank() == true) {
                        throw IllegalArgumentException("User email cannot be blank")
                    }
                    if (user.displayName?.isBlank() == true) {
                        throw IllegalArgumentException("User display name cannot be blank")
                    }

                    // Validate user against current authentication
                    val currentUserId = authManager.getCurrentUserId()
                        ?: throw IllegalStateException("User not authenticated")
                    if (user.id != currentUserId) {
                        throw IllegalArgumentException("Cannot add profile for different user")
                    }
                }
            ) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "addUser",
            entityType = "User"
        ) {
            val currentUserId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")
            val session = sessionManager.value.getCurrentSession()

            if (user.id != currentUserId) {
                throw IllegalArgumentException("Cannot add profile for different user")
            }

            Log.d(TAG, "addUser: Starting user creation for ${user.id} (session: ${session?.sessionId})")

            // ✅ REPOSITORY-LEVEL BUSINESS RULE VALIDATION (LENIENT FOR CREATION)
            val validationResult = validationEngine.value.validateUser(user, isCreation = true)
            if (!validationResult.isValid) {
                Log.w(TAG, "🚨 User validation failed at repository level for user ${user.id}: ${validationResult.errors}")
                throw IllegalArgumentException("User data validation failed: ${validationResult.errors.joinToString(", ")}")
            }
            
            // Log validation warnings if any (expected during creation)
            if (validationResult.warnings.isNotEmpty()) {
                Log.d(TAG, "🔧 User validation warnings during creation for user ${user.id}: ${validationResult.warnings}")
            } else {
                Log.d(TAG, "✅ User validation passed with no warnings during creation for user ${user.id}")
            }
            
            // 🔧 LENIENT: Additional business rules (no email requirement during creation)
            // Note: Email validation is now handled by ValidationEngine with isCreation=true
            // This ensures signup always works even if email is missing initially

            // 1. Map SSoT User to UserProfileDto
            val mapToDtoResult = userMapper.mapToDto(user)
            val userProfileDto = when (mapToDtoResult) {
                is Result.Success -> mapToDtoResult.data
                is Result.Error -> {
                    Log.e(TAG, "addUser: Failed to map SSoT to DTO for user ${user.id}", mapToDtoResult.exception)
                    throw mapToDtoResult.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "addUser: userMapper.mapToDto returned Loading for user ${user.id}")
                    throw IllegalStateException("userMapper.mapToDto returned Loading unexpectedly")
                }
            }

            // 2. Save to remote data source with request deduplication
            Log.d(TAG, "addUser: Adding user to remote for user ${user.id}")
            val firestoreStartTime = TimeSource.Monotonic.markNow()

            // Use RequestDeduplicationManager to prevent duplicate user creation
            val remoteResult = requestDeduplicationManager.value.deduplicateRequest(
                key = RequestKeys.userProfileCreate(user.id),
                timeout = RequestTimeouts.COMPLEX_OPERATION,
                operation = {
                    remoteDataSource.saveUserProfile(user.id, userProfileDto)
                }
            ) ?: throw IllegalStateException("Request deduplication timeout for user creation ${user.id}")

            // Monitor Firestore write operation with detailed context
            ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = "users",
                documentId = user.id,
                duration = firestoreStartTime.elapsedNow(),
                success = remoteResult is Result.Success,
                dataSizeBytes = userProfileDto.toString().length,
                fullPath = "users/${user.id}",
                writeType = "SET",
                fieldsUpdated = listOf("profileData"),
                userId = user.id,
                documentData = mapOf(
                    "profileData" to "nested_user_data"
                )
            )

            when (remoteResult) {
                is Result.Success -> {
                    Log.i(TAG, "addUser: User added remotely for user ${user.id}")
                    // 3. Save SSoT to local cache
                    val localSaveResult = localDataSource.saveUser(user.id, user)
                    if (localSaveResult is Result.Error) {
                        Log.w(TAG, "addUser: Failed to save user ${user.id} to local cache", localSaveResult.exception)
                    }
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    Log.e(TAG, "addUser: Remote data source failed to add user ${user.id}", remoteResult.exception)
                    throw remoteResult.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "addUser: remoteDataSource.saveUserProfile returned Loading for user ${user.id}")
                    throw IllegalStateException("remoteDataSource.saveUserProfile returned Loading unexpectedly")
                }
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
            } // Close enhanced error handling block
        } // Close priority task block
    }

    /**
     * Update an existing user using cache-first strategy like AddressRepositoryImpl
     * ✅ ENHANCED: With priority task scheduling for user-critical operations
     */
    override suspend fun updateUser(user: User): Result<Unit> = withContext(ioDispatcher) {
        // Execute with HIGH priority for user updates (critical operation)
        executeWithPriority(
            operation = "updateUser",
            entityType = "User",
            entityId = user.id,
            priority = com.autogratuity.data.util.TaskPriority.HIGH
        ) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "updateUser",
            entityType = "User"
        ) {
            val currentUserId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")

            if (user.id != currentUserId) {
                throw IllegalArgumentException("Cannot update profile for different user")
            }

            if (user.id.isBlank()) {
                throw IllegalArgumentException("User ID cannot be blank for update")
            }

            // 1. Map SSoT User to UserProfileDto
            val mapToDtoResult = userMapper.mapToDto(user)
            val userProfileDto = when (mapToDtoResult) {
                is Result.Success -> mapToDtoResult.data
                is Result.Error -> {
                    Log.e(TAG, "updateUser: Failed to map SSoT to DTO for user ${user.id}", mapToDtoResult.exception)
                    throw mapToDtoResult.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "updateUser: userMapper.mapToDto returned Loading for user ${user.id}")
                    throw IllegalStateException("userMapper.mapToDto returned Loading unexpectedly")
                }
            }

            // 2. Update remote data source with request deduplication
            Log.d(TAG, "updateUser: Updating user ${user.id} in remote")
            val firestoreStartTime = TimeSource.Monotonic.markNow()

            // Use RequestDeduplicationManager to prevent duplicate user updates
            val remoteUpdateResult = requestDeduplicationManager.value.deduplicateRequest(
                key = RequestKeys.userProfileUpdate(user.id),
                timeout = RequestTimeouts.COMPLEX_OPERATION,
                operation = {
                    remoteDataSource.saveUserProfile(user.id, userProfileDto)
                }
            ) ?: throw IllegalStateException("Request deduplication timeout for user update ${user.id}")

            // Monitor Firestore write operation for SSOT/DTO alignment
            ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = "users",
                documentId = user.id,
                duration = firestoreStartTime.elapsedNow(),
                success = remoteUpdateResult is Result.Success,
                dataSizeBytes = userProfileDto.toString().length,
                fullPath = "users/${user.id}",
                writeType = "UPDATE",
                fieldsUpdated = listOf("profileData"),
                userId = user.id,
                documentData = mapOf(
                    "profileData" to "nested_user_data_updated"
                )
            )

            when (remoteUpdateResult) {
                is Result.Success -> {
                    Log.i(TAG, "updateUser: User ${user.id} updated remotely")
                    // 3. Save updated SSoT to local cache
                    val localSaveResult = localDataSource.saveUser(user.id, user)
                    if (localSaveResult is Result.Error) {
                        Log.w(TAG, "updateUser: Failed to update user ${user.id} in local cache", localSaveResult.exception)
                    }

                    // ✅ MODERN CACHE PATTERN: Invalidate cache after successful update to ensure data consistency
                    val invalidateResult = invalidateCache(user.id)
                    if (invalidateResult is Result.Error) {
                        Log.w(TAG, "updateUser: Failed to invalidate cache for user ${user.id}", invalidateResult.exception)
                        // Continue - cache invalidation failure shouldn't fail the update
                    } else {
                        Log.d(TAG, "updateUser: Cache invalidated for user ${user.id} - next read will fetch fresh data")
                    }

                    Result.Success(Unit)
                }
                is Result.Error -> {
                    Log.e(TAG, "updateUser: Remote data source failed to update user ${user.id}", remoteUpdateResult.exception)

                    // ✅ ERROR RECOVERY: Clear potentially stale cache when remote update fails
                    val clearResult = clearCache(user.id)
                    if (clearResult is Result.Error) {
                        Log.w(TAG, "updateUser: Failed to clear cache during error recovery for user ${user.id}", clearResult.exception)
                    } else {
                        Log.d(TAG, "updateUser: Cache cleared for user ${user.id} during error recovery")
                    }

                    throw remoteUpdateResult.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "updateUser: remoteDataSource.saveUserProfile returned Loading for user ${user.id}")
                    throw IllegalStateException("remoteDataSource.saveUserProfile returned Loading unexpectedly")
                }
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
        } // Close priority task block
    }

    /**
     * Delete user using cache-first strategy like AddressRepositoryImpl
     * ✅ ENHANCED: With priority task scheduling for user-critical operations
     */
    override suspend fun deleteUser(id: String): Result<Unit> = withContext(ioDispatcher) {
        // Execute with CRITICAL priority for user deletion (highest priority operation)
        executeWithPriority(
            operation = "deleteUser",
            entityType = "User",
            entityId = id,
            priority = com.autogratuity.data.util.TaskPriority.CRITICAL
        ) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "deleteUser",
            entityType = "User"
        ) {
            val currentUserId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")

            if (id != currentUserId) {
                throw IllegalArgumentException("Cannot delete profile for different user")
            }

            if (id.isBlank()) {
                throw IllegalArgumentException("User ID cannot be blank for delete")
            }

            // 1. Delete from remote data source
            Log.d(TAG, "deleteUser: Deleting user $id from remote")
            val remoteDeleteResult = remoteDataSource.deleteUserProfile(id)

            when (remoteDeleteResult) {
                is Result.Success -> {
                    Log.i(TAG, "deleteUser: User $id deleted remotely")
                    // 2. Delete from local cache
                    val localDeleteResult = localDataSource.deleteUserById(id, id)
                    if (localDeleteResult is Result.Error) {
                        Log.w(TAG, "deleteUser: Failed to delete user $id from local cache", localDeleteResult.exception)
                    }

                    // ✅ COMPREHENSIVE CLEANUP: Clear cache after successful deletion to ensure no stale data
                    val clearResult = clearCache(id)
                    if (clearResult is Result.Error) {
                        Log.w(TAG, "deleteUser: Failed to clear cache after deletion for user $id", clearResult.exception)
                        // Continue - cache clearing failure shouldn't fail the deletion
                    } else {
                        Log.d(TAG, "deleteUser: Cache cleared after successful deletion for user $id")
                    }

                    Result.Success(Unit)
                }
                is Result.Error -> {
                    Log.e(TAG, "deleteUser: Remote data source failed to delete user $id", remoteDeleteResult.exception)
                    throw remoteDeleteResult.exception
                }
                is Result.Loading -> {
                    Log.w(TAG, "deleteUser: remoteDataSource.deleteUserProfile returned Loading for user $id")
                    throw IllegalStateException("remoteDataSource.deleteUserProfile returned Loading unexpectedly")
                }
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
        } // Close priority task block
    }

    override fun observeUserById(id: String): Flow<Result<User?>> {
        val currentUserId = authManager.getCurrentUserId()
        val session = sessionManager.value.getCurrentSession()

        if (currentUserId != id) {
            Log.w(TAG, "observeUserById: Cannot observe profile for different user (session: ${session?.sessionId})")
            return flowOf(Result.Error(Exception("Cannot observe profile for different user")))
        }

        Log.d(TAG, "observeUserById: Starting observation for user $id (session: ${session?.sessionId})")

        return localDataSource.observeById(currentUserId, id)
            .map { user ->
                // Add session correlation for reactive updates
                ClarityArchitectureMonitor.addSessionEvent("user_observe_update:$id")
                Result.Success(user)
            }
            .onStart {
                // Trigger cache warming for observed user
                applicationScope.launch(ioDispatcher) {
                    try {
                        Log.d(TAG, "observeUserById: Triggering cache refresh for user $id")
                        getUserById(id) // This will refresh cache if needed
                    } catch (e: Exception) {
                        Log.w(TAG, "observeUserById: Cache refresh failed for user $id", e)
                    }
                }
            }
    }

    override fun observeCurrentUser(): Flow<Result<User?>> {
        // ✅ ENHANCED: Use AuthenticationStateCoordinator for reactive authentication
        val session = sessionManager.value.getCurrentSession()

        return flow {
            try {
                val currentUserId = getCurrentUserIdSuspend()
                Log.d(TAG, "observeCurrentUser: Authentication ready for user $currentUserId, starting observation (session: ${session?.sessionId})")

                observeUserById(currentUserId).collect { result ->
                    // Add session correlation for current user updates
                    ClarityArchitectureMonitor.addSessionEvent("current_user_update:$currentUserId")
                    emit(result)
                }

            } catch (e: IllegalStateException) {
                // ✅ CRITICAL FIX: Only log authentication errors for actual authentication failures
                // Check if this is a real authentication error or a Flow cancellation issue
                val isAuthenticationError = e.message?.contains("authentication", ignoreCase = true) == true ||
                                          e.message?.contains("not authenticated", ignoreCase = true) == true ||
                                          e.message?.contains("timeout", ignoreCase = true) == true

                if (isAuthenticationError) {
                    Log.w(TAG, "observeCurrentUser: Authentication not available: ${e.message} (session: ${session?.sessionId})")
                } else {
                    // This is likely a Flow cancellation or other non-authentication issue
                    Log.d(TAG, "observeCurrentUser: Flow operation cancelled or interrupted: ${e.message} (session: ${session?.sessionId})")
                }
                emit(Result.Success(null))

            } catch (e: kotlinx.coroutines.CancellationException) {
                // ✅ ENHANCED: Handle Flow cancellation explicitly (not an error)
                Log.d(TAG, "observeCurrentUser: Flow cancelled (normal lifecycle event) (session: ${session?.sessionId})")
                // Don't emit anything for cancellation - let the Flow complete naturally

            } catch (e: Exception) {
                // ✅ ENHANCED: Filter out Flow cancellation exceptions from error logs
                val isFlowCancellation = e.message?.contains("Flow was aborted", ignoreCase = true) == true ||
                                        e.message?.contains("no more elements needed", ignoreCase = true) == true

                if (isFlowCancellation) {
                    Log.d(TAG, "observeCurrentUser: Flow collection cancelled (normal operation) (session: ${session?.sessionId})")
                    // Don't emit anything for Flow cancellation
                } else {
                    Log.e(TAG, "observeCurrentUser: Error in authentication or observation (session: ${session?.sessionId})", e)
                    emit(Result.Error(e))
                }
            }
        }.flowOn(ioDispatcher)
    }

    // ===== DOMAIN INTERFACE IMPLEMENTATION COMPLETE =====
    // Following DeliveryRepositoryImpl pattern: ONLY domain interface methods, no data layer DTO methods

    // ===== ESSENTIAL BUSINESS OPERATIONS (UserRepository Interface) =====

    override suspend fun updateUserPreferences(userId: String, preferences: UserPreferences): Result<Unit> = withContext(ioDispatcher) {
        // Execute with HIGH priority for user preferences (important user operation)
        executeWithPriority(
            operation = "updateUserPreferences",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.HIGH
        ) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "updateUserPreferences",
            entityType = "User"
        ) {
            val currentUserId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")
            if (userId != currentUserId) {
                throw IllegalArgumentException("Cannot update preferences for different user")
            }

            val userResult = getUserById(userId)
            when (userResult) {
                is Result.Success -> {
                    val user = userResult.data ?: throw IllegalStateException("User not found")
                    val updatedUser = user.copy(preferences = preferences)
                    updateUser(updatedUser)
                }
                is Result.Error -> throw userResult.exception
                is Result.Loading -> throw IllegalStateException("getUserById returned Loading unexpectedly")
            }
        }

        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> {
                val throwable = handlerResult.exceptionOrNull()
                val exception = when (throwable) {
                    is Exception -> throwable
                    else -> Exception("Unknown error", throwable)
                }
                Result.Error(exception)
            }
        }
        } // Close priority task block
    }

    override suspend fun updateUserSubscription(userId: String, subscription: UserSubscription): Result<Unit> = withContext(ioDispatcher) {
        // Execute with MEDIUM priority for subscription operations (important business operation)
        executeWithPriority(
            operation = "updateUserSubscription",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithEnhancedErrorHandling(
                operation = "updateUserSubscription",
                entityId = userId,
                validateInput = {
                    // Comprehensive input validation for subscription updates
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank for subscription update")
                    }
                    if (subscription.level.isNullOrBlank()) {
                        throw IllegalArgumentException("Subscription level cannot be blank")
                    }
                    if (subscription.status.isNullOrBlank()) {
                        throw IllegalArgumentException("Subscription status cannot be blank")
                    }
                }
            ) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "updateUserSubscription",
                    entityType = "User"
                ) {
                    val currentUserId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")
                    val session = sessionManager.value.getCurrentSession()

                    if (userId != currentUserId) {
                        throw IllegalArgumentException("Cannot update subscription for different user")
                    }

                    Log.d(TAG, "updateUserSubscription: Updating subscription for user $userId (session: ${session?.sessionId})")

                    // ✅ CRITICAL DATA CONSISTENCY: Refresh from remote before subscription update
                    // Subscription changes are critical business operations that need guaranteed fresh data
                    Log.d(TAG, "updateUserSubscription: Refreshing user data from remote for critical subscription update")
                    val refreshResult = refreshFromRemote(userId)
                    if (refreshResult is Result.Error) {
                        Log.w(TAG, "updateUserSubscription: Failed to refresh from remote, proceeding with cached data", refreshResult.exception)
                        // Continue with cached data - refresh failure shouldn't block subscription updates
                    } else {
                        Log.d(TAG, "updateUserSubscription: Successfully refreshed user data from remote")
                    }

                    // Get current user, update subscription, save back
                    val userResult = getUserById(userId)
                    when (userResult) {
                        is Result.Success -> {
                            val user = userResult.data ?: throw IllegalStateException("User not found")
                            val updatedUser = user.copy(subscription = subscription)

                            // Add session correlation for subscription updates
                            ClarityArchitectureMonitor.addSessionEvent("user_subscription_update:$userId")

                            updateUser(updatedUser)
                        }
                        is Result.Error -> throw userResult.exception
                        is Result.Loading -> throw IllegalStateException("getUserById returned Loading unexpectedly")
                    }
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }

    override suspend fun updateUserUsageStats(userId: String, stats: UserUsageStats): Result<Unit> = withContext(ioDispatcher) {
        // Execute with NORMAL priority for usage stats operations (important for analytics)
        executeWithPriority(
            operation = "updateUserUsageStats",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithEnhancedErrorHandling(
                operation = "updateUserUsageStats",
                entityId = userId,
                validateInput = {
                    // Input validation for usage stats updates
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank for usage stats update")
                    }
                    // Basic stats validation
                    if (stats.deliveryCount != null && stats.deliveryCount < 0) {
                        throw IllegalArgumentException("Delivery count cannot be negative")
                    }
                    if (stats.totalTips != null && stats.totalTips < 0.0) {
                        throw IllegalArgumentException("Total tips cannot be negative")
                    }
                }
            ) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "updateUserUsageStats",
                    entityType = "User"
                ) {
                    val currentUserId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")
                    val session = sessionManager.value.getCurrentSession()

                    if (userId != currentUserId) {
                        throw IllegalArgumentException("Cannot update usage stats for different user")
                    }

                    Log.d(TAG, "updateUserUsageStats: Updating usage stats for user $userId (session: ${session?.sessionId})")

                    val userResult = getUserById(userId)
                    when (userResult) {
                        is Result.Success -> {
                            val user = userResult.data ?: throw IllegalStateException("User not found")

                            // ✅ CLEANUP FIX: Stats are now maintained server-side by address-stats-updater
                            // Simply update the user with the provided stats (no client-side calculation)
                            val updatedUser = user.copy(usageStats = stats)

                            // Add session correlation for usage stats updates
                            ClarityArchitectureMonitor.addSessionEvent("user_usage_stats_update:$userId")

                            updateUser(updatedUser)
                        }
                        is Result.Error -> throw userResult.exception
                        is Result.Loading -> throw IllegalStateException("getUserById returned Loading unexpectedly")
                    }
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }



    override suspend fun setDefaultAddress(userId: String, addressId: String): Result<Unit> = withContext(ioDispatcher) {
        // Execute with NORMAL priority for address operations (important for user experience)
        executeWithPriority(
            operation = "setDefaultAddress",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithEnhancedErrorHandling(
                operation = "setDefaultAddress",
                entityId = userId,
                validateInput = {
                    // Input validation for default address setting
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank for default address setting")
                    }
                    if (addressId.isBlank()) {
                        throw IllegalArgumentException("Address ID cannot be blank for default address setting")
                    }
                }
            ) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "setDefaultAddress",
                    entityType = "User"
                ) {
                    val currentUserId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")
                    val session = sessionManager.value.getCurrentSession()

                    if (userId != currentUserId) {
                        throw IllegalArgumentException("Cannot set default address for different user")
                    }

                    Log.d(TAG, "setDefaultAddress: Setting default address $addressId for user $userId (session: ${session?.sessionId})")

                    val userResult = getUserById(userId)
                    when (userResult) {
                        is Result.Success -> {
                            val user = userResult.data ?: throw IllegalStateException("User not found")
                            val updatedUser = user.copy(defaultAddressId = addressId)

                            // Add session correlation for default address updates
                            ClarityArchitectureMonitor.addSessionEvent("user_default_address_update:$userId:$addressId")

                            updateUser(updatedUser)
                        }
                        is Result.Error -> throw userResult.exception
                        is Result.Loading -> throw IllegalStateException("getUserById returned Loading unexpectedly")
                    }
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }

    override suspend fun getDefaultAddressSummary(): Result<String?> = withContext(ioDispatcher) {
        // Execute with LOW priority for summary operations (utility function)
        executeWithPriority(
            operation = "getDefaultAddressSummary",
            entityType = "User",
            entityId = "current_user",
            priority = com.autogratuity.data.util.TaskPriority.LOW
        ) {
            executeWithProfiling("getDefaultAddressSummary", "current_user") {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "getDefaultAddressSummary",
                    entityType = "User"
                ) {
                    val currentUserId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")
                    val session = sessionManager.value.getCurrentSession()

                    Log.d(TAG, "getDefaultAddressSummary: Getting default address summary for user $currentUserId (session: ${session?.sessionId})")

                    // Add session correlation for address summary requests
                    ClarityArchitectureMonitor.addSessionEvent("user_default_address_summary:$currentUserId")

                    // Get current user to find default address ID
                    val userResult = getUserById(currentUserId)
            when (userResult) {
                is Result.Success -> {
                    val user = userResult.data
                    val defaultAddressId = user?.defaultAddressId

                    return@handleSuspendFunction if (defaultAddressId.isNullOrEmpty()) {
                        Result.Success("No default address set")
                    } else {
                        // Get actual address details from AddressRepository
                        val addressResult = addressRepository.getAddressById(defaultAddressId)
                        when (addressResult) {
                            is Result.Success -> {
                                val address = addressResult.data
                                if (address != null) {
                                    // Create a user-friendly address summary
                                    val summary = buildString {
                                        // Primary address line
                                        address.fullAddress?.let { fullAddr ->
                                            append(fullAddr)
                                        } ?: address.components?.let { components ->
                                            // Fallback to building from components
                                            listOfNotNull(
                                                components.streetNumber,
                                                components.streetName,
                                                components.city,
                                                components.state
                                            ).joinToString(" ").takeIf { it.isNotBlank() }?.let { addr ->
                                                append(addr)
                                            }
                                        } ?: append("Address ID: $defaultAddressId")
                                        
                                        // Add tags if present
                                        address.tags?.takeIf { it.isNotEmpty() }?.let { tags ->
                                            append(" (${tags.joinToString(", ")})")
                                        }
                                        
                                        // Add notes if present
                                        address.notes?.takeIf { it.isNotBlank() }?.let { notes ->
                                            append(" - $notes")
                                        }
                                    }
                                    Result.Success(summary.takeIf { it.isNotBlank() } ?: "Default address: $defaultAddressId")
                                } else {
                                    Result.Success("Default address not found (ID: $defaultAddressId)")
                                }
                            }
                            is Result.Error -> {
                                Log.w(TAG, "getDefaultAddressSummary: Failed to fetch address $defaultAddressId", addressResult.exception)
                                // Graceful degradation - return basic info instead of failing
                                Result.Success("Default address: $defaultAddressId (details unavailable)")
                            }
                            is Result.Loading -> {
                                // This shouldn't happen for repository calls, but handle gracefully
                                Result.Success("Default address: $defaultAddressId (loading...)")
                            }
                        }
                    }
                }
                is Result.Error -> throw userResult.exception
                is Result.Loading -> throw IllegalStateException("getUserById returned Loading unexpectedly")
            }
        }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }

    override suspend fun updateUserDisplayName(newDisplayName: String): Result<Unit> = withContext(ioDispatcher) {
        // Execute with LOW priority for display name operations (user preference)
        executeWithPriority(
            operation = "updateUserDisplayName",
            entityType = "User",
            entityId = "current_user",
            priority = com.autogratuity.data.util.TaskPriority.LOW
        ) {
            executeWithEnhancedErrorHandling(
                operation = "updateUserDisplayName",
                entityId = "current_user",
                validateInput = {
                    // Input validation for display name updates
                    if (newDisplayName.isBlank()) {
                        throw IllegalArgumentException("Display name cannot be blank")
                    }
                    if (newDisplayName.length > 100) {
                        throw IllegalArgumentException("Display name cannot exceed 100 characters")
                    }
                }
            ) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "updateUserDisplayName",
                    entityType = "User"
                ) {
                    val currentUserId = authManager.getCurrentUserId() ?: throw IllegalStateException("User not authenticated")
                    val session = sessionManager.value.getCurrentSession()

                    Log.d(TAG, "updateUserDisplayName: Updating display name for user $currentUserId (session: ${session?.sessionId})")

                    // Get current user
                    val userResult = getUserById(currentUserId)
                    when (userResult) {
                        is Result.Success -> {
                            val user = userResult.data ?: throw IllegalStateException("User not found")

                            // Update display name
                            val updatedUser = user.copy(displayName = newDisplayName)

                            // Add session correlation for display name updates
                            ClarityArchitectureMonitor.addSessionEvent("user_display_name_update:$currentUserId")

                            return@handleSuspendFunction updateUser(updatedUser)
                        }
                        is Result.Error -> throw userResult.exception
                        is Result.Loading -> throw IllegalStateException("getUserById returned Loading unexpectedly")
                    }
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }

    // ===== VALIDATION AND UTILITY (UserRepository Interface) =====

    override suspend fun validateUser(user: User): Result<Unit> = withContext(ioDispatcher) {
        // Execute with NORMAL priority for validation operations (important but not blocking)
        executeWithPriority(
            operation = "validateUser",
            entityType = "User",
            entityId = user.id,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithProfiling("validateUser", user.id) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "validateUser",
                    entityType = "User"
                ) {
                    val session = sessionManager.value.getCurrentSession()
                    val startTime = TimeSource.Monotonic.markNow()

                    Log.d(TAG, "validateUser: Validating user ${user.id} (session: ${session?.sessionId})")

                    // Delegate to mapper for business logic validation
                    val validationResult = userMapper.validateUser(user)

                    // Monitor validation operation with performance metrics
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "UserRepository",
                        operation = "validateUser",
                        duration = startTime.elapsedNow(),
                        success = validationResult is Result.Success,
                        cacheHit = false,
                        dataType = "User",
                        entityId = user.id,
                        userId = user.id,
                        resultCount = if (validationResult is Result.Success) 1 else 0,
                        dataSource = "validation",
                        dataSize = user.toString().length,
                        cacheStrategy = "none"
                    )

                    // Add session correlation for validation events
                    if (validationResult is Result.Success) {
                        ClarityArchitectureMonitor.addSessionEvent("user_validation_success:${user.id}")
                    } else {
                        ClarityArchitectureMonitor.addSessionEvent("user_validation_failure:${user.id}")
                    }

                    when (validationResult) {
                        is Result.Success -> {
                            Log.d(TAG, "validateUser: User ${user.id} validation passed")
                            validationResult
                        }
                        is Result.Error -> {
                            Log.w(TAG, "validateUser: User ${user.id} validation failed", validationResult.exception)
                            throw validationResult.exception
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "validateUser: Mapper returned Loading for user ${user.id}")
                            throw IllegalStateException("validateUser returned Loading unexpectedly")
                        }
                    }
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }



    override suspend fun userExistsByEmail(email: String): Result<Boolean> = withContext(ioDispatcher) {
        // Execute with LOW priority for utility operations (non-critical)
        executeWithPriority(
            operation = "userExistsByEmail",
            entityType = "User",
            entityId = email,
            priority = com.autogratuity.data.util.TaskPriority.LOW
        ) {
            executeWithProfiling("userExistsByEmail", email) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "userExistsByEmail",
                    entityType = "User"
                ) {
                    val session = sessionManager.value.getCurrentSession()
                    val startTime = TimeSource.Monotonic.markNow()

                    Log.d(TAG, "userExistsByEmail: Checking if user exists with email $email (session: ${session?.sessionId})")

                    // Input validation
                    if (email.isBlank()) {
                        throw IllegalArgumentException("Email cannot be blank for existence check")
                    }
                    if (!email.contains("@") || !email.contains(".")) {
                        throw IllegalArgumentException("Invalid email format for existence check: $email")
                    }

                    // Use RequestDeduplicationManager to prevent duplicate email checks
                    val existsResult = requestDeduplicationManager.value.deduplicateRequest(
                        key = RequestKeys.userExistsByEmail(email),
                        timeout = RequestTimeouts.QUICK_OPERATION,
                        operation = {
                            remoteDataSource.userExistsByEmail(email)
                        }
                    ) ?: throw IllegalStateException("Request deduplication timeout for email existence check")

                    // Monitor email existence check with performance metrics
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "UserRepository",
                        operation = "userExistsByEmail",
                        duration = startTime.elapsedNow(),
                        success = existsResult is Result.Success,
                        cacheHit = false,
                        dataType = "User",
                        entityId = email,
                        userId = "unknown",
                        resultCount = if (existsResult is Result.Success && existsResult.data == true) 1 else 0,
                        dataSource = "remote_query",
                        dataSize = email.length,
                        cacheStrategy = "none"
                    )

                    // Add session correlation for email check events
                    ClarityArchitectureMonitor.addSessionEvent("user_email_check:$email")

                    when (existsResult) {
                        is Result.Success -> {
                            val exists = existsResult.data
                            Log.d(TAG, "userExistsByEmail: Email $email exists: $exists")
                            existsResult
                        }
                        is Result.Error -> {
                            Log.w(TAG, "userExistsByEmail: Failed to check email existence for $email", existsResult.exception)
                            throw existsResult.exception
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "userExistsByEmail: RemoteDataSource returned Loading for email $email")
                            throw IllegalStateException("userExistsByEmail returned Loading unexpectedly")
                        }
                    }
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }

    override suspend fun createDefaultUser(userId: String, email: String?): Result<User> = withContext(ioDispatcher) {
        // Execute with HIGH priority for user creation operations (critical for onboarding)
        executeWithPriority(
            operation = "createDefaultUser",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.HIGH
        ) {
            executeWithEnhancedErrorHandling(
                operation = "createDefaultUser",
                entityId = userId,
                validateInput = {
                    // Comprehensive input validation for user creation
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank for default user creation")
                    }

                    // Validate email format if provided
                    email?.let { emailValue ->
                        if (emailValue.isBlank()) {
                            throw IllegalArgumentException("Email cannot be blank if provided")
                        }
                        // Basic email validation
                        if (!emailValue.contains("@") || !emailValue.contains(".")) {
                            throw IllegalArgumentException("Invalid email format: $emailValue")
                        }
                    }
                }
            ) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "createDefaultUser",
                    entityType = "User"
                ) {
                    val session = sessionManager.value.getCurrentSession()
                    val startTime = TimeSource.Monotonic.markNow()

                    Log.d(TAG, "createDefaultUser: Creating default user for $userId with email $email (session: ${session?.sessionId})")

                    // Use RequestDeduplicationManager to prevent duplicate user creation
                    val createResult = requestDeduplicationManager.value.deduplicateRequest(
                        key = RequestKeys.userCreateDefault(userId),
                        timeout = RequestTimeouts.COMPLEX_OPERATION,
                        operation = {
                            userMapper.createDefaultUser(userId, email)
                        }
                    ) ?: throw IllegalStateException("Request deduplication timeout for default user creation $userId")

                    // Monitor user creation operation with comprehensive metrics
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "UserRepository",
                        operation = "createDefaultUser",
                        duration = startTime.elapsedNow(),
                        success = createResult is Result.Success,
                        cacheHit = false,
                        dataType = "User",
                        entityId = userId,
                        userId = userId,
                        resultCount = if (createResult is Result.Success) 1 else 0,
                        dataSource = "user_creation",
                        dataSize = (email?.length ?: 0) + userId.length,
                        cacheStrategy = "none"
                    )

                    // Add session correlation for user creation events
                    ClarityArchitectureMonitor.addSessionEvent("user_create_default:$userId")

                    when (createResult) {
                        is Result.Success -> {
                            val defaultUser = createResult.data
                            Log.i(TAG, "createDefaultUser: Default user created successfully for $userId")

                            // ✅ ENHANCED: Proactively cache the newly created user
                            applicationScope.launch(ioDispatcher) {
                                try {
                                    Log.d(TAG, "createDefaultUser: Caching newly created default user $userId")
                                    localDataSource.saveUser(userId, defaultUser)
                                } catch (e: Exception) {
                                    Log.w(TAG, "createDefaultUser: Failed to cache default user $userId", e)
                                }
                            }

                            createResult
                        }
                        is Result.Error -> {
                            Log.e(TAG, "createDefaultUser: Failed to create default user for $userId", createResult.exception)
                            throw createResult.exception
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "createDefaultUser: Mapper returned Loading for user $userId")
                            throw IllegalStateException("createDefaultUser returned Loading unexpectedly")
                        }
                    }
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }

    // ===== AUTHENTICATION INTEGRATION (UserRepository Interface) =====

    override suspend fun handleUserSignIn(userId: String, authProvider: String): Result<Unit> = withContext(ioDispatcher) {
        // Execute with CRITICAL priority for authentication operations (highest priority)
        executeWithPriority(
            operation = "handleUserSignIn",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.CRITICAL
        ) {
            executeWithEnhancedErrorHandling(
                operation = "handleUserSignIn",
                entityId = userId,
                validateInput = {
                    // Comprehensive input validation for authentication
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank for sign in")
                    }
                    if (authProvider.isBlank()) {
                        throw IllegalArgumentException("Auth provider cannot be blank for sign in")
                    }

                    // Validate auth provider format
                    val validProviders = setOf("google", "facebook", "apple", "email", "phone", "anonymous")
                    if (authProvider.lowercase() !in validProviders) {
                        Log.w(TAG, "handleUserSignIn: Unknown auth provider '$authProvider' for user $userId")
                        // Don't fail - just log warning for monitoring
                    }
                }
            ) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "handleUserSignIn",
                    entityType = "User"
                ) {
                    val session = sessionManager.value.getCurrentSession()
                    val startTime = TimeSource.Monotonic.markNow()

                    Log.d(TAG, "handleUserSignIn: Processing sign-in for user $userId with provider $authProvider (session: ${session?.sessionId})")

                    // Use RequestDeduplicationManager to prevent duplicate sign-in operations
                    val signInResult = requestDeduplicationManager.value.deduplicateRequest(
                        key = "user_signin:$userId:$authProvider",
                        timeout = 10.seconds,
                        operation = {
                            userMapper.handleUserSignIn(userId, authProvider)
                        }
                    ) ?: throw IllegalStateException("Request deduplication timeout for user sign-in $userId")

                    // Monitor authentication operation with comprehensive metrics
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "UserRepository",
                        operation = "handleUserSignIn",
                        duration = startTime.elapsedNow(),
                        success = signInResult is Result.Success,
                        cacheHit = false,
                        dataType = "User",
                        entityId = userId,
                        userId = userId,
                        resultCount = if (signInResult is Result.Success) 1 else 0,
                        dataSource = "authentication",
                        dataSize = authProvider.length,
                        cacheStrategy = "none"
                    )

                    // Add session correlation for authentication events
                    ClarityArchitectureMonitor.addSessionEvent("user_signin:$userId:$authProvider")
                    if (signInResult is Result.Success) {
                        Log.d(TAG, "handleUserSignIn: User signed in successfully, cache warming handled by infrastructure layer")
                    }

                    when (signInResult) {
                        is Result.Success -> {
                            Log.i(TAG, "handleUserSignIn: User $userId signed in successfully with $authProvider")
                            signInResult
                        }
                        is Result.Error -> {
                            Log.e(TAG, "handleUserSignIn: Sign-in failed for user $userId with $authProvider", signInResult.exception)
                            throw signInResult.exception
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "handleUserSignIn: Mapper returned Loading for user $userId")
                            throw IllegalStateException("handleUserSignIn returned Loading unexpectedly")
                        }
                    }
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }

    override suspend fun handleUserSignOut(userId: String): Result<Unit> = withContext(ioDispatcher) {
        // Execute with HIGH priority for user sign-out operations (critical for security)
        executeWithPriority(
            operation = "handleUserSignOut",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.HIGH
        ) {
            executeWithEnhancedErrorHandling(
                operation = "handleUserSignOut",
                entityId = userId,
                validateInput = {
                    // Input validation for sign-out
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank for sign out")
                    }
                }
            ) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "handleUserSignOut",
                    entityType = "User"
                ) {
                    val session = sessionManager.value.getCurrentSession()
                    val startTime = TimeSource.Monotonic.markNow()

                    Log.d(TAG, "handleUserSignOut: Processing sign-out for user $userId (session: ${session?.sessionId})")

                    // Use RequestDeduplicationManager to prevent duplicate sign-out operations
                    val signOutResult = requestDeduplicationManager.value.deduplicateRequest(
                        key = "user_signout:$userId",
                        timeout = 10.seconds,
                        operation = {
                            // ✅ ENHANCED SECURITY: Clear ALL user caches on sign-out for comprehensive security
                            Log.d(TAG, "handleUserSignOut: Performing comprehensive cache cleanup for security")
                            val clearAllResult = clearAllCache()
                            if (clearAllResult is Result.Error) {
                                Log.w(TAG, "handleUserSignOut: Failed to clear all caches during sign-out", clearAllResult.exception)
                                // Still continue with sign-out even if cache clearing fails
                            } else {
                                Log.d(TAG, "handleUserSignOut: All user caches cleared successfully for security")
                            }

                            // Delegate to mapper for business logic
                            userMapper.handleUserSignOut(userId)
                        }
                    ) ?: throw IllegalStateException("Request deduplication timeout for user sign-out $userId")

                    // Monitor sign-out operation with comprehensive metrics
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = "UserRepository",
                        operation = "handleUserSignOut",
                        duration = startTime.elapsedNow(),
                        success = signOutResult is Result.Success,
                        cacheHit = false,
                        dataType = "User",
                        entityId = userId,
                        userId = userId,
                        resultCount = if (signOutResult is Result.Success) 1 else 0,
                        dataSource = "authentication",
                        dataSize = userId.length,
                        cacheStrategy = "cache_clear"
                    )

                    // Add session correlation for sign-out events
                    ClarityArchitectureMonitor.addSessionEvent("user_signout:$userId")

                    when (signOutResult) {
                        is Result.Success -> {
                            Log.i(TAG, "handleUserSignOut: User $userId signed out successfully")
                            signOutResult
                        }
                        is Result.Error -> {
                            Log.e(TAG, "handleUserSignOut: Sign-out failed for user $userId", signOutResult.exception)
                            throw signOutResult.exception
                        }
                        is Result.Loading -> {
                            Log.w(TAG, "handleUserSignOut: Mapper returned Loading for user $userId")
                            throw IllegalStateException("handleUserSignOut returned Loading unexpectedly")
                        }
                    }
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }

    // ===== INTERNAL CACHE MANAGEMENT METHODS =====
    // These are internal repository orchestration methods, not part of domain interface

    override suspend fun clearCache(userId: String): Result<Unit> = withContext(ioDispatcher) {
        // Execute with LOW priority for internal cache operations
        executeWithPriority(
            operation = "clearCache",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.LOW
        ) {
            executeWithProfiling("clearCache", userId) {
                val session = sessionManager.value.getCurrentSession()

                Log.d(TAG, "clearCache: Clearing cache for user $userId (session: ${session?.sessionId})")

                // Add session correlation for cache clearing
                ClarityArchitectureMonitor.addSessionEvent("user_cache_clear:$userId")

                localDataSource.deleteUserById(userId, userId)
            }
        }
    }

    /**
     * Internal utility for comprehensive cache clearing during security-critical operations.
     * Used in handleUserSignOut() for enhanced security - clears ALL user caches when any user signs out.
     * This follows the principle of "defense in depth" for authentication security.
     */
    override suspend fun clearAllCache(): Result<Unit> = withContext(ioDispatcher) {
        // Execute with LOW priority for global cache operations
        executeWithPriority(
            operation = "clearAllCache",
            entityType = "User",
            entityId = "all_users",
            priority = com.autogratuity.data.util.TaskPriority.LOW
        ) {
            executeWithProfiling("clearAllCache", "all_users") {
                val session = sessionManager.value.getCurrentSession()

                Log.d(TAG, "clearAllCache: Clearing all user caches (session: ${session?.sessionId})")

                // Add session correlation for global cache clearing
                ClarityArchitectureMonitor.addSessionEvent("user_cache_clear_all")

                localDataSource.clearAllCaches()
            }
        }
    }

    override suspend fun invalidateCache(userId: String): Result<Unit> = withContext(ioDispatcher) {
        // Execute with LOW priority for cache operations (utility function)
        executeWithPriority(
            operation = "invalidateCache",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.LOW
        ) {
            executeWithProfiling("invalidateCache", userId) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "invalidateCache",
                    entityType = "User"
                ) {
                    val session = sessionManager.value.getCurrentSession()

                    Log.d(TAG, "invalidateCache: Invalidating cache for user $userId (session: ${session?.sessionId})")

                    // Add session correlation for cache invalidation
                    ClarityArchitectureMonitor.addSessionEvent("user_cache_invalidate:$userId")

                    localDataSource.deleteUserById(userId, userId)
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }

    override suspend fun refreshFromRemote(userId: String): Result<Unit> = withContext(ioDispatcher) {
        // Execute with NORMAL priority for refresh operations (important for data consistency)
        executeWithPriority(
            operation = "refreshFromRemote",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithProfiling("refreshFromRemote", userId) {
                // Enhanced error handling with RepositoryErrorHandler
                val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
                    operationName = "refreshFromRemote",
                    entityType = "User"
                ) {
                    val session = sessionManager.value.getCurrentSession()

                    Log.d(TAG, "refreshFromRemote: Refreshing user $userId from remote (session: ${session?.sessionId})")

                    // Add session correlation for remote refresh
                    ClarityArchitectureMonitor.addSessionEvent("user_refresh_from_remote:$userId")

                    // Clear local cache first, then fetch from remote
                    val clearResult = localDataSource.deleteUserById(userId, userId)
                    if (clearResult is Result.Error) {
                        Log.w(TAG, "refreshFromRemote: Failed to clear cache for user $userId", clearResult.exception)
                    }

                    // Fetch from remote will automatically cache the result (uses reinforced getUserById)
                    getUserById(userId).let { result ->
                        when (result) {
                            is Result.Success -> Result.Success(Unit)
                            is Result.Error -> throw result.exception
                            is Result.Loading -> throw IllegalStateException("getUserById returned Loading unexpectedly")
                        }
                    }
                }

                // Convert kotlin.Result to com.autogratuity.data.model.Result
                when {
                    handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
                    else -> {
                        val throwable = handlerResult.exceptionOrNull()
                        val exception = when (throwable) {
                            is Exception -> throwable
                            else -> Exception("Unknown error", throwable)
                        }
                        Result.Error(exception)
                    }
                }
            }
        }
    }

    // ===== DATA INTERFACE METHODS (UserProfileRepository) =====
    // These methods implement the comprehensive data layer interface

    override suspend fun updateUserProfile(userId: String, updates: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "updateUserProfile",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.HIGH
        ) {
            executeWithEnhancedErrorHandling(
                operation = "updateUserProfile",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                    if (updates.isEmpty()) {
                        throw IllegalArgumentException("Updates cannot be empty")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot update profile for different user")
                }

                // Get current user, apply updates, and save
                val currentUserResult = getUserById(userId)
                when (currentUserResult) {
                    is Result.Success -> {
                        val currentUser = currentUserResult.data
                            ?: throw IllegalStateException("User not found for profile update")

                        // Apply updates to user (this would need proper field mapping)
                        // For now, delegate to updateUser with current user
                        updateUser(currentUser)
                    }
                    is Result.Error -> throw currentUserResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                }
            }
        }
    }



    override suspend fun incrementUsageStat(userId: String, statName: String, incrementBy: Long): Result<Unit> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "incrementUsageStat",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithEnhancedErrorHandling(
                operation = "incrementUsageStat",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                    if (statName.isBlank()) {
                        throw IllegalArgumentException("Stat name cannot be blank")
                    }
                    if (incrementBy <= 0) {
                        throw IllegalArgumentException("Increment value must be positive")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot update usage stats for different user")
                }

                // Delegate to UserMapper for business logic (follows Clarity Architecture)
                val incrementResult = userMapper.incrementUsageStat(userId, statName, incrementBy)
                when (incrementResult) {
                    is Result.Success -> incrementResult
                    is Result.Error -> throw incrementResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state from mapper")
                }
            }
        }
    }

    override suspend fun performInitialSetupIfNeeded(userId: String): Result<Unit> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "performInitialSetupIfNeeded",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.HIGH
        ) {
            executeWithEnhancedErrorHandling(
                operation = "performInitialSetupIfNeeded",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot perform setup for different user")
                }

                // Delegate to UserMapper for business logic (follows Clarity Architecture)
                val setupResult = userMapper.performInitialSetupIfNeeded(userId)
                when (setupResult) {
                    is Result.Success -> setupResult
                    is Result.Error -> throw setupResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state from mapper")
                }
            }
        }
    }

    override suspend fun normalizeUser(user: User): User {
        // Delegate to UserMapper for business logic (follows Clarity Architecture)
        return userMapper.normalizeUser(user)
    }

    override suspend fun getUserStats(userId: String): Result<UserUsageStats?> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "getUserStats",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithEnhancedErrorHandling(
                operation = "getUserStats",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot get stats for different user")
                }

                // Get user and delegate to UserMapper for business logic
                val userResult = getUserById(userId)
                when (userResult) {
                    is Result.Success -> {
                        val user = userResult.data
                        if (user != null) {
                            // Delegate to UserMapper for business logic (follows Clarity Architecture)
                            Result.Success(userMapper.getUserStats(user))
                        } else {
                            Result.Success(null)
                        }
                    }
                    is Result.Error -> throw userResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                }
            }
        }
    }

    override fun observeUserSubscription(userId: String): Flow<Result<UserSubscription?>> {
        return flow {
            try {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    emit(Result.Error(IllegalArgumentException("Cannot observe subscription for different user")))
                    return@flow
                }

                observeUserById(userId).collect { userResult ->
                    when (userResult) {
                        is Result.Success -> {
                            val subscription = userResult.data?.subscription
                            emit(Result.Success(subscription))
                        }
                        is Result.Error -> emit(Result.Error(userResult.exception))
                        is Result.Loading -> emit(Result.Loading)
                    }
                }
            } catch (e: Exception) {
                emit(Result.Error(e))
            }
        }.flowOn(ioDispatcher)
    }

    override suspend fun getAccountCreationDate(userId: String): Result<OffsetDateTime?> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "getAccountCreationDate",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.LOW
        ) {
            executeWithEnhancedErrorHandling(
                operation = "getAccountCreationDate",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot get account creation date for different user")
                }

                // Get user and delegate to UserMapper for business logic
                val userResult = getUserById(userId)
                when (userResult) {
                    is Result.Success -> {
                        val user = userResult.data
                        if (user != null) {
                            // Delegate to UserMapper for business logic (follows Clarity Architecture)
                            Result.Success(userMapper.getAccountCreationDate(user))
                        } else {
                            Result.Success(null)
                        }
                    }
                    is Result.Error -> throw userResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                }
            }
        }
    }

    override suspend fun prefetchUserData(userId: String): Result<Unit> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "prefetchUserData",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithEnhancedErrorHandling(
                operation = "prefetchUserData",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot prefetch data for different user")
                }
                Result.Success(Unit)
            }
        }
    }



    override suspend fun initialize(): Result<Unit> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "initialize",
            entityType = "User",
            entityId = "repository",
            priority = com.autogratuity.data.util.TaskPriority.HIGH
        ) {
            executeWithEnhancedErrorHandling(
                operation = "initialize",
                entityId = "repository"
            ) {
                Log.i(TAG, "initialize: Initializing UserProfileRepository")
                // Initialize any required components
                Result.Success(Unit)
            }
        }
    }

    override suspend fun cleanup(): Result<Unit> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "cleanup",
            entityType = "User",
            entityId = "repository",
            priority = com.autogratuity.data.util.TaskPriority.LOW
        ) {
            executeWithEnhancedErrorHandling(
                operation = "cleanup",
                entityId = "repository"
            ) {
                Log.i(TAG, "cleanup: Cleaning up UserProfileRepository")
                // Clear all caches and cleanup resources
                clearAllCache()
            }
        }
    }

    override suspend fun syncWithAuthProfile(userId: String): Result<Unit> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "syncWithAuthProfile",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.HIGH
        ) {
            executeWithEnhancedErrorHandling(
                operation = "syncWithAuthProfile",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot sync auth profile for different user")
                }

                // Delegate to UserMapper for business logic (follows Clarity Architecture)
                val syncResult = userMapper.syncWithAuthProfile(userId)
                when (syncResult) {
                    is Result.Success -> syncResult
                    is Result.Error -> throw syncResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state from mapper")
                }
            }
        }
    }

    override suspend fun exportUserData(userId: String, format: String): Result<String> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "exportUserData",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.LOW
        ) {
            executeWithEnhancedErrorHandling(
                operation = "exportUserData",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                    if (format.isBlank()) {
                        throw IllegalArgumentException("Export format cannot be blank")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot export data for different user")
                }

                // Get user data and delegate to UserMapper for business logic
                val userResult = getUserById(userId)
                when (userResult) {
                    is Result.Success -> {
                        val user = userResult.data
                            ?: throw IllegalStateException("User not found for export")

                        // Delegate to UserMapper for business logic (follows Clarity Architecture)
                        val exportResult = userMapper.exportUserData(user, format)
                        when (exportResult) {
                            is Result.Success -> exportResult
                            is Result.Error -> throw exportResult.exception
                            is Result.Loading -> throw IllegalStateException("Unexpected Loading state from mapper")
                        }
                    }
                    is Result.Error -> throw userResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                }
            }
        }
    }

    override suspend fun importUserData(userId: String, data: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "importUserData",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithEnhancedErrorHandling(
                operation = "importUserData",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                    if (data.isEmpty()) {
                        throw IllegalArgumentException("Import data cannot be empty")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot import data for different user")
                }

                // Delegate to UserMapper for business logic (follows Clarity Architecture)
                val importResult = userMapper.importUserData(userId, data)
                when (importResult) {
                    is Result.Success -> {
                        val importedUser = importResult.data
                        // Update the user with imported data
                        updateUser(importedUser)
                    }
                    is Result.Error -> throw importResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state from mapper")
                }
            }
        }
    }

    override suspend fun createUserBackup(userId: String): Result<Map<String, Any>> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "createUserBackup",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.LOW
        ) {
            executeWithEnhancedErrorHandling(
                operation = "createUserBackup",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot create backup for different user")
                }

                // Get user data and delegate to UserMapper for business logic
                val userResult = getUserById(userId)
                when (userResult) {
                    is Result.Success -> {
                        val user = userResult.data
                            ?: throw IllegalStateException("User not found for backup")

                        // Delegate to UserMapper for business logic (follows Clarity Architecture)
                        val backupResult = userMapper.createUserBackup(user)
                        when (backupResult) {
                            is Result.Success -> backupResult
                            is Result.Error -> throw backupResult.exception
                            is Result.Loading -> throw IllegalStateException("Unexpected Loading state from mapper")
                        }
                    }
                    is Result.Error -> throw userResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                }
            }
        }
    }

    override suspend fun restoreUserBackup(userId: String, backup: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "restoreUserBackup",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithEnhancedErrorHandling(
                operation = "restoreUserBackup",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                    if (backup.isEmpty()) {
                        throw IllegalArgumentException("Backup data cannot be empty")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot restore backup for different user")
                }

                // Delegate to UserMapper for business logic (follows Clarity Architecture)
                val restoreResult = userMapper.restoreUserBackup(userId, backup)
                when (restoreResult) {
                    is Result.Success -> {
                        val restoredUser = restoreResult.data
                        // Update the user with restored data
                        updateUser(restoredUser)
                    }
                    is Result.Error -> throw restoreResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state from mapper")
                }
            }
        }
    }

    override suspend fun migrateUserData(userId: String, fromVersion: Long, toVersion: Long): Result<Unit> = withContext(ioDispatcher) {
        executeWithPriority(
            operation = "migrateUserData",
            entityType = "User",
            entityId = userId,
            priority = com.autogratuity.data.util.TaskPriority.NORMAL
        ) {
            executeWithEnhancedErrorHandling(
                operation = "migrateUserData",
                entityId = userId,
                validateInput = {
                    if (userId.isBlank()) {
                        throw IllegalArgumentException("User ID cannot be blank")
                    }
                    if (fromVersion < 0 || toVersion < 0) {
                        throw IllegalArgumentException("Version numbers must be non-negative")
                    }
                    if (fromVersion >= toVersion) {
                        throw IllegalArgumentException("Target version must be greater than source version")
                    }
                }
            ) {
                val currentUserId = getCurrentUserIdSuspend()
                if (userId != currentUserId) {
                    throw IllegalArgumentException("Cannot migrate data for different user")
                }

                // Get user data and delegate to UserMapper for business logic
                val userResult = getUserById(userId)
                when (userResult) {
                    is Result.Success -> {
                        val user = userResult.data
                            ?: throw IllegalStateException("User not found for migration")

                        // Delegate to UserMapper for business logic (follows Clarity Architecture)
                        val migrationResult = userMapper.migrateUserData(user, fromVersion, toVersion)
                        when (migrationResult) {
                            is Result.Success -> {
                                val migratedUser = migrationResult.data
                                // Update the user with migrated data
                                updateUser(migratedUser)
                            }
                            is Result.Error -> throw migrationResult.exception
                            is Result.Loading -> throw IllegalStateException("Unexpected Loading state from mapper")
                        }
                    }
                    is Result.Error -> throw userResult.exception
                    is Result.Loading -> throw IllegalStateException("Unexpected Loading state")
                }
            }
        }
    }


}