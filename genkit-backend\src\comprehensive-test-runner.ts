import { initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import * as admin from 'firebase-admin';

// Import our test classes
import { CloudFunctionTester, TestResult } from './test-cloud-functions';
import { HttpCallableTester, HttpTestResult } from './test-http-callables';

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
    initializeApp();
}

interface TriggerTestResult {
    triggerName: string;
    testName: string;
    success: boolean;
    result?: any;
    error?: string;
    duration: number;
}

class FirestoreTriggerSimulator {
    private results: TriggerTestResult[] = [];
    private firestore = getFirestore();

    async runTriggerSimulations(): Promise<TriggerTestResult[]> {
        console.log('🔥 Starting Firestore Trigger Simulations...\n');

        await this.simulateDeliveryWrittenTriggers();
        await this.simulateUserPreferencesChangeTrigger();
        await this.simulateGcsFileTrigger();

        this.printTriggerSummary();
        return this.results;
    }

    private async simulateDeliveryWrittenTriggers(): Promise<void> {
        console.log('📦 Simulating Delivery Written Triggers...');

        const testUserId = 'test-user-123';
        const testDeliveryId = `test-delivery-${Date.now()}`;
        const testAddressId = 'test-address-456';

        // Create test delivery document
        const deliveryData = {
            deliveryData: {
                userId: testUserId,
                orderId: 'test-order-789',
                addressId: testAddressId,
                reference: {
                    addressId: testAddressId
                },
                status: {
                    isCompleted: false,
                    isTipped: false
                },
                amounts: {
                    tipAmount: null, // Pending tip
                    basePay: 10.00
                },
                platform: 'shipt',
                timestamp: new Date().toISOString()
            }
        };

        // Test 1: Create delivery (should trigger both pending tips and address stats)
        await this.runTriggerTest(
            'onDeliveryWrittenCheckPendingTips',
            'Create Delivery with Pending Tip',
            async () => {
                const docRef = this.firestore
                    .collection('users')
                    .doc(testUserId)
                    .collection('user_deliveries')
                    .doc(testDeliveryId);

                await docRef.set(deliveryData);
                
                // Wait a moment for triggers to process
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Verify document was created
                const doc = await docRef.get();
                return { created: doc.exists, data: doc.data() };
            }
        );

        // Test 2: Update delivery with tip (should trigger address stats but not pending tips)
        await this.runTriggerTest(
            'onDeliveryWrittenUpdateAddressStats',
            'Update Delivery with Tip Amount',
            async () => {
                const updatedData = {
                    ...deliveryData,
                    deliveryData: {
                        ...deliveryData.deliveryData,
                        status: {
                            isCompleted: true,
                            isTipped: true
                        },
                        amounts: {
                            ...deliveryData.deliveryData.amounts,
                            tipAmount: 5.50
                        }
                    }
                };

                const docRef = this.firestore
                    .collection('users')
                    .doc(testUserId)
                    .collection('user_deliveries')
                    .doc(testDeliveryId);

                await docRef.update(updatedData);
                
                // Wait for triggers
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const doc = await docRef.get();
                return { updated: doc.exists, data: doc.data() };
            }
        );

        // Test 3: Delete delivery (should trigger address stats)
        await this.runTriggerTest(
            'onDeliveryWrittenUpdateAddressStats',
            'Delete Delivery Document',
            async () => {
                const docRef = this.firestore
                    .collection('users')
                    .doc(testUserId)
                    .collection('user_deliveries')
                    .doc(testDeliveryId);

                await docRef.delete();
                
                // Wait for triggers
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const doc = await docRef.get();
                return { deleted: !doc.exists };
            }
        );
    }

    private async simulateUserPreferencesChangeTrigger(): Promise<void> {
        console.log('⚙️ Simulating User Preferences Change Trigger...');

        const testUserId = 'test-user-123';
        const testDocId = `test-prefs-${Date.now()}`;

        await this.runTriggerTest(
            'onUserPreferencesChange',
            'Update User Preferences',
            async () => {
                const preferencesData = {
                    notifications: {
                        tipNotifications: true,
                        emailNotifications: false
                    },
                    dndSettings: {
                        enableAutoDnd: true,
                        threshold: 0.50
                    },
                    lastUpdated: new Date().toISOString()
                };

                const docRef = this.firestore
                    .collection('users')
                    .doc(testUserId)
                    .collection('preferences')
                    .doc(testDocId);

                await docRef.set(preferencesData);
                
                // Wait for trigger
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Clean up
                await docRef.delete();
                
                return { preferencesUpdated: true, data: preferencesData };
            }
        );
    }

    private async simulateGcsFileTrigger(): Promise<void> {
        console.log('📁 Simulating GCS File Upload Trigger...');

        // Note: This simulates the trigger logic without actually uploading to GCS
        await this.runTriggerTest(
            'onGeoJsonFileFinalized',
            'Simulate GeoJSON File Upload',
            async () => {
                // This would normally be triggered by GCS, but we can test the flow logic
                const mockGcsEvent = {
                    bucket: 'autogratuity-me.firebasestorage.app',
                    file: 'user-uploads/test-user-123/test-geojson.json'
                };

                // The actual trigger would call the flow, but we're just simulating the event structure
                return { 
                    simulatedEvent: mockGcsEvent,
                    note: 'GCS trigger simulation - actual file processing would require real GCS upload'
                };
            }
        );
    }

    private async runTriggerTest(
        triggerName: string,
        testName: string,
        testFunction: () => Promise<any>
    ): Promise<void> {
        const startTime = Date.now();
        
        try {
            console.log(`  ⏳ Running: ${testName}...`);
            const result = await testFunction();
            const duration = Date.now() - startTime;
            
            this.results.push({
                triggerName,
                testName,
                success: true,
                result,
                duration
            });
            
            console.log(`  ✅ ${testName} - Success (${duration}ms)`);
        } catch (error: any) {
            const duration = Date.now() - startTime;
            
            this.results.push({
                triggerName,
                testName,
                success: false,
                error: error.message,
                duration
            });
            
            console.log(`  ❌ ${testName} - Failed: ${error.message} (${duration}ms)`);
        }
        
        console.log(''); // Add spacing
    }

    private printTriggerSummary(): void {
        console.log('📋 Firestore Trigger Test Summary:');
        console.log('==================================');
        
        const successful = this.results.filter(r => r.success).length;
        const failed = this.results.filter(r => !r.success).length;
        const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
        
        console.log(`Total Trigger Tests: ${this.results.length}`);
        console.log(`Successful: ${successful}`);
        console.log(`Failed: ${failed}`);
        console.log(`Total Duration: ${totalDuration}ms`);
        console.log('');
    }
}

class ComprehensiveTestRunner {
    async runAllTests(): Promise<void> {
        console.log('🚀 Starting Comprehensive Cloud Function Testing Suite');
        console.log('=====================================================\n');

        const startTime = Date.now();
        
        try {
            // 1. Test individual flows
            console.log('Phase 1: Testing Individual Flows');
            console.log('----------------------------------');
            const flowTester = new CloudFunctionTester();
            const flowResults = await flowTester.runAllTests();
            
            console.log('\n' + '='.repeat(50) + '\n');
            
            // 2. Test HTTP callable functions
            console.log('Phase 2: Testing HTTP Callable Functions');
            console.log('----------------------------------------');
            const httpTester = new HttpCallableTester();
            const httpResults = await httpTester.runAllHttpTests();
            
            console.log('\n' + '='.repeat(50) + '\n');
            
            // 3. Test Firestore triggers
            console.log('Phase 3: Testing Firestore Triggers');
            console.log('-----------------------------------');
            const triggerSimulator = new FirestoreTriggerSimulator();
            const triggerResults = await triggerSimulator.runTriggerSimulations();
            
            console.log('\n' + '='.repeat(50) + '\n');
            
            // 4. Overall summary
            this.printOverallSummary(flowResults, httpResults, triggerResults, startTime);
            
        } catch (error: any) {
            console.error('❌ Test suite failed:', error.message);
            process.exit(1);
        }
    }

    private printOverallSummary(
        flowResults: TestResult[],
        httpResults: HttpTestResult[],
        triggerResults: TriggerTestResult[],
        startTime: number
    ): void {
        const totalDuration = Date.now() - startTime;
        
        console.log('🎯 OVERALL TEST SUITE SUMMARY');
        console.log('=============================');
        
        const flowSuccess = flowResults.filter(r => r.success).length;
        const httpSuccess = httpResults.filter(r => r.success).length;
        const triggerSuccess = triggerResults.filter(r => r.success).length;
        
        const totalTests = flowResults.length + httpResults.length + triggerResults.length;
        const totalSuccess = flowSuccess + httpSuccess + triggerSuccess;
        const totalFailed = totalTests - totalSuccess;
        
        console.log(`📊 Test Categories:`);
        console.log(`   Flow Tests: ${flowSuccess}/${flowResults.length} passed`);
        console.log(`   HTTP Tests: ${httpSuccess}/${httpResults.length} passed`);
        console.log(`   Trigger Tests: ${triggerSuccess}/${triggerResults.length} passed`);
        console.log('');
        console.log(`📈 Overall Results:`);
        console.log(`   Total Tests: ${totalTests}`);
        console.log(`   Successful: ${totalSuccess}`);
        console.log(`   Failed: ${totalFailed}`);
        console.log(`   Success Rate: ${((totalSuccess / totalTests) * 100).toFixed(1)}%`);
        console.log(`   Total Duration: ${(totalDuration / 1000).toFixed(2)}s`);
        
        if (totalFailed === 0) {
            console.log('\n🎉 All tests passed! Your cloud functions are working correctly.');
        } else {
            console.log(`\n⚠️ ${totalFailed} test(s) failed. Review the detailed results above.`);
        }
        
        console.log('\n📝 Next Steps:');
        console.log('   1. Review any failed tests and fix issues');
        console.log('   2. Deploy functions: npm run build && firebase deploy --only functions');
        console.log('   3. Test in production with real data');
        console.log('   4. Monitor cloud function logs for any runtime issues');
    }
}

// Main execution function
async function main() {
    const runner = new ComprehensiveTestRunner();
    await runner.runAllTests();
}

// Export for use in other scripts
export { ComprehensiveTestRunner, FirestoreTriggerSimulator };

// Run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}
