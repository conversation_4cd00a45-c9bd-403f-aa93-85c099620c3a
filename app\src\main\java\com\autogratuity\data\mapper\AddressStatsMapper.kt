package com.autogratuity.data.mapper

import android.util.Log
import com.autogratuity.domain.model.AddressStats
import com.autogratuity.data.model.generated_kt.Delivery_stats
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.util_kt.parseUniversalTimestamp
import com.autogratuity.debug.ClarityArchitectureMonitor
import kotlinx.coroutines.ExperimentalCoroutinesApi
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter
import kotlin.time.TimeSource

/**
 * ✅ MAPPER: AddressStats ↔ DTO conversion following Clarity Architecture
 * 
 * Responsibilities:
 * - Convert cloud-calculated Delivery_stats DTO to AddressStats SSoT domain model
 * - Handle timestamp parsing and conversion
 * - Provide business logic for address-level statistics
 * - No encryption needed (statistics are not PII)
 * 
 * Following Clarity.md principles:
 * - Mappers contain DTO↔SSoT conversion + business logic
 * - Handle all data transformation between layers
 * - Provide robust error handling with Result<T>
 */
@ExperimentalCoroutinesApi
class AddressStatsMapper {
    
    companion object {
        private const val TAG = "AddressStatsMapper"
    }
    
    /**
     * Map cloud-calculated Delivery_stats DTO to AddressStats SSoT domain model
     * 
     * @param deliveryStatsDto The cloud-calculated statistics from Firestore
     * @return Result containing AddressStats domain model or error
     */
    fun mapToDomain(deliveryStatsDto: Delivery_stats?): Result<AddressStats> {
        val mappingStartTime = TimeSource.Monotonic.markNow()
        return try {
            if (deliveryStatsDto == null) {
                Log.d(TAG, "Delivery_stats DTO is null, returning empty AddressStats")
                
                // Monitor null DTO mapping
                ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                    mapperClass = "AddressStatsMapper",
                    entityType = "AddressStats",
                    duration = mappingStartTime.elapsedNow(),
                    success = true,
                    fieldsTransformed = 0,
                    businessLogicApplied = listOf("null_handling"),
                    cacheUpdated = false,
                    entityId = "null",
                    dataSize = 0
                )
                
                return Result.Success(AddressStats.empty())
            }
            
            // 🔧 ENHANCED: Validate cloud function data and provide detailed logging
            val deliveryCount = deliveryStatsDto.deliveryCount ?: 0L
            val tipCount = deliveryStatsDto.tipCount ?: 0L
            val totalTips = deliveryStatsDto.totalTips ?: 0.0
            val averageTipAmount = deliveryStatsDto.averageTipAmount ?: 0.0

            // 🔍 CLOUD FUNCTION VALIDATION: Check for potential issues
            if (deliveryCount > 0 && tipCount == 0L && totalTips == 0.0) {
                Log.w(TAG, "⚠️ CLOUD FUNCTION WARNING: Address has deliveries ($deliveryCount) but no tips - possible cloud function processing issue")
            }

            // ✅ FIXED: Cloud function always provides non-null values for core stats
            val addressStats = AddressStats(
                deliveryCount = deliveryCount,
                tipCount = tipCount,
                totalTips = totalTips,
                averageTipAmount = averageTipAmount, // Cloud calculates: tipCount > 0 ? totalTips / tipCount : 0
                highestTip = deliveryStatsDto.highestTip ?: 0.0,
                pendingCount = deliveryStatsDto.pendingCount ?: 0L, // Not calculated by cloud function
                averageTimeMinutes = deliveryStatsDto.averageTimeMinutes ?: 0.0, // Not calculated by cloud function
                lastDeliveryTimestamp = parseTimestamp(deliveryStatsDto.lastDeliveryTimestamp?.toString())
            )
            
            Log.d(TAG, "✅ CLOUD STATS MAPPED: deliveries=${addressStats.deliveryCount}, tips=${addressStats.tipCount}, total=\$${addressStats.totalTips}, avg=\$${addressStats.averageTipAmount}")
            
            // 🔍 SESSION CORRELATION: Add mapping success to session
            ClarityArchitectureMonitor.addSessionEvent("address_stats_mapping_success")
            
            // Monitor successful mapping
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "AddressStatsMapper",
                entityType = "AddressStats",
                duration = mappingStartTime.elapsedNow(),
                success = true,
                fieldsTransformed = 8, // deliveryCount, tipCount, totalTips, etc.
                businessLogicApplied = listOf("timestamp_parsing", "stats_calculation"),
                cacheUpdated = false,
                entityId = deliveryStatsDto.toString(),
                dataSize = deliveryStatsDto.toString().length
            )
            
            Result.Success(addressStats)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error mapping Delivery_stats DTO to AddressStats domain model", e)
            
            // 🔍 SESSION CORRELATION: Add mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("address_stats_mapping_failure:exception")
            
            // Monitor mapping error
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "AddressStatsMapper",
                entityType = "AddressStats",
                duration = mappingStartTime.elapsedNow(),
                success = false,
                error = e,
                fieldsTransformed = 0,
                businessLogicApplied = listOf("mapping_failed"),
                cacheUpdated = false,
                entityId = "error",
                dataSize = deliveryStatsDto?.toString()?.length ?: 0
            )
            
            Result.Error(e)
        }
    }
    
    /**
     * Map AddressStats SSoT domain model to Delivery_stats DTO
     * Note: This is primarily for completeness - address stats are typically read-only from cloud
     * 
     * @param addressStats The SSoT AddressStats domain model
     * @return Result containing Delivery_stats DTO or error
     */
    fun mapToDto(addressStats: AddressStats): Result<Delivery_stats> {
        return try {
            val deliveryStatsDto = Delivery_stats(
                deliveryCount = addressStats.deliveryCount,
                tipCount = addressStats.tipCount,
                totalTips = addressStats.totalTips,
                averageTipAmount = addressStats.averageTipAmount,
                highestTip = addressStats.highestTip,
                pendingCount = addressStats.pendingCount,
                averageTimeMinutes = addressStats.averageTimeMinutes,
                lastDeliveryTimestamp = addressStats.lastDeliveryTimestamp
            )
            
            Log.d(TAG, "Successfully mapped AddressStats domain model to Delivery_stats DTO")
            Result.Success(deliveryStatsDto)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error mapping AddressStats domain model to Delivery_stats DTO", e)
            Result.Error(e)
        }
    }
    
    /**
     * ✅ STANDARDIZED: Parse timestamp using universal timestamp parsing function
     * Uses parseUniversalTimestamp for consistent timestamp handling across the entire codebase
     */
    private fun parseTimestamp(timestampString: String?): OffsetDateTime? {
        return parseUniversalTimestamp(timestampString)
    }
    
    /**
     * Format OffsetDateTime from domain model to string for DTO
     */
    private fun formatTimestamp(timestamp: OffsetDateTime?): String? {
        return timestamp?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    }
    
    // ===== BUSINESS LOGIC METHODS =====
    // Following Clarity.md: Mappers contain domain business logic
    
    /**
     * Calculate tip rate percentage from address statistics
     * 
     * @param addressStats The address statistics to calculate from
     * @return Tip rate as percentage (0.0 to 100.0)
     */
    fun calculateTipRate(addressStats: AddressStats): Double {
        return if (addressStats.deliveryCount > 0) {
            (addressStats.tipCount.toDouble() / addressStats.deliveryCount.toDouble()) * 100.0
        } else 0.0
    }
    
    /**
     * Determine if an address is a high-value address based on statistics
     * 
     * @param addressStats The address statistics to evaluate
     * @param minDeliveries Minimum deliveries required (default: 5)
     * @param minTipRate Minimum tip rate percentage required (default: 70.0)
     * @param minAverageTip Minimum average tip amount required (default: 3.0)
     * @return True if address meets high-value criteria
     */
    fun isHighValueAddress(
        addressStats: AddressStats,
        minDeliveries: Long = 5,
        minTipRate: Double = 70.0,
        minAverageTip: Double = 3.0
    ): Boolean {
        return addressStats.deliveryCount >= minDeliveries &&
                calculateTipRate(addressStats) >= minTipRate &&
                addressStats.averageTipAmount >= minAverageTip
    }
    
    /**
     * Determine if an address should be marked as "Do Not Deliver" based on statistics
     * 
     * @param addressStats The address statistics to evaluate
     * @param minDeliveries Minimum deliveries required for evaluation (default: 3)
     * @param maxTipRate Maximum tip rate percentage allowed (default: 20.0)
     * @return True if address meets DND criteria
     */
    fun shouldMarkAsDoNotDeliver(
        addressStats: AddressStats,
        minDeliveries: Long = 3,
        maxTipRate: Double = 20.0
    ): Boolean {
        return addressStats.deliveryCount >= minDeliveries &&
                calculateTipRate(addressStats) <= maxTipRate
    }
    
    /**
     * Get address performance category based on statistics
     * 
     * @param addressStats The address statistics to categorize
     * @return Performance category
     */
    fun getPerformanceCategory(addressStats: AddressStats): AddressPerformanceCategory {
        if (addressStats.deliveryCount < 3) {
            return AddressPerformanceCategory.INSUFFICIENT_DATA
        }
        
        val tipRate = calculateTipRate(addressStats)
        
        return when {
            tipRate >= 80.0 && addressStats.averageTipAmount >= 5.0 -> AddressPerformanceCategory.EXCELLENT
            tipRate >= 60.0 && addressStats.averageTipAmount >= 3.0 -> AddressPerformanceCategory.GOOD
            tipRate >= 40.0 -> AddressPerformanceCategory.AVERAGE
            tipRate >= 20.0 -> AddressPerformanceCategory.POOR
            else -> AddressPerformanceCategory.VERY_POOR
        }
    }
    
    /**
     * Combine multiple address statistics (e.g., for summary views)
     * 
     * @param statsList List of AddressStats to combine
     * @return Combined AddressStats representing totals/averages
     */
    fun combineAddressStats(statsList: List<AddressStats>): AddressStats {
        if (statsList.isEmpty()) {
            return AddressStats.empty()
        }
        
        val totalDeliveryCount = statsList.sumOf { it.deliveryCount }
        val totalTipCount = statsList.sumOf { it.tipCount }
        val totalTips = statsList.sumOf { it.totalTips }
        val averageTipAmount = if (totalTipCount > 0) totalTips / totalTipCount else 0.0
        
        return AddressStats(
            deliveryCount = totalDeliveryCount,
            tipCount = totalTipCount,
            totalTips = totalTips,
            averageTipAmount = averageTipAmount,
            highestTip = statsList.maxOfOrNull { it.highestTip } ?: 0.0,
            pendingCount = statsList.sumOf { it.pendingCount },
            averageTimeMinutes = statsList.map { it.averageTimeMinutes }.average(),
            lastDeliveryTimestamp = statsList.mapNotNull { it.lastDeliveryTimestamp }.maxOrNull()
        )
    }
    
    /**
     * Performance categories for addresses based on statistics
     */
    enum class AddressPerformanceCategory {
        EXCELLENT,      // High tip rate (80%+) and high average tip ($5+)
        GOOD,           // Good tip rate (60%+) and decent average tip ($3+)
        AVERAGE,        // Moderate tip rate (40%+)
        POOR,           // Low tip rate (20%+)
        VERY_POOR,      // Very low tip rate (<20%)
        INSUFFICIENT_DATA // Less than 3 deliveries
    }
}
