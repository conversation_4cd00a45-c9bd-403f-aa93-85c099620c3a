package com.autogratuity.data.datasource.remote

import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.util_kt.documentSnapshotToUserProfileDto
import com.autogratuity.data.model.util_kt.wrapUserProfileDtoForFirestore
import com.autogratuity.data.model.util_kt.wrapUserProfileFieldsForFirestore
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.SetOptions
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import javax.inject.Inject
import kotlin.time.TimeSource
import com.autogratuity.data.model.generated_kt.User_profile as UserProfileDto

/**
 * Interface for remote data operations related to User Preferences.
 * All methods operate with UserProfileDto objects following AddressRemoteDataSource pattern.
 */
interface PreferenceRemoteDataSource {
    suspend fun getUserProfileDto(userId: String): Result<UserProfileDto?>
    suspend fun updateUserProfileFieldsDto(userId: String, fields: Map<String, Any>): Result<Unit>
    suspend fun updateUserProfileDto(userId: String, profile: UserProfileDto): Result<Unit>
    suspend fun createUserProfileIfNotExistsDto(userId: String, defaultProfile: UserProfileDto): Result<UserProfileDto>
    fun observeUserProfileDto(userId: String): Flow<UserProfileDto?>
}

@ExperimentalCoroutinesApi
class PreferenceRemoteDataSourceImpl @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val ioDispatcher: CoroutineDispatcher
) : PreferenceRemoteDataSource {

    private val TAG = "PreferenceRemoteDataSourceImpl"

    companion object {
        private const val USERS_COLLECTION = "users"
    }

    private fun getUserProfileDocumentReference(userId: String): DocumentReference {
        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for getUserProfileDocumentReference.")
        }
        return firestore.collection(USERS_COLLECTION).document(userId)
    }

    override suspend fun getUserProfileDto(userId: String): Result<UserProfileDto?> = withContext(ioDispatcher) {
        val startTime = TimeSource.Monotonic.markNow()
        
        try {
            // ENHANCED: Add session correlation for preference fetch
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("preference_remote_fetch:$userId")
            
            val docRef = getUserProfileDocumentReference(userId)
            val snapshot = docRef.get().await()
            
            val duration = startTime.elapsedNow()
            val documentExists = snapshot.exists()
            val dataSize = if (documentExists) snapshot.data?.toString()?.length ?: 0 else 0

            // ENHANCED: Monitor Firestore preference read
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = "users",
                documentId = userId,
                duration = duration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = "users/$userId",
                queryType = "GET_DOCUMENT",
                queryFilters = listOf("preference_field_access"),
                resultCount = if (documentExists) 1 else 0,
                userId = userId,
                cacheSource = "SERVER"
            )
            
            val userProfileDto = if (snapshot.exists()) {
                documentSnapshotToUserProfileDto(snapshot)
            } else {
                null
            }
            
            Log.d(TAG, "getUserProfileDto: Successfully fetched user profile for preferences")
            Log.d(TAG, "  User ID: $userId")
            Log.d(TAG, "  Document exists: $documentExists")
            Log.d(TAG, "  Data size: $dataSize bytes")
            Log.d(TAG, "  Duration: ${duration.inWholeMilliseconds}ms")
            
            Result.Success(userProfileDto)
        } catch (e: Exception) {
            val duration = startTime.elapsedNow()
            
            // ENHANCED: Monitor failed Firestore preference read
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = "users",
                documentId = userId,
                duration = duration,
                success = false,
                dataSizeBytes = 0,
                error = e,
                fullPath = "users/$userId",
                queryType = "GET_DOCUMENT",
                queryFilters = listOf("preference_field_access"),
                resultCount = 0,
                userId = userId,
                cacheSource = "ERROR"
            )
            
            Log.e(TAG, "Error fetching user profile DTO for $userId: ${e.message}", e)
            Result.Error(e)
        }
    }

    override suspend fun updateUserProfileFieldsDto(userId: String, fields: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        try {
            val docRef = getUserProfileDocumentReference(userId)
            
            // ENHANCED: Wrap fields with profileData prefix for nested Firestore structure
            val wrappedFields = wrapUserProfileFieldsForFirestore(fields)
            
            Log.d(TAG, "updateUserProfileFieldsDto: Updating ${fields.size} fields with profileData prefix for user $userId")
            docRef.update(wrappedFields).await()
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating user profile fields DTO for $userId: ${e.message}", e)
            Result.Error(e)
        }
    }

    override suspend fun updateUserProfileDto(userId: String, profile: UserProfileDto): Result<Unit> = withContext(ioDispatcher) {
        try {
            val docRef = getUserProfileDocumentReference(userId)
            
            // ENHANCED: Wrap UserProfileDto in profileData structure for Firestore
            val wrappedProfile = wrapUserProfileDtoForFirestore(profile)
            
            Log.d(TAG, "updateUserProfileDto: Updating user profile with profileData wrapper for user $userId")
            docRef.set(wrappedProfile, SetOptions.merge()).await()
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating entire user profile DTO for $userId: ${e.message}", e)
            Result.Error(e)
        }
    }

    override suspend fun createUserProfileIfNotExistsDto(userId: String, defaultProfile: UserProfileDto): Result<UserProfileDto> = withContext(ioDispatcher) {
        try {
            val docRef = getUserProfileDocumentReference(userId)
            val snapshot = docRef.get().await()
            
            if (!snapshot.exists()) {
                // ENHANCED: Wrap UserProfileDto in profileData structure for Firestore
                val wrappedProfile = wrapUserProfileDtoForFirestore(defaultProfile)
                
                Log.d(TAG, "createUserProfileIfNotExistsDto: Creating user profile with profileData wrapper for user $userId")
                docRef.set(wrappedProfile).await()
                Result.Success(defaultProfile)
            } else {
                val existingProfile = documentSnapshotToUserProfileDto(snapshot)
                Result.Success(existingProfile)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating/getting user profile DTO for $userId: ${e.message}", e)
            Result.Error(e)
        }
    }

    override fun observeUserProfileDto(userId: String): Flow<UserProfileDto?> {
        return callbackFlow {
            try {
                val docRef = getUserProfileDocumentReference(userId)
                val listener = docRef.addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        Log.e(TAG, "Error observing user profile DTO for $userId: ${error.message}", error)
                        trySend(null)
                        return@addSnapshotListener
                    }
                    
                    val userProfileDto = if (snapshot?.exists() == true) {
                        documentSnapshotToUserProfileDto(snapshot)
                    } else {
                        null
                    }
                    
                    trySend(userProfileDto)
                }
                
                awaitClose { listener.remove() }
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up profile observation DTO for $userId: ${e.message}", e)
                trySend(null)
            }
        }.flowOn(ioDispatcher)
    }
}