package com.autogratuity.data.model

import java.io.InterruptedIOException
import java.net.ConnectException
import java.net.SocketException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.Date

/**
 * Standard model class for representing error information throughout the application.
 * This class provides a unified approach to error representation, with support for
 * error severity levels, recovery actions, and standardized error codes.
 */
class ErrorInfo() {
    private var code: String? = null
    /**
     * Get the error message
     * @return Error message
     */
    /**
     * Set the error message
     * @param message Error message
     */
    @JvmField
    var message: String? = null
    /**
     * Get the error timestamp
     * @return Error timestamp as string
     */
    /**
     * Set the error timestamp
     * @param timestamp Error timestamp
     */
    var timestamp: String? = null
    /**
     * Get the error severity
     * @return Error severity
     */
    /**
     * Set the error severity
     * @param severity Error severity
     */
    var severity: String?
    /**
     * Get the recommended recovery action
     * @return Recovery action
     */
    /**
     * Set the recommended recovery action
     * @param recoveryAction Recovery action
     */
    var recoveryAction: String
    private var details: MutableMap<String?, Any?>?
    /**
     * Get the retry count for this error
     * @return Number of retry attempts
     */
    /**
     * Set the retry count for this error
     * @param retryCount Number of retry attempts
     */
    var retryCount: Int = 0

    /**
     * Default constructor for Firestore
     */
    init {
        this.details = HashMap<String?, Any?>()
        this.severity = SEVERITY_MEDIUM
        this.recoveryAction = RECOVERY_RETRY
    }

    /**
     * Constructor with basic error details
     *
     * @param code Error code
     * @param message Error message
     * @param timestamp Error timestamp
     */
    constructor(code: String, message: String?, timestamp: Date?) : this() {
        this.code = code
        this.message = message
        this.timestamp = timestamp?.toString()
    }

    /**
     * Constructor with full error details
     *
     * @param code Error code
     * @param message Error message
     * @param timestamp Error timestamp
     * @param severity Error severity
     * @param recoveryAction Recommended recovery action
     */
    constructor(
        code: String,
        message: String?,
        timestamp: Date?,
        severity: String?,
        recoveryAction: String
    ) : this(code, message, timestamp) {
        this.severity = severity
        this.recoveryAction = recoveryAction
    }

    /**
     * Constructor with full error details and additional information
     *
     * @param code Error code
     * @param message Error message
     * @param timestamp Error timestamp
     * @param severity Error severity
     * @param recoveryAction Recommended recovery action
     * @param details Additional error details
     */
    constructor(
        code: String,
        message: String?,
        timestamp: Date?,
        severity: String?,
        recoveryAction: String,
        details: MutableMap<String?, Any?>?
    ) : this(code, message, timestamp, severity, recoveryAction) {
        this.details = details ?: HashMap<String?, Any?>()
    }

    /**
     * Get the error code
     * @return Error code
     */
    fun getCode(): String {
        return code!!
    }

    /**
     * Set the error code
     * @param code Error code
     */
    fun setCode(code: String) {
        this.code = code
    }

    /**
     * Get additional error details
     * @return Map of error details
     */
    fun getDetails(): MutableMap<String?, Any?>? {
        return details
    }

    /**
     * Set additional error details
     * @param details Map of error details
     */
    fun setDetails(details: MutableMap<String?, Any?>?) {
        this.details = details ?: HashMap<String?, Any?>()
    }

    /**
     * Add a detail to the error information
     * @param key Detail key
     * @param value Detail value
     */
    fun addDetail(key: String?, value: Any?) {
        if (details == null) {
            details = HashMap<String?, Any?>()
        }
        details!!.put(key, value)
    }

    val isNetworkError: Boolean
        /**
         * Check if this is a network-related error
         * @return true if network error
         */
        get() = CODE_NETWORK == code ||
                (details != null && java.lang.Boolean.TRUE == details!!["isNetworkError"])

    val isAuthenticationError: Boolean
        /**
         * Check if this is an authentication error
         * @return true if authentication error
         */
        get() = CODE_AUTHENTICATION == code

    /**
     * Check if this error requires user action
     * @return true if user action required
     */
    fun requiresUserAction(): Boolean {
        return RECOVERY_LOGIN_AGAIN == recoveryAction ||
                RECOVERY_CHECK_CONNECTION == recoveryAction ||
                RECOVERY_UPDATE_APP == recoveryAction ||
                RECOVERY_CONTACT_SUPPORT == recoveryAction
    }

    val isCritical: Boolean
        /**
         * Check if this error is critical (high or critical severity)
         * @return true if critical error
         */
        get() = SEVERITY_HIGH == severity || SEVERITY_CRITICAL == severity

    /**
     * Check if this error should be retried automatically
     * @return true if should retry
     */
    fun shouldRetry(): Boolean {
        return RECOVERY_RETRY == recoveryAction ||
                CODE_NETWORK == code ||
                CODE_TIMEOUT == code
    }

    val userFriendlyMessage: String
        /**
         * Get a user-friendly message for this error
         * @return User-friendly error message
         */
        get() {
            if (message != null && !message!!.isEmpty()) {
                return message!!
            }


            // Return standard messages based on code
            return when (code) {
                CODE_NETWORK -> "Network connection error. Please check your internet connection."
                CODE_AUTHENTICATION -> "Authentication error. Please log in again."
                CODE_PERMISSION -> "You don't have permission to perform this action."
                CODE_VALIDATION -> "Invalid data. Please check your input."
                CODE_SERVER -> "Server error. Please try again later."
                CODE_CONFLICT -> "Data conflict error. The data has been changed by another user."
                CODE_NOT_FOUND -> "The requested resource was not found."
                CODE_TIMEOUT -> "Operation timed out. Please try again."
                CODE_CANCELLED -> "Operation was cancelled."
                else -> "An unexpected error occurred."
            }
        }

    val recoveryActionMessage: String
        /**
         * Get a user-friendly recovery action message
         * @return User-friendly recovery action message
         */
        get() {
            return when (recoveryAction) {
                RECOVERY_RETRY -> "Please try again."
                RECOVERY_CONTACT_SUPPORT -> "Please contact support for assistance."
                RECOVERY_CHECK_CONNECTION -> "Please check your internet connection and try again."
                RECOVERY_LOGIN_AGAIN -> "Please log in again."
                RECOVERY_UPDATE_APP -> "Please update the app to the latest version."
                RECOVERY_WAIT -> "Please wait a moment and try again."
                RECOVERY_NONE -> ""
                else -> ""
            }
        }

    val detailsForLogging: String
        /**
         * Get detailed error information for logging purposes
         * @return Formatted error information string
         */
        get() {
            val details = StringBuilder()
            details.append("Error Code: ").append(code).append("\n")
            details.append("Message: ").append(message).append("\n")

            if (timestamp != null) {
                details.append("Timestamp: ").append(timestamp).append("\n")
            }

            details.append("Severity: ").append(severity).append("\n")
            details.append("Recovery Action: ").append(recoveryAction).append("\n")

            if (retryCount > 0) {
                details.append("Retry Count: ").append(retryCount).append("\n")
            }

            if (this.details != null && !this.details!!.isEmpty()) {
                details.append("Additional Details: ").append(this.details.toString()).append("\n")
            }

            return details.toString()
        }

    val isRetryable: Boolean
        /**
         * Check if this error is retryable
         * @return true if error can be retried
         */
        get() = RECOVERY_RETRY == recoveryAction ||
                RECOVERY_WAIT == recoveryAction ||
                CODE_NETWORK == code ||
                CODE_TIMEOUT == code

    val isRecoverable: Boolean
        /**
         * Check if this error is potentially recoverable through retry or user action.
         * @return true if the error might be recoverable
         */
        get() =// An error is considered recoverable if it's retryable or requires user action
            this.isRetryable || requiresUserAction()

    companion object {
        // Error severity levels
        const val SEVERITY_LOW: String = "low"
        const val SEVERITY_MEDIUM: String = "medium"
        const val SEVERITY_HIGH: String = "high"
        const val SEVERITY_CRITICAL: String = "critical"

        // Standard error codes
        const val CODE_NETWORK: String = "network_error"
        const val CODE_AUTHENTICATION: String = "auth_error"
        const val CODE_PERMISSION: String = "permission_error"
        const val CODE_VALIDATION: String = "validation_error"
        const val CODE_SERVER: String = "server_error"
        const val CODE_CONFLICT: String = "conflict_error"
        const val CODE_NOT_FOUND: String = "not_found_error"
        const val CODE_TIMEOUT: String = "timeout_error"
        const val CODE_CANCELLED: String = "cancelled_error"
        const val CODE_UNKNOWN: String = "unknown_error"
        const val CODE_DEPENDENCY_NOT_READY: String = "dependency_not_ready"

        // Standard recovery actions
        const val RECOVERY_RETRY: String = "retry"
        const val RECOVERY_CONTACT_SUPPORT: String = "contact_support"
        const val RECOVERY_CHECK_CONNECTION: String = "check_connection"
        const val RECOVERY_LOGIN_AGAIN: String = "login_again"
        const val RECOVERY_UPDATE_APP: String = "update_app"
        const val RECOVERY_WAIT: String = "wait"
        const val RECOVERY_NONE: String = "none"
        const val RECOVERY_MANUAL_RESOLVE: String = "manual_resolve"

        //-----------------------------------------------------------------------------------
        // Static factory methods for common error scenarios
        //-----------------------------------------------------------------------------------
        /**
         * Create a network error
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun createNetworkError(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_NETWORK,
                message ?: "Network connection error",
                Date(),
                SEVERITY_MEDIUM,
                RECOVERY_CHECK_CONNECTION
            )
        }

        /**
         * Create an authentication error
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun createAuthError(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_AUTHENTICATION,
                message ?: "Authentication error",
                Date(),
                SEVERITY_HIGH,
                RECOVERY_LOGIN_AGAIN
            )
        }

        /**
         * Create a server error
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun createServerError(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_SERVER,
                message ?: "Server error",
                Date(),
                SEVERITY_HIGH,
                RECOVERY_RETRY
            )
        }

        /**
         * Create a validation error
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun createValidationError(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_VALIDATION,
                message ?: "Validation error",
                Date(),
                SEVERITY_MEDIUM,
                RECOVERY_NONE
            )
        }

        /**
         * Create a timeout error
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun createTimeoutError(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_TIMEOUT,
                message ?: "Operation timed out",
                Date(),
                SEVERITY_MEDIUM,
                RECOVERY_RETRY
            )
        }

        /**
         * Create an unknown error
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun createUnknownError(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_UNKNOWN,
                message ?: "Unknown error",
                Date(),
                SEVERITY_MEDIUM,
                RECOVERY_RETRY
            )
        }

        /**
         * Create a permission error
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun createPermissionError(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_PERMISSION,
                message ?: "Permission error",
                Date(),
                SEVERITY_HIGH,
                RECOVERY_CONTACT_SUPPORT
            )
        }

        /**
         * Create an authentication error
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun createAuthenticationError(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_AUTHENTICATION,
                message ?: "Authentication required",
                Date(),
                SEVERITY_HIGH,
                RECOVERY_LOGIN_AGAIN
            )
        }

        /**
         * Create a throttled error
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun createThrottledError(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_TIMEOUT,
                message ?: "Too many requests",
                Date(),
                SEVERITY_MEDIUM,
                RECOVERY_WAIT
            )
        }

        /**
         * Create a not found error
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun createNotFoundError(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_NOT_FOUND,
                message ?: "Resource not found",
                Date(),
                SEVERITY_MEDIUM,
                RECOVERY_NONE
            )
        }

        /**
         * Create an error from a simple message
         * @param message Error message
         * @return ErrorInfo instance
         */
        fun fromMessage(message: String?): ErrorInfo {
            return ErrorInfo(
                CODE_UNKNOWN,
                message,
                Date(),
                SEVERITY_MEDIUM,
                RECOVERY_NONE
            )
        }

        /**
         * Create an error from a Throwable
         * @param throwable The exception or error
         * @return ErrorInfo instance
         */
        @JvmStatic
        fun fromThrowable(throwable: Throwable?): ErrorInfo {
            if (throwable == null) {
                return ErrorInfo(CODE_UNKNOWN, "Unknown error", Date())
            }

            var message = throwable.message
            if (message == null || message.isEmpty()) {
                message = throwable.javaClass.getSimpleName()
            }


            // Determine error type from exception class
            if (throwable is UnknownHostException ||
                throwable is ConnectException ||
                throwable is SocketException ||
                throwable is SocketTimeoutException
            ) {
                return createNetworkError(message)
            } else if (throwable is InterruptedIOException) {
                return createTimeoutError(message)
            } else if (throwable is SecurityException ||
                throwable is IllegalAccessException
            ) {
                return ErrorInfo(
                    CODE_PERMISSION,
                    message,
                    Date(),
                    SEVERITY_HIGH,
                    RECOVERY_CONTACT_SUPPORT
                )
            } else if (throwable is IllegalArgumentException ||
                throwable is IllegalStateException
            ) {
                return createValidationError(message)
            }


            // Default to unknown error
            return ErrorInfo(
                CODE_UNKNOWN,
                message,
                Date(),
                SEVERITY_MEDIUM,
                RECOVERY_RETRY
            )
        }
    }
}
