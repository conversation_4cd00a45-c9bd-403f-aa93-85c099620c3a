package com.autogratuity.data.util

import android.util.Log
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.atomicfu.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ✅ MODERNIZED (2025-01-28): Coroutine dispatcher provider optimized for repository operations
 * 
 * Replaces legacy RxJava scheduler patterns with 2025 Kotlin standards including:
 * - CoroutineDispatcher instead of RxJava Scheduler  
 * - kotlinx.atomicfu for thread-safe atomic operations
 * - Structured concurrency patterns with limitedParallelism
 * - Resource-aware thread pool management
 * - Operation-specific dispatcher selection
 * 
 * Key Features:
 * - Optimized dispatchers for different operation types
 * - Atomic task counting for monitoring
 * - No RxJava dependencies - pure coroutines
 * - Efficient resource utilization
 */
@OptIn(ExperimentalCoroutinesApi::class)
@Singleton
class OptimizedDispatchers @Inject constructor() {
    companion object {
        private const val TAG = "OptimizedDispatchers"
    }

    // Core pool sizes for different thread pools
    private val ioPoolSize = maxOf(2, Runtime.getRuntime().availableProcessors())
    private val networkPoolSize = maxOf(2, Runtime.getRuntime().availableProcessors() / 2)
    private val computationPoolSize = Runtime.getRuntime().availableProcessors()
    private val backgroundPoolSize = 2

    // Atomic counters for monitoring - using kotlinx.atomicfu (2025 standard)
    // ✅ FIXED: Changed atomic(0L) to atomic(0) to resolve KSP compilation errors
    private val activeIOTasks = atomic(0)
    private val activeNetworkTasks = atomic(0)
    private val activeComputationTasks = atomic(0)
    private val activeBackgroundTasks = atomic(0)

    // Modern coroutine dispatchers using limitedParallelism instead of deprecated newFixedThreadPoolContext
    val io: CoroutineDispatcher by lazy {
        Dispatchers.IO.limitedParallelism(ioPoolSize)
    }

    val network: CoroutineDispatcher by lazy {
        Dispatchers.IO.limitedParallelism(networkPoolSize)
    }

    val computation: CoroutineDispatcher by lazy {
        Dispatchers.Default.limitedParallelism(computationPoolSize)
    }

    val background: CoroutineDispatcher by lazy {
        Dispatchers.Default.limitedParallelism(backgroundPoolSize)
    }

    val main: CoroutineDispatcher = Dispatchers.Main.immediate

    /**
     * Get an optimized dispatcher for a specific operation type and priority
     */
    fun forOperation(operationType: String, priority: Int): CoroutineDispatcher {
        Log.d(TAG, "Getting dispatcher for operation: $operationType with priority: $priority")

        return when {
            operationType.contains("observe") || operationType.contains("get") -> {
                activeIOTasks.incrementAndGet()
                io
            }
            operationType.contains("add") || operationType.contains("update") ||
            operationType.contains("delete") || operationType.contains("sync") -> {
                activeNetworkTasks.incrementAndGet()
                network
            }
            operationType.contains("compute") || operationType.contains("calculate") ||
            operationType.contains("process") -> {
                activeComputationTasks.incrementAndGet()
                computation
            }
            else -> {
                activeBackgroundTasks.incrementAndGet()
                background
            }
        }
    }

    /**
     * Get dispatcher for entity-based operations (replacement for legacy forOperationAsDispatcher)
     */
    fun forEntity(entityType: String?, priority: Int = 0): CoroutineDispatcher {
        val operationNameForDispatcher = entityType ?: "default"
        Log.d(TAG, "Getting dispatcher for entity: $entityType with priority: $priority")
        
        return when {
            operationNameForDispatcher.contains("observe") || operationNameForDispatcher.contains("get") -> {
                activeIOTasks.incrementAndGet()
                io
            }
            operationNameForDispatcher.contains("add") || operationNameForDispatcher.contains("update") -> {
                activeNetworkTasks.incrementAndGet()  
                network
            }
            operationNameForDispatcher.contains("delete") || operationNameForDispatcher.contains("sync") -> {
                activeBackgroundTasks.incrementAndGet()
                background
            }
            else -> {
                activeIOTasks.incrementAndGet()
                io
            }
        }
    }

    /**
     * Get dispatcher metrics for monitoring
     */
    fun getDispatcherMetrics(): DispatcherMetrics {
        return DispatcherMetrics(
            activeIOTasks = activeIOTasks.value,
            activeNetworkTasks = activeNetworkTasks.value,
            activeComputationTasks = activeComputationTasks.value,
            activeBackgroundTasks = activeBackgroundTasks.value,
            totalActiveTasks = activeIOTasks.value + activeNetworkTasks.value + 
                             activeComputationTasks.value + activeBackgroundTasks.value
        )
    }

    /**
     * Reset task counters for monitoring
     */
    fun resetMetrics() {
        activeIOTasks.value = 0
        activeNetworkTasks.value = 0
        activeComputationTasks.value = 0
        activeBackgroundTasks.value = 0
    }

    /**
     * Decrement task counter when operation completes
     */
    fun onOperationComplete(dispatcher: CoroutineDispatcher) {
        when (dispatcher) {
            io -> activeIOTasks.decrementAndGet()
            network -> activeNetworkTasks.decrementAndGet()
            computation -> activeComputationTasks.decrementAndGet()
            background -> activeBackgroundTasks.decrementAndGet()
        }
    }
}

/**
 * Dispatcher metrics data class
 */
data class DispatcherMetrics(
    val activeIOTasks: Int,
    val activeNetworkTasks: Int,
    val activeComputationTasks: Int,
    val activeBackgroundTasks: Int,
    val totalActiveTasks: Int
)

/**
 * ✅ MODERNIZED: Global dispatcher access object for convenient static-like usage
 * 
 * Provides convenient access to optimized dispatchers throughout the app.
 * This replaces the legacy OptimizedSchedulers object with pure coroutine patterns.
 */
object RxSchedulers {
    private val dispatchers by lazy { OptimizedDispatchers() }
    
    /**
     * IO dispatcher optimized for repository read operations
     */
    fun io(): CoroutineDispatcher = dispatchers.io
    
    /**
     * Network dispatcher optimized for repository write operations  
     */
    fun network(): CoroutineDispatcher = dispatchers.network
    
    /**
     * Computation dispatcher for CPU-intensive operations
     */
    fun computation(): CoroutineDispatcher = dispatchers.computation
    
    /**
     * Background dispatcher for low-priority operations
     */
    fun background(): CoroutineDispatcher = dispatchers.background
    
    /**
     * Main dispatcher for UI operations
     */
    fun main(): CoroutineDispatcher = dispatchers.main
    
    /**
     * Get dispatcher for specific operation type
     */
    fun forOperation(operationType: String, priority: Int = 0): CoroutineDispatcher {
        return dispatchers.forOperation(operationType, priority)
    }
    
    /**
     * Get dispatcher metrics
     */
    fun getMetrics(): DispatcherMetrics = dispatchers.getDispatcherMetrics()
} 