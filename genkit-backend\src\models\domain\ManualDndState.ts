// Auto-generated from ManualDndState.kt
// TypeScript equivalent of Android domain.model.ManualDndState

export enum ManualDndState {
  User = 'User',
  User = 'User',
  Address = 'Address',
  companion = 'companion',
  Convert = 'Convert',
  FORCE_DND = 'FORCE_DND',
  FORCE_ALLOW = 'FORCE_ALLOW',
  AUTOMATIC = 'AUTOMATIC',
  else = 'else'
}

/**
 * Domain model generated from Kotlin ManualDndState
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface ManualDndState {

}