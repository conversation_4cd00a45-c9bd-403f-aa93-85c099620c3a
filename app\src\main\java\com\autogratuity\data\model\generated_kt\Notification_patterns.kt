/*
 * Notification_patterns.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

import java.time.OffsetDateTime

/**
 * Defines the structure for notification parsing patterns, typically stored in 'system_config/notification_patterns' in Firestore.
 */
data class Notification_patterns(
    /** Regular expressions for extracting specific data points from notifications. */
    val extractors: Extractors,
    /** Platform-specific notification message patterns. */
    val patterns: Patterns,
    /** Timestamp of the last update to these patterns. */
    val updatedAt: OffsetDateTime? = null,
    /** Schema version or data version for these patterns. */
    val version: Long
) {

    /**
     * Regular expressions for extracting specific data points from notifications.
     */
    data class Extractors(
        /** Extractors specific to Shipt notifications. */
        val shipt: ShiptExtractor
    )

    /**
     * Extractors specific to Shipt notifications.
     */
    data class ShiptExtractor(
        /** Regex to extract Shipt order ID. */
        val orderId: String,
        /** Regex to extract Shipt tip amount. */
        val tipAmount: String
    )

    /**
     * Platform-specific notification message patterns.
     */
    data class Patterns(
        /** Regex patterns for DoorDash notifications. */
        val doordash: List<String>,
        /** Regex patterns for Shipt notifications. */
        val shipt: List<String>,
        /** Regex patterns for Uber Eats notifications. */
        val ubereats: List<String>
    )

}
