package com.autogratuity.data.repository.core

import com.autogratuity.data.model.ErrorInfo
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

/**
 * Modern exception hierarchy for repository errors, integrating with the ErrorInfo system
 * and supporting structured error handling patterns.
 *
 * Modernized in 2025 to provide comprehensive error context and type safety.
 */
sealed class RepositoryException(
    message: String,
    cause: Throwable? = null
) : RuntimeException(message, cause) {

    /**
     * Timestamp when the error occurred
     */
    val timestamp: Instant = Clock.System.now()

    /**
     * Context information about the error
     */
    abstract val context: Map<String, Any>

    /**
     * Severity level of the error
     */
    abstract val severity: ErrorSeverity

    /**
     * Whether this error should be retried
     */
    abstract val isRetryable: Boolean

    /**
     * Error severity levels for categorization
     */
    enum class ErrorSeverity {
        LOW,     // Warnings, non-critical issues
        MEDIUM,  // Errors that affect functionality but don't crash
        HIGH,    // Critical errors that significantly impact user experience
        CRITICAL // System-level errors that may require immediate attention
    }

    /**
     * Network-related repository errors
     */
    class NetworkError(
        message: String,
        cause: Throwable? = null,
        val networkType: String? = null,
        val connectionStatus: String? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.MEDIUM
        override val isRetryable = true
    }

    /**
     * Authentication and authorization errors
     */
    class AuthenticationError(
        message: String,
        cause: Throwable? = null,
        val authCode: String? = null,
        val userId: String? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.HIGH
        override val isRetryable = false
    }

    /**
     * Data validation and integrity errors
     */
    class ValidationError(
        message: String,
        cause: Throwable? = null,
        val fieldName: String? = null,
        val invalidValue: Any? = null,
        val validationRules: List<String> = emptyList(),
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.MEDIUM
        override val isRetryable = false
    }

    /**
     * Database and storage errors
     */
    class DatabaseError(
        message: String,
        cause: Throwable? = null,
        val errorCode: String? = null,
        val operation: String? = null,
        val collectionPath: String? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.HIGH
        override val isRetryable = true
    }

    /**
     * Timeout and deadline exceeded errors
     */
    class TimeoutError(
        message: String,
        cause: Throwable? = null,
        val timeoutDuration: kotlin.time.Duration? = null,
        val operation: String? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.MEDIUM
        override val isRetryable = true
    }

    /**
     * Resource not found errors
     */
    class NotFoundError(
        message: String,
        cause: Throwable? = null,
        val resourceId: String? = null,
        val resourceType: String? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.LOW
        override val isRetryable = false
    }

    /**
     * Concurrency and conflict errors
     */
    class ConcurrencyError(
        message: String,
        cause: Throwable? = null,
        val conflictingOperation: String? = null,
        val expectedVersion: String? = null,
        val actualVersion: String? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.MEDIUM
        override val isRetryable = true
    }

    /**
     * Rate limiting and quota exceeded errors
     */
    class RateLimitError(
        message: String,
        cause: Throwable? = null,
        val retryAfter: kotlin.time.Duration? = null,
        val quotaType: String? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.MEDIUM
        override val isRetryable = true
    }

    /**
     * Cache-related errors
     */
    class CacheError(
        message: String,
        cause: Throwable? = null,
        val cacheKey: String? = null,
        val cacheOperation: String? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.LOW
        override val isRetryable = true
    }

    /**
     * Configuration and setup errors
     */
    class ConfigurationError(
        message: String,
        cause: Throwable? = null,
        val configKey: String? = null,
        val expectedType: String? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.HIGH
        override val isRetryable = false
    }

    /**
     * Cryptographic and encryption errors
     */
    class CryptographicError(
        message: String,
        cause: Throwable? = null,
        val operation: String? = null,
        val fieldName: String? = null,
        val cryptoErrorType: String? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.HIGH
        override val isRetryable = false
    }

    /**
     * Unknown or unexpected errors
     */
    class UnknownError(
        message: String,
        cause: Throwable? = null,
        override val context: Map<String, Any> = emptyMap()
    ) : RepositoryException(message, cause) {
        override val severity = ErrorSeverity.CRITICAL
        override val isRetryable = false
    }

    /**
     * Convert this exception to an ErrorInfo for compatibility with legacy systems
     */
    fun toErrorInfo(): ErrorInfo {
        val baseDetails = mutableMapOf<String?, Any?>(
            "timestamp" to timestamp.toString(),
            "severity" to severity.name,
            "retryable" to isRetryable,
            "exception_type" to (this::class.simpleName ?: "Unknown")
        )

        // Add specific context - convert Map<String, Any> to MutableMap<String?, Any?>
        context.forEach { (key, value) ->
            baseDetails[key] = value
        }

        return when (this) {
            is NetworkError -> {
                val errorInfo = ErrorInfo(
                    "NETWORK_ERROR",
                    message ?: "Network error",
                    null,
                    "medium",
                    "check_connection",
                    baseDetails
                )
                errorInfo
            }
            is AuthenticationError -> {
                val errorInfo = ErrorInfo(
                    "AUTH_ERROR",
                    message ?: "Authentication error",
                    null,
                    "high",
                    "login_again",
                    baseDetails
                )
                errorInfo
            }
            is ValidationError -> {
                val errorInfo = ErrorInfo(
                    "VALIDATION_ERROR",
                    message ?: "Validation error",
                    null,
                    "medium",
                    "none",
                    baseDetails
                )
                errorInfo
            }
            is DatabaseError -> {
                val errorInfo = ErrorInfo(
                    "SERVER_ERROR",
                    message ?: "Database error",
                    null,
                    "high",
                    "retry",
                    baseDetails
                )
                errorInfo
            }
            is TimeoutError -> {
                val errorInfo = ErrorInfo(
                    "TIMEOUT_ERROR",
                    message ?: "Timeout error",
                    null,
                    "medium",
                    "retry",
                    baseDetails
                )
                errorInfo
            }
            is NotFoundError -> {
                val errorInfo = ErrorInfo(
                    "NOT_FOUND_ERROR",
                    message ?: "Resource not found",
                    null,
                    "medium",
                    "none",
                    baseDetails
                )
                errorInfo
            }
            is ConcurrencyError -> {
                val errorInfo = ErrorInfo(
                    "CONFLICT_ERROR",
                    message ?: "Concurrency error",
                    null,
                    "medium",
                    "retry",
                    baseDetails
                )
                errorInfo
            }
            is RateLimitError -> {
                val errorInfo = ErrorInfo(
                    "RATE_LIMIT_ERROR",
                    message ?: "Rate limit exceeded",
                    null,
                    "medium",
                    "wait",
                    baseDetails
                )
                errorInfo
            }
            is CacheError -> {
                val errorInfo = ErrorInfo(
                    "CACHE_ERROR",
                    message ?: "Cache error",
                    null,
                    "low",
                    "retry",
                    baseDetails
                )
                errorInfo
            }
            is ConfigurationError -> {
                val errorInfo = ErrorInfo(
                    "CONFIG_ERROR",
                    message ?: "Configuration error",
                    null,
                    "high",
                    "contact_support",
                    baseDetails
                )
                errorInfo
            }
            is CryptographicError -> {
                val errorInfo = ErrorInfo(
                    "CRYPTO_ERROR",
                    message ?: "Cryptographic error",
                    null,
                    "high",
                    "retry",
                    baseDetails
                )
                errorInfo
            }
            is UnknownError -> {
                val errorInfo = ErrorInfo(
                    "UNKNOWN_ERROR",
                    message ?: "Unknown error",
                    null,
                    "critical",
                    "retry",
                    baseDetails
                )
                errorInfo
            }
        }
    }

    /**
     * Get a comprehensive error summary for logging and debugging
     */
    fun getErrorSummary(): String = buildString {
        append("${this@RepositoryException::class.simpleName}: ")
        append(message ?: "Unknown error")
        append(" [Severity: ${severity.name}, Retryable: $isRetryable]")

        if (context.isNotEmpty()) {
            append(" Context: {")
            append(context.entries.joinToString(", ") { "${it.key}=${it.value}" })
            append("}")
        }

        cause?.let { append(" Caused by: ${it::class.simpleName}: ${it.message}") }
    }

    companion object {
        /**
         * Create a RepositoryException from an ErrorInfo for compatibility
         */
        fun fromErrorInfo(errorInfo: ErrorInfo): RepositoryException {
            val context = try {
                errorInfo.getDetails()?.let { details ->
                    // Convert MutableMap<String?, Any?> to Map<String, Any>
                    details.filterKeys { it != null }
                          .mapKeys { it.key!! }
                          .filterValues { it != null }
                          .mapValues { it.value!! }
                } ?: emptyMap()
            } catch (e: Exception) {
                emptyMap<String, Any>()
            }

            val message = try {
                errorInfo.message ?: "Unknown error"
            } catch (e: Exception) {
                "Unknown error"
            }

            val code = try {
                errorInfo.getCode()
            } catch (e: Exception) {
                "UNKNOWN_ERROR"
            }

            return when (code) {
                "NETWORK_ERROR" -> NetworkError(message, context = context)
                "AUTH_ERROR" -> AuthenticationError(message, context = context)
                "VALIDATION_ERROR" -> ValidationError(message, context = context)
                "SERVER_ERROR", "DATABASE_ERROR" -> DatabaseError(message, context = context)
                "TIMEOUT_ERROR" -> TimeoutError(message, context = context)
                "NOT_FOUND_ERROR" -> NotFoundError(message, context = context)
                "CONFLICT_ERROR" -> ConcurrencyError(message, context = context)
                "RATE_LIMIT_ERROR" -> RateLimitError(message, context = context)
                "CACHE_ERROR" -> CacheError(message, context = context)
                "CONFIG_ERROR" -> ConfigurationError(message, context = context)
                "CRYPTO_ERROR" -> CryptographicError(message, context = context)
                else -> UnknownError(message, context = context)
            }
        }

        /**
         * Create a RepositoryException from a Throwable with context
         */
        fun fromThrowable(
            throwable: Throwable,
            operation: String? = null,
            entityType: String? = null
        ): RepositoryException {
            val context = buildMap<String, Any> {
                operation?.let { put("operation", it) }
                entityType?.let { put("entity_type", it) }
                put("original_exception", throwable::class.simpleName ?: "Unknown")
                throwable.message?.let { put("original_message", it) }
            }

            return when (throwable) {
                is com.google.firebase.FirebaseNetworkException,
                is java.net.UnknownHostException,
                is java.net.ConnectException ->
                    NetworkError(throwable.message ?: "Network error", throwable, context = context)

                is com.google.firebase.auth.FirebaseAuthException ->
                    AuthenticationError(
                        throwable.message ?: "Authentication error",
                        throwable,
                        authCode = throwable.errorCode,
                        context = context
                    )

                is IllegalArgumentException ->
                    ValidationError(throwable.message ?: "Validation error", throwable, context = context)

                is com.google.firebase.firestore.FirebaseFirestoreException ->
                    DatabaseError(
                        throwable.message ?: "Database error",
                        throwable,
                        errorCode = throwable.code.name,
                        context = context
                    )

                is java.util.concurrent.TimeoutException,
                is java.net.SocketTimeoutException ->
                    TimeoutError(throwable.message ?: "Timeout error", throwable, context = context)

                else -> UnknownError(throwable.message ?: "Unknown error", throwable, context = context)
            }
        }
    }
}

/**
 * Legacy compatibility - wrapper around ErrorInfo
 */
@Deprecated(
    "Use the new RepositoryException sealed class hierarchy instead",
    ReplaceWith("RepositoryException.fromErrorInfo(errorInfo)"),
    level = DeprecationLevel.WARNING
)
class LegacyRepositoryException(val errorInfo: ErrorInfo) : RuntimeException(errorInfo.message) {

    /**
     * Convert to the modern RepositoryException
     */
    fun toModernException(): RepositoryException = RepositoryException.fromErrorInfo(errorInfo)
}

