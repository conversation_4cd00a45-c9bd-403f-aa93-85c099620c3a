import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore, Timestamp, FieldValue } from 'firebase-admin/firestore';

// ✅ ONLY use generated models from schemas

import type {
  Delivery
} from '../models/generated/delivery.schema';

const db = getFirestore();

// Input/Output schemas
const CalculateAddressStatsInputSchema = z.object({
  userId: z.string(),
  addressId: z.string()
});

const CalculateAddressStatsOutputSchema = z.object({
  success: z.boolean(),
  stats: z.object({
    deliveryCount: z.number(),
    tipCount: z.number(),
    pendingCount: z.number(),
    totalTips: z.number(),
    averageTipAmount: z.number(),
    highestTip: z.number().nullable(),
    lastDeliveryTimestamp: z.any().nullable(), // Timestamp
    // Enhanced stats from original
    averageTimeMinutes: z.number().nullable(),
    collectedOrderIds: z.array(z.string()),
    totalBasePay: z.number(),
    totalFinalPay: z.number(),
    averageDistance: z.number().nullable(),
    verifiedDeliveryCount: z.number(),
    cancelledDeliveryCount: z.number(),
    platformBreakdown: z.record(z.string(), z.number()),
    currencyBreakdown: z.record(z.string(), z.number())
  }),
  // ✅ NEW: Delta tracking for coordination
  deltas: z.object({
    deliveryCount: z.number(),
    tipCount: z.number(),
    totalTips: z.number(),
    pendingCount: z.number(),
    dndCreated: z.boolean(),
    dndRemoved: z.boolean()
  }),
  // ✅ NEW: Coordination metadata
  coordination: z.object({
    hasSignificantChanges: z.boolean(),
    isFirstDeliveryToAddress: z.boolean(),
    requiresDndEvaluation: z.boolean(),
    requiresUserProfileUpdate: z.boolean(),
    version: z.number(),
    operationType: z.string()
  }),
  error: z.string().optional()
});

// Enhanced stats interface matching original address-stats-updater
interface EnhancedStats {
  deliveryCount: number;
  tipCount: number;
  pendingCount: number;
  totalTips: number;
  averageTipAmount: number;
  highestTip: number | null;
  lastDeliveryTimestamp: any | null; // Timestamp
  // Enhanced stats from original
  averageTimeMinutes: number | null;
  collectedOrderIds: string[];
  totalBasePay: number;
  totalFinalPay: number;
  averageDistance: number | null;
  verifiedDeliveryCount: number;
  cancelledDeliveryCount: number;
  platformBreakdown: { [platform: string]: number };
  currencyBreakdown: { [currency: string]: number };
}

// ✅ NEW: Delta calculation interface for coordination
interface StatsDeltas {
  deliveryCount: number;
  tipCount: number;
  totalTips: number;
  pendingCount: number;
  dndCreated: boolean;
  dndRemoved: boolean;
}

// ✅ NEW: Coordination metadata interface
interface CoordinationMetadata {
  hasSignificantChanges: boolean;
  isFirstDeliveryToAddress: boolean;
  requiresDndEvaluation: boolean;
  requiresUserProfileUpdate: boolean;
  version: number;
  operationType: string;
}

/**
 * ✅ FOCUSED FUNCTION: Calculate delivery statistics for a single address
 * 
 * Purpose: Simple aggregation of delivery data into statistics
 * Uses: Only generated schema models
 * Updates: Address deliveryStats only
 */
export const calculateAddressStatsFlow = defineFlow(
  {
    name: 'calculateAddressStats',
    inputSchema: CalculateAddressStatsInputSchema,
    outputSchema: CalculateAddressStatsOutputSchema,
  },
  async (input) => {
    const logPrefix = `[CalculateAddressStats][${input.userId}][${input.addressId}]`;
    const startTime = Date.now();
    console.log(`${logPrefix} Starting stats calculation`);

    try {
      // PHASE 1: Parallel data fetching with timeouts (from original)
      const fetchStartTime = Date.now();
      console.log(`${logPrefix} PHASE 1: Starting parallel data reads with optimized timeouts`);

      const [addressDoc, deliveriesSnapshot] = await Promise.all([
        // Fetch address document with timeout
        Promise.race([
          db.doc(`users/${input.userId}/user_addresses/${input.addressId}`).get(),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Address read timeout')), 25000))
        ]) as Promise<FirebaseFirestore.DocumentSnapshot>,
        // Fetch deliveries with timeout and ordering
        Promise.race([
          db.collection(`users/${input.userId}/user_deliveries`)
            .where('deliveryData.reference.addressId', '==', input.addressId)
            .orderBy('deliveryData.metadata.createdAt', 'desc')
            .limit(50) // Reduced limit for performance
            .get(),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Deliveries read timeout')), 30000))
        ]) as Promise<FirebaseFirestore.QuerySnapshot>
      ]);

      const fetchDuration = Date.now() - fetchStartTime;
      const deliveryCount = deliveriesSnapshot.size;
      const addressExists = addressDoc.exists;

      console.log(`${logPrefix} Data fetch completed in ${fetchDuration}ms - address: ${addressExists}, deliveries: ${deliveryCount}, truncated: ${deliveryCount >= 50}`);

      // Get existing order IDs and previous stats for delta calculation (from original logic)
      const currentAddress = addressDoc.exists ? addressDoc.data() : null;
      const existingOrderIds = currentAddress?.addressData?.orderIds || [];

      // Get previous stats for delta calculation
      const previousStats = currentAddress?.addressData?.deliveryStats || {
        deliveryCount: 0,
        tipCount: 0,
        totalTips: 0,
        pendingCount: 0
      };

      // ✅ NEW: Get previous DND status for delta tracking
      const previousDndStatus = currentAddress?.addressData?.flags?.doNotDeliver ?? false;
      const currentVersion = currentAddress?.addressData?.metadata?.version || 0;

      console.log(`${logPrefix} Found ${existingOrderIds.length} existing order IDs, previous stats:`, previousStats);
      console.log(`${logPrefix} Previous DND status: ${previousDndStatus}, current version: ${currentVersion}`);

      // Extract delivery data using schema types
      const deliveries: Delivery[] = deliveriesSnapshot.docs.map((doc: any) => ({
        id: doc.id,
        deliveryData: doc.data().deliveryData
      }));

      // Calculate enhanced stats using comprehensive aggregation
      const stats = calculateEnhancedStats(deliveries, existingOrderIds);

      // ✅ ENHANCED: Calculate comprehensive deltas for coordination
      const deltas = calculateStatsDeltas(stats, previousStats, previousDndStatus);
      const coordination = calculateCoordinationMetadata(deltas, currentVersion, 'stats_calculation');

      console.log(`${logPrefix} Calculated stats:`, stats);
      console.log(`${logPrefix} DELTA ANALYSIS:`, deltas);
      console.log(`${logPrefix} COORDINATION:`, coordination);

      // PHASE 3: Comprehensive transaction with address and user profile updates (from original)
      console.log(`${logPrefix} PHASE 3: Starting comprehensive transaction`);
      await db.runTransaction(async (transaction) => {
        const addressRef = db.doc(`users/${input.userId}/user_addresses/${input.addressId}`);
        const userProfileRef = db.doc(`users/${input.userId}`);

        // 1. Update address document with enhanced stats
        transaction.set(addressRef, {
          addressData: {
            deliveryStats: {
              deliveryCount: stats.deliveryCount,
              tipCount: stats.tipCount,
              totalTips: stats.totalTips,
              pendingCount: stats.pendingCount,
              averageTipAmount: stats.averageTipAmount,
              averageTimeMinutes: stats.averageTimeMinutes,
              highestTip: stats.highestTip,
              lastDeliveryTimestamp: stats.lastDeliveryTimestamp,
              // Enhanced stats from original
              totalBasePay: stats.totalBasePay,
              totalFinalPay: stats.totalFinalPay,
              averageDistance: stats.averageDistance,
              verifiedDeliveryCount: stats.verifiedDeliveryCount,
              cancelledDeliveryCount: stats.cancelledDeliveryCount,
              platformBreakdown: stats.platformBreakdown,
              currencyBreakdown: stats.currencyBreakdown
            },
            orderIds: stats.collectedOrderIds,
            metadata: {
              updatedAt: FieldValue.serverTimestamp()
            }
          }
        }, { merge: true });

        // ✅ ENHANCED: Update user profile with comprehensive coordination
        if (coordination.requiresUserProfileUpdate) {
          const userProfileUpdate = buildUserProfileUpdate(deltas, coordination);

          // ✅ NEW: Add version tracking for coordination
          userProfileUpdate['metadata.lastStatsUpdate'] = FieldValue.serverTimestamp();
          userProfileUpdate['metadata.lastStatsVersion'] = coordination.version + 1;

          transaction.update(userProfileRef, userProfileUpdate);
          console.log(`${logPrefix} Updated user profile with coordinated deltas:`, deltas);
        }
      });

      const totalDuration = Date.now() - startTime;
      console.log(`${logPrefix} Successfully completed stats calculation in ${totalDuration}ms - deliveries: ${stats.deliveryCount}, confirmed: ${stats.tipCount}, pending: ${stats.pendingCount}`);

      return {
        success: true,
        stats,
        deltas,
        coordination
      };

    } catch (error) {
      const totalDuration = Date.now() - startTime;
      console.error(`${logPrefix} Error calculating stats after ${totalDuration}ms:`, error);
      return {
        success: false,
        stats: getEmptyStats(),
        deltas: getEmptyDeltas(),
        coordination: getEmptyCoordination(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
);

/**
 * Enhanced stats calculation matching original address-stats-updater logic
 */
function calculateEnhancedStats(deliveries: Delivery[], existingOrderIds: string[] = []): EnhancedStats {
  let deliveryCount = 0;
  let tipCount = 0;
  let pendingCount = 0;
  let totalTips = 0;
  let totalBasePay = 0;
  let totalFinalPay = 0;
  let totalDeliveryTimeMinutes = 0;
  let deliveriesWithTime = 0;
  let totalDistance = 0;
  let deliveriesWithDistance = 0;
  let verifiedDeliveryCount = 0;
  let cancelledDeliveryCount = 0;
  let highestTip: number | null = null;
  let lastDeliveryTimestamp: Timestamp | null = null;

  const collectedOrderIds: string[] = [...existingOrderIds]; // Start with existing IDs
  const platformBreakdown: { [platform: string]: number } = {};
  const currencyBreakdown: { [currency: string]: number } = {};

  for (const delivery of deliveries) {
    const amounts = delivery.deliveryData?.amounts;
    const times = delivery.deliveryData?.times;
    const status = delivery.deliveryData?.status;
    const reference = delivery.deliveryData?.reference;
    const platform = delivery.deliveryData?.platform;

    deliveryCount++;

    // Collect order IDs (avoid duplicates)
    if (reference?.orderId && !collectedOrderIds.includes(reference.orderId)) {
      collectedOrderIds.push(reference.orderId);
    }

    // Platform breakdown
    if (platform?.name) {
      platformBreakdown[platform.name] = (platformBreakdown[platform.name] || 0) + 1;
    }

    // ✅ ENHANCED AMOUNTS AND CURRENCY TRACKING: Distinguish between pending and confirmed deliveries
    if (amounts) {
      const tipAmount = amounts.tipAmount;
      const basePay = amounts.basePay || 0;
      const finalPay = amounts.finalPay || 0;
      const currency = amounts.currencyCode || 'USD';
      const isCompleted = status?.isCompleted ?? false;

      // Currency breakdown
      currencyBreakdown[currency] = (currencyBreakdown[currency] || 0) + 1;

      // ✅ CRITICAL: Proper pending vs confirmed logic
      if (tipAmount != null && isCompleted) {
        // Confirmed delivery: tip amount is set AND delivery is completed
        tipCount++;
        totalTips += tipAmount;

        if (highestTip === null || tipAmount > highestTip) {
          highestTip = tipAmount;
        }
      } else if (tipAmount == null && isCompleted) {
        // Completed but no tip recorded yet - this is pending
        pendingCount++;
      } else if (tipAmount != null && !isCompleted) {
        // Tip set but delivery not completed - unusual case, treat as pending
        pendingCount++;
      }
      // If tipAmount == null && !isCompleted, it's an incomplete delivery (not counted in either)

      // Pay tracking
      totalBasePay += basePay;
      totalFinalPay += finalPay;
    }

    // Time calculations
    if (times?.completedAt && times?.acceptedAt) {
      const deliveryTimeMs = times.completedAt.toMillis() - times.acceptedAt.toMillis();
      const deliveryTimeMinutes = deliveryTimeMs / (1000 * 60);
      totalDeliveryTimeMinutes += deliveryTimeMinutes;
      deliveriesWithTime++;
    }

    // Distance calculations (using distanceMiles from amounts)
    if (amounts?.distanceMiles) {
      totalDistance += amounts.distanceMiles;
      deliveriesWithDistance++;
    }

    // Status tracking
    if (status?.isVerified) {
      verifiedDeliveryCount++;
    }
    if (status?.cancellationReason) {
      cancelledDeliveryCount++;
    }

    // Track latest delivery
    const completedAt = times?.completedAt;
    if (completedAt && (lastDeliveryTimestamp === null || completedAt > lastDeliveryTimestamp)) {
      lastDeliveryTimestamp = completedAt;
    }
  }

  const averageTipAmount = tipCount > 0 ? totalTips / tipCount : 0;
  const averageTimeMinutes = deliveriesWithTime > 0 ? totalDeliveryTimeMinutes / deliveriesWithTime : null;
  const averageDistance = deliveriesWithDistance > 0 ? totalDistance / deliveriesWithDistance : null;

  return {
    deliveryCount,
    tipCount,
    pendingCount,
    totalTips,
    averageTipAmount,
    highestTip,
    lastDeliveryTimestamp,
    averageTimeMinutes,
    collectedOrderIds,
    totalBasePay,
    totalFinalPay,
    averageDistance,
    verifiedDeliveryCount,
    cancelledDeliveryCount,
    platformBreakdown,
    currencyBreakdown
  };
}

/**
 * Get empty enhanced stats object
 */
function getEmptyStats(): EnhancedStats {
  return {
    deliveryCount: 0,
    tipCount: 0,
    pendingCount: 0,
    totalTips: 0,
    averageTipAmount: 0,
    highestTip: null,
    lastDeliveryTimestamp: null,
    averageTimeMinutes: null,
    collectedOrderIds: [],
    totalBasePay: 0,
    totalFinalPay: 0,
    averageDistance: null,
    verifiedDeliveryCount: 0,
    cancelledDeliveryCount: 0,
    platformBreakdown: {},
    currencyBreakdown: {}
  };
}

// ✅ NEW: Coordination helper functions

/**
 * Calculate comprehensive deltas for coordination
 */
function calculateStatsDeltas(
  newStats: EnhancedStats,
  previousStats: any,
  previousDndStatus: boolean
): StatsDeltas {
  const deliveryCountDelta = newStats.deliveryCount - (previousStats.deliveryCount || 0);
  const tipCountDelta = newStats.tipCount - (previousStats.tipCount || 0);
  const totalTipsDelta = newStats.totalTips - (previousStats.totalTips || 0);
  const pendingCountDelta = newStats.pendingCount - (previousStats.pendingCount || 0);

  // DND status changes will be determined by evaluate-address-dnd function
  // For now, we track if conditions might trigger DND changes
  const hasZeroTipDeliveries = newStats.tipCount > (previousStats.tipCount || 0) &&
    newStats.totalTips === (previousStats.totalTips || 0); // New tips but no tip amount increase = $0 tips

  return {
    deliveryCount: deliveryCountDelta,
    tipCount: tipCountDelta,
    totalTips: totalTipsDelta,
    pendingCount: pendingCountDelta,
    dndCreated: false, // Will be updated by DND evaluation
    dndRemoved: false  // Will be updated by DND evaluation
  };
}

/**
 * Calculate coordination metadata
 */
function calculateCoordinationMetadata(
  deltas: StatsDeltas,
  currentVersion: number,
  operationType: string
): CoordinationMetadata {
  const hasSignificantChanges = Math.abs(deltas.deliveryCount) > 0 ||
    Math.abs(deltas.tipCount) > 0 ||
    Math.abs(deltas.totalTips) > 0.01 ||
    Math.abs(deltas.pendingCount) > 0;

  const isFirstDeliveryToAddress = deltas.deliveryCount > 0 &&
    (deltas.deliveryCount === deltas.tipCount + deltas.pendingCount);

  return {
    hasSignificantChanges,
    isFirstDeliveryToAddress,
    requiresDndEvaluation: hasSignificantChanges || deltas.tipCount !== 0,
    requiresUserProfileUpdate: hasSignificantChanges || isFirstDeliveryToAddress,
    version: currentVersion + 1,
    operationType
  };
}

/**
 * Build user profile update object from deltas
 */
function buildUserProfileUpdate(deltas: StatsDeltas, coordination: CoordinationMetadata): any {
  const update: any = {
    'usage.lastUsageUpdate': FieldValue.serverTimestamp(),
    'metadata.updatedAt': FieldValue.serverTimestamp(),
    'metadata.version': FieldValue.increment(1)
  };

  // Apply deltas using FieldValue.increment
  if (deltas.deliveryCount !== 0) {
    update['usage.deliveryCount'] = FieldValue.increment(deltas.deliveryCount);
    update['usageStats.deliveryCount'] = FieldValue.increment(deltas.deliveryCount);
  }

  if (deltas.tipCount !== 0) {
    update['usageStats.tipCount'] = FieldValue.increment(deltas.tipCount);
  }

  if (deltas.totalTips !== 0) {
    update['usageStats.totalTips'] = FieldValue.increment(deltas.totalTips);
  }

  // Track address count for first delivery
  if (coordination.isFirstDeliveryToAddress) {
    update['usage.addressCount'] = FieldValue.increment(1);
    update['usageStats.addressCount'] = FieldValue.increment(1);
  }

  // Update daily tracking
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  update[`usageStats.featureUsage.daily_${today}`] = FieldValue.increment(1);
  update['usageStats.featureUsage.STATS_UPDATED'] = FieldValue.increment(1);

  return update;
}

/**
 * Get empty deltas object
 */
function getEmptyDeltas(): StatsDeltas {
  return {
    deliveryCount: 0,
    tipCount: 0,
    totalTips: 0,
    pendingCount: 0,
    dndCreated: false,
    dndRemoved: false
  };
}

/**
 * Get empty coordination object
 */
function getEmptyCoordination(): CoordinationMetadata {
  return {
    hasSignificantChanges: false,
    isFirstDeliveryToAddress: false,
    requiresDndEvaluation: false,
    requiresUserProfileUpdate: false,
    version: 0,
    operationType: 'unknown'
  };
}
