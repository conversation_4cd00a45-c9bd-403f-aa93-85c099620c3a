package com.autogratuity.data.model.util_kt

// Performance monitoring
import android.util.Log
import com.autogratuity.data.model.generated_kt.Address
import com.autogratuity.data.model.generated_kt.Address.AddressData
import com.autogratuity.data.model.generated_kt.Address.Components
import com.autogratuity.data.model.generated_kt.Address.SearchFields
import com.autogratuity.data.model.generated_kt.Amounts
import com.autogratuity.data.model.generated_kt.Coordinates
import com.autogratuity.data.model.generated_kt.Delivery
import com.autogratuity.data.model.generated_kt.Delivery.SimpleAddress
import com.autogratuity.data.model.generated_kt.Delivery_stats
import com.autogratuity.data.model.generated_kt.Flags
import com.autogratuity.data.model.generated_kt.Metadata
import com.autogratuity.data.model.generated_kt.Platform
import com.autogratuity.data.model.generated_kt.Reference
import com.autogratuity.data.model.generated_kt.Status
import com.autogratuity.data.model.generated_kt.Times
import com.autogratuity.data.model.generated_kt.User_profile
import com.google.firebase.Timestamp
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Date

// addressDataToMap function removed - was only used in commented-out/dead code
// that violated Clarity Architecture patterns (findOrCreateAddressTransactional method)
// If needed in the future, use AddressMapper.mapToDtoData() instead per architecture guidelines

/**
 * ✅ DEPRECATED: Use parseUniversalTimestamp() instead for better standardization
 * Kept for backward compatibility but delegates to the universal function.
 *
 * @param dateObj The date object from Firestore.
 * @return The parsed OffsetDateTime, or null if parsing fails or input is null.
 */
@Deprecated("Use parseUniversalTimestamp() for better standardization", ReplaceWith("parseUniversalTimestamp(dateObj)"))
fun parseOffsetDateTime(dateObj: Any?): OffsetDateTime? {
    return parseUniversalTimestamp(dateObj)
}

/**
 * ✅ CORRECTED: Converts a Firestore DocumentSnapshot to an Address DTO object (PURE FIRESTORE UTILITY).
 * This function follows clarity.md principles: Pure Firestore structure adaptation only.
 * No business logic, minimal monitoring, returns actual AddressDto.
 *
 * @param document The DocumentSnapshot to convert.
 * @return The converted Address DTO (actual DTO, not domain model).
 * @throws IllegalArgumentException if the document data is invalid or doesn't contain required fields.
 */
@Suppress("UNCHECKED_CAST")
fun documentSnapshotToAddressDtoOnly(document: com.google.firebase.firestore.DocumentSnapshot): Address {
    // ✅ PURE FIRESTORE UTILITY: Minimal processing, no business logic
    val docId = document.id
    val rawData = document.data

    if (rawData == null) {
        throw IllegalArgumentException("Document data is null for document: ${document.id}")
    }

    // ✅ PURE FIRESTORE UTILITY: Simple field extraction without business logic
    @Suppress("UNCHECKED_CAST")
    val addressDataMap = rawData["addressData"] as? Map<String, Any?>
    if (addressDataMap == null) {
        throw IllegalArgumentException("AddressData map ('addressData') is null or not found for document: ${document.id}")
    }

    // ✅ PURE FIRESTORE UTILITY: Simple nested field extraction
    @Suppress("UNCHECKED_CAST")
    fun getNestedMap(key: String): Map<String, Any?>? = addressDataMap[key] as? Map<String, Any?>

    val components = getNestedMap("components")?.let { componentsMap ->
        Components(
            streetNumber = componentsMap["streetNumber"] as? String,
            streetName = componentsMap["streetName"] as? String,
            city = componentsMap["city"] as? String,
            state = componentsMap["state"] as? String,
            postalCode = componentsMap["postalCode"] as? String,
            country = componentsMap["country"] as? String
        )
    }

    val coordinates = getNestedMap("coordinates")?.let { coordinatesMap ->
        Coordinates(
            latitude = coordinatesMap["latitude"] as? Double,
            longitude = coordinatesMap["longitude"] as? Double
        )
    }

    val searchFields = getNestedMap("searchFields")?.let { searchFieldsMap ->
        SearchFields(
            searchTerms = searchFieldsMap["searchTerms"] as? List<String>,
            normalizedKey = searchFieldsMap["normalizedKey"] as? String
        )
    }

    val deliveryStats = getNestedMap("deliveryStats")?.let {
        Delivery_stats(
            deliveryCount = it["deliveryCount"] as? Long,
            tipCount = it["tipCount"] as? Long,
            totalTips = it["totalTips"] as? Double,
            highestTip = it["highestTip"] as? Double,
            pendingCount = it["pendingCount"] as? Long,
            averageTimeMinutes = it["averageTimeMinutes"] as? Double,
            lastDeliveryDate = parseUniversalTimestamp(it["lastDeliveryDate"]),
            averageTipAmount = it["averageTipAmount"] as? Double,
            lastDeliveryTimestamp = parseUniversalTimestamp(it["lastDeliveryTimestamp"])
        )
    }

    val flags = getNestedMap("flags")?.let {
        Flags(
            isFavorite = it["isFavorite"] as? Boolean,
            isVerified = it["isVerified"] as? Boolean,
            doNotDeliver = it["doNotDeliver"] as? Boolean,
            dndSource = it["dndSource"] as? String,
            hasAccessIssues = it["hasAccessIssues"] as? Boolean,
            manualDndState = it["manualDndState"] as? String,
            isApartment = it["isApartment"] as? Boolean,
            isArchived = it["isArchived"] as? Boolean
        )
    }

    val metadata = getNestedMap("metadata")?.let {
        val customDataMap = it["customData"] as? Map<String, Any?>
        val customData = null // Fix: Don't instantiate empty CustomData class
        Metadata(
            createdAt = parseUniversalTimestamp(it["createdAt"]),
            updatedAt = parseUniversalTimestamp(it["updatedAt"]),
            importedAt = parseUniversalTimestamp(it["importedAt"]),
            source = it["source"] as? String,
            importId = it["importId"] as? String,
            captureId = it["captureId"] as? String,
            version = it["version"] as? Long,
            customData = customData
        )
    }

    val platform = getNestedMap("platform")?.let {
        Platform(
            name = it["name"] as? String,
            type = it["type"] as? String,
            version = it["version"] as? String,
            source = it["source"] as? String,
            displayName = it["displayName"] as? String,
            iconUrl = it["iconUrl"] as? String
        )
    }

    val addressDataInstance = AddressData(
        userId = addressDataMap["userId"] as? String,
        fullAddress = addressDataMap["fullAddress"] as? String,
        normalizedAddress = addressDataMap["normalizedAddress"] as? String,
        placeId = addressDataMap["placeId"] as? String,
        isDefault = addressDataMap["isDefault"] as? Boolean,
        notes = addressDataMap["notes"] as? String,
        tags = addressDataMap["tags"] as? List<String>,
        orderIds = addressDataMap["orderIds"] as? List<String>,
        searchTerms = addressDataMap["searchTerms"] as? List<String>,
        components = components,
        coordinates = coordinates,
        searchFields = searchFields,
        deliveryStats = deliveryStats,
        flags = flags,
        metadata = metadata,
        platform = platform
    )

    // ✅ PURE FIRESTORE UTILITY: Simple return without business logic
    return Address(id = docId, addressData = addressDataInstance)
}



/**
 * ✅ CORRECTED: Converts a Firestore DocumentSnapshot to a Delivery DTO object (PURE FIRESTORE UTILITY).
 * This function follows clarity.md principles: Pure Firestore structure adaptation only.
 * No business logic, minimal monitoring, returns actual DeliveryDto.
 *
 * @param document The DocumentSnapshot to convert.
 * @return The converted Delivery DTO (actual DTO, not domain model).
 * @throws IllegalArgumentException if the document data is invalid or doesn't contain required fields.
 */
@Suppress("UNCHECKED_CAST")
fun documentSnapshotToDeliveryDtoOnly(document: com.google.firebase.firestore.DocumentSnapshot): Delivery {
    // ✅ PURE FIRESTORE UTILITY: Minimal processing, no business logic
    val docId = document.id
    val rawData = document.data

    if (rawData == null) {
        throw IllegalArgumentException("Document data is null for document: ${document.id}")
    }

    // ✅ PURE FIRESTORE UTILITY: Simple field extraction without business logic
    @Suppress("UNCHECKED_CAST")
    val deliveryDataMap = rawData["deliveryData"] as? Map<String, Any?>
    if (deliveryDataMap == null) {
        throw IllegalArgumentException("DeliveryData map ('deliveryData') is null or not found for document: ${document.id}")
    }

    // ✅ PURE FIRESTORE UTILITY: Simple nested field extraction
    @Suppress("UNCHECKED_CAST")
    fun getNestedMap(key: String): Map<String, Any?>? = deliveryDataMap[key] as? Map<String, Any?>

    // Parse SimpleAddress
    val addressMap = getNestedMap("address")
    val simpleAddress = addressMap?.let {
        SimpleAddress(
            id = it["id"] as? String ?: "",
            fullAddress = it["fullAddress"] as? String ?: "",
            latitude = (it["latitude"] as? Number)?.toDouble() ?: 0.0,
            longitude = (it["longitude"] as? Number)?.toDouble() ?: 0.0,
            placeId = it["placeId"] as? String
        )
    } ?: SimpleAddress(id = "", fullAddress = "", latitude = 0.0, longitude = 0.0, placeId = null)

    // Parse Status
    val statusMap = getNestedMap("status")
    val status = statusMap?.let {
        Status(
            state = it["state"] as? String,
            isTipped = it["isTipped"] as? Boolean == true,
            isCompleted = it["isCompleted"] as? Boolean,
            isVerified = it["isVerified"] as? Boolean,
            doNotDeliver = it["doNotDeliver"] as? Boolean,
            cancellationReason = it["cancellationReason"] as? String,
            verificationSource = it["verificationSource"] as? String,
            verificationTimestamp = parseUniversalTimestamp(it["verificationTimestamp"]),
            dndReason = it["dndReason"] as? String
        )
    } ?: Status(
        state = "CREATED",
        isTipped = false,
        isCompleted = false,
        isVerified = false,
        doNotDeliver = false,
        cancellationReason = null,
        verificationSource = null,
        verificationTimestamp = null,
        dndReason = null
    )

    // Parse Times
    val timesMap = getNestedMap("times")
    val times = timesMap?.let {
        Times(
            acceptedAt = parseUniversalTimestamp(it["acceptedAt"]),
            completedAt = parseUniversalTimestamp(it["completedAt"]),
            tippedAt = parseUniversalTimestamp(it["tippedAt"])
        )
    } ?: Times(
        acceptedAt = null,
        completedAt = null,
        tippedAt = null
    )

    // Parse Amounts
    val amountsMap = getNestedMap("amounts")
    val amounts = amountsMap?.let {
        Amounts(
            basePay = it["basePay"] as? Double,
            tipAmount = it["tipAmount"] as? Double,
            tipPercentage = it["tipPercentage"] as? Double,
            totalAmount = it["totalAmount"] as? Double,
            estimatedPay = it["estimatedPay"] as? Double,
            finalPay = it["finalPay"] as? Double,
            distanceMiles = it["distanceMiles"] as? Double,
            currencyCode = it["currencyCode"] as? String
        )
    } ?: Amounts(
        basePay = 0.0,
        tipAmount = 0.0,
        tipPercentage = null,
        totalAmount = 0.0,
        estimatedPay = null,
        finalPay = null,
        distanceMiles = null,
        currencyCode = "USD"
    )

    // Parse Reference
    val referenceMap = getNestedMap("reference")
    val reference = referenceMap?.let {
        Reference(
            addressId = it["addressId"] as? String,
            orderId = it["orderId"] as? String,
            externalId = it["externalId"] as? String
        )
    } ?: Reference(
        addressId = null,
        orderId = null,
        externalId = null
    )

    // Parse Platform
    val platformMap = getNestedMap("platform")
    val platform = platformMap?.let {
        Platform(
            name = it["name"] as? String
        )
    } ?: Platform(
        name = "Shipt"
    )

    // Parse Metadata
    val metadataMap = getNestedMap("metadata")
    val metadata = metadataMap?.let {
        Metadata(
            createdAt = parseUniversalTimestamp(it["createdAt"]),
            updatedAt = parseUniversalTimestamp(it["updatedAt"]),
            version = it["version"] as? Long,
            source = it["source"] as? String,
            importId = it["importId"] as? String,
            importedAt = parseUniversalTimestamp(it["importedAt"]),
            captureId = it["captureId"] as? String,
            customData = null // Fix: Don't instantiate empty CustomData class
        )
    } ?: Metadata(
        createdAt = OffsetDateTime.now(),
        updatedAt = OffsetDateTime.now(),
        version = 1L,
        source = "manual",
        importId = null,
        importedAt = null,
        captureId = null,
        customData = null
    )

    // Parse Items (TODO: implement proper Item parsing when needed)
    // For now, set to null since we don't have a proper Item parser
    // val itemsList = deliveryDataMap["items"] as? List<Map<String, Any?>>

    val deliveryDataInstance = Delivery.DeliveryData(
        userId = deliveryDataMap["userId"] as? String ?: "",
        orderId = deliveryDataMap["orderId"] as? String ?: "",
        notes = deliveryDataMap["notes"] as? String,
        address = simpleAddress,
        status = status,
        times = times,
        amounts = amounts,
        reference = reference,
        platform = platform,
        items = null, // TODO: implement proper Item parsing
        metadata = metadata
    )

    // ✅ PURE FIRESTORE UTILITY: Simple return without business logic
    return Delivery(id = docId, deliveryData = deliveryDataInstance)
}



// Helper to add to map only if value is not null
fun <K, V> MutableMap<K, V>.putIfValueNotNull(key: K, value: V?) {
    value?.let { put(key, it) }
}

fun Metadata.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("createdAt", this.createdAt?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("updatedAt", this.updatedAt?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("version", this.version)
    map.putIfValueNotNull("source", this.source)
    map.putIfValueNotNull("importId", this.importId)
    map.putIfValueNotNull("importedAt", this.importedAt?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("captureId", this.captureId)
    map.putIfValueNotNull("customData", this.customData)
    return map
}

fun Flags.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("isFavorite", this.isFavorite)
    map.putIfValueNotNull("isVerified", this.isVerified)
    map.putIfValueNotNull("doNotDeliver", this.doNotDeliver)
    map.putIfValueNotNull("dndSource", this.dndSource)
    map.putIfValueNotNull("hasAccessIssues", this.hasAccessIssues)
    map.putIfValueNotNull("manualDndState", this.manualDndState)
    return map
}

fun Delivery_stats.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("deliveryCount", this.deliveryCount)
    map.putIfValueNotNull("tipCount", this.tipCount)
    map.putIfValueNotNull("totalTips", this.totalTips)
    map.putIfValueNotNull("highestTip", this.highestTip)
    map.putIfValueNotNull("pendingCount", this.pendingCount)
    map.putIfValueNotNull("averageTimeMinutes", this.averageTimeMinutes)
    map.putIfValueNotNull("lastDeliveryDate", this.lastDeliveryDate?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("averageTipAmount", this.averageTipAmount)
    map.putIfValueNotNull("lastDeliveryTimestamp", this.lastDeliveryTimestamp)
    return map
}

fun SearchFields.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("searchTerms", this.searchTerms)
    map.putIfValueNotNull("normalizedKey", this.normalizedKey)
    return map
}

fun Coordinates.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("latitude", this.latitude)
    map.putIfValueNotNull("longitude", this.longitude)
    return map
}

fun Components.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("streetNumber", this.streetNumber)
    map.putIfValueNotNull("streetName", this.streetName)
    map.putIfValueNotNull("city", this.city)
    map.putIfValueNotNull("state", this.state)
    map.putIfValueNotNull("postalCode", this.postalCode)
    map.putIfValueNotNull("country", this.country)
    return map
}



/**
 * Converts a Java Date to OffsetDateTime.
 * Extracted from DeliveryModelTransformations for centralized utility access.
 *
 * @param date The Date to convert.
 * @return The equivalent OffsetDateTime, or null if date is null.
 */
fun dateToOffsetDateTime(date: Date?): OffsetDateTime? {
    return date?.toInstant()?.atZone(ZoneId.systemDefault())?.toOffsetDateTime()
}

/**
 * Converts an OffsetDateTime to Java Date.
 * Extracted from DeliveryModelTransformations for centralized utility access.
 *
 * @param offsetDateTime The OffsetDateTime to convert.
 * @return The equivalent Date, or null if offsetDateTime is null.
 */
fun offsetDateTimeToDate(offsetDateTime: OffsetDateTime?): Date? {
    return offsetDateTime?.toInstant()?.let { Date.from(it) }
}

/**
 * ✅ STANDARDIZED: Universal timestamp parsing function for all components
 * This function should be used by all mappers, repositories, and utilities
 * to ensure consistent timestamp handling across the entire codebase.
 *
 * @param dateObj Any timestamp object from Firestore or other sources
 * @return Parsed OffsetDateTime or null if parsing fails
 */
fun parseUniversalTimestamp(dateObj: Any?): OffsetDateTime? {
    if (dateObj == null) return null
    return when (dateObj) {
        is Timestamp -> dateObj.toDate().toInstant().atZone(ZoneOffset.UTC).toOffsetDateTime()
        is String -> try {
            // ✅ CRITICAL FIX: Handle all ISO string formats properly
            when {
                dateObj.contains("T") && dateObj.contains("Z") -> {
                    // Parse as instant and convert to OffsetDateTime
                    val instant = java.time.Instant.parse(dateObj)
                    instant.atZone(ZoneOffset.UTC).toOffsetDateTime()
                }
                dateObj.contains("T") && (dateObj.contains("+") || dateObj.endsWith("Z") || dateObj.matches(Regex(".*[+-]\\d{2}:\\d{2}$"))) -> {
                    OffsetDateTime.parse(dateObj, DateTimeFormatter.ISO_OFFSET_DATE_TIME)
                }
                dateObj.contains("T") -> {
                    // Parse local datetime and assume UTC timezone - try multiple formatters
                    try {
                        val localDateTime = java.time.LocalDateTime.parse(dateObj)
                        localDateTime.atOffset(ZoneOffset.UTC)
                    } catch (_: java.time.format.DateTimeParseException) {
                        // Fallback: try parsing as instant with Z suffix
                        val instant = java.time.Instant.parse(dateObj + "Z")
                        instant.atZone(ZoneOffset.UTC).toOffsetDateTime()
                    }
                }
                else -> OffsetDateTime.parse(dateObj)
            }
        } catch (e: java.time.format.DateTimeParseException) {
            Log.w("DtoUtils", "Failed to parse date string '$dateObj' to OffsetDateTime: ${e.message}")
            null
        }
        is Date -> dateObj.toInstant().atZone(ZoneOffset.UTC).toOffsetDateTime()
        is Map<*, *> -> {
            // ✅ FIX: Handle both OffsetDateTime structure and Firestore Timestamp maps
            try {
                // 1) Handle serialized OffsetDateTime from Android model (complex structure)
                val year = dateObj["year"] as? Number
                val monthValue = dateObj["monthValue"] as? Number
                val dayOfMonth = dateObj["dayOfMonth"] as? Number

                if (year != null && monthValue != null && dayOfMonth != null) {
                    Log.d("DtoUtils", "Parsing OffsetDateTime structure: year=${year}, month=${monthValue}, day=${dayOfMonth}")

                    val hour = (dateObj["hour"] as? Number)?.toInt() ?: 0
                    val minute = (dateObj["minute"] as? Number)?.toInt() ?: 0
                    val second = (dateObj["second"] as? Number)?.toInt() ?: 0
                    val nano = (dateObj["nano"] as? Number)?.toInt() ?: 0

                    // Handle offset (default to UTC if missing)
                    val offsetMap = dateObj["offset"] as? Map<*, *>
                    val offsetSec = (offsetMap?.get("totalSeconds") as? Number)?.toLong() ?: 0L
                    val zoneOffset = ZoneOffset.ofTotalSeconds(offsetSec.toInt())

                    val result = OffsetDateTime.of(
                        year.toInt(),
                        monthValue.toInt(), // Java OffsetDateTime months are 1-based, keep as-is
                        dayOfMonth.toInt(),
                        hour, minute, second, nano,
                        zoneOffset
                    )

                    Log.d("DtoUtils", "Successfully parsed OffsetDateTime: $result")
                    return result
                }

                // 2) Handle Firestore Timestamp represented as Map (original logic)
                val seconds = dateObj["_seconds"] as? Long ?: dateObj["seconds"] as? Long
                // ✅ CRITICAL FIX: Handle both Int and Long for nanoseconds (test compatibility)
                val nanoseconds = (dateObj["_nanoseconds"] as? Number)?.toLong()
                    ?: (dateObj["nanoseconds"] as? Number)?.toLong()
                    ?: 0L

                if (seconds != null) {
                    // ✅ CRITICAL FIX: Properly handle nanoseconds conversion
                    // Firestore nanoseconds are already in the correct format (0-999,999,999)
                    val instant = java.time.Instant.ofEpochSecond(seconds, nanoseconds)
                    instant.atZone(ZoneOffset.UTC).toOffsetDateTime()
                } else {
                    Log.w("DtoUtils", "HashMap does not contain valid Firestore Timestamp data: $dateObj")
                    null
                }
            } catch (e: Exception) {
                Log.w("DtoUtils", "Failed to parse timestamp from HashMap: ${e.message}, data: $dateObj")
                null
            }
        }
        is OffsetDateTime -> dateObj // Already the correct type
        is Long -> {
            // Handle epoch milliseconds
            try {
                java.time.Instant.ofEpochMilli(dateObj).atZone(ZoneOffset.UTC).toOffsetDateTime()
            } catch (e: Exception) {
                Log.w("DtoUtils", "Failed to parse Long as epoch milliseconds: ${e.message}")
                null
            }
        }
        else -> {
            Log.w("DtoUtils", "Unsupported date type for OffsetDateTime conversion: ${dateObj.javaClass.name}")
            null
        }
    }
}

/**
 * Converts a Firestore DocumentSnapshot to a UserProfile DTO object.
 * Assumes the snapshot contains data structured for a User_profile.
 *
 * @param document The DocumentSnapshot to convert.
 * @return The converted UserProfile DTO.
 * @throws IllegalArgumentException if the document data is invalid or doesn't contain required fields.
 */
@Suppress("UNCHECKED_CAST")
fun documentSnapshotToUserProfileDto(document: com.google.firebase.firestore.DocumentSnapshot): User_profile {
    val docId = document.id
    val rawData = document.data

    if (rawData == null) {
        val errorMsg = "Document data is null for user profile document: ${document.id}"
        Log.e("DtoUtils", errorMsg)
        throw IllegalArgumentException(errorMsg)
    }

    // ENHANCED: Extract profileData from nested Firestore structure
    // Firestore structure: { profileData: { userId, email, subscription, ... } }
    val profileData = rawData["profileData"] as? Map<String, Any?>
    if (profileData == null) {
        val errorMsg = "ProfileData is null or missing in document: ${document.id}. Expected nested structure with 'profileData' field."
        Log.e("DtoUtils", errorMsg)
        Log.e("DtoUtils", "Available top-level fields: ${rawData.keys}")
        throw IllegalArgumentException(errorMsg)
    }

    Log.d("DtoUtils", "Successfully extracted profileData from document $docId")
    Log.d("DtoUtils", "ProfileData fields: ${profileData.keys}")

    fun getNestedMap(key: String): Map<String, Any?>? {
        return profileData[key] as? Map<String, Any?>
    }

    // Parse UserSubscription
    val subscriptionMap = getNestedMap("subscription")
    val subscription = subscriptionMap?.let {
        val verificationMap = it["verification"] as? Map<String, Any?>
        val verification = verificationMap?.let { vm ->
            User_profile.Verification(
                lastVerified = parseUniversalTimestamp(vm["lastVerified"]),
                status = vm["status"] as? String,
                error = vm["error"] as? String
            )
        }

        User_profile.UserSubscription(
            status = it["status"] as? String,
            level = it["level"] as? String,
            isActive = it["isActive"] as? Boolean ?: it["active"] as? Boolean,
            startDate = parseUniversalTimestamp(it["startDate"]),
            expiryDate = parseUniversalTimestamp(it["expiryDate"]),
            isLifetime = it["isLifetime"] as? Boolean ?: it["lifetime"] as? Boolean,
            provider = it["provider"] as? String,
            orderId = it["orderId"] as? String,
            verification = verification
        )
    }

    // Parse UserPreferences
    val preferencesMap = getNestedMap("preferences")
    val preferences = preferencesMap?.let {
        val dndMap = it["dnd"] as? Map<String, Any?>
        val dnd = dndMap?.let { dm ->
            val customRuleMap = dm["customRule"] as? Map<String, Any?>
            val customRule = customRuleMap?.let { crm ->
                val comparisonTypeStr = crm["comparisonType"] as? String ?: "less_than_or_equal_to"
                val comparisonType = when (comparisonTypeStr) {
                    "less_than" -> User_profile.ComparisonType.less_than
                    "equal_to" -> User_profile.ComparisonType.equal_to
                    else -> User_profile.ComparisonType.less_than_or_equal_to
                }
                User_profile.CustomRule(
                    isEnabled = crm["isEnabled"] as? Boolean == true,
                    tipAmountThreshold = crm["tipAmountThreshold"] as? Double ?: 0.0,
                    comparisonType = comparisonType
                )
            }
            User_profile.Dnd(customRule = customRule)
        }

        User_profile.UserPreferences(
            notificationsEnabled = it["notificationsEnabled"] as? Boolean,
            theme = it["theme"] as? String,
            useLocation = it["useLocation"] as? Boolean,
            dnd = dnd
        )
    }

    // Parse UserPermissions
    val permissionsMap = getNestedMap("permissions")
    val permissions = permissionsMap?.let {
        User_profile.UserPermissions(
            bypassLimits = it["bypassLimits"] as? Boolean,
            maxUploads = it["maxUploads"] as? Long
        )
    }

    // Parse UserUsage
    val usageMap = getNestedMap("usage")
    val usage = usageMap?.let {
        User_profile.UserUsage(
            mappingCount = it["mappingCount"] as? Long,
            deliveryCount = it["deliveryCount"] as? Long,
            addressCount = it["addressCount"] as? Long,
            lastUsageUpdate = parseUniversalTimestamp(it["lastUsageUpdate"])
        )
    }

    // Parse UserSyncInfo
    val syncInfoMap = getNestedMap("syncInfo")
    val syncInfo = syncInfoMap?.let {
        User_profile.UserSyncInfo(
            lastSyncTime = parseUniversalTimestamp(it["lastSyncTime"]),
            deviceIds = it["deviceIds"] as? List<String>,
            version = it["version"] as? Long
        )
    }

    // Parse UserAppSettings
    val appSettingsMap = getNestedMap("appSettings")
    val appSettings = appSettingsMap?.let {
        User_profile.UserAppSettings(
            dataCollectionOptIn = it["dataCollectionOptIn"] as? Boolean,
            lastVersion = it["lastVersion"] as? String,
            onboardingCompleted = it["onboardingCompleted"] as? Boolean
        )
    }

    // Parse UserCommunication
    val communicationMap = getNestedMap("communication")
    val communication = communicationMap?.let {
        User_profile.UserCommunication(
            emailOptIn = it["emailOptIn"] as? Boolean,
            marketingOptIn = it["marketingOptIn"] as? Boolean,
            pushNotificationsEnabled = it["pushNotificationsEnabled"] as? Boolean
        )
    }

    // Parse UserUsageStats
    val usageStatsMap = getNestedMap("usageStats")
    val usageStats = usageStatsMap?.let {
        // FeatureUsage is an open class - handle as needed
        val featureUsage = it["featureUsage"]?.let { User_profile.FeatureUsage() }

        User_profile.UserUsageStats(
            deliveryCount = it["deliveryCount"] as? Long,
            addressCount = it["addressCount"] as? Long,
            lastUsageDate = parseUniversalTimestamp(it["lastUsageDate"]),
            totalRuns = it["totalRuns"] as? Long,
            activeDaysCount = it["activeDaysCount"] as? Long,
            totalTips = it["totalTips"] as? Double,
            featureUsage = featureUsage
        )
    }

    // Parse Metadata
    val metadataMap = getNestedMap("metadata")
    val metadata = metadataMap?.let {
        val customDataMap = it["customData"] as? Map<String, Any?>
        val customData = null // Fix: Don't instantiate empty CustomData class
        Metadata(
            createdAt = parseUniversalTimestamp(it["createdAt"]),
            updatedAt = parseUniversalTimestamp(it["updatedAt"]),
            importedAt = parseUniversalTimestamp(it["importedAt"]),
            source = it["source"] as? String,
            importId = it["importId"] as? String,
            captureId = it["captureId"] as? String,
            version = it["version"] as? Long,
            customData = customData
        )
    }

    return User_profile(
        userId = profileData["userId"] as? String ?: docId,
        email = profileData["email"] as? String,
        displayName = profileData["displayName"] as? String,
        defaultAddressId = profileData["defaultAddressId"] as? String,
        photoUrl = profileData["photoUrl"] as? String,
        authProviders = profileData["authProviders"] as? List<String> ?: emptyList(),
        accountStatus = profileData["accountStatus"] as? String,
        timezone = profileData["timezone"] as? String,
        createdAt = parseUniversalTimestamp(profileData["createdAt"]),
        lastLoginAt = parseUniversalTimestamp(profileData["lastLoginAt"]),
        privacyPolicyAccepted = parseUniversalTimestamp(profileData["privacyPolicyAccepted"]),
        termsAccepted = parseUniversalTimestamp(profileData["termsAccepted"]),
        subscription = subscription,
        preferences = preferences,
        permissions = permissions,
        usage = usage,
        syncInfo = syncInfo,
        appSettings = appSettings,
        communication = communication,
        usageStats = usageStats,
        metadata = metadata,
        version = profileData["version"] as? Long
    )
}

/**
 * Wraps a User_profile DTO in the profileData structure expected by Firestore.
 * Converts flat DTO structure to nested Firestore document structure.
 * 
 * @param userProfileDto The flat User_profile DTO to wrap
 * @return Map with profileData wrapper: { profileData: { userId, email, ... } }
 */
/**
 * 🚀 FIRESTORE SERIALIZATION FIX: Convert complex DTO to simple Map structure
 * Based on legacy Java approach that worked - converts complex Kotlin objects to primitive types
 * that Firestore can serialize without hanging.
 */
fun wrapUserProfileDtoForFirestore(userProfileDto: User_profile): Map<String, Any?> {
    Log.d("DtoUtils", "Converting complex DTO to simple Map structure for Firestore serialization")
    
    // Convert complex DTO to simple Map with primitive types only
    val profileDataMap = mutableMapOf<String, Any?>()
    
    // 🔑 CRITICAL: Always include userId - required by Firestore security rules
    profileDataMap["userId"] = userProfileDto.userId
    
    // Simple string fields (already primitives)
    profileDataMap["email"] = userProfileDto.email
    profileDataMap["displayName"] = userProfileDto.displayName
    profileDataMap["defaultAddressId"] = userProfileDto.defaultAddressId
    profileDataMap["photoUrl"] = userProfileDto.photoUrl
    profileDataMap["accountStatus"] = userProfileDto.accountStatus
    profileDataMap["timezone"] = userProfileDto.timezone
    
    // 📋 Simple list/array fields
    profileDataMap["authProviders"] = userProfileDto.authProviders
    
    // 📅 Date fields - convert to proper format for Firestore
    profileDataMap["createdAt"] = userProfileDto.createdAt?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    profileDataMap["lastLoginAt"] = userProfileDto.lastLoginAt?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    profileDataMap["privacyPolicyAccepted"] = userProfileDto.privacyPolicyAccepted?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    profileDataMap["termsAccepted"] = userProfileDto.termsAccepted?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    
    // ��️ Complex objects - use existing toMap() extensions
    profileDataMap["subscription"] = userProfileDto.subscription?.toMap()
    profileDataMap["preferences"] = userProfileDto.preferences?.toMap()
    profileDataMap["permissions"] = userProfileDto.permissions?.toMap()
    profileDataMap["usage"] = userProfileDto.usage?.toMap()
    profileDataMap["syncInfo"] = userProfileDto.syncInfo?.toMap()
    profileDataMap["appSettings"] = userProfileDto.appSettings?.toMap()
    profileDataMap["communication"] = userProfileDto.communication?.toMap()
    profileDataMap["usageStats"] = userProfileDto.usageStats?.toMap()
    profileDataMap["metadata"] = userProfileDto.metadata?.toMap()
    
    // 🔢 Simple number field
    profileDataMap["version"] = userProfileDto.version
    
    Log.d("DtoUtils", "Converted to simple Map - userId: ${profileDataMap["userId"]}, fields: ${profileDataMap.size}")
    
    return mapOf("profileData" to profileDataMap)
}

/**
 * Wraps User_profile DTO fields for partial Firestore updates.
 * Prefixes all field paths with 'profileData.' for nested structure.
 * 
 * @param fields Map of field names to values for updating
 * @return Map with profileData-prefixed field paths
 */
fun wrapUserProfileFieldsForFirestore(fields: Map<String, Any>): Map<String, Any> {
    Log.d("DtoUtils", "Wrapping ${fields.size} fields with profileData prefix for Firestore update")
    
    return fields.mapKeys { (key, _) -> "profileData.$key" }
}

/**
 * 🚫 LEGACY FUNCTION - COMMENTED OUT BUT PRESERVED FOR REFERENCE
 *
 * ORIGINAL PURPOSE:
 * Converts a UserProfile DTO to a flat Map<String, Any?> for Firestore operations.
 * This was useful when Firestore documents had a flat structure without nested wrappers.
 *
 * WHY SUPERSEDED:
 * 1. FIRESTORE STRUCTURE EVOLUTION: App now uses nested structure { profileData: { ... } }
 * 2. ARCHITECTURAL COMPLIANCE: Current Clarity Architecture uses specialized functions:
 *    - wrapUserProfileDtoForFirestore() - for complete document writes with nested structure
 *    - wrapUserProfileFieldsForFirestore() - for partial updates with field prefixing
 * 3. BACKEND COMPATIBILITY: TypeScript backend expects nested profileData structure
 * 4. FIRESTORE SERIALIZATION: Current approach handles complex object serialization better
 *
 * POTENTIAL FUTURE USE CASES:
 * - Data export functionality requiring flat maps
 * - Testing utilities that need simplified structure
 * - Migration scripts for legacy data formats
 * - Alternative Firestore schemas (if architecture changes)
 *
 * REPLACEMENT FUNCTIONS:
 * - For Firestore writes: Use wrapUserProfileDtoForFirestore(userProfile)
 * - For Firestore updates: Use wrapUserProfileFieldsForFirestore(fields)
 * - For nested objects: Use individual .toMap() extension functions
 *
 * @param userProfile The UserProfile DTO to convert.
 * @return A flat map representation of the UserProfile, or null if the input is null.
 */
/*
fun userProfileDataToMap(userProfile: User_profile?): Map<String, Any?>? {
    if (userProfile == null) return null
    val map = mutableMapOf<String, Any?>()

    map.putIfValueNotNull("userId", userProfile.userId)
    map.putIfValueNotNull("email", userProfile.email)
    map.putIfValueNotNull("displayName", userProfile.displayName)
    map.putIfValueNotNull("defaultAddressId", userProfile.defaultAddressId)
    map.putIfValueNotNull("photoUrl", userProfile.photoUrl)
    map.putIfValueNotNull("authProviders", userProfile.authProviders)
    map.putIfValueNotNull("accountStatus", userProfile.accountStatus)
    map.putIfValueNotNull("timezone", userProfile.timezone)
    map.putIfValueNotNull("createdAt", userProfile.createdAt?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("lastLoginAt", userProfile.lastLoginAt?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("privacyPolicyAccepted", userProfile.privacyPolicyAccepted?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("termsAccepted", userProfile.termsAccepted?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("version", userProfile.version)
    map.putIfValueNotNull("subscription", userProfile.subscription?.toMap())
    map.putIfValueNotNull("preferences", userProfile.preferences?.toMap())
    map.putIfValueNotNull("permissions", userProfile.permissions?.toMap())
    map.putIfValueNotNull("usage", userProfile.usage?.toMap())
    map.putIfValueNotNull("syncInfo", userProfile.syncInfo?.toMap())
    map.putIfValueNotNull("appSettings", userProfile.appSettings?.toMap())
    map.putIfValueNotNull("communication", userProfile.communication?.toMap())
    map.putIfValueNotNull("usageStats", userProfile.usageStats?.toMap())
    map.putIfValueNotNull("metadata", userProfile.metadata?.toMap())

    return map
}
*/

// UserProfile DTO extension functions for toMap()
fun User_profile.UserSubscription.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("status", this.status)
    map.putIfValueNotNull("level", this.level)
    map.putIfValueNotNull("isActive", this.isActive)
    map.putIfValueNotNull("startDate", this.startDate?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("expiryDate", this.expiryDate?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("isLifetime", this.isLifetime)
    map.putIfValueNotNull("provider", this.provider)
    map.putIfValueNotNull("orderId", this.orderId)
    map.putIfValueNotNull("verification", this.verification?.toMap())
    return map
}

fun User_profile.Verification.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("lastVerified", this.lastVerified?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("status", this.status)
    map.putIfValueNotNull("error", this.error)
    return map
}

fun User_profile.UserPreferences.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("notificationsEnabled", this.notificationsEnabled)
    map.putIfValueNotNull("theme", this.theme)
    map.putIfValueNotNull("useLocation", this.useLocation)
    map.putIfValueNotNull("dnd", this.dnd?.toMap())
    return map
}

fun User_profile.Dnd.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("customRule", this.customRule?.toMap())
    return map
}

fun User_profile.CustomRule.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("isEnabled", this.isEnabled)
    map.putIfValueNotNull("tipAmountThreshold", this.tipAmountThreshold)
    map.putIfValueNotNull("comparisonType", this.comparisonType.name)
    return map
}

fun User_profile.UserPermissions.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("bypassLimits", this.bypassLimits)
    map.putIfValueNotNull("maxUploads", this.maxUploads)
    return map
}

fun User_profile.UserUsage.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("mappingCount", this.mappingCount)
    map.putIfValueNotNull("deliveryCount", this.deliveryCount)
    map.putIfValueNotNull("addressCount", this.addressCount)
    map.putIfValueNotNull("lastUsageUpdate", this.lastUsageUpdate?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    return map
}

fun User_profile.UserSyncInfo.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("lastSyncTime", this.lastSyncTime?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("deviceIds", this.deviceIds)
    map.putIfValueNotNull("version", this.version)
    return map
}

fun User_profile.UserAppSettings.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("dataCollectionOptIn", this.dataCollectionOptIn)
    map.putIfValueNotNull("lastVersion", this.lastVersion)
    map.putIfValueNotNull("onboardingCompleted", this.onboardingCompleted)
    return map
}

fun User_profile.UserCommunication.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("emailOptIn", this.emailOptIn)
    map.putIfValueNotNull("marketingOptIn", this.marketingOptIn)
    map.putIfValueNotNull("pushNotificationsEnabled", this.pushNotificationsEnabled)
    return map
}

fun User_profile.UserUsageStats.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    map.putIfValueNotNull("deliveryCount", this.deliveryCount)
    map.putIfValueNotNull("addressCount", this.addressCount)
    map.putIfValueNotNull("lastUsageDate", this.lastUsageDate?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
    map.putIfValueNotNull("totalRuns", this.totalRuns)
    map.putIfValueNotNull("activeDaysCount", this.activeDaysCount)
    map.putIfValueNotNull("totalTips", this.totalTips)
    map.putIfValueNotNull("featureUsage", this.featureUsage) // FeatureUsage is open class, handle as-is
    return map
}

