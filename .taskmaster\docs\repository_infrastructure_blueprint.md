# Repository Infrastructure Blueprint

## Overview
This document provides the complete technical blueprint for modernizing repository implementations to match the SubscriptionRepositoryImpl.kt gold standard. All infrastructure components have been analyzed and their integration patterns documented.

## Infrastructure Components

### 1. CacheWarmingManager
**Location:** `data/util/CacheWarmingManager.kt`

**Purpose:** Prevents duplicate requests and provides performance optimization through coordinated cache warming.

**Constructor Pattern:**
```kotlin
CacheWarmingManager(
    private val userRepository: UserRepository,
    private val subscriptionRepository: Lazy<SubscriptionRepository>, // Prevents circular dependency
    private val addressRepository: AddressRepository,
    private val requestDeduplicationManager: RequestDeduplicationManager,
    private val ioDispatcher: CoroutineDispatcher
)
```

**Key Method:**
```kotlin
suspend fun warmCriticalData(userId: String): WarmingResult
```

**Integration Notes:**
- Uses `Lazy<SubscriptionRepository>` to prevent circular DI dependencies
- Integrates with RequestDeduplicationManager for operation deduplication
- Timeout configuration: 8s overall, 5s user/subscription, 3s addresses

### 2. AuthenticationStateCoordinator
**Location:** `data/util/AuthenticationStateCoordinator.kt`

**Purpose:** Coordinates authentication state with existing AuthenticationManager, provides centralized auth readiness checking.

**Constructor Pattern:**
```kotlin
AuthenticationStateCoordinator(
    private val authenticationManager: AuthenticationManager,
    private val scope: CoroutineScope
)
```

**Key Methods:**
```kotlin
suspend fun waitForAuthentication(timeoutMs: Long = 5000): AuthReadyState
fun getCurrentUserIdSafe(): String?
```

**Reactive Properties:**
```kotlin
val isReady: StateFlow<Boolean>
val currentUserId: StateFlow<String?>
val authReadyState: StateFlow<AuthReadyState>
```

### 3. ValidationEngine
**Location:** `data/util/ValidationEngine.kt`

**Purpose:** Core data validation system with domain-specific business rules.

**Key Features:**
- Stateless validation functions
- Lenient validation during user creation vs strict during updates
- Business rule validation (tip amounts, coordinates, order IDs)

**Key Methods:**
```kotlin
fun validateUser(user: User, isCreation: Boolean = false): ValidationResult
fun validateDelivery(delivery: Delivery): ValidationResult
fun validateAddress(address: Address): ValidationResult
fun validateDeliveries(deliveries: List<Delivery>): BulkValidationResult
```

### 4. RequestDeduplicationManager
**Location:** `data/util/RequestDeduplicationManager.kt`

**Purpose:** Prevents duplicate Firestore operations that cause 8+ second delays.

**Key Features:**
- Thread-safe concurrent operations using ConcurrentHashMap
- Automatic cleanup of completed requests
- Timeout handling with comprehensive monitoring

**Key Method:**
```kotlin
suspend fun <T> deduplicateRequest(
    key: String,
    timeout: Duration = 5.seconds,
    operation: suspend () -> T
): T?
```

**Standard Operation Keys:**
```kotlin
object RequestKeys {
    fun userProfile(userId: String) = "users/$userId"
    fun userSubscription(userId: String) = "users/$userId/subscription"
    fun userAddresses(userId: String) = "addresses/user/$userId"
    // ... more keys
}
```

### 5. SessionManager
**Location:** `data/util/SessionManager.kt`

**Purpose:** Prevents duplicate monitoring sessions and manages session lifecycle.

**Constructor Pattern:**
```kotlin
SessionManager(
    private val clarityArchitectureMonitor: ClarityArchitectureMonitor,
    private val requestDeduplicationManager: RequestDeduplicationManager,
    private val applicationScope: CoroutineScope
)
```

**Key Method:**
```kotlin
suspend fun getOrCreateSession(userId: String): MonitoringSession
```

### 6. RepositoryErrorHandler
**Location:** `data/repository/core/RepositoryErrorHandler.kt`

**Purpose:** Modern error handling with intelligent retry logic and Flow<Result<T>> patterns.

**Key Methods:**
```kotlin
fun <T> handleFlow(flow: Flow<T>, operationName: String, entityType: String?): Flow<Result<T>>
fun <T> handleFlowWithRetry(flow: Flow<T>, operationName: String, entityType: String?): Flow<Result<T>>
suspend fun <T> handleSuspendFunction(operationName: String, entityType: String?, block: suspend () -> T): Result<T>
suspend fun <T> handleSuspendFunctionWithRetry(operationName: String, entityType: String?, block: suspend () -> T): Result<T>
```

**Features:**
- Exponential backoff with jitter
- Comprehensive error categorization
- StateFlow error monitoring
- Retry statistics tracking

## DI Configuration Patterns

### DataModule.kt - Repository Injection
```kotlin
single<TargetRepository> {
    TargetRepositoryImpl(
        remoteDataSource = get(),
        localDataSource = get(),
        targetMapper = get(),
        authManager = get(),
        ioDispatcher = get(named(KoinQualifiers.IO_DISPATCHER)),
        // Infrastructure components from SubscriptionRepositoryImpl pattern:
        requestDeduplicationManager = get(),
        priorityTaskScheduler = get(),        // Note: ModernPriorityTaskScheduler in CoreModule
        sessionManager = get(),
        cacheWarmingManager = get(),
        authStateCoordinator = get(),
        repositoryErrorHandler = get()
    )
}
```

### CoreModule.kt - Infrastructure Providers
Essential services provided:
- `AuthenticationManager` / `AuthenticationManagerImpl`
- `ValidationEngine`
- `ModernPriorityTaskScheduler` (referenced as `priorityTaskScheduler` in repositories)
- `EncryptionUtils`
- `TimeSource`
- `WorkManager`

### CacheModule.kt - Cache Systems
- Individual cache systems per repository type
- `CacheLifecycleManager` for coordination

## Repository Modernization Checklist

### Constructor Pattern
```kotlin
class TargetRepositoryImpl(
    private val remoteDataSource: TargetRemoteDataSource,
    private val localDataSource: TargetLocalDataSource,
    private val targetMapper: TargetMapper,
    private val authManager: AuthenticationManager,
    private val ioDispatcher: CoroutineDispatcher,
    // Infrastructure components:
    private val requestDeduplicationManager: RequestDeduplicationManager,
    private val priorityTaskScheduler: ModernPriorityTaskScheduler,
    private val sessionManager: SessionManager,
    private val cacheWarmingManager: CacheWarmingManager,
    private val authStateCoordinator: AuthenticationStateCoordinator,
    private val repositoryErrorHandler: RepositoryErrorHandler
) : TargetRepository {
    // Implementation...
}
```

### Required Infrastructure Integration
1. **CacheWarmingManager** - For performance optimization
2. **AuthenticationStateCoordinator** - For auth state management
3. **RequestDeduplicationManager** - For preventing duplicate operations
4. **SessionManager** - For session lifecycle management
5. **RepositoryErrorHandler** - For consistent error handling
6. **ValidationEngine** - For data validation (injected into mappers)

### Method Signature Preservation
All existing public methods must maintain their signatures:
```kotlin
suspend fun methodName(...): Result<DomainType>
```

## Verification Commands

### Infrastructure Component Verification
```bash
# Verify infrastructure injection
rg "CacheWarmingManager" --type kt app/src/main/java/com/autogratuity/data/repository/
rg "AuthenticationStateCoordinator" --type kt app/src/main/java/com/autogratuity/data/repository/
rg "RequestDeduplicationManager" --type kt app/src/main/java/com/autogratuity/data/repository/
rg "SessionManager" --type kt app/src/main/java/com/autogratuity/data/repository/
rg "RepositoryErrorHandler" --type kt app/src/main/java/com/autogratuity/data/repository/
```

### DI Module Integration Verification
```bash
# Verify DI configuration
rg "single<.*Repository>" --type kt app/src/main/java/com/autogratuity/di/modules/DataModule.kt
rg "get<.*>" --type kt app/src/main/java/com/autogratuity/data/repository/
```

### Method Signature Verification
```bash
# Verify Result<T> patterns preserved
rg "suspend fun.*: Result<" --type kt app/src/main/java/com/autogratuity/data/repository/
rg "fun.*: Flow<Result<" --type kt app/src/main/java/com/autogratuity/data/repository/
```

## Implementation Priority

### High Priority (Missing Infrastructure)
1. **DeliveryRepositoryImpl** - 3,138 lines, complex delivery logic
2. **UserProfileRepositoryImpl** - 1,041 lines, core user operations
3. **AddressRepositoryImpl** - 2,168 lines, location services integration

### Medium Priority
4. **ConfigRepositoryImpl** - 771 lines, configuration management
5. **PreferenceRepositoryImpl** - User preference handling

### Gold Standard Reference
- **SubscriptionRepositoryImpl** - Complete modernization template with all infrastructure components

## Next Steps

1. **Update DI Modules** - Add missing infrastructure component bindings
2. **Modernize Repository Constructors** - Follow SubscriptionRepositoryImpl pattern
3. **Integrate Infrastructure** - Wire cache warming, auth coordination, error handling
4. **Verify Signatures** - Ensure backward compatibility
5. **Test Integration** - Validate infrastructure components work together

This blueprint provides the complete roadmap for repository modernization with all technical details needed for implementation. 