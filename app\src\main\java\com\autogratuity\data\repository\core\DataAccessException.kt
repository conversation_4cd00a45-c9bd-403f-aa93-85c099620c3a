package com.autogratuity.data.repository.core

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

/**
 * Modern data access exception that integrates with the RepositoryException hierarchy.
 * This exception is specifically for low-level data access operations.
 * 
 * Modernized in 2025 to provide better error context and integration with the repository error system.
 */
class DataAccessException(
    message: String,
    cause: Throwable? = null,
    val operation: String? = null,
    val entityType: String? = null,
    val resourceId: String? = null
) : Exception(message, cause) {

    /**
     * Timestamp when the error occurred
     */
    val timestamp: Instant = Clock.System.now()

    /**
     * Convert this DataAccessException to a RepositoryException for unified error handling
     */
    fun toRepositoryException(): RepositoryException {
        val context = buildMap<String, Any> {
            operation?.let { put("operation", it) }
            entityType?.let { put("entity_type", it) }
            resourceId?.let { put("resource_id", it) }
            put("timestamp", timestamp.toString())
            put("data_access_error", true)
        }

        return when {
            message?.contains("network", ignoreCase = true) == true ||
            message?.contains("connection", ignoreCase = true) == true ||
            cause is java.net.UnknownHostException ||
            cause is java.net.ConnectException -> {
                RepositoryException.NetworkError(
                    message ?: "Data access network error",
                    cause,
                    context = context
                )
            }

            message?.contains("timeout", ignoreCase = true) == true ||
            cause is java.net.SocketTimeoutException ||
            cause is java.util.concurrent.TimeoutException -> {
                RepositoryException.TimeoutError(
                    message ?: "Data access timeout error",
                    cause,
                    operation = operation,
                    context = context
                )
            }

            message?.contains("not found", ignoreCase = true) == true ||
            message?.contains("does not exist", ignoreCase = true) == true -> {
                RepositoryException.NotFoundError(
                    message ?: "Resource not found in data access",
                    cause,
                    resourceId = resourceId,
                    resourceType = entityType,
                    context = context
                )
            }

            message?.contains("validation", ignoreCase = true) == true ||
            message?.contains("invalid", ignoreCase = true) == true ||
            cause is IllegalArgumentException -> {
                RepositoryException.ValidationError(
                    message ?: "Data validation error",
                    cause,
                    context = context
                )
            }

            message?.contains("auth", ignoreCase = true) == true ||
            message?.contains("permission", ignoreCase = true) == true ||
            cause is SecurityException -> {
                RepositoryException.AuthenticationError(
                    message ?: "Data access authentication error",
                    cause,
                    context = context
                )
            }

            else -> {
                RepositoryException.DatabaseError(
                    message ?: "Unknown data access error",
                    cause,
                    operation = operation,
                    context = context
                )
            }
        }
    }

    /**
     * Get a comprehensive error summary for logging
     */
    fun getErrorSummary(): String = buildString {
        append("DataAccessException: ")
        append(message ?: "Unknown data access error")
        
        operation?.let { append(" [Operation: $it]") }
        entityType?.let { append(" [Entity: $it]") }
        resourceId?.let { append(" [Resource: $it]") }
        
        cause?.let { append(" Caused by: ${it::class.simpleName}: ${it.message}") }
    }

    companion object {
        /**
         * Create a DataAccessException for network-related errors
         */
        fun networkError(
            message: String,
            cause: Throwable? = null,
            operation: String? = null
        ) = DataAccessException(
            message = "Network error during data access: $message",
            cause = cause,
            operation = operation
        )

        /**
         * Create a DataAccessException for timeout errors
         */
        fun timeoutError(
            message: String,
            cause: Throwable? = null,
            operation: String? = null,
            timeoutDuration: kotlin.time.Duration? = null
        ) = DataAccessException(
            message = buildString {
                append("Timeout error during data access: $message")
                timeoutDuration?.let { append(" (timeout: ${it.inWholeMilliseconds}ms)") }
            },
            cause = cause,
            operation = operation
        )

        /**
         * Create a DataAccessException for resource not found errors
         */
        fun notFoundError(
            resourceType: String,
            resourceId: String,
            operation: String? = null
        ) = DataAccessException(
            message = "$resourceType with ID '$resourceId' not found",
            operation = operation,
            entityType = resourceType,
            resourceId = resourceId
        )

        /**
         * Create a DataAccessException for validation errors
         */
        fun validationError(
            message: String,
            entityType: String? = null,
            operation: String? = null
        ) = DataAccessException(
            message = "Data validation error: $message",
            operation = operation,
            entityType = entityType
        )

        /**
         * Create a DataAccessException for database errors
         */
        fun databaseError(
            message: String,
            cause: Throwable? = null,
            operation: String? = null,
            entityType: String? = null
        ) = DataAccessException(
            message = "Database error during data access: $message",
            cause = cause,
            operation = operation,
            entityType = entityType
        )

        /**
         * Convert any Throwable to a DataAccessException with context
         */
        fun fromThrowable(
            throwable: Throwable,
            operation: String? = null,
            entityType: String? = null,
            resourceId: String? = null
        ): DataAccessException {
            val message = throwable.message ?: "Unknown error during data access"
            
            return DataAccessException(
                message = message,
                cause = throwable,
                operation = operation,
                entityType = entityType,
                resourceId = resourceId
            )
        }
    }
}

/**
 * Extension function to convert any Throwable to a modern DataAccessException
 */
fun Throwable.toDataAccessException(
    operation: String? = null,
    entityType: String? = null,
    resourceId: String? = null
): DataAccessException = DataAccessException.fromThrowable(this, operation, entityType, resourceId) 