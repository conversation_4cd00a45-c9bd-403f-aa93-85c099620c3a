package com.autogratuity.data.datasource.remote

import android.util.Log
import com.autogratuity.data.model.Result
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.MetadataChanges
import com.google.firebase.firestore.SetOptions
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import javax.inject.Inject
import com.autogratuity.data.model.generated_kt.App_config as AppConfigDto
import com.autogratuity.data.model.generated_kt.Notification_patterns as NotificationPatternsDto

/**
 * Interface for remote data operations related to Config.
 * All methods operate with DTO objects (App_config, Notification_patterns).
 *
 * Follows the delivery domain pattern for remote data sources.
 */
interface ConfigRemoteDataSource {
    suspend fun getAppConfigDto(): Result<AppConfigDto?>
    suspend fun saveAppConfigDto(appConfigDto: AppConfigDto, merge: Boolean = false): Result<Unit>
    suspend fun updateAppConfigFieldsDto(fields: Map<String, Any>): Result<Unit>

    suspend fun getNotificationPatternsDto(): Result<NotificationPatternsDto?>
    suspend fun saveNotificationPatternsDto(patternsDto: NotificationPatternsDto, merge: Boolean = false): Result<Unit>

    fun observeAppConfigDto(): Flow<AppConfigDto?>
    fun observeNotificationPatternsDto(): Flow<NotificationPatternsDto?>
}

/**
 * ConfigRemoteDataSource implementation handling Firestore operations for config domain.
 * Operates exclusively with DTOs (App_config, Notification_patterns).
 *
 * Follows the delivery domain pattern exactly - interface + implementation in same file.
 */
@ExperimentalCoroutinesApi
class ConfigRemoteDataSourceImpl @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val ioDispatcher: CoroutineDispatcher
) : ConfigRemoteDataSource {

    companion object {
        private const val TAG = "ConfigRemoteDataSource"
        private const val SYSTEM_CONFIG_COLLECTION = "system_config"
        private const val APP_CONFIG_DOCUMENT_ID = "app_config_default"
        private const val NOTIFICATION_PATTERNS_DOCUMENT_ID = "notification_patterns_default"
    }

    // ===== APP CONFIG OPERATIONS =====

    override suspend fun getAppConfigDto(): Result<AppConfigDto?> = withContext(ioDispatcher) {
        val startTime = kotlin.time.TimeSource.Monotonic.markNow()
        
        try {
            // ENHANCED: Add session correlation for config fetch
            com.autogratuity.debug.ClarityArchitectureMonitor.addSessionEvent("config_remote_fetch:app_config")
            
            val snapshot = firestore.collection(SYSTEM_CONFIG_COLLECTION)
                .document(APP_CONFIG_DOCUMENT_ID)
                .get()
                .await()

            val duration = startTime.elapsedNow()
            val documentExists = snapshot.exists()
            val dataSize = if (documentExists) snapshot.data?.toString()?.length ?: 0 else 0

            // ENHANCED: Monitor Firestore config read
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = SYSTEM_CONFIG_COLLECTION,
                documentId = APP_CONFIG_DOCUMENT_ID,
                duration = duration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = "$SYSTEM_CONFIG_COLLECTION/$APP_CONFIG_DOCUMENT_ID",
                queryType = "GET_DOCUMENT",
                queryFilters = listOf("app_config_access"),
                resultCount = if (documentExists) 1 else 0,
                cacheSource = "SERVER"
            )

            val appConfig = if (snapshot.exists()) {
                snapshot.toObject(AppConfigDto::class.java)
            } else {
                Log.w(TAG, "App_config document ('$APP_CONFIG_DOCUMENT_ID') not found.")
                null
            }
            
            Log.d(TAG, "getAppConfigDto: Successfully fetched app config")
            Log.d(TAG, "  Document exists: $documentExists")
            Log.d(TAG, "  Data size: $dataSize bytes")
            Log.d(TAG, "  Duration: ${duration.inWholeMilliseconds}ms")
            
            Result.Success(appConfig)
        } catch (e: Exception) {
            val duration = startTime.elapsedNow()
            
            // ENHANCED: Monitor failed Firestore config read
            com.autogratuity.debug.ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = SYSTEM_CONFIG_COLLECTION,
                documentId = APP_CONFIG_DOCUMENT_ID,
                duration = duration,
                success = false,
                dataSizeBytes = 0,
                error = e,
                fullPath = "$SYSTEM_CONFIG_COLLECTION/$APP_CONFIG_DOCUMENT_ID",
                queryType = "GET_DOCUMENT",
                queryFilters = listOf("app_config_access"),
                resultCount = 0,
                cacheSource = "ERROR"
            )
            
            Log.e(TAG, "Error fetching App_config ('$APP_CONFIG_DOCUMENT_ID')", e)
            Result.Error(e)
        }
    }

    override suspend fun saveAppConfigDto(appConfigDto: AppConfigDto, merge: Boolean): Result<Unit> = withContext(ioDispatcher) {
        try {
            val docRef = firestore.collection(SYSTEM_CONFIG_COLLECTION).document(APP_CONFIG_DOCUMENT_ID)
            if (merge) {
                docRef.set(appConfigDto, SetOptions.merge()).await()
            } else {
                docRef.set(appConfigDto).await()
            }
            Log.d(TAG, "App_config ('$APP_CONFIG_DOCUMENT_ID') saved. Merge: $merge")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error saving App_config ('$APP_CONFIG_DOCUMENT_ID')", e)
            Result.Error(e)
        }
    }

    override suspend fun updateAppConfigFieldsDto(fields: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        if (fields.isEmpty()) {
            Log.w(TAG, "App_config update map is empty.")
            return@withContext Result.Success(Unit)
        }
        try {
            firestore.collection(SYSTEM_CONFIG_COLLECTION)
                .document(APP_CONFIG_DOCUMENT_ID)
                .update(fields)
                .await()
            Log.d(TAG, "App_config fields updated for '$APP_CONFIG_DOCUMENT_ID'.")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating App_config fields for '$APP_CONFIG_DOCUMENT_ID'.", e)
            Result.Error(e)
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeAppConfigDto(): Flow<AppConfigDto?> {
        return callbackFlow {
            val listenerRegistration = firestore.collection(SYSTEM_CONFIG_COLLECTION)
                .document(APP_CONFIG_DOCUMENT_ID)
                .addSnapshotListener(MetadataChanges.INCLUDE) { snapshot, error ->
                    if (error != null) {
                        Log.e(TAG, "Error listening to App_config ('$APP_CONFIG_DOCUMENT_ID')", error)
                        trySend(null).isSuccess
                        return@addSnapshotListener
                    }

                    val config = if (snapshot != null && snapshot.exists()) {
                        try {
                            snapshot.toObject(AppConfigDto::class.java)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error converting snapshot to App_config ('$APP_CONFIG_DOCUMENT_ID')", e)
                            null
                        }
                    } else {
                        Log.w(TAG, "App_config document ('$APP_CONFIG_DOCUMENT_ID') snapshot does not exist.")
                        null
                    }

                    trySend(config).isSuccess
                }

            awaitClose {
                Log.d(TAG, "ObserveAppConfigDto: Cancelling listener for $APP_CONFIG_DOCUMENT_ID")
                listenerRegistration.remove()
            }
        }
        .catch { exception ->
            Log.e(TAG, "Error in observeAppConfigDto flow ('$APP_CONFIG_DOCUMENT_ID')", exception)
            emit(null)
        }
        .flowOn(ioDispatcher)
    }

    // ===== NOTIFICATION PATTERNS OPERATIONS =====

    override suspend fun getNotificationPatternsDto(): Result<NotificationPatternsDto?> = withContext(ioDispatcher) {
        try {
            val snapshot = firestore.collection(SYSTEM_CONFIG_COLLECTION)
                .document(NOTIFICATION_PATTERNS_DOCUMENT_ID)
                .get()
                .await()

            val patterns = if (snapshot.exists()) {
                snapshot.toObject(NotificationPatternsDto::class.java)
            } else {
                Log.w(TAG, "Notification_patterns document ('$NOTIFICATION_PATTERNS_DOCUMENT_ID') not found.")
                null
            }
            Result.Success(patterns)
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching Notification_patterns ('$NOTIFICATION_PATTERNS_DOCUMENT_ID')", e)
            Result.Error(e)
        }
    }

    override suspend fun saveNotificationPatternsDto(patternsDto: NotificationPatternsDto, merge: Boolean): Result<Unit> = withContext(ioDispatcher) {
        try {
            val docRef = firestore.collection(SYSTEM_CONFIG_COLLECTION).document(NOTIFICATION_PATTERNS_DOCUMENT_ID)
            if (merge) {
                docRef.set(patternsDto, SetOptions.merge()).await()
            } else {
                docRef.set(patternsDto).await()
            }
            Log.d(TAG, "Notification_patterns ('$NOTIFICATION_PATTERNS_DOCUMENT_ID') saved. Merge: $merge")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error saving Notification_patterns ('$NOTIFICATION_PATTERNS_DOCUMENT_ID')", e)
            Result.Error(e)
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeNotificationPatternsDto(): Flow<NotificationPatternsDto?> {
        return callbackFlow {
            val listenerRegistration = firestore.collection(SYSTEM_CONFIG_COLLECTION)
                .document(NOTIFICATION_PATTERNS_DOCUMENT_ID)
                .addSnapshotListener(MetadataChanges.INCLUDE) { snapshot, error ->
                    if (error != null) {
                        Log.e(TAG, "Error listening to Notification_patterns ('$NOTIFICATION_PATTERNS_DOCUMENT_ID')", error)
                        trySend(null).isSuccess
                        return@addSnapshotListener
                    }

                    val patterns = if (snapshot != null && snapshot.exists()) {
                        try {
                            snapshot.toObject(NotificationPatternsDto::class.java)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error converting snapshot to Notification_patterns ('$NOTIFICATION_PATTERNS_DOCUMENT_ID')", e)
                            null
                        }
                    } else {
                        Log.w(TAG, "Notification_patterns document ('$NOTIFICATION_PATTERNS_DOCUMENT_ID') snapshot does not exist.")
                        null
                    }

                    trySend(patterns).isSuccess
                }

            awaitClose {
                Log.d(TAG, "ObserveNotificationPatternsDto: Cancelling listener for $NOTIFICATION_PATTERNS_DOCUMENT_ID")
                listenerRegistration.remove()
            }
        }
        .catch { exception ->
            Log.e(TAG, "Error in observeNotificationPatternsDto flow ('$NOTIFICATION_PATTERNS_DOCUMENT_ID')", exception)
            emit(null)
        }
        .flowOn(ioDispatcher)
    }
}
