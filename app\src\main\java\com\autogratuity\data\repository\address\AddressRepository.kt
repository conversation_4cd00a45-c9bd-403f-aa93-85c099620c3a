package com.autogratuity.data.repository.address

import com.autogratuity.data.model.generated_kt.Address
import com.autogratuity.data.model.generated_kt.Address.AddressData
import com.autogratuity.data.model.generated_kt.Address.Components as AddressComponents
import com.autogratuity.data.model.generated_kt.Delivery_stats as AddressDeliveryStats
import com.autogratuity.data.model.Result
import com.google.firebase.firestore.DocumentReference
import com.google.android.libraries.places.api.model.Place
import kotlinx.coroutines.flow.Flow

/**
 * Data layer interface for Address repository operations.
 * 
 * This interface operates with DTOs (data.model.generated_kt.Address) and handles
 * all data persistence concerns. The domain layer AddressRepository operates with
 * SSoT models and coordinates between this data layer, mappers, and business logic.
 * 
 * Follows the clean, simple pattern established by DeliveryRepository and other
 * modern repository interfaces in the codebase.
 */
interface AddressRepository {

    // ===== CORE CRUD OPERATIONS =====

    /**
     * Retrieves an address by its unique ID.
     * @param addressId The unique identifier of the address.
     * @return Result holding the [Address] DTO if found, or null if not found, or an error.
     */
    suspend fun getAddressById(addressId: String): Result<com.autogratuity.domain.model.Address?>

    /**
     * Retrieves all addresses for the current user.
     * @return Result holding a list of [Address] DTOs or an error.
     */
    suspend fun getAddresses(): Result<List<Address>>

    /**
     * Finds an address by its normalized form.
     * @param normalizedAddress The normalized address string to search for.
     * @return Result holding the [Address] DTO if found, or null if not found, or an error.
     */
    suspend fun findAddressByNormalizedAddress(normalizedAddress: String): Result<com.autogratuity.domain.model.Address?>

    /**
     * Finds an address by comparing key fields from AddressData.
     * @param addressData The AddressData containing address details to match.
     * @return The matching Address DTO if found, or null.
     */
    suspend fun findAddressByDetails(addressData: AddressData): Address?

    /**
     * Adds a new address. Will check for duplicates.
     * @param addressData The AddressData object containing the address data to add.
     * @return Result holding the DocumentReference of the added or existing address, or an error.
     */
    suspend fun addAddress(addressData: AddressData): Result<DocumentReference>

    /**
     * Updates an existing address.
     * @param address The Address DTO containing the updated address data.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun updateAddress(address: Address): Result<Unit>

    /**
     * Deletes an address by its ID.
     * @param addressId The address ID to delete.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun deleteAddress(addressId: String): Result<Unit>

    /**
     * Updates specific flags for an address using atomic updates.
     * @param addressId The address ID.
     * @param flagsToUpdate A map containing flag fields to update (e.g., {"flags.doNotDeliver": true}).
     * @return Result holding Unit on success, or an error.
     */
    suspend fun updateAddressFlags(addressId: String, flagsToUpdate: Map<String, Any>): Result<Unit>

    // ===== REACTIVE FLOW OPERATIONS =====

    /**
     * Observes changes to all addresses in real-time.
     * @return Flow that emits Result holding updates to the address list or errors.
     */
    fun observeAddresses(): Flow<Result<List<com.autogratuity.domain.model.Address>>>

    /**
     * Observes changes to a specific address in real-time.
     * @param addressId The address ID to observe.
     * @return Flow that emits Result holding updates to the address or errors.
     */
    fun observeAddress(addressId: String): Flow<Result<Address?>>

    // ===== CONVENIENCE METHODS =====

    /**
     * Gets favorite addresses.
     * @return List of favorite Address DTOs.
     */
    suspend fun getFavoriteAddresses(): List<Address>

    /**
     * Gets addresses with the best tips.
     * @param limit Maximum number of addresses to return.
     * @return List of Address DTOs sorted by tip amount.
     */
    suspend fun getBestTippingAddresses(limit: Int): List<Address>

    /**
     * Gets addresses sorted by most recent delivery.
     * @param limit Maximum number of addresses to return.
     * @return List of Address DTOs.
     */
    suspend fun getRecentlyUsedAddresses(limit: Int): List<Address>

    /**
     * Searches addresses by query string.
     * @param query The search query.
     * @return List of Address DTOs matching the query.
     */
    suspend fun searchAddresses(query: String): List<Address>

    /**
     * Gets addresses within a certain radius of a location.
     * @param latitude Latitude of the center point.
     * @param longitude Longitude of the center point.
     * @param radiusKm Radius in kilometers.
     * @return List of Address DTOs within the radius.
     */
    suspend fun getAddressesNearLocation(latitude: Double, longitude: Double, radiusKm: Double): List<Address>

    // ===== FLAG OPERATIONS =====

    /**
     * Marks an address as favorite.
     * @param addressId The address ID.
     * @param isFavorite Whether to mark as favorite or not.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun setAddressFavorite(addressId: String, isFavorite: Boolean): Result<Unit>

    /**
     * Marks an address as verified.
     * @param addressId The address ID.
     * @param isVerified Whether to mark as verified or not.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun setAddressVerified(addressId: String, isVerified: Boolean): Result<Unit>

    /**
     * Sets access issues flag for an address.
     * @param addressId The address ID.
     * @param hasAccessIssues Whether the address has access issues.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun setAddressAccessIssues(addressId: String, hasAccessIssues: Boolean): Result<Unit>

    /**
     * Updates address notes.
     * @param addressId The address ID.
     * @param notes The notes to set.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun updateAddressNotes(addressId: String, notes: String?): Result<Unit>

    /**
     * Updates delivery statistics for an address.
     * @param addressId The address ID.
     * @param deliveryStats The updated delivery statistics.
     */
    suspend fun updateAddressDeliveryStats(addressId: String, deliveryStats: AddressDeliveryStats)

    // ===== UTILITY METHODS =====

    /**
     * Normalizes an address string for consistent lookup and comparison.
     * @param addressString The address string to normalize.
     * @return The normalized address string.
     */
    fun normalizeAddress(addressString: String): String

    /**
     * Parses an address string into components.
     * @param addressString The full address string to parse.
     * @return AddressComponents DTO.
     */
    fun parseAddressComponents(addressString: String): com.autogratuity.domain.model.AddressComponents

    /**
     * Creates a new address from a Google Places Place object.
     * @param place The Place object from Google Places SDK.
     * @param userId The user ID for whom to create the address.
     * @return Result holding the created Address DTO or an error.
     */
    suspend fun createAddressFromPlace(place: Place, userId: String): Result<Address>

    /**
     * Updates an address with data from a Google Places Place object.
     * @param addressId The ID of the address to update.
     * @param place The Place object containing new data.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun updateAddressFromPlace(addressId: String, place: Place): Result<Unit>

    // ===== TRANSACTIONAL OPERATIONS =====

    /**
     * Finds an existing address or creates a new one within a Firestore transaction.
     * This method ensures atomic address creation/lookup for delivery associations.
     *
     * @param addressData The address data to find or create.
     * @param userId The user ID for whom to find or create the address.
     * @param transaction The Firestore transaction to use (optional, for transactional operations).
     * @return The address ID (existing or newly created).
     * @throws IllegalArgumentException if userId is blank.
     * @throws Exception for any Firestore or processing errors.
     */
    fun findOrCreateAddressTransactional(
        addressData: AddressData,
        userId: String,
        transaction: com.google.firebase.firestore.Transaction? = null
    ): String



    // ===== REPOSITORY LIFECYCLE (INFRASTRUCTURE FOCUS) =====

    /**
     * Initializes the address repository.
     * Sets up any required state or connections.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun initialize(): Result<Unit>

    /**
     * Cleans up repository resources.
     * Called during application shutdown or repository cleanup.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun cleanup(): Result<Unit>

    // ===== IMPORT/EXPORT OPERATIONS (INFRASTRUCTURE FOCUS) =====

    /**
     * Exports user address data in the specified format.
     * Used for data portability and backup operations.
     * @param userId The user ID whose data to export.
     * @param format The export format (e.g., "json", "csv").
     * @return Result holding the exported data as a string, or an error.
     */
    suspend fun exportUserData(userId: String, format: String = "json"): Result<String>

    /**
     * Imports user address data from a map.
     * Used for data restoration and migration operations.
     * @param userId The user ID for whom to import data.
     * @param data The address data to import.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun importUserData(userId: String, data: Map<String, Any>): Result<Unit>

    // ===== BACKUP AND RECOVERY (INFRASTRUCTURE FOCUS) =====

    /**
     * Creates a backup of user address data.
     * @param userId The user ID whose data to backup.
     * @return Result holding the backup data as a map, or an error.
     */
    suspend fun createUserBackup(userId: String): Result<Map<String, Any>>

    /**
     * Restores user address data from a backup.
     * @param userId The user ID for whom to restore data.
     * @param backup The backup data to restore.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun restoreUserBackup(userId: String, backup: Map<String, Any>): Result<Unit>

    /**
     * Migrates user address data between versions.
     * @param userId The user ID whose data to migrate.
     * @param fromVersion The source version.
     * @param toVersion The target version.
     * @return Result holding Unit on success, or an error.
     */
    suspend fun migrateUserData(userId: String, fromVersion: Long, toVersion: Long): Result<Unit>
}
