package com.autogratuity.data.model.util

import android.util.Log
import java.util.Map as JavaMap

/**
 * Utility class for handling Java-Kotlin map conversions in Firestore models.
 * This provides standardized methods for safely converting between Java and Kotlin maps
 * when working with Firestore data.
 */
object FirestoreMapUtil {
    private const val TAG = "FirestoreMapUtil"

    /**
     * Safely converts a Java Map or any map-like object to a Kotlin Map<String, Any?>.
     * Handles nulls and ensures keys are strings.
     *
     * @param map The Java Map or any map-like object to convert
     * @return A Kotlin Map<String, Any?> or empty map if input is null or not a map
     */
    @Suppress("UNCHECKED_CAST")
    fun toKotlinStringMap(map: Any?): Map<String, Any?> {
        if (map == null) return emptyMap()
        if (map !is JavaMap<*, *>) return emptyMap()
        
        val kotlinMap = mutableMapOf<String, Any?>()
        val javaMap = map
        
        try {
            val keySet = javaMap.keySet()
            for (key in keySet) {
                if (key is String) {
                    kotlinMap[key] = javaMap[key]
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error converting Java map to Kotlin map", e)
            return emptyMap()
        }
        
        return kotlinMap
    }

    /**
     * Checks if a map is empty in a null-safe way.
     * Works with both Java and Kotlin maps.
     *
     * @param map The map to check
     * @return true if the map is null or empty, false otherwise
     */
    fun isNullOrEmpty(map: Any?): Boolean {
        return when (map) {
            null -> true
            is Map<*, *> -> map.isEmpty()
            is JavaMap<*, *> -> map.isEmpty
            else -> true
        }
    }

    /**
     * Gets a String value from a map safely, handling nulls and type casting.
     *
     * @param map The map to get the value from
     * @param key The key to look up
     * @return The String value or null if not found or not a String
     */
    fun getStringOrNull(map: Map<String, Any?>, key: String): String? {
        val value = map[key]
        return value as? String
    }

    /**
     * Gets a Boolean value from a map safely, handling nulls and type casting.
     *
     * @param map The map to get the value from
     * @param key The key to look up
     * @return The Boolean value or null if not found or not a Boolean
     */
    fun getBooleanOrNull(map: Map<String, Any?>, key: String): Boolean? {
        val value = map[key]
        return value as? Boolean
    }

    /**
     * Gets a List<String> value from a map safely, handling nulls and type casting.
     *
     * @param map The map to get the value from
     * @param key The key to look up
     * @return A List<String> or empty list if not found or not a List
     */
    fun getStringList(map: Map<String, Any?>, key: String): List<String> {
        val value = map[key]
        return if (value is List<*>) {
            value.filterIsInstance<String>()
        } else {
            emptyList()
        }
    }
} 