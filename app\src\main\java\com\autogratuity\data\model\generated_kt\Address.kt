/*
 * Address.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

/**
 * Represents an address document in Firestore, mirroring the Address.ts model.
 */
data class Address(
    /** Firestore Document ID */
    val id: String,
    /** Wrapper for all address-specific data. */
    val addressData: AddressData
) {

    /**
     * Wrapper for all address-specific data.
     */
    data class AddressData(
        /** ID of the user associated with this address */
        val userId: String? = null,
        /** The complete, formatted address string */
        val fullAddress: String? = null,
        /** A normalized version of the address for searching/comparison */
        val normalizedAddress: String? = null,
        /** Google Place ID for the address */
        val placeId: String? = null,
        /** Indicates if this is the user's default address */
        val isDefault: Boolean? = null,
        /** User-provided notes about the address */
        val notes: String? = null,
        /** User-defined tags for organizing addresses */
        val tags: List<String>? = null,
        /** List of associated order IDs (e.g., deliveries made to this address) */
        val orderIds: List<String>? = null,
        /** Terms derived from the address for search purposes */
        val searchTerms: List<String>? = null,
        /** Structured components of the address */
        val components: Components? = null,
        /** Geographic coordinates (latitude/longitude) */
        val coordinates: Coordinates? = null,
        /** Specific fields used for searching */
        val searchFields: SearchFields? = null,
        /** Statistics related to deliveries at this address */
        val deliveryStats: Delivery_stats? = null,
        /** Boolean flags associated with the address */
        val flags: Flags? = null,
        /** Metadata like creation/update timestamps */
        val metadata: Metadata? = null,
        /** Platform information associated with this address, if directly known or inferred. */
        val platform: Platform? = null
    )

    /**
     * Structured components of the address
     */
    data class Components(
        val streetNumber: String? = null, // MANUAL OVERRIDE 
        val streetName: String? = null,
        val city: String? = null,
        val state: String? = null,
        val postalCode: String? = null,
        val country: String? = null
    )

    /**
     * Specific fields used for searching
     */
    data class SearchFields(
        val searchTerms: List<String>? = null,
        val normalizedKey: String? = null
    )

}
