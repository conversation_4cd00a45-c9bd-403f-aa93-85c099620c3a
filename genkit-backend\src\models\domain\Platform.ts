// Auto-generated from Platform.kt
// TypeScript equivalent of Android domain.model.Platform

/**
 * Domain model generated from Kotlin Platform
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface Platform {
  name?: string | null;
  type?: string? = null;
  version?: string? = null;
  source?: string? = null;
  displayName?: string? = null;
  iconUrl?: string? = null;
}