/*
 * Amounts.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

import kotlin.Double

/**
 * Represents monetary amounts, typically associated with a Delivery.
 */
data class Amounts(
    /** The base payment amount. */
    val basePay: Double? = null,
    /** The tip amount received. */
    val tipAmount: Double? = null,
    /** The tip percentage relative to some base. */
    val tipPercentage: Double? = null,
    /** The total amount (potentially base + tip), may be calculated elsewhere. */
    val totalAmount: Double? = null,
    /** The currency code (e.g., USD). */
    val currencyCode: String? = null,
    /** The estimated pay for the delivery. */
    val estimatedPay: Double? = null,
    /** The final pay amount received. */
    val finalPay: Double? = null,
    /** The distance of the delivery in miles. */
    val distanceMiles: Double? = null
)
