package com.autogratuity.data.util

import android.util.Log
import android.util.Patterns
import com.autogratuity.data.model.generated_kt.User_profile
import com.autogratuity.domain.model.Address
import com.autogratuity.domain.model.Delivery
import com.autogratuity.domain.model.User
import com.autogratuity.domain.model.UserPreferences
import com.autogratuity.domain.model.UserSubscription
import java.time.Instant
import java.time.LocalDateTime

/**
 * ValidationEngine - Core data validation system following Clarity Architecture principles
 * 
 * Contains actual validation logic without external dependencies to avoid circular references.
 * Validates domain objects, DTOs, and business rules at system boundaries.
 * 
 * Design principles:
 * - Stateless validation functions
 * - Clear, actionable error messages
 * - Domain-specific validation rules
 * - No repository dependencies (prevents circular dependencies)
 * - Fast, synchronous validation operations
 */
class ValidationEngine {

    companion object {
        private const val TAG = "ValidationEngine"
        
        // Validation constraints
        private const val MIN_TIP_AMOUNT = 0.0
        private const val MAX_TIP_AMOUNT = 999.99
        private const val MIN_DELIVERY_DISTANCE = 0.0
        private const val MAX_DELIVERY_DISTANCE = 500.0 // miles
        private const val MIN_ORDER_VALUE = 0.01
        private const val MAX_ORDER_VALUE = 9999.99
        private const val MAX_ADDRESS_LENGTH = 500
        private const val MAX_ORDER_ID_LENGTH = 100
        private const val MAX_CUSTOMER_NAME_LENGTH = 100
        private const val MAX_NOTES_LENGTH = 1000
        
        // Time constraints
        private val EARLIEST_DELIVERY_DATE = LocalDateTime.of(2020, 1, 1, 0, 0)
        private val LATEST_DELIVERY_DATE = LocalDateTime.now().plusDays(1)
    }

    /**
     * Validate User domain object
     * @param user The User domain model to validate
     * @param isCreation Whether this is during initial user creation (more lenient) or update (stricter)
     */
    fun validateUser(user: User, isCreation: Boolean = false): ValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        // CRITICAL: User ID is always required
        if (user.id.isBlank()) {
            errors.add("User ID is required")
        }

        // 🔧 LENIENT EMAIL VALIDATION FOR SIGNUP
        if (user.email.isNullOrBlank()) {
            if (isCreation) {
                warnings.add("Email is empty during user creation (will be set later)")
            } else {
                errors.add("Email is required")
            }
        } else {
            // Skip email format validation during creation or if email appears encrypted
            val isEncrypted = user.email.matches(Regex("^[A-Za-z0-9+/]+=*$")) && user.email.length > 20
            val skipFormatValidation = isCreation || isEncrypted
            
            if (!skipFormatValidation && !Patterns.EMAIL_ADDRESS.matcher(user.email).matches()) {
                errors.add("Invalid email format: ${user.email}")
            }
        }

        // 🔧 LENIENT DISPLAY NAME VALIDATION FOR SIGNUP
        user.displayName?.let { displayName ->
            val maxLength = if (isCreation) 100 else 50  // More lenient during creation
            if (displayName.length > maxLength) {
                if (isCreation) {
                    warnings.add("Display name is long (${displayName.length} chars) but acceptable during creation")
                } else {
                    errors.add("Display name must be $maxLength characters or less")
                }
            }
            if (displayName.isBlank()) {
                warnings.add("Display name is empty")
            }
        }

        // 🔧 LENIENT NESTED OBJECT VALIDATION FOR SIGNUP
        user.preferences?.let { prefs ->
            validateUserPreferences(prefs).let { prefResult ->
                if (isCreation) {
                    // Convert preference errors to warnings during creation
                    warnings.addAll(prefResult.errors.map { "Preference issue (non-blocking): $it" })
                    warnings.addAll(prefResult.warnings)
                } else {
                    errors.addAll(prefResult.errors)
                    warnings.addAll(prefResult.warnings)
                }
            }
        }

        // 🔧 LENIENT SUBSCRIPTION VALIDATION FOR SIGNUP
        user.subscription?.let { subscription ->
            validateUserSubscription(subscription).let { subResult ->
                if (isCreation) {
                    // Convert subscription errors to warnings during creation
                    warnings.addAll(subResult.errors.map { "Subscription issue (non-blocking): $it" })
                    warnings.addAll(subResult.warnings)
                } else {
                    errors.addAll(subResult.errors)
                    warnings.addAll(subResult.warnings)
                }
            }
        }

        // 🔧 LENIENT DEFAULT ADDRESS VALIDATION FOR SIGNUP
        user.defaultAddressId?.let { addressId ->
            if (addressId.isBlank()) {
                warnings.add("Default address ID is empty")
            }
        }

        // 🔧 CREATION-SPECIFIC LOGGING
        if (isCreation) {
            Log.d(TAG, "🔧 LENIENT User validation (creation mode): ${errors.size} errors, ${warnings.size} warnings")
            if (errors.isEmpty()) {
                Log.d(TAG, "✅ User creation validation PASSED - signup can proceed")
            } else {
                Log.w(TAG, "❌ User creation validation FAILED - critical errors: ${errors.joinToString(", ")}")
            }
        } else {
            Log.d(TAG, "User validation (update mode): ${errors.size} errors, ${warnings.size} warnings")
        }
        
        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }

    /**
     * Validate User_profile DTO from network
     * NOTE: DTOs may contain encrypted PII, so format validation is limited
     */
    fun validateUserProfileDto(dto: User_profile): ValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        // Required DTO fields
        if (dto.userId.isNullOrBlank()) {
            errors.add("User_profile.userId is required")
        }

        if (dto.email.isNullOrBlank()) {
            errors.add("User_profile.email is required")
        } else {
            // Skip email format validation for encrypted emails (base64 pattern)
            val isEncrypted = dto.email.matches(Regex("^[A-Za-z0-9+/]+=*$")) && dto.email.length > 20
            if (!isEncrypted && !Patterns.EMAIL_ADDRESS.matcher(dto.email).matches()) {
                errors.add("Invalid email format in DTO: ${dto.email}")
            }
        }

        // Validate timestamps
        dto.createdAt?.let { timestamp ->
            val instant = timestamp.toInstant()
            if (instant.isBefore(Instant.ofEpochMilli(0))) {
                errors.add("Invalid createdAt timestamp: $timestamp")
            }
        }

        dto.lastLoginAt?.let { timestamp ->
            val instant = timestamp.toInstant()
            if (instant.isBefore(Instant.ofEpochMilli(0))) {
                errors.add("Invalid lastLoginAt timestamp: $timestamp")
            }
        }

        Log.d(TAG, "User_profile validation complete: ${errors.size} errors, ${warnings.size} warnings")
        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }

    /**
     * Validate Delivery domain object
     */
    fun validateDelivery(delivery: Delivery): ValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        // ✅ ARCHITECTURAL FIX: Details are guaranteed non-null by domain model
        Log.d(TAG, "Validation entry - Thread: ${Thread.currentThread().id}, Delivery: ${delivery.id}")

        // Required fields
        if (delivery.id.isBlank()) {
            errors.add("Delivery ID is required")
        }

        // ✅ SIMPLIFIED VALIDATION: Details are guaranteed non-null by type system
        val details = delivery.details
            // User ID validation
            if (details.userId.isBlank()) {
                errors.add("User ID is required for delivery")
            }

            // Order ID validation
            if (details.orderId.isBlank()) {
                warnings.add("Order ID is empty")
            } else if (details.orderId.length > MAX_ORDER_ID_LENGTH) {
                errors.add("Order ID too long: ${details.orderId.length} characters (max: $MAX_ORDER_ID_LENGTH)")
            }

            // Address validation
            details.address?.let { address ->
                validateSimpleAddress(address).let { addressResult ->
                    errors.addAll(addressResult.errors.map { "Address: $it" })
                    warnings.addAll(addressResult.warnings.map { "Address: $it" })
                }
            } ?: warnings.add("Delivery address is missing")

            // Amounts validation
            details.amounts?.let { amounts ->
                amounts.totalAmount?.let { value ->
                    if (value < MIN_ORDER_VALUE) {
                        errors.add("Total amount too low: $$value (min: $$MIN_ORDER_VALUE)")
                    } else if (value > MAX_ORDER_VALUE) {
                        errors.add("Total amount too high: $$value (max: $$MAX_ORDER_VALUE)")
                    }
                }

                amounts.tipAmount?.let { tip ->
                    if (tip < MIN_TIP_AMOUNT) {
                        errors.add("Tip amount cannot be negative: $$tip")
                    } else if (tip > MAX_TIP_AMOUNT) {
                        errors.add("Tip amount too high: $$tip (max: $$MAX_TIP_AMOUNT)")
                    }
                }

                amounts.basePay?.let { pay ->
                    if (pay < 0) {
                        errors.add("Base pay cannot be negative: $$pay")
                    }
                }

                amounts.finalPay?.let { pay ->
                    if (pay < 0) {
                        errors.add("Final pay cannot be negative: $$pay")
                    }
                }
            }

            // Notes validation
            details.notes?.let { notes ->
                if (notes.length > MAX_NOTES_LENGTH) {
                    errors.add("Notes too long: ${notes.length} characters (max: $MAX_NOTES_LENGTH)")
                }
            }

            // Status validation
            details.status?.let { status ->
                if (status.isCompleted == true) {
                    // Completed deliveries should have tip amount and completion time
                    if (details.amounts?.tipAmount == null) {
                        warnings.add("Completed delivery missing tip amount")
                    }
                    if (details.times?.completedAt == null) {
                        warnings.add("Completed delivery missing completion timestamp")
                    }
                }
            }

            // Time validation
            details.times?.let { times ->
                times.acceptedAt?.let { accepted ->
                    val acceptedDateTime = accepted.toLocalDateTime()
                    if (acceptedDateTime.isBefore(EARLIEST_DELIVERY_DATE)) {
                        errors.add("Delivery accepted date too early: $acceptedDateTime")
                    } else if (acceptedDateTime.isAfter(LATEST_DELIVERY_DATE)) {
                        errors.add("Delivery accepted date in future: $acceptedDateTime")
                    }
                }

                times.completedAt?.let { completed ->
                    val completedDateTime = completed.toLocalDateTime()
                    if (completedDateTime.isBefore(EARLIEST_DELIVERY_DATE)) {
                        errors.add("Delivery completed date too early: $completedDateTime")
                    } else if (completedDateTime.isAfter(LATEST_DELIVERY_DATE)) {
                        errors.add("Delivery completed date in future: $completedDateTime")
                    }

                    // Check logical order of timestamps
                    times.acceptedAt?.let { accepted ->
                        if (completed.isBefore(accepted)) {
                            errors.add("Delivery completed before it was accepted")
                        }
                    }
                }
            }

        Log.d(TAG, "Delivery validation complete: ${errors.size} errors, ${warnings.size} warnings")
        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }

    /**
     * Validate SimpleAddress (used in deliveries)
     */
    private fun validateSimpleAddress(address: com.autogratuity.domain.model.SimpleAddress): ValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        // Required fields
        if (address.id.isBlank()) {
            errors.add("Address ID is required")
        }

        // Full address validation
        if (address.fullAddress.isNullOrBlank()) {
            errors.add("Full address is required")
        } else if (address.fullAddress.length > MAX_ADDRESS_LENGTH) {
            errors.add("Address too long: ${address.fullAddress.length} characters (max: $MAX_ADDRESS_LENGTH)")
        }

        // Coordinates validation
        address.latitude?.let { lat ->
            if (lat < -90.0 || lat > 90.0) {
                errors.add("Invalid latitude: $lat (must be between -90 and 90)")
            }
        }

        address.longitude?.let { lng ->
            if (lng < -180.0 || lng > 180.0) {
                errors.add("Invalid longitude: $lng (must be between -180 and 180)")
            }
        }

        // If one coordinate is present, both should be
        if ((address.latitude == null) != (address.longitude == null)) {
            warnings.add("Both latitude and longitude should be provided together")
        }

        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }

    /**
     * Validate Address domain object
     */
    fun validateAddress(address: Address): ValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        // Required fields
        if (address.id.isBlank()) {
            errors.add("Address ID is required")
        }

        if (address.userId.isNullOrBlank()) {
            errors.add("User ID is required for address")
        }

        // Full address validation
        if (address.fullAddress.isNullOrBlank()) {
            errors.add("Full address is required")
        } else if (address.fullAddress.length > MAX_ADDRESS_LENGTH) {
            errors.add("Address too long: ${address.fullAddress.length} characters (max: $MAX_ADDRESS_LENGTH)")
        }

        // Coordinates validation
        address.coordinates?.let { coords ->
            coords.latitude?.let { lat ->
                if (lat < -90.0 || lat > 90.0) {
                    errors.add("Invalid latitude: $lat (must be between -90 and 90)")
                }
            }

            coords.longitude?.let { lng ->
                if (lng < -180.0 || lng > 180.0) {
                    errors.add("Invalid longitude: $lng (must be between -180 and 180)")
                }
            }

            // If one coordinate is present, both should be
            if ((coords.latitude == null) != (coords.longitude == null)) {
                warnings.add("Both latitude and longitude should be provided together")
            }
        }

        // Components validation
        address.components?.let { components ->
            if (components.streetNumber.isNullOrBlank() && components.streetName.isNullOrBlank()) {
                warnings.add("Street information is incomplete")
            }
            if (components.city.isNullOrBlank()) {
                warnings.add("City is missing")
            }
            if (components.state.isNullOrBlank()) {
                warnings.add("State is missing")
            }
            if (components.postalCode.isNullOrBlank()) {
                warnings.add("ZIP code is missing")
            }
        }

        Log.d(TAG, "Address validation complete: ${errors.size} errors, ${warnings.size} warnings")
        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }

    /**
     * Validate UserPreferences
     */
    private fun validateUserPreferences(preferences: UserPreferences): ValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        // Theme validation - add error conditions for invalid themes
        preferences.theme?.let { theme ->
            if (theme.isBlank()) {
                errors.add("Theme cannot be blank")
            } else if (theme !in listOf("light", "dark", "system")) {
                warnings.add("Unknown theme: $theme")
            }
        }

        // DND validation - add actual validation logic
        preferences.dnd?.let { dnd ->
            // Add specific DND validation if needed
            // For now, just validate it's not empty if present
            if (dnd.toString().isBlank()) {
                errors.add("DND preferences cannot be blank")
            }
        }

        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }

    /**
     * Validate UserSubscription
     */
    private fun validateUserSubscription(subscription: UserSubscription): ValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        // Status validation
        if (subscription.status.isNullOrBlank()) {
            errors.add("Subscription status is required")
        }

        // Date validation
        subscription.startDate?.let { start ->
            subscription.expiryDate?.let { end ->
                if (end.isBefore(start)) {
                    errors.add("Subscription end date cannot be before start date")
                }
            }
        }

        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }

    /**
     * Validate a list of deliveries and return bulk results
     */
    fun validateDeliveries(deliveries: List<Delivery>): BulkValidationResult {
        val validDeliveries = mutableListOf<Delivery>()
        val issues = mutableListOf<String>()
        var warningCount = 0
        var errorCount = 0

        deliveries.forEachIndexed { index, delivery ->
            val result = validateDelivery(delivery)
            
            if (result.isValid) {
                validDeliveries.add(delivery)
                warningCount += result.warnings.size
                if (result.warnings.isNotEmpty()) {
                    issues.addAll(result.warnings.map { "Delivery $index: $it" })
                }
            } else {
                errorCount += result.errors.size
                warningCount += result.warnings.size
                issues.addAll(result.errors.map { "Delivery $index ERROR: $it" })
                issues.addAll(result.warnings.map { "Delivery $index WARNING: $it" })
            }
        }

        Log.d(TAG, "Bulk delivery validation complete: ${validDeliveries.size}/${deliveries.size} valid, $errorCount errors, $warningCount warnings")
        
        return BulkValidationResult(
            validDeliveries = validDeliveries,
            totalProcessed = deliveries.size,
            warningCount = warningCount,
            errorCount = errorCount,
            issues = issues
        )
    }

    /**
     * Check if order ID format is valid (business rule)
     */
    fun isValidOrderIdFormat(orderId: String?): Boolean {
        if (orderId.isNullOrBlank()) return false
        
        // Order ID should be alphanumeric with optional hyphens/underscores
        return orderId.matches(Regex("^[a-zA-Z0-9_-]+$")) && orderId.length <= MAX_ORDER_ID_LENGTH
    }

    /**
     * Check if coordinates appear to be in a valid delivery area (business rule)
     */
    fun isValidDeliveryLocation(latitude: Double?, longitude: Double?): Boolean {
        if (latitude == null || longitude == null) return false

        // Basic sanity check for coordinates
        return latitude in -90.0..90.0 && longitude in -180.0..180.0
    }

    /**
     * ✅ VALIDATION TEST: Test the validation approach
     * This method can be called to verify that validation works correctly
     */
    fun testValidation(delivery: Delivery, context: String = "TEST"): ValidationResult {
        Log.d(TAG, "🧪 VALIDATION TEST [$context]: Starting for delivery ${delivery.id}")

        // Since details are guaranteed non-null by domain model, no defensive copying needed
        val detailsSnapshot = delivery.details
        Log.d(TAG, "🧪 [$context] Details captured: non-null guaranteed by type system")
        Log.d(TAG, "🧪 [$context] Details identity: ${System.identityHashCode(detailsSnapshot)}")

        // Validate delivery directly
        val result = validateDelivery(delivery)
        Log.d(TAG, "🧪 [$context] Validation result: valid=${result.isValid}, errors=${result.errors.size}")

        return result
    }
}

/**
 * Validation result for single entity
 */
data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String> = emptyList(),
    val warnings: List<String> = emptyList()
)

/**
 * Validation result for bulk operations
 */
data class BulkValidationResult(
    val validDeliveries: List<Delivery>,
    val totalProcessed: Int,
    val warningCount: Int,
    val errorCount: Int,
    val issues: List<String>
) 