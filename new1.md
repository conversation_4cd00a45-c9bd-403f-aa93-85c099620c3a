 D  findOrCreateAddressFromPlace (SSoT) - Looking up or creating address for Place ID: ChIJH0f__R6Z7ocRUDtistop7Xk, User ID: L8H0VqJmI0dZEUz9g61vJYPKESB2
 D  Existing address found for placeId ChIJH0f__R6Z7ocRUDtistop7Xk via remoteDataSource
 D  === ADDRESS MAPPING TRANSFORMATION ===
 D  Input DTO State: {dto_id=DsHcI9mTEErwEzVvt5VH, dto_userId=L8H0VqJmI0dZEUz9g61vJYPKESB2, dto_fullAddress=555 17th St, Des Moines, IA 50309, USA, dto_coordinates=Coordinates(latitude=41.586480300000005, longitude=-93.63950969999999), dto_tags_count=0, dto_isDefault=null}
 D  Output Domain State: {domain_id=DsHcI9mTEErwEzVvt5VH, domain_userId=L8H0VqJmI0dZEUz9g61vJYPKESB2, domain_fullAddress=555 17th St, Des Moines, IA 50309, USA, domain_coordinates=Coordinates(latitude=41.586480300000005, longitude=-93.63950969999999), domain_tags_count=0, domain_isDefault=null}
 D  Field Transformations: [fullAddress: DECRYPT, normalizedAddress: DECRYPT, placeId: DECRYPT, notes: DECRYPT, coordinates: DECRYPT_COMPONENTS, tags: DECRYPT_LIST, components: DECRYPT_NESTED]
 D  Business Logic Applied: [pii_decryption, field_validation, flags_mapping, platform_mapping]
 D  PII Fields Processed: 10
 D  Mapping Duration: 0ms
 D  [AddressMapper] toSsot(Address) ID:DsHcI9mTEErwEzVvt5VH User:L8H0VqJmI0dZEUz9g61vJYPKESB2 Size:1246bytes Fields:7 PII:10 Decrypt:0ms Logic:[pii_decryption,field_validation,flags_mapping,platform_mapping] Address: 0ms [OK]
 D  findOrCreateAddressFromPlace (SSoT) - Successfully found/created and mapped SSoT address: DsHcI9mTEErwEzVvt5VH
 D  getCurrentUserIdSuspend: Checking authentication state
 D  waitForAuthentication: Starting authentication readiness check (timeout: 5000ms)
 D  waitForAuthentication: Authentication state immediately available: Authenticated(userId=L8H0VqJmI0dZEUz9g61vJYPKESB2)
 D  getCurrentUserIdSuspend: Authentication confirmed for user L8H0VqJmI0dZEUz9g61vJYPKESB2
 D  Scheduling task addDelivery_1750022328098 with priority HIGH
 D  Executing task addDelivery_1750022328098 (HIGH)
 D  validateDelivery: Validating delivery 
 D  validateDelivery: Delivery  is valid
 D  CONVERSION: Delivery SSoT -> DTO | ID:  | User: L8H0VqJmI0dZEUz9g61vJYPKESB2
 D    Input: orderId=555565265 | status=CREATED | tipAmount=null
 W    ⚠️ CLOUD FUNCTION WARNING: Delivery  missing tipAmount - will be skipped for tip counting
 W    ⚠️ CLOUD FUNCTION WARNING: Delivery  missing completedAt - may have sorting issues
 D    Converting address: 555 17th St, Des Moines, IA 50309, USA... (ID: DsHcI9mTEErwEzVvt5VH)
 D    Created missing reference with addressId: DsHcI9mTEErwEzVvt5VH
 D  === DELIVERY SSoT -> DTO TRANSFORMATION ===
 D  Input SSoT State: {ssot_id=, ssot_userId=L8H0VqJmI0dZEUz9g61vJYPKESB2, ssot_orderId=555565265, ssot_tipAmount=null, ssot_status=CREATED, ssot_notes=empty, ssot_addressId=DsHcI9mTEErwEzVvt5VH}
 D  Output DTO State: {dto_userId=L8H0VqJmI0dZEUz9g61vJYPKESB2, dto_orderId=555565265, dto_tipAmount=0.0, dto_status=CREATED, dto_notes=empty, dto_addressId=DsHcI9mTEErwEzVvt5VH}
 D  Field Transformations: [notes: ENCRYPT, address.fullAddress: PLAIN_TEXT, address.placeId: PLAIN_TEXT, address.coordinates: PLAIN_TEXT, amounts: CURRENCY_CONVERSION, status: STATE_MAPPING, times: TIMESTAMP_MAPPING]
 D  Business Logic Applied: [notes_encryption, field_validation, address_plain_mapping, status_mapping, amounts_calculation]
 D  PII Fields Encrypted: 0
 D  Mapping Duration: 1ms
 D  CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: 
 D    Output: encrypted_fields=0 | validation_errors=0 | duration=1ms
 D    DTO size: 1111 bytes
 D    Business logic: address_mapping, status_mapping, amounts_calculation
 D  executeAddDeliveryOperation: Adding delivery with transaction for user L8H0VqJmI0dZEUz9g61vJYPKESB2
 D  [DeliveryMapper] toDto Delivery ID: User:L8H0VqJmI0dZEUz9g61vJYPKESB2 Size:1111bytes Delivery: 1ms [OK]
 D  Attempting to add new delivery (from map) and update stats for user: L8H0VqJmI0dZEUz9g61vJYPKESB2
 D  isAssociateAddressIfNotFound: false, rawAddressDetails provided: false
 D  app_time_stats: avg=58.80ms min=11.58ms max=499.93ms count=17
 D  Delivery (from map) added to transaction with ID: v8NF1156UlDpYksPHiio
 D  Address DsHcI9mTEErwEzVvt5VH already has 1 deliveries - not incrementing address count.
 D  Incrementing user profile delivery count (both usage and usageStats) for L8H0VqJmI0dZEUz9g61vJYPKESB2.
 D  Incremented address stats for DsHcI9mTEErwEzVvt5VH.
 D  Delivery (from map) added to transaction with ID: g753tKMJWWOB1Nx4nqa3
 D  Address DsHcI9mTEErwEzVvt5VH already has 1 deliveries - not incrementing address count.
 D  Incrementing user profile delivery count (both usage and usageStats) for L8H0VqJmI0dZEUz9g61vJYPKESB2.
 D  Incremented address stats for DsHcI9mTEErwEzVvt5VH.
 E  Error in addNewDeliveryAndUpdateAllStatsTransaction (map version) for user L8H0VqJmI0dZEUz9g61vJYPKESB2: FAILED_PRECONDITION: the stored version (1750022329357743) does not match the required base version (1750022329202207) (Ask Gemini)
com.google.firebase.firestore.FirebaseFirestoreException: FAILED_PRECONDITION: the stored version (1750022329357743) does not match the required base version (1750022329202207)
	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:155)
	at com.google.firebase.firestore.remote.FirestoreChannel.exceptionFromStatus(FirestoreChannel.java:305)
	at com.google.firebase.firestore.remote.FirestoreChannel.access$100(FirestoreChannel.java:43)
	at com.google.firebase.firestore.remote.FirestoreChannel$4.onClose(FirestoreChannel.java:279)
	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574)
	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72)
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742)
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723)
	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
	at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: FAILED_PRECONDITION: the stored version (1750022329357743) does not match the required base version (1750022329202207)
	at io.grpc.Status.asException(Status.java:541)
	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:153)
	at com.google.firebase.firestore.remote.FirestoreChannel.exceptionFromStatus(FirestoreChannel.java:305) 
	at com.google.firebase.firestore.remote.FirestoreChannel.access$100(FirestoreChannel.java:43) 
	at com.google.firebase.firestore.remote.FirestoreChannel$4.onClose(FirestoreChannel.java:279) 
	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574) 
	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72) 
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742) 
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723) 
	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
	at java.lang.Thread.run(Thread.java:1012) 
 E  executeAddDeliveryOperation: Unexpected error for user L8H0VqJmI0dZEUz9g61vJYPKESB2 (Ask Gemini)
com.google.firebase.firestore.FirebaseFirestoreException: FAILED_PRECONDITION: the stored version (1750022329357743) does not match the required base version (1750022329202207)
	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:155)
	at com.google.firebase.firestore.remote.FirestoreChannel.exceptionFromStatus(FirestoreChannel.java:305)
	at com.google.firebase.firestore.remote.FirestoreChannel.access$100(FirestoreChannel.java:43)
	at com.google.firebase.firestore.remote.FirestoreChannel$4.onClose(FirestoreChannel.java:279)
	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574)
	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72)
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742)
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723)
	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
	at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: FAILED_PRECONDITION: the stored version (1750022329357743) does not match the required base version (1750022329202207)
	at io.grpc.Status.asException(Status.java:541)
	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:153)
	at com.google.firebase.firestore.remote.FirestoreChannel.exceptionFromStatus(FirestoreChannel.java:305) 
	at com.google.firebase.firestore.remote.FirestoreChannel.access$100(FirestoreChannel.java:43) 
	at com.google.firebase.firestore.remote.FirestoreChannel$4.onClose(FirestoreChannel.java:279) 
	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574) 
	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72) 
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742) 
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723) 
	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
	at java.lang.Thread.run(Thread.java:1012) 
 D  Task addDelivery_1750022321608 completed in 7388ms
 D  [data.DeliveryRepository] addDelivery(Delivery) ID: User:L8H0VqJmI0dZEUz9g61vJYPKESB2 Count:0 Source:error Strategy:write-through [CACHE_MISS]: 7389ms [ERROR]
 E  Task addDelivery_1750022321608 failed (Ask Gemini)
com.google.firebase.firestore.FirebaseFirestoreException: FAILED_PRECONDITION: the stored version (1750022329357743) does not match the required base version (1750022329202207)
	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:155)
	at com.google.firebase.firestore.remote.FirestoreChannel.exceptionFromStatus(FirestoreChannel.java:305)
	at com.google.firebase.firestore.remote.FirestoreChannel.access$100(FirestoreChannel.java:43)
	at com.google.firebase.firestore.remote.FirestoreChannel$4.onClose(FirestoreChannel.java:279)
	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574)
	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72)
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742)
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723)
	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
	at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: FAILED_PRECONDITION: the stored version (1750022329357743) does not match the required base version (1750022329202207)
	at io.grpc.Status.asException(Status.java:541)
	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:153)
	at com.google.firebase.firestore.remote.FirestoreChannel.exceptionFromStatus(FirestoreChannel.java:305) 
	at com.google.firebase.firestore.remote.FirestoreChannel.access$100(FirestoreChannel.java:43) 
	at com.google.firebase.firestore.remote.FirestoreChannel$4.onClose(FirestoreChannel.java:279) 
	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574) 
	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72) 
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742) 
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723) 
	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
	at java.lang.Thread.run(Thread.java:1012) 
 E  [server_error] Firestore database error during addDelivery (Code: FAILED_PRECONDITION) (Operation: addDelivery, Timestamp: 2025-06-15T21:18:48.998039Z) (Ask Gemini)
com.google.firebase.firestore.FirebaseFirestoreException: FAILED_PRECONDITION: the stored version (1750022329357743) does not match the required base version (1750022329202207)
	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:155)
	at com.google.firebase.firestore.remote.FirestoreChannel.exceptionFromStatus(FirestoreChannel.java:305)
	at com.google.firebase.firestore.remote.FirestoreChannel.access$100(FirestoreChannel.java:43)
	at com.google.firebase.firestore.remote.FirestoreChannel$4.onClose(FirestoreChannel.java:279)
	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574)
	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72)
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742)
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723)
	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
	at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: FAILED_PRECONDITION: the stored version (1750022329357743) does not match the required base version (1750022329202207)
	at io.grpc.Status.asException(Status.java:541)
	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:153)
	at com.google.firebase.firestore.remote.FirestoreChannel.exceptionFromStatus(FirestoreChannel.java:305) 
	at com.google.firebase.firestore.remote.FirestoreChannel.access$100(FirestoreChannel.java:43) 
	at com.google.firebase.firestore.remote.FirestoreChannel$4.onClose(FirestoreChannel.java:279) 
	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574) 
	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72) 
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742) 
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723) 
	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
	at java.lang.Thread.run(Thread.java:1012) 
 E  Error adding delivery (Ask Gemini)
com.autogratuity.data.repository.core.RepositoryException$UnknownError: Firestore database error during addDelivery (Code: FAILED_PRECONDITION) (Operation: addDelivery, Timestamp: 2025-06-15T21:18:48.998039Z)
	at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:155)
	at com.google.firebase.firestore.remote.FirestoreChannel.exceptionFromStatus(FirestoreChannel.java:305)
	at com.google.firebase.firestore.remote.FirestoreChannel.access$100(FirestoreChannel.java:43)
	at com.google.firebase.firestore.remote.FirestoreChannel$4.onClose(FirestoreChannel.java:279)
	at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:574)
	at io.grpc.internal.ClientCallImpl.access$300(ClientCallImpl.java:72)
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInternal(ClientCallImpl.java:742)
	at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1StreamClosed.runInContext(ClientCallImpl.java:723)
	at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
	at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
	at java.lang.Thread.run(Thread.java:1012)
 D  Delivery (from map) added to transaction with ID: FnJ6LlfUnF3BSwntSksq
 D  Address DsHcI9mTEErwEzVvt5VH already has 1 deliveries - not incrementing address count.
 D  Incrementing user profile delivery count (both usage and usageStats) for L8H0VqJmI0dZEUz9g61vJYPKESB2.
 D  Incremented address stats for DsHcI9mTEErwEzVvt5VH.
 D  app_time_stats: avg=13.13ms min=3.33ms max=27.87ms count=60
 D  app_time_stats: avg=116.71ms min=2.53ms max=516.98ms count=8
 D  Delivery (from map) added to transaction with ID: DnJEm4hMJnh7iWwLSgOJ
 D  Address DsHcI9mTEErwEzVvt5VH already has 1 deliveries - not incrementing address count.
 D  Incrementing user profile delivery count (both usage and usageStats) for L8H0VqJmI0dZEUz9g61vJYPKESB2.
 D  Incremented address stats for DsHcI9mTEErwEzVvt5VH.
 D  app_time_stats: avg=499.80ms min=499.28ms max=500.18ms count=3
 D  app_time_stats: avg=444.39ms min=333.55ms max=501.22ms count=3
 D  Delivery (from map) added to transaction with ID: 1qNAsAgWCZFENYYz71MS
 D  Address DsHcI9mTEErwEzVvt5VH already has 1 deliveries - not incrementing address count.
 D  Incrementing user profile delivery count (both usage and usageStats) for L8H0VqJmI0dZEUz9g61vJYPKESB2.
 D  Incremented address stats for DsHcI9mTEErwEzVvt5VH.
 D  app_time_stats: avg=145.66ms min=14.30ms max=500.05ms count=8
 D  app_time_stats: avg=499.97ms min=499.39ms max=500.90ms count=3
 D  app_time_stats: avg=499.24ms min=497.15ms max=500.90ms count=3
 E  Error in addNewDeliveryAndUpdateAllStatsTransaction (map version) for user L8H0VqJmI0dZEUz9g61vJYPKESB2: Timed out waiting for 10000 ms (Ask Gemini)
kotlinx.coroutines.TimeoutCancellationException: Timed out waiting for 10000 ms
	at kotlinx.coroutines.TimeoutKt.TimeoutCancellationException(Timeout.kt:189)
	at kotlinx.coroutines.TimeoutCoroutine.run(Timeout.kt:157)
	at kotlinx.coroutines.EventLoopImplBase$DelayedRunnableTask.run(EventLoop.common.kt:505)
	at kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
	at kotlinx.coroutines.DefaultExecutor.run(DefaultExecutor.kt:105)
	at java.lang.Thread.run(Thread.java:1012)
 E  executeAddDeliveryOperation: Unexpected error for user L8H0VqJmI0dZEUz9g61vJYPKESB2 (Ask Gemini)
kotlinx.coroutines.TimeoutCancellationException: Timed out waiting for 10000 ms
	at kotlinx.coroutines.TimeoutKt.TimeoutCancellationException(Timeout.kt:189)
	at kotlinx.coroutines.TimeoutCoroutine.run(Timeout.kt:157)
	at kotlinx.coroutines.EventLoopImplBase$DelayedRunnableTask.run(EventLoop.common.kt:505)
	at kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
	at kotlinx.coroutines.DefaultExecutor.run(DefaultExecutor.kt:105)
	at java.lang.Thread.run(Thread.java:1012)
 W  Task addDelivery_1750022328098 timed out after 10s
 D  [data.DeliveryRepository] addDelivery(Delivery) ID: User:L8H0VqJmI0dZEUz9g61vJYPKESB2 Count:0 Source:error Strategy:write-through [CACHE_MISS]: 10004ms [ERROR]
 E  Exception adding delivery (Ask Gemini)
kotlinx.coroutines.TimeoutCancellationException: Timed out waiting for 10000 ms
	at kotlinx.coroutines.TimeoutKt.TimeoutCancellationException(Timeout.kt:189)
	at kotlinx.coroutines.TimeoutCoroutine.run(Timeout.kt:157)
	at kotlinx.coroutines.EventLoopImplBase$DelayedRunnableTask.run(EventLoop.common.kt:505)
	at kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
	at kotlinx.coroutines.DefaultExecutor.run(DefaultExecutor.kt:105)
	at java.lang.Thread.run(Thread.java:1012)
 D  app_time_stats: avg=100.60ms min=9.96ms max=499.51ms count=10
 D  Delivery (from map) added to transaction with ID: FOZAl1V2peNDejc7x7Ie
 D  Address DsHcI9mTEErwEzVvt5VH already has 1 deliveries - not incrementing address count.
 D  Incrementing user profile delivery count (both usage and usageStats) for L8H0VqJmI0dZEUz9g61vJYPKESB2.
 D  Incremented address stats for DsHcI9mTEErwEzVvt5VH.
 D  app_time_stats: avg=252.08ms min=11.77ms max=517.00ms count=4
 D  app_time_stats: avg=499.89ms min=498.73ms max=500.91ms count=3
 D  app_time_stats: avg=499.95ms min=499.77ms max=500.13ms count=2
 D  app_time_stats: avg=166.29ms min=13.83ms max=498.74ms count=9
 D  app_time_stats: avg=500.01ms min=499.54ms max=500.47ms count=2