/*
 * App_config.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

import java.time.OffsetDateTime

/**
 * Defines the structure for application configuration, typically stored in 'system_config/app_config' in Firestore.
 */
data class App_config(
    /** Application version control information. */
    val versions: Versions,
    /** Feature flags for enabling/disabling application functionalities. */
    val features: Features,
    /** Usage limits for different subscription tiers. */
    val limits: Limits,
    /** Configuration for data synchronization. */
    val sync: Sync,
    /** Application maintenance mode settings. */
    val maintenance: Maintenance,
    /** Timestamp of the last update to the app configuration. */
    val updatedAt: OffsetDateTime? = null,
    /** Schema version or data version for optimistic concurrency or migrations. */
    val version: Long,
    /** A map for storing any custom or ad-hoc configuration data. */
    val customData: CustomData? = null
) {

    /**
     * Application version control information.
     */
    data class Versions(
        /** Minimum required application version. */
        val minimum: String,
        /** Recommended application version. */
        val recommended: String,
        /** Latest available application version. */
        val latest: String
    )

    /**
     * Feature flags for enabling/disabling application functionalities.
     */
    data class Features(
        val useNewSyncSystem: Boolean = true,
        val enableOfflineMode: Boolean = true,
        val enableAnalytics: Boolean = true,
        val enableBackgroundSync: Boolean = true,
        val enforceVersionCheck: Boolean = false
    )

    /**
     * Usage limits for different subscription tiers.
     */
    data class Limits(
        val freeTier: TierLimits,
        val proTier: TierLimits
    )

    data class TierLimits(
        /** Limit for mappings. */
        val mappingLimit: Long,
        /** Limit for imports. */
        val importLimit: Long,
        /** Limit for exports. */
        val exportLimit: Long
    )

    /**
     * Configuration for data synchronization.
     */
    data class Sync(
        /** Sync interval in seconds for foreground. */
        val interval: Long,
        /** Sync interval in seconds for background. */
        val backgroundInterval: Long,
        /** Maximum batch size for sync operations. */
        val maxBatchSize: Long,
        /** Strategy to resolve sync conflicts (e.g., 'server_wins', 'client_wins'). */
        val conflictStrategy: String
    )

    /**
     * Application maintenance mode settings.
     */
    data class Maintenance(
        /** Flag indicating if the app is currently in maintenance mode. */
        val isInMaintenance: Boolean = false,
        /** Message to display to users during maintenance. */
        val maintenanceMessage: String? = null,
        /** Estimated end time for the maintenance window. */
        val estimatedEndTime: OffsetDateTime? = null
    )

    /**
     * A map for storing any custom or ad-hoc configuration data.
     */
    open class CustomData

}
