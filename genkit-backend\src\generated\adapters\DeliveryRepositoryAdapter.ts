// Auto-generated from DeliveryRepository.kt
// IMPORTS FROM EXISTING GENERATED MODELS (not recreated)

import type { Result } from '../types/Result';
import type {
  Delivery as TsDeliveryInterface,
  Address as TsAddressInterface,
  User_profile as TsUserProfileInterface
} from '../../models/generated/delivery.schema';
import type {
  DeliveryStats as TsDeliveryStatsInterface
} from '../../models/generated/address.schema';
import type { DeliveryData as TsDeliveryData } from '../../models/generated/delivery.schema';

/**
 * TypeScript adapter interface generated from DeliveryRepository.kt
 * Maintains identical contract to Kotlin implementation
 * Uses existing generated models from schemas
 */
export interface DeliveryRepositoryAdapter {
  getDeliveryById(id: string): Promise<Result<Delivery | null>>;
  getDeliveriesByUserId(userId: string): Promise<Result<Delivery[]>>;
  getDeliveriesByOrderId(orderId: string): Promise<Result<Delivery[]>>;
  addDelivery(delivery: Delivery): Result<string>;
  updateDelivery(delivery: Delivery): Promise<Result<void>>;
  deleteDelivery(id: string): Promise<Result<void>>;
  observeDeliveryById(id: string): Flow<Promise<Result<Delivery | null>>>;
  observeDeliveriesByUserId(userId: string): Flow<Promise<Result<Delivery[]>>>;
  observeDeliveriesByOrderId(orderId: string): Flow<Promise<Result<Delivery[]>>>;
  getDeliveriesByDateRange(userId: string, startDate: Date, endDate: Date): Promise<Result<Delivery[]>>;
  getDeliveriesByStatus(userId: string, status: Status): Promise<Result<Delivery[]>>;
  getRecentDeliveries(userId: string, limit: number): Promise<Result<Delivery[]>>;
  getTippedDeliveries(userId: string, limit: number): Promise<Result<Delivery[]>>;
  getUntippedDeliveries(userId: string, limit: number): Promise<Result<Delivery[]>>;
  getDeliveriesByAddress(userId: string, addressId: string): Promise<Result<Delivery[]>>;
  findDeliveryByMetadataOrderId(orderId: string): Promise<Result<Delivery | null>>;
  updateDeliveryTip(deliveryId: string, tipAmount: number, tipPercentage: number? = null, timestamp: Date? = null): Promise<Result<void>>;
  updateDeliveryStatus(deliveryId: string, status: Status, timestamp: Date? = null): Promise<Result<void>>;
  markDeliveryAsCompleted(deliveryId: string, completionTime: Date): Promise<Result<void>>;
  reassociateDeliveryAddress(userId: string, deliveryId: string, newAddressId: string): Promise<Result<void>>;
  getOldestDeliveryTimestamp(userId: string): Result<Date?>;
  createDefaultDelivery(): Result<Delivery>;
  prefetchCriticalData(userId: string): Promise<Result<void>>;
  clearCache(userId: string): Promise<Result<void>>;
  clearAllCache(): Promise<Result<void>>;
  invalidateCache(deliveryId: string): Promise<Result<void>>;
  invalidateUserCache(userId: string): Promise<Result<void>>;
  prefetch(userId: string, limit: number): Result<Map<string, Any>>;
  initialize(): Promise<Result<void>>;
  cleanup(): Promise<Result<void>>;
  importDeliveries(userId: string, deliveries: List<Map<string): Result<number>;
  exportDeliveries(userId: string, startDate: Date? = null, endDate: Date? = null, format: string = "json"): Result<string>;
  getDeliveriesPaginated(userId: string, limit: number, offset: number): Promise<Result<Delivery[]>>;
  getDeliveryCountByDateRange(userId: string, startDate: Date, endDate: Date): Result<number>;
  validateDelivery(delivery: Delivery): Promise<Result<void>>;
  validateDeliveryWithReferences(delivery: Delivery): Result<com.autogratuity.data.model.SingleValidationResult>;
  validateDeliveries(deliveries: Delivery[]): Result<com.autogratuity.data.model.BulkValidationResult>;
  normalizeDelivery(delivery: Delivery): Delivery;
  deliveryExistsByOrderId(userId: string, orderId: string): Result<boolean>;
  getDeliveryCompletionRate(userId: string, startDate: Date, endDate: Date): Result<number>;
  getAverageTipAmount(userId: string, startDate: Date, endDate: Date): Result<number>;
  getAllDeliveries(): Promise<Result<Delivery[]>>;
  getCompletedAndUntippedDeliveries(): Promise<Result<Delivery[]>>;
  getDeliveriesByMetadataSource(source: string): Promise<Result<Delivery[]>>;
  getDeliveriesByOrderIds(orderIds: string[]): Promise<Result<Delivery[]>>;
  getImportedVerifiedDeliveriesForDndCheck(importedBeforeDate: java.util.Date): Promise<Result<Delivery[]>>;
}