package com.autogratuity.data.security

import android.util.Log
import com.autogratuity.data.repository.core.RepositoryException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.GetTokenResult
import kotlinx.atomicfu.atomic
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.tasks.await
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

/**
 * Modern implementation of AuthenticationManager following 2025 Kotlin standards.
 * 
 * ✅ KOIN COMPATIBLE - No Hilt annotations, uses KOIN manual constructor injection
 *
 * This implementation provides:
 * - Thread-safe token caching with TTL support using atomicfu
 * - Reactive authentication state observation using StateFlow
 * - Comprehensive error handling with Result<T> pattern
 * - Modern time handling with kotlinx-datetime
 * - Intelligent token refresh with configurable margins
 * - Proper resource cleanup and lifecycle management
 *
 * @property firebaseAuth The Firebase Authentication instance
 * @property applicationScope Coroutine scope tied to the application lifecycle
 * @since 2025.05.22
 */
class AuthenticationManagerImpl(
    private val firebaseAuth: FirebaseAuth,
    private val applicationScope: CoroutineScope
) : AuthenticationManager {

    companion object {
        private const val TAG = "AuthenticationManagerImpl"
        private val TOKEN_EXPIRY_MARGIN = 5.minutes
        private val TOKEN_CACHE_TTL = 55.minutes
        private val MIN_TOKEN_REFRESH_INTERVAL = 30.seconds
    }

    private val tokenCache = atomic<Map<String, CachedToken>>(emptyMap())
    private val lastTokenRefreshTime = atomic<Map<String, Instant>>(emptyMap())
    private val _authenticationState = MutableStateFlow(firebaseAuth.currentUser != null)
    private val _currentUser = MutableStateFlow<FirebaseUser?>(firebaseAuth.currentUser)
    private val operationCounter = atomic(0) // For metrics or debugging

    init {
        observeAuthStateChangesInternal()
            .onEach { user: FirebaseUser? ->
                _authenticationState.value = user != null
                _currentUser.value = user
                if (user == null) {
                    Log.d(TAG, "User signed out or auth state changed to null. Clearing cache.")
                    clearAuthenticationCache()
                }
            }
            .launchIn(applicationScope)
        Log.d(TAG, "AuthenticationManagerImpl initialized and auth state listener started.")
    }

    private data class CachedToken(
        val token: String,
        val expiresAt: Instant,
        val issuedAt: Instant,
        val claims: Map<String, Any>,
        val cachedAt: Instant = Clock.System.now()
    ) {
        fun isStale(ttl: Duration = TOKEN_CACHE_TTL): Boolean = (Clock.System.now() - cachedAt) > ttl
        
        fun isValid(margin: Duration = Duration.ZERO): Boolean {
            val now = Clock.System.now()
            return now < (expiresAt - margin)
        }
    }

    override fun getCurrentUser(): FirebaseUser? = firebaseAuth.currentUser

    override fun getCurrentUserId(): String? = firebaseAuth.currentUser?.uid

    override fun isAuthenticated(): Boolean = firebaseAuth.currentUser != null

    override fun observeAuthenticationState(): StateFlow<Boolean> = _authenticationState.asStateFlow()

    override fun observeCurrentUser(): StateFlow<FirebaseUser?> = _currentUser.asStateFlow()

    override suspend fun getAuthToken(forceRefresh: Boolean): Result<String> {
        operationCounter.incrementAndGet()
        val userId = getCurrentUserId() ?: return Result.failure(RepositoryException.AuthenticationError("User not authenticated"))

        val cachedEntry = tokenCache.value[userId]
        if (!forceRefresh && cachedEntry != null) {
            if (!cachedEntry.isStale() && cachedEntry.isValid(TOKEN_EXPIRY_MARGIN)) {
                Log.d(TAG, "Returning valid cached token for user $userId.")
                return Result.success(cachedEntry.token)
            }
            val lastRefreshForUser = lastTokenRefreshTime.value[userId]
            if (lastRefreshForUser != null && (Clock.System.now() - lastRefreshForUser) < MIN_TOKEN_REFRESH_INTERVAL) {
                if (cachedEntry.isValid()) { // Check core validity without margin
                    Log.d(TAG, "Returning recently refreshed cached token for user $userId (core validity OK).")
                    return Result.success(cachedEntry.token)
                }
            }
        }
        Log.d(TAG, "No suitable cached token for user $userId or forceRefresh=$forceRefresh. Fetching fresh token.")
        return fetchFreshTokenInternal(userId, forceRefresh)
    }

    override suspend fun refreshToken(): Result<Unit> {
        Log.d(TAG, "Explicit token refresh requested.")
        return getAuthToken(forceRefresh = true).map { /* Transform String to Unit */ Unit }
    }

    override suspend fun getTokenExpirationTime(): Result<Instant> {
        val userId = getCurrentUserId() ?: return Result.failure(RepositoryException.AuthenticationError("User not authenticated"))
        val cachedEntry = tokenCache.value[userId]
        if (cachedEntry != null && !cachedEntry.isStale() && cachedEntry.isValid(TOKEN_EXPIRY_MARGIN)) {
            return Result.success(cachedEntry.expiresAt)
        }
        Log.d(TAG, "Token expiration time not readily available in cache for $userId. Attempting refresh.")
        return getAuthToken(forceRefresh = false).fold(
            onSuccess = {
                val updatedEntry = tokenCache.value[userId]
                if (updatedEntry != null) Result.success(updatedEntry.expiresAt)
                else {
                    Log.e(TAG, "Token cache inconsistency for $userId after refresh for expiration check.")
                    Result.failure(RepositoryException.AuthenticationError("Token cache inconsistency after refresh"))
                }
            },
            onFailure = { error ->
                Log.e(TAG, "Failed to get token for $userId while getting expiration time.", error)
                Result.failure(error)
            }
        )
    }

    override fun clearAuthenticationCache() {
        Log.d(TAG, "Clearing all authentication token caches.")
        tokenCache.value = emptyMap()
        lastTokenRefreshTime.value = emptyMap()
        // operationCounter.value = 0 // Resetting this depends on its intended use.
    }

    override suspend fun signOut(): Result<Unit> {
        val userIdForLog = getCurrentUserId()
        return try {
            firebaseAuth.signOut()
            // AuthStateListener in init handles cache clearing via _currentUser update.
            Log.d(TAG, "User ${userIdForLog ?: "<unknown>"} signed out successfully from Firebase.")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error signing out user ${userIdForLog ?: "<unknown>"}", e)
            Result.failure(RepositoryException.AuthenticationError("Sign out failed: ${e.message}", e))
        }
    }

    override suspend fun getAuthenticationInfo(): Result<AuthenticationInfo> {
        val fbUser = getCurrentUser() ?: return Result.failure(RepositoryException.AuthenticationError("User not authenticated"))
        return try {
            Result.success(AuthenticationInfo(
                userId = fbUser.uid,
                email = fbUser.email,
                emailVerified = fbUser.isEmailVerified,
                displayName = fbUser.displayName,
                photoUrl = fbUser.photoUrl?.toString(),
                providers = fbUser.providerData.mapNotNull { it.providerId },
                createdAt = fbUser.metadata?.creationTimestamp?.let { Instant.fromEpochMilliseconds(it) },
                lastSignInAt = fbUser.metadata?.lastSignInTimestamp?.let { Instant.fromEpochMilliseconds(it) },
                isAnonymous = fbUser.isAnonymous,
                phoneNumber = fbUser.phoneNumber
            ))
        } catch (e: Exception) {
            Log.e(TAG, "Error constructing AuthenticationInfo for user ${fbUser.uid}", e)
            Result.failure(RepositoryException.AuthenticationError("Failed to create AuthenticationInfo: ${e.message}", e))
        }
    }

    private fun observeAuthStateChangesInternal(): Flow<FirebaseUser?> = callbackFlow {
        val listener = FirebaseAuth.AuthStateListener { auth ->
            trySend(auth.currentUser) // No need to check isSuccess, trySend handles capacity
        }
        firebaseAuth.addAuthStateListener(listener)
        awaitClose {
            Log.d(TAG, "Removing AuthStateListener due to Flow cancellation.")
            firebaseAuth.removeAuthStateListener(listener)
        }
    }.flowOn(Dispatchers.IO) // Ensure listener registration/removal happens off the main thread.

    private suspend fun fetchFreshTokenInternal(userId: String, forceRefresh: Boolean): Result<String> {
        return fetchFreshToken(userId, forceRefresh)
    }

    /**
     * Fetches a fresh token from Firebase.
     *
     * @param userId The user ID to fetch token for
     * @param forceRefresh Whether to force a server refresh
     * @return Result containing the token or an error
     */
    private suspend fun fetchFreshToken(userId: String, forceRefresh: Boolean): Result<String> {
        val currentUser = firebaseAuth.currentUser // Capture current user at the start of the operation
        if (currentUser == null || currentUser.uid != userId) {
            Log.w(TAG, "fetchFreshToken: User mismatch or not authenticated. Expected $userId, got ${currentUser?.uid}")
            return Result.failure(
                RepositoryException.AuthenticationError("User mismatch or not authenticated during token fetch for $userId")
            )
        }

        return try {
            Log.d(TAG, "Fetching fresh token for user $userId (forceRefresh=$forceRefresh)")

            // Use the captured currentUser for the getIdToken call
            val tokenResult: GetTokenResult? = currentUser.getIdToken(forceRefresh).await()

            if (tokenResult == null) {
                Log.e(TAG, "fetchFreshToken: getIdToken().await() returned null for user $userId")
                return Result.failure(RepositoryException.AuthenticationError("getIdToken().await() returned null for user $userId"))
            }

            val tokenString = tokenResult.token
            if (tokenString.isNullOrEmpty()) {
                Log.e(TAG, "fetchFreshToken: Fetched token string is null or empty for user $userId")
                return Result.failure(RepositoryException.AuthenticationError("Fetched token string is null or empty for user $userId"))
            }

            val expiresAt = Instant.fromEpochSeconds(tokenResult.expirationTimestamp)
            val issuedAt = Instant.fromEpochSeconds(tokenResult.authTimestamp)
            val claims = tokenResult.claims ?: emptyMap<String, Any>()
            val newCachedToken = CachedToken(tokenString, expiresAt, issuedAt, claims)

            tokenCache.value = tokenCache.value + (userId to newCachedToken)
            lastTokenRefreshTime.value = lastTokenRefreshTime.value + (userId to Clock.System.now())

            Log.i(TAG, "Successfully fetched and cached new token for user $userId")
            Result.success(tokenString)
        } catch (e: CancellationException) {
            Log.w(TAG, "fetchFreshToken cancelled for user $userId", e)
            Result.failure(RepositoryException.UnknownError("Token fetch cancelled for $userId", e))
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching token for user $userId: ${e.message}", e)
            Result.failure(
                RepositoryException.AuthenticationError("Failed to fetch token for user $userId due to ${e.javaClass.simpleName}: ${e.message}", e)
            )
        }
    }

    /**
     * Gets metrics about authentication operations.
     *
     * @return Map of metric names to values
     */
    fun getMetrics(): Map<String, Any> {
        return mapOf(
            "totalOperations" to operationCounter.value,
            "cachedTokens" to tokenCache.value.size,
            "isAuthenticated" to isAuthenticated(),
            "currentUserId" to (getCurrentUserId() ?: "none")
        )
    }

    /**
     * Cleans up resources when the manager is no longer needed.
     * This might be called by a DI framework or manually if necessary.
     */
    private fun cleanup() {
        Log.d(TAG, "Cleaning up AuthenticationManagerImpl resources.")
        // Example: Cancel specific coroutines if authScope was instance-specific
        // authScope.cancel() // Only if authScope is not ApplicationScope and needs cleanup
        // Clear any listeners or resources manually registered if not handled by awaitClose etc.
        clearAuthenticationCache()
        // ApplicationScope is managed by Hilt - no manual cleanup needed
    }
}