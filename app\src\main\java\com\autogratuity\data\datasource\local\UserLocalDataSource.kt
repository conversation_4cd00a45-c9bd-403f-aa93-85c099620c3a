package com.autogratuity.data.datasource.local

import android.util.Log
import com.autogratuity.domain.model.User // SSoT Model
import com.autogratuity.domain.model.UserUsageStats
import com.autogratuity.data.model.Result // Common Result class
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import com.autogratuity.data.repository.user.UserProfileCacheSystem
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Interface for local data operations (caching) related to Users.
 * All methods operate with SSoT User models following Delivery/Address patterns.
 *
 * Enhanced with atomic caching integration:
 * - Reactive flows for loading states, errors, and statistics
 * - Domain-specific cache operations
 * - SSOT-aware profile management
 */
interface UserLocalDataSource {
    // Core CRUD operations
    suspend fun getUserById(currentUserId: String, userId: String): Result<User?>
    suspend fun getAllUsers(currentUserId: String): Result<List<User>>
    suspend fun saveUser(currentUserId: String, user: User): Result<Unit>
    suspend fun saveAllUsers(currentUserId: String, users: List<User>): Result<Unit>
    suspend fun deleteUserById(currentUserId: String, userId: String): Result<Unit>
    suspend fun deleteAllUsers(currentUserId: String): Result<Unit>
    suspend fun clearAllCaches(): Result<Unit>

    // Domain-specific operations (atomic caching integration)
    suspend fun getUserProfiles(currentUserId: String): Result<List<User>>
    suspend fun invalidateUserProfile(userId: String): Result<Unit>
    suspend fun cacheUserList(cacheKey: String, users: List<User>, userId: String): Result<Unit>

    // SSOT-aware operations (atomic caching integration)
    suspend fun updateUserProfileWithSsot(userId: String, updates: Map<String, Any>): Result<Unit>
    suspend fun updateFromCloudFunction(userId: String, cloudUser: User): Result<Unit>

    // Reactive observations
    fun observeById(currentUserId: String, userId: String): Flow<User?>
    fun observeAll(currentUserId: String): Flow<List<User>>
    fun observeCurrent(): Flow<User?>
    fun getCurrentUserProfile(): Flow<User?>

    // Reactive state flows (atomic caching integration)
    fun observeLoading(): Flow<Boolean>
    fun observeErrors(): Flow<String?>
    fun observeStats(): Flow<UserUsageStats?>
}

class UserLocalDataSourceImpl @Inject constructor(
    private val userProfileCacheSystem: UserProfileCacheSystem,
    private val ioDispatcher: CoroutineDispatcher // Koin will provide this via module definition
) : UserLocalDataSource {

    private val TAG = "UserLocalDataSource"

    override suspend fun getUserById(currentUserId: String, userId: String): Result<User?> = withContext(ioDispatcher) {
        try {
            // Validate user access - users can only access their own profile
            if (currentUserId != userId) {
                Log.w(TAG, "Access denied: User $currentUserId cannot access profile for $userId")
                return@withContext Result.Error(SecurityException("Cannot access profile for different user"))
            }

            val user = userProfileCacheSystem.getUser(userId)
            Log.d(TAG, "Retrieved user $userId for current user $currentUserId: ${user != null}")
            Result.Success(user)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user $userId for current user $currentUserId", e)
            Result.Error(e)
        }
    }

    override suspend fun getAllUsers(currentUserId: String): Result<List<User>> = withContext(ioDispatcher) {
        try {
            // For user profiles, typically only return the current user
            val user = userProfileCacheSystem.getUser(currentUserId)
            val users = if (user != null) listOf(user) else emptyList()
            Log.d(TAG, "Retrieved ${users.size} users for current user $currentUserId")
            Result.Success(users)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting all users for current user $currentUserId", e)
            Result.Error(e)
        }
    }

    override suspend fun saveUser(currentUserId: String, user: User): Result<Unit> = withContext(ioDispatcher) {
        try {
            // Validate user ownership
            if (currentUserId != user.id) {
                Log.w(TAG, "Access denied: User $currentUserId cannot save profile for ${user.id}")
                return@withContext Result.Error(SecurityException("Cannot save profile for different user"))
            }

            userProfileCacheSystem.cacheUser(user.id, user)
            Log.d(TAG, "Saved user ${user.id} for current user $currentUserId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error saving user ${user.id} for current user $currentUserId", e)
            Result.Error(e)
        }
    }

    override suspend fun saveAllUsers(currentUserId: String, users: List<User>): Result<Unit> = withContext(ioDispatcher) {
        try {
            // Validate all users belong to current user
            val invalidUsers = users.filter { it.id != currentUserId }
            if (invalidUsers.isNotEmpty()) {
                Log.w(TAG, "Access denied: User $currentUserId cannot save profiles for other users")
                return@withContext Result.Error(SecurityException("Cannot save profiles for different users"))
            }

            // Cache each user individually
            users.forEach { user ->
                userProfileCacheSystem.cacheUser(user.id, user)
            }
            Log.d(TAG, "Saved ${users.size} users for current user $currentUserId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error saving ${users.size} users for current user $currentUserId", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteUserById(currentUserId: String, userId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            // Validate user ownership
            if (currentUserId != userId) {
                Log.w(TAG, "Access denied: User $currentUserId cannot delete profile for $userId")
                return@withContext Result.Error(SecurityException("Cannot delete profile for different user"))
            }

            // Check if user exists before deletion
            val existingUser = userProfileCacheSystem.getUser(userId)
            if (existingUser != null) {
                userProfileCacheSystem.deleteUser(userId)
                Log.d(TAG, "Deleted user $userId for current user $currentUserId")
            } else {
                Log.w(TAG, "User $userId not found for current user $currentUserId")
            }
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting user $userId for current user $currentUserId", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteAllUsers(currentUserId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            // For user profiles, this means deleting the current user's profile
            userProfileCacheSystem.deleteUser(currentUserId)
            Log.d(TAG, "Deleted all users for current user $currentUserId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting all users for current user $currentUserId", e)
            Result.Error(e)
        }
    }

    override suspend fun clearAllCaches(): Result<Unit> = withContext(ioDispatcher) {
        try {
            userProfileCacheSystem.clear()
            Log.d(TAG, "Cleared all user caches")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all caches", e)
            Result.Error(e)
        }
    }

    override fun observeById(currentUserId: String, userId: String): Flow<User?> {
        // Validate user access in the flow
        if (currentUserId != userId) {
            Log.w(TAG, "Access denied: User $currentUserId cannot observe profile for $userId")
            return kotlinx.coroutines.flow.flowOf(null)
        }
        return userProfileCacheSystem.observe(userId)
    }

    override fun observeAll(currentUserId: String): Flow<List<User>> {
        // For user profiles, observe only the current user
        return userProfileCacheSystem.observe(currentUserId).map { user ->
            if (user != null) listOf(user) else emptyList()
        }
    }

    override fun observeCurrent(): Flow<User?> {
        return userProfileCacheSystem.observeCurrentUserProfile()
    }

    // Domain-specific operations (atomic caching integration)
    override suspend fun getUserProfiles(currentUserId: String): Result<List<User>> = withContext(ioDispatcher) {
        try {
            val profiles = userProfileCacheSystem.getUserProfiles(currentUserId)
            Log.d(TAG, "Retrieved ${profiles.size} user profiles for current user $currentUserId")
            Result.Success(profiles)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user profiles for current user $currentUserId", e)
            Result.Error(e)
        }
    }

    override suspend fun invalidateUserProfile(userId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            userProfileCacheSystem.invalidateUserProfile(userId)
            Log.d(TAG, "Invalidated user profile for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error invalidating user profile for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun cacheUserList(cacheKey: String, users: List<User>, userId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            userProfileCacheSystem.cacheUserList(cacheKey, users, userId)
            Log.d(TAG, "Cached user list with key $cacheKey for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error caching user list with key $cacheKey for user $userId", e)
            Result.Error(e)
        }
    }

    // Additional reactive observations
    override fun getCurrentUserProfile(): Flow<User?> {
        return userProfileCacheSystem.getCurrentUserProfile()
    }

    // Reactive state flows (atomic caching integration)
    override fun observeLoading(): Flow<Boolean> {
        return userProfileCacheSystem.profileLoadingFlow
    }

    override fun observeErrors(): Flow<String?> {
        return userProfileCacheSystem.profileErrorFlow
    }

    override fun observeStats(): Flow<UserUsageStats?> {
        return userProfileCacheSystem.currentStats
    }

    // SSOT-aware operations (atomic caching integration)
    override suspend fun updateUserProfileWithSsot(userId: String, updates: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        try {
            userProfileCacheSystem.updateUserProfileWithSsot(userId, updates)
            Log.d(TAG, "Updated user profile with SSOT rules for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating user profile with SSOT rules for user $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun updateFromCloudFunction(userId: String, cloudUser: User): Result<Unit> = withContext(ioDispatcher) {
        try {
            userProfileCacheSystem.updateFromCloudFunction(userId, cloudUser)
            Log.d(TAG, "Updated user profile from cloud function for user $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating user profile from cloud function for user $userId", e)
            Result.Error(e)
        }
    }
}
