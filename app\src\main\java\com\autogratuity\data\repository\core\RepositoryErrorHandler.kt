package com.autogratuity.data.repository.core

import android.util.Log
import com.autogratuity.data.model.ErrorInfo
import com.google.firebase.FirebaseApiNotAvailableException
import com.google.firebase.FirebaseNetworkException
import com.google.firebase.auth.FirebaseAuthException
import com.google.firebase.firestore.FirebaseFirestoreException
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.retryWhen
import kotlinx.datetime.Clock
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.concurrent.TimeoutException
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.pow
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds

/**
 * Modern error handling utility for repositories that provides consistent
 * error handling, logging, propagation, and recovery across the application.
 *
 * Fully modernized in 2025 to use Kotlin Flow, Result, and structured concurrency patterns.
 * All RxJava dependencies have been completely removed.
 */
@Singleton
class ModernRepositoryErrorHandler @Inject constructor(
    private val maxRetries: Int = 3,
    private val initialRetryDelay: Duration = 1000.milliseconds,
    private val retryBackoffFactor: Float = 2.0f
) {
    // StateFlow counters for tracking retry attempts across coroutines
    private val _retryCounter = MutableStateFlow(0)

    // StateFlow for tracking and observing the latest error
    private val _lastError = MutableStateFlow<ErrorInfo?>(null)
    val lastError: StateFlow<ErrorInfo?> = _lastError

    // StateFlow counters for tracking error handling metrics
    private val _totalErrorsHandled = MutableStateFlow<Long>(0L)
    private val _totalRetriesAttempted = MutableStateFlow<Long>(0L)

    companion object {
        private const val TAG = "ModernRepoErrorHandler"
    }

    /**
     * Handle an error by converting it to an ErrorInfo, logging it, and updating
     * the sync status with modern error tracking.
     *
     * @param error The error to handle
     * @param operationName Description of the operation that failed
     * @param entityType Type of entity being operated on (e.g., "user", "delivery")
     * @return ErrorInfo representing the handled error
     */
    fun handleError(error: Throwable, operationName: String, entityType: String?): ErrorInfo {
        val errorInfo = createErrorInfo(error, operationName, entityType)
        logError(errorInfo, error)
        _lastError.value = errorInfo
        // ✅ FIXED: Directly increment Long value
        _totalErrorsHandled.value = _totalErrorsHandled.value + 1
        return errorInfo
    }

    /**
     * Create a standardized ErrorInfo from an exception with enhanced categorization.
     *
     * @param error The exception to convert
     * @param operationName Description of the operation that failed
     * @param entityType Type of entity being operated on
     * @return Standardized ErrorInfo object
     */
    fun createErrorInfo(error: Throwable, operationName: String, entityType: String?): ErrorInfo {
        val errorInfo = when (error) {
            is FirebaseNetworkException, is UnknownHostException, is ConnectException -> {
                createNetworkErrorInfo(buildString {
                    append("Network connectivity error during ")
                    append(operationName)
                    error.message?.let { append(": $it") }
                })
            }
            is FirebaseAuthException -> {
                createAuthErrorInfo(buildString {
                    append("Authentication error during ")
                    append(operationName)
                    append(" (Code: ${error.errorCode})")
                })
            }
            is FirebaseFirestoreException -> {
                createServerErrorInfo(buildString {
                    append("Firestore database error during ")
                    append(operationName)
                    append(" (Code: ${error.code})")
                })
            }
            is SocketTimeoutException, is TimeoutException -> {
                createTimeoutErrorInfo(buildString {
                    append("Operation timeout: ")
                    append(operationName)
                    error.message?.let { append(" - $it") }
                })
            }
            is IllegalArgumentException -> {
                createValidationErrorInfo(buildString {
                    append("Invalid input for ")
                    append(operationName)
                    error.message?.let { append(": $it") }
                })
            }
            is CancellationException -> {
                createOperationCancelledInfo(buildString {
                    append("Operation cancelled: ")
                    append(operationName)
                })
            }
            else -> {
                createErrorInfoFromThrowable(error, operationName)
            }
        }

        // Add contextual metadata - create enhanced ErrorInfo with additional details
        val enhancedDetails = hashMapOf<String, Any?>(
            "operation" to operationName,
            "timestamp" to Clock.System.now().toString(),
            "retryCount" to _retryCounter.value
        )

        entityType?.let { enhancedDetails["entityType"] = it }

        return createEnhancedErrorInfo(errorInfo, enhancedDetails)
    }

    // Helper methods to create ErrorInfo using static factory methods for better compatibility
    private fun createNetworkErrorInfo(message: String): ErrorInfo {
        val errorInfo = ErrorInfo.createNetworkError(message)
        errorInfo.setCode(ErrorInfo.CODE_NETWORK)
        errorInfo.severity = ErrorInfo.SEVERITY_MEDIUM
        errorInfo.recoveryAction = ErrorInfo.RECOVERY_CHECK_CONNECTION
        return errorInfo
    }

    private fun createAuthErrorInfo(message: String): ErrorInfo {
        val errorInfo = ErrorInfo.createAuthError(message)
        errorInfo.setCode(ErrorInfo.CODE_AUTHENTICATION)
        errorInfo.severity = ErrorInfo.SEVERITY_HIGH
        errorInfo.recoveryAction = ErrorInfo.RECOVERY_LOGIN_AGAIN
        return errorInfo
    }

    private fun createServerErrorInfo(message: String): ErrorInfo {
        val errorInfo = ErrorInfo.createServerError(message)
        errorInfo.setCode(ErrorInfo.CODE_SERVER)
        errorInfo.severity = ErrorInfo.SEVERITY_HIGH
        errorInfo.recoveryAction = ErrorInfo.RECOVERY_RETRY
        return errorInfo
    }

    private fun createTimeoutErrorInfo(message: String): ErrorInfo {
        val errorInfo = ErrorInfo.createTimeoutError(message)
        errorInfo.setCode(ErrorInfo.CODE_TIMEOUT)
        errorInfo.severity = ErrorInfo.SEVERITY_MEDIUM
        errorInfo.recoveryAction = ErrorInfo.RECOVERY_RETRY
        return errorInfo
    }

    private fun createValidationErrorInfo(message: String): ErrorInfo {
        val errorInfo = ErrorInfo.createValidationError(message)
        errorInfo.setCode(ErrorInfo.CODE_VALIDATION)
        errorInfo.severity = ErrorInfo.SEVERITY_MEDIUM
        errorInfo.recoveryAction = ErrorInfo.RECOVERY_NONE
        return errorInfo
    }

    private fun createOperationCancelledInfo(message: String): ErrorInfo {
        val errorInfo = ErrorInfo.createUnknownError(message)
        errorInfo.setCode(ErrorInfo.CODE_CANCELLED)
        errorInfo.severity = ErrorInfo.SEVERITY_LOW
        errorInfo.recoveryAction = ErrorInfo.RECOVERY_NONE
        return errorInfo
    }

    private fun createErrorInfoFromThrowable(error: Throwable, operationName: String): ErrorInfo {
        val errorInfo = ErrorInfo.fromThrowable(error)
        // Update message if needed
        if (errorInfo.message.isNullOrEmpty()) {
            errorInfo.message = error.message ?: "Unknown error during $operationName"
        }
        errorInfo.setCode(ErrorInfo.CODE_UNKNOWN)
        errorInfo.severity = ErrorInfo.SEVERITY_HIGH
        errorInfo.recoveryAction = ErrorInfo.RECOVERY_RETRY
        return errorInfo
    }

    private fun createEnhancedErrorInfo(baseErrorInfo: ErrorInfo, additionalDetails: Map<String, Any?>): ErrorInfo {
        // Add additional details to the existing ErrorInfo
        additionalDetails.forEach { (key, value) ->
            baseErrorInfo.addDetail(key, value)
        }

        // Enhance the message with operation context
        val originalMessage = baseErrorInfo.message ?: "Unknown error"
        val operation = additionalDetails["operation"]
        val timestamp = additionalDetails["timestamp"]
        baseErrorInfo.message = "$originalMessage (Operation: $operation, Timestamp: $timestamp)"

        return baseErrorInfo
    }

    // Safe accessors for ErrorInfo fields
    private fun getErrorInfoCode(errorInfo: ErrorInfo): String? {
        return try {
            errorInfo.getCode()
        } catch (_: Exception) {
            null
        }
    }

    private fun getErrorInfoMessage(errorInfo: ErrorInfo): String? {
        return try {
            errorInfo.message
        } catch (_: Exception) {
            null
        }
    }

    private fun getErrorInfoCritical(errorInfo: ErrorInfo): Boolean? {
        return try {
            errorInfo.isCritical
        } catch (_: Exception) {
            null
        }
    }

    /**
     * Log an error with structured formatting and contextual details.
     *
     * @param errorInfo Standardized error info
     * @param originalError Original exception
     */
    private fun logError(errorInfo: ErrorInfo, originalError: Throwable) {
        val logMessage = buildString {
            append("[${getErrorInfoCode(errorInfo) ?: "UNKNOWN"}] ")
            append(getErrorInfoMessage(errorInfo) ?: "Unknown error message")
        }

        val isCritical = getErrorInfoCritical(errorInfo) != false
        if (isCritical) {
            Log.e(TAG, logMessage, originalError)
        } else {
            Log.w(TAG, logMessage, originalError)
        }
    }

    /**
     * Apply standard error handling to a Flow with Result-based error propagation.
     * This is the primary method for modern Flow error handling.
     *
     * @param flow The source Flow
     * @param operationName Description of the operation
     * @param entityType Type of entity being operated on
     * @param T The type of object emitted by the Flow
     * @return Flow with standardized error handling via Result
     */
    fun <T> handleFlow(flow: Flow<T>, operationName: String, entityType: String?): Flow<Result<T>> {
        return flow
            .map { Result.success(it) }
            .catch { error ->
                val errorInfo = handleError(error, operationName, entityType)
                emit(Result.failure(mapToRepositoryError(error, errorInfo, operationName, entityType)))
            }
    }

    /**
     * Apply standard error handling to a Flow with intelligent retry logic.
     * Implements exponential backoff with the Kotlin Duration API.
     *
     * @param flow The source Flow
     * @param operationName Description of the operation
     * @param entityType Type of entity being operated on
     * @param T The type of object emitted by the Flow
     * @return Flow with standardized error handling via Result and retry logic
     */
    fun <T> handleFlowWithRetry(flow: Flow<T>, operationName: String, entityType: String?): Flow<Result<T>> {
        return flow
            .retryWhen { cause, attempt ->
                if (!shouldRetry(attempt.toInt(), cause)) {
                    return@retryWhen false
                }

                // ✅ FIXED: Safe arithmetic with Number type to avoid KSP compilation errors
                _totalRetriesAttempted.value = _totalRetriesAttempted.value + 1

                // Log retry attempt with structured details
                Log.d(TAG, buildString {
                    append("Retry attempt $attempt for $operationName")
                    entityType?.let { append(" ($it)") }
                    append(": ${cause.message}")
                })

                // Calculate backoff delay using the Kotlin Duration API
                val delay = getRetryDelayForAttempt(attempt.toInt())
                delay(delay)

                return@retryWhen true
            }
            .map { Result.success(it) }
            .catch { error ->
                val errorInfo = handleError(error, operationName, entityType)
                emit(Result.failure(mapToRepositoryError(error, errorInfo, operationName, entityType)))
            }
    }

    /**
     * Maps a Throwable to the appropriate RepositoryError from the modern error hierarchy.
     * This integrates with the RepositoryError sealed class from FirestoreRepository.
     */
    private fun mapToRepositoryError(error: Throwable, errorInfo: ErrorInfo, operationName: String, entityType: String?): Throwable {
        // Create a modern RepositoryException from the ErrorInfo using the companion object method
        val repositoryException = try {
            RepositoryException.fromErrorInfo(errorInfo)
        } catch (_: Exception) {
            // Fallback to creating from throwable if fromErrorInfo fails
            RepositoryException.fromThrowable(error, operationName, entityType)
        }

        // Add stack trace preservation
        if (error.stackTrace.isNotEmpty()) {
            repositoryException.stackTrace = error.stackTrace
        }

        return repositoryException
    }

    /**
     * Determine if an operation should be retried based on retry count and error type.
     * Enhanced with modern error categorization.
     *
     * @param retryCount Current retry attempt count
     * @param error The error that occurred
     * @return True if the operation should be retried, false otherwise
     */
    private fun shouldRetry(retryCount: Int, error: Throwable): Boolean {
        if (retryCount > maxRetries) {
            return false // Max retries exceeded
        }

        // Never retry cancellation exceptions to respect coroutine cancellation
        if (error is CancellationException) {
            return false
        }

        // Enhanced retry logic based on error type
        return when (error) {
            // Never retry these error types
            is FirebaseAuthException,
            is FirebaseApiNotAvailableException,
            is IllegalArgumentException,
            is SecurityException -> false

            // Retry network-related errors
            is FirebaseNetworkException,
            is UnknownHostException,
            is ConnectException,
            is SocketTimeoutException,
            is TimeoutException,
            is IOException -> true

            // Retry transient Firestore errors
            is FirebaseFirestoreException -> {
                when (error.code) {
                    FirebaseFirestoreException.Code.UNAVAILABLE,
                    FirebaseFirestoreException.Code.DEADLINE_EXCEEDED,
                    FirebaseFirestoreException.Code.RESOURCE_EXHAUSTED,
                    FirebaseFirestoreException.Code.INTERNAL,
                    FirebaseFirestoreException.Code.ABORTED -> true
                    else -> false
                }
            }

            // Retry other transient errors by default
            else -> true
        }
    }

    /**
     * Calculate the delay before the next retry attempt using exponential backoff.
     * Enhanced with jitter to prevent thundering herd.
     *
     * @param retryCount Current retry attempt count
     * @return Delay as Duration
     */
    fun getRetryDelayForAttempt(retryCount: Int): Duration {
        if (retryCount <= 0) return initialRetryDelay

        // Calculate exponential backoff multiplier
        val backoffMultiplier = retryBackoffFactor.toDouble().pow(retryCount - 1)
        val baseDelayMs = initialRetryDelay.inWholeMilliseconds
        val delayMs = (baseDelayMs * backoffMultiplier).toLong()

        // Add jitter (±25%) to prevent thundering herd
        val jitterFactor = 0.75 + (Math.random() * 0.5) // Random between 0.75 and 1.25
        val delayWithJitterMs = (delayMs * jitterFactor).toLong()

        // Cap maximum delay at 30 seconds
        val maxDelayMs = 30.seconds.inWholeMilliseconds
        val finalDelayMs = if (delayWithJitterMs > maxDelayMs) maxDelayMs else delayWithJitterMs

        return finalDelayMs.milliseconds
    }

    /**
     * Handle errors in a suspend function, wrapping the result in Result.
     * Modern replacement for legacy handleSingle methods.
     *
     * @param operationName Description of the operation
     * @param entityType Type of entity being operated on
     * @param block The suspend function to execute
     * @return Result containing the operation result or a failure
     */
    suspend fun <T> handleSuspendFunction(operationName: String, entityType: String?, block: suspend () -> T): Result<T> {
        return try {
            Result.success(block())
        } catch (e: CancellationException) {
            // Always rethrow cancellation exceptions to respect coroutine cancellation
            throw e
        } catch (e: Exception) {
            val errorInfo = handleError(e, operationName, entityType)
            Result.failure(mapToRepositoryError(e, errorInfo, operationName, entityType))
        }
    }

    /**
     * Handle errors in a suspend function with intelligent retry capability.
     * Modern replacement for legacy handleSingleWithRetry methods.
     *
     * @param operationName Description of the operation
     * @param entityType Type of entity being operated on
     * @param block The suspend function to execute
     * @return Result containing the operation result or a failure
     */
    suspend fun <T> handleSuspendFunctionWithRetry(operationName: String, entityType: String?, block: suspend () -> T): Result<T> {
        var attempts = 0
        var lastException: Exception? = null

        while (attempts <= maxRetries) {
            try {
                return Result.success(block())
            } catch (e: CancellationException) {
                // Always rethrow cancellation exceptions
                throw e
            } catch (e: Exception) {
                lastException = e

                if (!shouldRetry(attempts, e)) {
                    break // Don't retry certain errors
                }

                // ✅ FIXED: Safe arithmetic with Number type to avoid KSP compilation errors
                _totalRetriesAttempted.value = _totalRetriesAttempted.value + 1

                // Log retry attempt with structured details
                Log.d(TAG, buildString {
                    append("Retry attempt $attempts for $operationName")
                    entityType?.let { append(" ($it)") }
                    append(": ${e.message}")
                })

                // Calculate and apply backoff delay
                val delay = getRetryDelayForAttempt(attempts)
                delay(delay)

                attempts++
            }
        }

        // If we get here, all retries failed or we decided not to retry
        val error = lastException ?: Exception("Unknown error during $operationName")
        val errorInfo = handleError(error, operationName, entityType)
        return Result.failure(mapToRepositoryError(error, errorInfo, operationName, entityType))
    }

    /**
     * Get the last error that occurred as a StateFlow.
     * Modern reactive error monitoring.
     *
     * @return StateFlow emitting the last ErrorInfo or null
     */
    fun getLastErrorFlow(): StateFlow<ErrorInfo?> = lastError

    /**
     * Get comprehensive error handling statistics for monitoring and debugging.
     *
     * @return Map containing error handling metrics
     */
    fun getErrorStatistics(): Map<String, Any> {
        return buildMap {
            put("total_errors_handled", _totalErrorsHandled.value)
            put("total_retries_attempted", _totalRetriesAttempted.value)
            put("current_retry_count", _retryCounter.value)
            put("max_retries_configured", maxRetries)
            put("initial_retry_delay_ms", initialRetryDelay.inWholeMilliseconds)
            put("retry_backoff_factor", retryBackoffFactor)
            // Safely access last error details using safe accessors
            try {
                put("last_error_timestamp", Clock.System.now().toString())
                put("last_error_type", _lastError.value?.let { getErrorInfoCode(it) } ?: "none")
            } catch (_: Exception) {
                put("last_error_timestamp", "unknown")
                put("last_error_type", "unknown")
            }
        }
    }

    /**
     * Reset error statistics - primarily for testing purposes.
     */
    @JvmSynthetic
    internal fun resetStatisticsForTesting() {
        _totalErrorsHandled.value = 0L
        _totalRetriesAttempted.value = 0L
        _retryCounter.value = 0
        _lastError.value = null
        Log.d(TAG, "Reset error statistics for testing")
    }
}

// Typealias for backward compatibility
typealias RepositoryErrorHandler = ModernRepositoryErrorHandler