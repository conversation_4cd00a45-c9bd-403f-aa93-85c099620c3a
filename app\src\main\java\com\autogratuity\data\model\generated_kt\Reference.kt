/*
 * Reference.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

/**
 * Represents reference information, often linking a Delivery to an Address.
 */
data class Reference(
    /** The ID of the related Address document. */
    val addressId: String? = null,
    /** The order ID, often matching the parent Delivery document. */
    val orderId: String? = null,
    /** An external identifier, if applicable. */
    val externalId: String? = null,
    /** A platform-specific order identifier. */
    val platformOrderId: String? = null
)
