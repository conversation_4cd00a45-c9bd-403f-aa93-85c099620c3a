@file:OptIn(ExperimentalCoroutinesApi::class)

package com.autogratuity.data.util

import android.util.Log
import com.autogratuity.data.datasource.remote.UserRemoteDataSource
import com.autogratuity.data.mapper.UserMapper
import com.autogratuity.data.model.Result as DataResult
import com.autogratuity.data.security.AuthenticationManager
import com.autogratuity.data.util.ValidationEngine
import com.autogratuity.domain.model.User
import com.google.firebase.firestore.DocumentSnapshot
import javax.inject.Inject
import javax.inject.Singleton
import com.autogratuity.debug.ClarityArchitectureMonitor
import kotlinx.coroutines.ExperimentalCoroutinesApi

/**
 * Page loader for User data using Firestore pagination.
 * Provides memory-efficient loading of user data with proper error handling.
 * 
 * ARCHITECTURAL PATTERN: Delegates ALL Firestore operations to UserRemoteDataSource
 * following clarity.md architectural guidelines and mirroring AddressPageLoader patterns.
 * 
 * Uses existing FlowPageLoader interface and PageResult from PaginatedDataSource.kt
 */
@Singleton
class UserPageLoader @Inject constructor(
    private val remoteDataSource: UserRemoteDataSource,
    private val userMapper: UserMapper,
    private val authManager: AuthenticationManager,
    private val validationEngine: ValidationEngine
) : FlowPageLoader<User, DocumentSnapshot> {

    companion object {
        private const val TAG = "UserPageLoader"
    }

    override suspend fun loadPage(
        pageSize: Int,
        pageKey: DocumentSnapshot?
    ): kotlin.Result<PageResult<User, DocumentSnapshot>> {
        val loadStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        var userId: String? = null
        try {
            userId = authManager.getCurrentUserId()
                ?: run {
                    val error = IllegalStateException("User not authenticated for UserPageLoader")
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = false,
                        error = error,
                        dataType = "User",
                        userId = null,
                        resultCount = 0,
                        dataSource = "Authentication",
                        cacheStrategy = "none"
                    )
                    return kotlin.Result.failure(error)
                }
            
            Log.d(TAG, "loadPage: Loading user page for user $userId with size: $pageSize, key: ${pageKey?.id}")

            // Delegate to UserRemoteDataSource for actual Firestore pagination
            // Following the pattern from AddressRemoteDataSource.loadAddressPage
            val pageResult = remoteDataSource.loadUserPage(userId, pageSize, pageKey)
            
            when (pageResult) {
                is DataResult.Success -> {
                    val userProfileDtos = pageResult.data.items
                    val nextPageKeyFromRemote = pageResult.data.nextPageKey
                    
                    val mappingStartTime = kotlin.time.TimeSource.Monotonic.markNow()
                    val users = userProfileDtos.mapNotNull { dto ->
                        val mapperResult = userMapper.mapToDomain(dto.userId!!, dto)
                        when (mapperResult) {
                            is DataResult.Success -> mapperResult.data
                            is DataResult.Error -> {
                                Log.w(TAG, "Error mapping user DTO to domain model", mapperResult.exception)
                                null
                            }
                            is DataResult.Loading -> {
                                Log.w(TAG, "Unexpected Loading state from mapper")
                                null
                            }
                        }
                    }
                    val mappingDuration = mappingStartTime.elapsedNow()

                    // ✅ VALIDATE PAGE DATA QUALITY
                    if (users.isNotEmpty()) {
                        val pageValidationStartTime = kotlin.time.TimeSource.Monotonic.markNow()
                        var validationWarnings = 0
                        var validationErrors = 0
                        
                        users.forEach { user ->
                            val userValidation = validationEngine.validateUser(user)
                            validationWarnings += userValidation.warnings.size
                            validationErrors += userValidation.errors.size
                            
                            if (!userValidation.isValid) {
                                Log.w(TAG, "User validation failed for ${user.id}: ${userValidation.errors}")
                            }
                            if (userValidation.warnings.isNotEmpty()) {
                                Log.w(TAG, "User validation warnings for ${user.id}: ${userValidation.warnings}")
                            }
                        }
                        
                        val pageValidationDuration = pageValidationStartTime.elapsedNow()
                        
                        Log.d(TAG, "Page validation complete: ${users.size} users, $validationErrors errors, $validationWarnings warnings, ${pageValidationDuration.inWholeMilliseconds}ms")
                        
                        // Track data quality metrics
                        ClarityArchitectureMonitor.addSessionEvent("user_page_validation:${users.size}:errors_$validationErrors:warnings_$validationWarnings")
                    }

                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = true,
                        error = null,
                        dataType = "User",
                        userId = userId,
                        resultCount = users.size,
                        dataSource = "RemoteDataSource",
                        cacheStrategy = "none"
                    )
                    Log.d(TAG, "loadPage: Successfully loaded ${users.size} users")
                    return kotlin.Result.success(PageResult(users, nextPageKeyFromRemote))
                }
                is DataResult.Error -> {
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = false,
                        error = pageResult.exception,
                        dataType = "User",
                        userId = userId,
                        resultCount = 0,
                        dataSource = "RemoteDataSource_Error",
                        cacheStrategy = "none"
                    )
                    Log.e(TAG, "loadPage: Error loading page from RemoteDataSource", pageResult.exception)
                    return kotlin.Result.failure(pageResult.exception)
                }
                is DataResult.Loading -> {
                    val error = IllegalStateException("RemoteDataSource returned Loading unexpectedly for UserPageLoader")
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = false,
                        error = error,
                        dataType = "User",
                        userId = userId,
                        resultCount = 0,
                        dataSource = "RemoteDataSource_UnexpectedState",
                        cacheStrategy = "none"
                    )
                    Log.w(TAG, "loadPage: Unexpected Loading state from RemoteDataSource")
                    return kotlin.Result.failure(error)
                }
            }
        } catch (e: Exception) {
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = TAG,
                operation = "loadPage",
                duration = loadStartTime.elapsedNow(),
                success = false,
                error = e,
                dataType = "User",
                userId = userId ?: "unknown",
                resultCount = 0,
                dataSource = "PageLoader_Internal_Error",
                cacheStrategy = "none"
            )
            Log.e(TAG, "loadPage: Unexpected error in UserPageLoader", e)
            return kotlin.Result.failure(e)
        }
    }
}

/**
 * Factory for creating user-specific page loaders
 */
@Singleton
class UserPageLoaderFactory @Inject constructor(
    private val remoteDataSource: UserRemoteDataSource,
    private val userMapper: UserMapper,
    private val authManager: AuthenticationManager,
    private val validationEngine: ValidationEngine
) {
    
    /**
     * Create a page loader for user profiles
     */
    fun createUserPageLoader(): UserPageLoader {
        return UserPageLoader(remoteDataSource, userMapper, authManager, validationEngine)
    }
    
    /**
     * Create a page loader for active users only
     * Uses RemoteDataSource's loadActiveUserPage method
     */
    fun createActiveUserPageLoader(): ActiveUserPageLoader {
        return ActiveUserPageLoader(remoteDataSource, userMapper, authManager, validationEngine)
    }
    
    /**
     * Create a page loader for premium users only
     * Uses RemoteDataSource's loadPremiumUserPage method
     */
    fun createPremiumUserPageLoader(): PremiumUserPageLoader {
        return PremiumUserPageLoader(remoteDataSource, userMapper, authManager, validationEngine)
    }
}

/**
 * Specialized page loader for active users only
 */
@Singleton
class ActiveUserPageLoader @Inject constructor(
    private val remoteDataSource: UserRemoteDataSource,
    private val userMapper: UserMapper,
    private val authManager: AuthenticationManager,
    private val validationEngine: ValidationEngine
) : FlowPageLoader<User, DocumentSnapshot> {

    companion object {
        private const val TAG = "ActiveUserPageLoader"
    }

    override suspend fun loadPage(
        pageSize: Int,
        pageKey: DocumentSnapshot?
    ): kotlin.Result<PageResult<User, DocumentSnapshot>> {
        val loadStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        var userId: String? = null
        try {
            userId = authManager.getCurrentUserId()
                ?: run {
                    val error = IllegalStateException("User not authenticated for ActiveUserPageLoader")
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = false,
                        error = error,
                        dataType = "User",
                        userId = null,
                        resultCount = 0,
                        dataSource = "Authentication",
                        cacheStrategy = "none"
                    )
                    return kotlin.Result.failure(error)
                }
            
            Log.d(TAG, "loadPage: Loading active user page with size: $pageSize, key: ${pageKey?.id}")

            // Delegate to UserRemoteDataSource for filtered active user pagination
            // Following the pattern from AddressRemoteDataSource specialized loaders
            val pageResult = remoteDataSource.loadActiveUserPage(pageSize, pageKey)
            
            when (pageResult) {
                is DataResult.Success -> {
                    val userProfileDtos = pageResult.data.items
                    val nextPageKeyFromRemote = pageResult.data.nextPageKey
                    
                    val mappingStartTime = kotlin.time.TimeSource.Monotonic.markNow()
                    val users = userProfileDtos.mapNotNull { dto ->
                        val mapperResult = userMapper.mapToDomain(dto.userId!!, dto)
                        when (mapperResult) {
                            is DataResult.Success -> mapperResult.data
                            is DataResult.Error -> {
                                Log.w(TAG, "Error mapping active user DTO to domain model", mapperResult.exception)
                                null
                            }
                            is DataResult.Loading -> {
                                Log.w(TAG, "Unexpected Loading state from mapper")
                                null
                            }
                        }
                    }
                    val mappingDuration = mappingStartTime.elapsedNow()

                    // ✅ VALIDATE PAGE DATA QUALITY
                    if (users.isNotEmpty()) {
                        val pageValidationStartTime = kotlin.time.TimeSource.Monotonic.markNow()
                        var validationWarnings = 0
                        var validationErrors = 0
                        
                        users.forEach { user ->
                            val userValidation = validationEngine.validateUser(user)
                            validationWarnings += userValidation.warnings.size
                            validationErrors += userValidation.errors.size
                            
                            if (!userValidation.isValid) {
                                Log.w(TAG, "User validation failed for ${user.id}: ${userValidation.errors}")
                            }
                            if (userValidation.warnings.isNotEmpty()) {
                                Log.w(TAG, "User validation warnings for ${user.id}: ${userValidation.warnings}")
                            }
                        }
                        
                        val pageValidationDuration = pageValidationStartTime.elapsedNow()
                        
                        Log.d(TAG, "Page validation complete: ${users.size} users, $validationErrors errors, $validationWarnings warnings, ${pageValidationDuration.inWholeMilliseconds}ms")
                        
                        // Track data quality metrics
                        ClarityArchitectureMonitor.addSessionEvent("user_page_validation:${users.size}:errors_$validationErrors:warnings_$validationWarnings")
                    }

                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = true,
                        error = null,
                        dataType = "User",
                        userId = userId,
                        resultCount = users.size,
                        dataSource = "RemoteDataSource",
                        cacheStrategy = "none"
                    )
                    Log.d(TAG, "loadPage: Successfully loaded ${users.size} active users")
                    return kotlin.Result.success(PageResult(users, nextPageKeyFromRemote))
                }
                is DataResult.Error -> {
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = false,
                        error = pageResult.exception,
                        dataType = "User",
                        userId = userId,
                        resultCount = 0,
                        dataSource = "RemoteDataSource_Error",
                        cacheStrategy = "none"
                    )
                    Log.e(TAG, "loadPage: Error loading active user page from RemoteDataSource", pageResult.exception)
                    return kotlin.Result.failure(pageResult.exception)
                }
                is DataResult.Loading -> {
                    val error = IllegalStateException("RemoteDataSource returned Loading unexpectedly for ActiveUserPageLoader")
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = false,
                        error = error,
                        dataType = "User",
                        userId = userId,
                        resultCount = 0,
                        dataSource = "RemoteDataSource_UnexpectedState",
                        cacheStrategy = "none"
                    )
                    Log.w(TAG, "loadPage: Unexpected Loading state from RemoteDataSource")
                    return kotlin.Result.failure(error)
                }
            }
        } catch (e: Exception) {
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = TAG,
                operation = "loadPage",
                duration = loadStartTime.elapsedNow(),
                success = false,
                error = e,
                dataType = "User",
                userId = userId ?: "unknown",
                resultCount = 0,
                dataSource = "PageLoader_Internal_Error",
                cacheStrategy = "none"
            )
            Log.e(TAG, "loadPage: Unexpected error in ActiveUserPageLoader", e)
            return kotlin.Result.failure(e)
        }
    }
}

/**
 * Specialized page loader for premium users only
 */
@Singleton
class PremiumUserPageLoader @Inject constructor(
    private val remoteDataSource: UserRemoteDataSource,
    private val userMapper: UserMapper,
    private val authManager: AuthenticationManager,
    private val validationEngine: ValidationEngine
) : FlowPageLoader<User, DocumentSnapshot> {

    companion object {
        private const val TAG = "PremiumUserPageLoader"
    }

    override suspend fun loadPage(
        pageSize: Int,
        pageKey: DocumentSnapshot?
    ): kotlin.Result<PageResult<User, DocumentSnapshot>> {
        val loadStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        var userId: String? = null
        try {
            userId = authManager.getCurrentUserId()
                ?: run {
                    val error = IllegalStateException("User not authenticated for PremiumUserPageLoader")
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = false,
                        error = error,
                        dataType = "User",
                        userId = null,
                        resultCount = 0,
                        dataSource = "Authentication",
                        cacheStrategy = "none"
                    )
                    return kotlin.Result.failure(error)
                }
            
            Log.d(TAG, "loadPage: Loading premium user page with size: $pageSize, key: ${pageKey?.id}")

            // Delegate to UserRemoteDataSource for filtered premium user pagination
            // Following the pattern from AddressRemoteDataSource specialized loaders
            val pageResult = remoteDataSource.loadPremiumUserPage(pageSize, pageKey)
            
            when (pageResult) {
                is DataResult.Success -> {
                    val userProfileDtos = pageResult.data.items
                    val nextPageKeyFromRemote = pageResult.data.nextPageKey
                    
                    val mappingStartTime = kotlin.time.TimeSource.Monotonic.markNow()
                    val users = userProfileDtos.mapNotNull { dto ->
                        val mapperResult = userMapper.mapToDomain(dto.userId!!, dto)
                        when (mapperResult) {
                            is DataResult.Success -> mapperResult.data
                            is DataResult.Error -> {
                                Log.w(TAG, "Error mapping premium user DTO to domain model", mapperResult.exception)
                                null
                            }
                            is DataResult.Loading -> {
                                Log.w(TAG, "Unexpected Loading state from mapper")
                                null
                            }
                        }
                    }
                    val mappingDuration = mappingStartTime.elapsedNow()

                    // ✅ VALIDATE PAGE DATA QUALITY
                    if (users.isNotEmpty()) {
                        val pageValidationStartTime = kotlin.time.TimeSource.Monotonic.markNow()
                        var validationWarnings = 0
                        var validationErrors = 0
                        
                        users.forEach { user ->
                            val userValidation = validationEngine.validateUser(user)
                            validationWarnings += userValidation.warnings.size
                            validationErrors += userValidation.errors.size
                            
                            if (!userValidation.isValid) {
                                Log.w(TAG, "User validation failed for ${user.id}: ${userValidation.errors}")
                            }
                            if (userValidation.warnings.isNotEmpty()) {
                                Log.w(TAG, "User validation warnings for ${user.id}: ${userValidation.warnings}")
                            }
                        }
                        
                        val pageValidationDuration = pageValidationStartTime.elapsedNow()
                        
                        Log.d(TAG, "Page validation complete: ${users.size} users, $validationErrors errors, $validationWarnings warnings, ${pageValidationDuration.inWholeMilliseconds}ms")
                        
                        // Track data quality metrics
                        ClarityArchitectureMonitor.addSessionEvent("user_page_validation:${users.size}:errors_$validationErrors:warnings_$validationWarnings")
                    }

                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = true,
                        error = null,
                        dataType = "User",
                        userId = userId,
                        resultCount = users.size,
                        dataSource = "RemoteDataSource",
                        cacheStrategy = "none"
                    )
                    Log.d(TAG, "loadPage: Successfully loaded ${users.size} premium users")
                    return kotlin.Result.success(PageResult(users, nextPageKeyFromRemote))
                }
                is DataResult.Error -> {
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = false,
                        error = pageResult.exception,
                        dataType = "User",
                        userId = userId,
                        resultCount = 0,
                        dataSource = "RemoteDataSource_Error",
                        cacheStrategy = "none"
                    )
                    Log.e(TAG, "loadPage: Error loading premium user page from RemoteDataSource", pageResult.exception)
                    return kotlin.Result.failure(pageResult.exception)
                }
                is DataResult.Loading -> {
                    val error = IllegalStateException("RemoteDataSource returned Loading unexpectedly for PremiumUserPageLoader")
                    ClarityArchitectureMonitor.monitorRepositoryOperation(
                        repositoryClass = TAG,
                        operation = "loadPage",
                        duration = loadStartTime.elapsedNow(),
                        success = false,
                        error = error,
                        dataType = "User",
                        userId = userId,
                        resultCount = 0,
                        dataSource = "RemoteDataSource_UnexpectedState",
                        cacheStrategy = "none"
                    )
                    Log.w(TAG, "loadPage: Unexpected Loading state from RemoteDataSource")
                    return kotlin.Result.failure(error)
                }
            }
        } catch (e: Exception) {
            ClarityArchitectureMonitor.monitorRepositoryOperation(
                repositoryClass = TAG,
                operation = "loadPage",
                duration = loadStartTime.elapsedNow(),
                success = false,
                error = e,
                dataType = "User",
                userId = userId ?: "unknown",
                resultCount = 0,
                dataSource = "PageLoader_Internal_Error",
                cacheStrategy = "none"
            )
            Log.e(TAG, "loadPage: Unexpected error in PremiumUserPageLoader", e)
            return kotlin.Result.failure(e)
        }
    }
} 