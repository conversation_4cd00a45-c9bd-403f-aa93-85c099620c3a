interface HttpTestResult {
    functionName: string;
    testName: string;
    success: boolean;
    result?: any;
    error?: string;
    duration: number;
    statusCode?: number;
}
declare class HttpCallableTester {
    private results;
    runAllHttpTests(): Promise<HttpTestResult[]>;
    private testParseImportCallable;
    private testManualDndOverrideCallable;
    private runHttpTest;
    private isSuccessfulResult;
    private extractStatusCode;
    private printHttpSummary;
}
export { HttpCallableTester, HttpTestResult };
