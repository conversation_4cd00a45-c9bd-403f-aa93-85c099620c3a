{"mcpServers": {"filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "C:\\\\Users\\\\<USER>"]}, "atom-of-thoughts": {"autoApprove": ["AoT-light", "atomcommands", "AoT"], "disabled": false, "timeout": 300, "command": "node", "args": ["C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Claude\\\\MCP_Atom_of_Thoughts\\\\build\\\\index.js"], "transportType": "stdio"}, "perplexity": {"autoApprove": ["reason", "deep_research"], "disabled": false, "timeout": 300, "command": "C:\\\\WINDOWS\\\\system32\\\\cmd.exe", "args": ["/c", "npx.cmd", "-y", "perplexity-mcp"], "env": {"PERPLEXITY_API_KEY": "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"}, "transportType": "stdio"}, "n8n-mcp-server": {"autoApprove": [], "disabled": false, "timeout": 600, "command": "node", "args": ["C:\\Users\\<USER>\\Documents\\Cline\\MCP\\n8n-mcp-server\\dist\\index.js"], "env": {"N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJiMzY4MTQwYS1iYjEwLTRlMWEtYTZhZC1mZTE4ZDQ5NTY1NjUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ0NTgxOTg5fQ.60oNGecIPC0IyrWl9wt0tV1BNh7uf3M23IONDpk88_M"}, "transportType": "stdio"}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "think-tool": {"command": "npx", "args": ["-y", "@cgize/mcp-think-tool"], "type": "stdio", "pollingInterval": 30000, "startupTimeout": 30000, "restartOnFailure": true}, "just-prompt-server": {"disabled": false, "timeout": 300, "command": "C:\\\\WINDOWS\\\\system32\\\\cmd.exe", "args": ["/c", "uv", "--directory", "C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\just-prompt", "run", "just-prompt", "--default-models", "openai:anthropic/claude-3.7-sonnet:thinking,openai:openai/o3-mini-high,openai:openai/o4-mini-high,openai:openai/gpt-4.1"], "transportType": "stdio", "cwd": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\just-prompt"}, "tavily-mcp": {"command": "npx", "args": ["-y", "tavily-mcp@0.1.3"], "env": {"TAVILY_API_KEY": "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"}}, "@21st-dev/magic": {"command": "cmd", "args": ["/c", "npx", "-y", "@21st-dev/magic@latest", "API_KEY=\"f679e7afa4182f9575c9825b046046f9edf299cf4a588a1c98fa9ccd09b1a461\""]}, "github": {"command": "C:\\\\Users\\\\<USER>\\\\github-mcp-server.exe", "args": ["stdio"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}, "morphik": {"command": "npx", "args": ["-y", "@morphik/mcp", "--uri=morphik://autogratuity:<EMAIL>"]}, "task-master-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "ANTHROPIC_API_KEY_HERE", "PERPLEXITY_API_KEY": "PERPLEXITY_API_KEY_HERE", "OPENAI_API_KEY": "OPENAI_API_KEY_HERE", "GOOGLE_API_KEY": "GOOGLE_API_KEY_HERE", "XAI_API_KEY": "XAI_API_KEY_HERE", "OPENROUTER_API_KEY": "OPENROUTER_API_KEY_HERE", "MISTRAL_API_KEY": "MISTRAL_API_KEY_HERE", "AZURE_OPENAI_API_KEY": "AZURE_OPENAI_API_KEY_HERE", "OLLAMA_API_KEY": "OLLAMA_API_KEY_HERE"}}}}