// Auto-generated from ComparisonType.kt
// TypeScript equivalent of Android domain.model.ComparisonType

export enum ComparisonType {
  LESS_THAN_OR_EQUAL_TO = 'LESS_THAN_OR_EQUAL_TO',
  LESS_THAN = 'LESS_THAN',
  EQUAL_TO = 'EQUAL_TO',
  companion = 'companion',
  com = 'com',
  com = 'com',
  com = 'com',
  null = 'null'
}

/**
 * Domain model generated from Kotlin ComparisonType
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface ComparisonType {

}