/*
 * User_profile.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

import kotlin.Double
import java.time.OffsetDateTime

/**
 * Defines the direct attributes of a user's profile data, corresponding to the content of the 'profileData' map in Firestore documents. This schema generates the UserProfileSchema.java POJO.
 */
data class User_profile(
    /** The user's unique ID (often same as document ID) */
    val userId: String?,
    /** User's email address */
    val email: String?,
    /** User's display name */
    val displayName: String?,
    /** ID of the user's default address */
    val defaultAddressId: String? = null,
    /** URL of the user's profile photo, typically from an external provider. */
    val photoUrl: String? = null,
    /** List of authentication providers linked to this account. */
    val authProviders: List<String> = listOf(),
    /** Status of the user account (e.g., active, suspended) */
    val accountStatus: String?,
    /** User's preferred timezone */
    val timezone: String? = null,
    /** Timestamp when the profile was created */
    val createdAt: OffsetDateTime?,
    /** Timestamp of the user's last login */
    val lastLoginAt: OffsetDateTime?,
    /** Timestamp when the privacy policy was accepted */
    val privacyPolicyAccepted: OffsetDateTime? = null,
    /** Timestamp when the terms of service were accepted */
    val termsAccepted: OffsetDateTime? = null,
    /** User's subscription details */
    val subscription: UserSubscription?,
    /** User's application preferences */
    val preferences: UserPreferences?,
    /** User's permissions and limits */
    val permissions: UserPermissions?,
    /** Basic usage counters */
    val usage: UserUsage?,
    /** Information related to data synchronization */
    val syncInfo: UserSyncInfo?,
    /** User-specific application settings */
    val appSettings: UserAppSettings?,
    /** User's communication preferences */
    val communication: UserCommunication?,
    /** Detailed usage statistics */
    val usageStats: UserUsageStats?,
    /** Metadata like creation/update timestamps and migration flags */
    val metadata: Metadata?,
    /** Version number for optimistic concurrency control */
    val version: Long?
) {

    /**
     * User's subscription details
     */
    data class UserSubscription(
        /** Current status (e.g., active, expired, cancelled) */
        val status: String? = null,
        /** Subscription level (e.g., free, premium) */
        val level: String? = null,
        /** Calculated field indicating if subscription is currently active */
        val isActive: Boolean? = null,
        /** Date the subscription started */
        val startDate: OffsetDateTime? = null,
        /** Date the subscription expires (if not lifetime) */
        val expiryDate: OffsetDateTime? = null,
        /** Flag indicating a lifetime subscription */
        val isLifetime: Boolean? = null,
        /** Subscription provider (e.g., google_play, stripe) - Encrypted */
        val provider: String? = null,
        /** Associated order ID from the provider - Encrypted */
        val orderId: String? = null,
        /** Details about the last verification attempt */
        val verification: Verification? = null
    )

    /**
     * Details about the last verification attempt
     */
    data class Verification(
        /** Timestamp of the last verification check */
        val lastVerified: OffsetDateTime? = null,
        /** Result of the last verification (e.g., verified, pending, failed) */
        val status: String? = null,
        /** Error message from the last verification attempt, if any */
        val error: String? = null
    )

    /**
     * User's application preferences
     */
    data class UserPreferences(
        val notificationsEnabled: Boolean? = null,
        val theme: String? = null,
        val useLocation: Boolean? = null,
        /** User's Do Not Deliver (DND) preferences. */
        val dnd: Dnd? = null
    )

    /**
     * User's Do Not Deliver (DND) preferences.
     */
    data class Dnd(
        /** Custom DND rule, primarily for premium users. */
        val customRule: CustomRule?
    )

    /**
     * Custom DND rule, primarily for premium users.
     */
    data class CustomRule(
        val isEnabled: Boolean = false,
        val tipAmountThreshold: Double = 0.0,
        val comparisonType: ComparisonType = ComparisonType.less_than_or_equal_to
    )

    enum class ComparisonType {
        less_than_or_equal_to,
        less_than,
        equal_to
    }

    /**
     * User's permissions and limits
     */
    data class UserPermissions(
        val bypassLimits: Boolean? = null,
        val maxUploads: Long? = null
    )

    /**
     * Basic usage counters
     */
    data class UserUsage(
        val mappingCount: Long? = null,
        val deliveryCount: Long? = null,
        val addressCount: Long? = null,
        val lastUsageUpdate: OffsetDateTime? = null
    )

    /**
     * Information related to data synchronization
     */
    data class UserSyncInfo(
        val lastSyncTime: OffsetDateTime? = null,
        val deviceIds: List<String>? = null,
        val version: Long? = null
    )

    /**
     * User-specific application settings
     */
    data class UserAppSettings(
        val dataCollectionOptIn: Boolean? = null,
        val lastVersion: String? = null,
        val onboardingCompleted: Boolean? = null
    )

    /**
     * User's communication preferences
     */
    data class UserCommunication(
        val emailOptIn: Boolean? = null,
        val marketingOptIn: Boolean? = null,
        val pushNotificationsEnabled: Boolean? = null
    )

    /**
     * Detailed usage statistics
     */
    data class UserUsageStats(
        val deliveryCount: Long? = null,
        val addressCount: Long? = null,
        val lastUsageDate: OffsetDateTime? = null,
        val totalRuns: Long? = null,
        val activeDaysCount: Long? = null,
        /** Total amount of tips received across all deliveries. */
        val totalTips: Double? = null,
        /** Map of feature names to usage count. Should generate as Map<String, Long>. */
        val featureUsage: FeatureUsage? = null
    )

    /**
     * Map of feature names to usage count. Should generate as Map<String, Long>.
     */
    open class FeatureUsage

}
