package com.autogratuity.data.repository.preference

import android.content.SharedPreferences
import android.util.Log
import com.autogratuity.domain.model.User
import com.autogratuity.data.model.generated_kt.User_profile as UserProfileDto
import com.autogratuity.data.repository.core.AtomicCacheSystem
import com.autogratuity.data.repository.core.CacheSource
import com.autogratuity.data.repository.core.SsotOwnership
import com.fasterxml.jackson.databind.ObjectMapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.datetime.Clock
import kotlin.time.Duration
import kotlin.time.Duration.Companion.hours
import kotlin.time.TimeSource
import javax.inject.Inject
import javax.inject.Singleton
import androidx.core.content.edit
import kotlin.time.Duration.Companion.minutes

/**
 * A+ Level domain-specific preference cache system that extends AtomicCacheSystem
 * with SharedPreferences persistence and reactive programming support.
 * 
 * This system provides:
 * - Dual-layer caching: AtomicCacheSystem (in-memory) + SharedPreferences (persistent)
 * - Reactive programming with StateFlow for real-time preference updates
 * - Domain-specific atomic operations for preference management
 * - SSOT awareness for client vs cloud preference synchronization
 * - Cloud function integration support for preference sync
 * - Comprehensive metadata tracking for preference analytics
 */
@Singleton
class PreferenceCacheSystem @Inject constructor(
    private val sharedPreferences: SharedPreferences,
    private val objectMapper: ObjectMapper,
    timeSource: TimeSource,
    private val ioDispatcher: kotlinx.coroutines.CoroutineDispatcher,
    private val applicationScope: kotlinx.coroutines.CoroutineScope
) : AtomicCacheSystem<String, Any>(timeSource) {
    
    private val TAG = "PreferenceCacheSystem"
    
    // Reactive state flows for real-time preference updates
    private val _userProfileFlow = MutableStateFlow<User?>(null)
    val userProfileFlow: StateFlow<User?> = _userProfileFlow.asStateFlow()

    // ✅ CACHE SYSTEM BOUNDARY ADAPTATION: DTO-based reactive flows for LocalDataSource conversion
    private val _userProfileDtoFlow = MutableStateFlow<UserProfileDto?>(null)
    val userProfileDtoFlow: StateFlow<UserProfileDto?> = _userProfileDtoFlow.asStateFlow()

    private val _themeFlow = MutableStateFlow("system")
    val themeFlow: StateFlow<String> = _themeFlow.asStateFlow()
    
    private val _notificationsEnabledFlow = MutableStateFlow(false) // ✅ FIX: Default false - user must manually enable
    val notificationsEnabledFlow: StateFlow<Boolean> = _notificationsEnabledFlow.asStateFlow()
    
    private val _onboardingCompletedFlow = MutableStateFlow(false)
    val onboardingCompletedFlow: StateFlow<Boolean> = _onboardingCompletedFlow.asStateFlow()
    
    private val _dataCollectionOptedInFlow = MutableStateFlow(false)
    val dataCollectionOptedInFlow: StateFlow<Boolean> = _dataCollectionOptedInFlow.asStateFlow()
    
    // Domain-specific configuration
    override val defaultTtl: Duration = 24.hours  // Preferences cached for a day
    override val maxCacheSize: Int = 100         // Preferences are small in number

    // ✅ SMART CACHE TTL: DND preference-aware caching
    private val dndPreferencesTtl: Duration = 10.minutes  // DND preferences change more frequently
    private val subscriptionDataTtl: Duration = 30.minutes // Subscription affects DND rules
    private val regularPreferencesTtl: Duration = 2.hours  // Regular preferences are stable

    /**
     * ✅ SMART CACHE TTL: Determine appropriate TTL based on preference type
     * DND-related preferences get shorter TTL for better cloud synchronization
     */
    private fun getSmartPreferenceTtl(key: String, metadata: Map<String, Any> = emptyMap()): Duration {
        val keyLower = key.lowercase()
        val metadataString = metadata.toString().lowercase()

        return when {
            // DND-related preferences get shortest TTL
            keyLower.contains("dnd") ||
            keyLower.contains("tip") && keyLower.contains("threshold") ||
            keyLower.contains("custom") && keyLower.contains("rule") ||
            metadataString.contains("dnd") -> dndPreferencesTtl

            // Subscription data affects DND rules
            keyLower.contains("subscription") ||
            keyLower.contains("premium") ||
            keyLower.contains("pro") ||
            metadataString.contains("subscription") -> subscriptionDataTtl

            // Regular preferences are stable
            else -> regularPreferencesTtl
        }
    }
    
    companion object {
        private const val PREF_USER_PROFILE_KEY_PREFIX = "user_profile_"
        private const val PREF_THEME = "pref_theme"
        private const val PREF_NOTIFICATIONS_ENABLED = "pref_notifications_enabled"
        private const val PREF_ONBOARDING_COMPLETED = "pref_onboarding_completed"
        private const val PREF_DATA_COLLECTION_OPTED_IN = "pref_data_collection_opted_in"
        
        // SSOT field ownership for preferences
        private val PREFERENCE_OWNERSHIP = mapOf(
            "theme" to SsotOwnership.CLIENT_OWNED,
            "notifications_enabled" to SsotOwnership.CLIENT_OWNED,
            "onboarding_completed" to SsotOwnership.CLIENT_OWNED,
            "data_collection_opted_in" to SsotOwnership.SHARED, // Can be updated by privacy policy changes
            "sync_settings" to SsotOwnership.CLOUD_OWNED,
            "privacy_settings" to SsotOwnership.SHARED
        )
    }
    
    
    
    init {
        // Initialize flows with current SharedPreferences values
        initializeFlowsFromPreferences()
    }
    
    /**
     * Initialize reactive flows with current SharedPreferences values
     */
    private fun initializeFlowsFromPreferences() {
        try {
            _themeFlow.value = sharedPreferences.getString(PREF_THEME, "system") ?: "system"
            _notificationsEnabledFlow.value = sharedPreferences.getBoolean(PREF_NOTIFICATIONS_ENABLED, false) // ✅ FIX: Default false
            _onboardingCompletedFlow.value = sharedPreferences.getBoolean(PREF_ONBOARDING_COMPLETED, false)
            _dataCollectionOptedInFlow.value = sharedPreferences.getBoolean(PREF_DATA_COLLECTION_OPTED_IN, false)
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing flows from preferences", e)
        }
    }
    
    /**
     * Domain-specific atomic operation: Cache user profile with rich metadata
     */
    suspend fun cacheUserProfile(
        userId: String,
        user: User,
        source: CacheSource = CacheSource.MANUAL
    ) {
        val metadata = buildMap<String, Any> {
            put("userId", userId)
            put("profileType", "user_profile")
            put("hasSubscription", user.subscription != null)
            put("hasPreferences", user.preferences != null)
            put("lastCached", Clock.System.now().toEpochMilliseconds())
            put("source", source.name)
            
            // Track SSOT fields
            put("ssotFields", listOf(
                "subscription",      // Cloud-owned
                "sync_info",        // Cloud-owned
                "preferences",      // Client-owned
                "app_settings"      // Client-owned
            ))
            
            user.subscription?.let { sub ->
                put("subscriptionType", sub.level ?: "free")
                put("isProUser", sub.isActive == true)
            }
        }
        
        // ✅ SMART CACHE TTL: Use preference-aware TTL for user profiles
        val smartTtl = getSmartPreferenceTtl("user_profile_$userId", metadata)
        put("user_profile_$userId", user, smartTtl, metadata, source)
        
        // Persist to SharedPreferences
        persistUserProfileToPrefs(userId, user)
        
        // Update reactive flow
        _userProfileFlow.value = user
    }
    
    /**
     * Domain-specific atomic operation: Update preference with conflict resolution
     */
    suspend fun updatePreferenceAtomic(
        key: String,
        value: Any,
        userId: String? = null,
        source: CacheSource = CacheSource.MANUAL
    ) {
        val ownership = PREFERENCE_OWNERSHIP[key] ?: SsotOwnership.CLIENT_OWNED
        
        // Check if source is allowed to modify this preference
        val canModify = when (ownership) {
            SsotOwnership.CLIENT_OWNED -> source != CacheSource.CLOUD_FUNCTION
            SsotOwnership.CLOUD_OWNED -> source == CacheSource.CLOUD_FUNCTION
            SsotOwnership.SHARED -> true
            SsotOwnership.READ_ONLY -> false
        }
        
        if (!canModify) {
            Log.w(TAG, "Source $source cannot modify $key (ownership: $ownership)")
            return
        }
        
        val metadata = buildMap<String, Any> {
            put("preferenceKey", key)
            put("ownership", ownership.name)
            put("lastModified", Clock.System.now().toEpochMilliseconds())
            put("source", source.name)
            userId?.let { put("userId", it) }
        }
        
        // ✅ SMART CACHE TTL: Use preference-aware TTL
        val smartTtl = getSmartPreferenceTtl(key, metadata)
        put(key, value, smartTtl, metadata, source)
        
        // Persist to SharedPreferences
        persistPreferenceToPrefs(key, value)
        
        // Update corresponding reactive flow
        updateReactiveFlow(key, value)
    }
    
    /**
     * Domain-specific atomic operation: Bulk preference update with transaction semantics
     */
    suspend fun updatePreferencesBulk(
        preferences: Map<String, Any>,
        userId: String? = null,
        source: CacheSource = CacheSource.MANUAL
    ) {
        // Validate all preferences can be modified by source
        val invalidPrefs = preferences.keys.filter { key ->
            val ownership = PREFERENCE_OWNERSHIP[key] ?: SsotOwnership.CLIENT_OWNED
            when (ownership) {
                SsotOwnership.CLIENT_OWNED -> source == CacheSource.CLOUD_FUNCTION
                SsotOwnership.CLOUD_OWNED -> source != CacheSource.CLOUD_FUNCTION
                SsotOwnership.SHARED -> false
                SsotOwnership.READ_ONLY -> true
            }
        }
        
        if (invalidPrefs.isNotEmpty()) {
            Log.w(TAG, "Cannot modify preferences $invalidPrefs with source $source")
            return
        }
        
        // Apply all changes atomically
        withContext(ioDispatcher) {
            sharedPreferences.edit {

                preferences.forEach { (key, value) ->
                    val metadata = buildMap<String, Any> {
                        put("preferenceKey", key)
                        put(
                            "ownership",
                            (PREFERENCE_OWNERSHIP[key] ?: SsotOwnership.CLIENT_OWNED).name
                        )
                        put("lastModified", Clock.System.now().toEpochMilliseconds())
                        put("source", source.name)
                        put("bulkUpdate", true)
                        userId?.let { put("userId", it) }
                    }

                    // ✅ SMART CACHE TTL: Use preference-aware TTL for bulk updates
                    val smartTtl = getSmartPreferenceTtl(key, metadata)
                    put(key, value, smartTtl, metadata, source)

                    // Add to SharedPreferences transaction
                    when (value) {
                        is String -> putString(key, value)
                        is Boolean -> putBoolean(key, value)
                        is Int -> putInt(key, value)
                        is Long -> putLong(key, value)
                        is Float -> putFloat(key, value)
                    }

                    // Update reactive flow
                    updateReactiveFlow(key, value)
                }

                // Commit all changes atomically
            }
        }
    }
    
    /**
     * Domain-specific operation: Invalidate user-specific preferences
     */
    suspend fun invalidateUserPreferences(userId: String) {
        invalidateByPredicate { _, metadata ->
            metadata["userId"] == userId
        }
        
        // Clear user profile from SharedPreferences
        clearProfileFromPrefs(userId)
        
        // Reset user profile flow
        _userProfileFlow.value = null
    }
    
    /**
     * Domain-specific operation: Invalidate preferences by ownership
     */
    suspend fun invalidatePreferencesByOwnership(ownership: SsotOwnership) {
        invalidateByPredicate { _, metadata ->
            metadata["ownership"] == ownership.name
        }
    }
    
    /**
     * Domain-specific operation: Sync preferences from cloud
     */
    suspend fun syncPreferencesFromCloud(
        cloudPreferences: Map<String, Any>,
        userId: String
    ) {
        Log.d(TAG, "Syncing ${cloudPreferences.size} preferences from cloud for user $userId")
        
        // Filter to only cloud-owned and shared preferences
        val validCloudPrefs = cloudPreferences.filter { (key, _) ->
            val ownership = PREFERENCE_OWNERSHIP[key] ?: SsotOwnership.CLIENT_OWNED
            ownership != SsotOwnership.CLIENT_OWNED
        }
        
        if (validCloudPrefs.isNotEmpty()) {
            updatePreferencesBulk(validCloudPrefs, userId, CacheSource.CLOUD_FUNCTION)
        }
    }
    
    /**
     * Get preferences cache metrics with domain-specific insights
     */
    suspend fun getPreferenceCacheMetrics(): Map<String, Any> {
        val baseMetrics = getDetailedStats()
        
        val currentCache = observeAll().first()
        val preferencesByOwnership = currentCache.keys
            .groupBy { key -> PREFERENCE_OWNERSHIP[key] ?: SsotOwnership.CLIENT_OWNED }
            .mapValues { it.value.size }
        
        val cloudManagedCount = preferencesByOwnership[SsotOwnership.CLOUD_OWNED] ?: 0
        val clientManagedCount = preferencesByOwnership[SsotOwnership.CLIENT_OWNED] ?: 0
        val sharedCount = preferencesByOwnership[SsotOwnership.SHARED] ?: 0
        
        return baseMetrics + mapOf(
            "cloudManagedPrefs" to cloudManagedCount,
            "clientManagedPrefs" to clientManagedCount,
            "sharedPrefs" to sharedCount,
            "totalPreferences" to (cloudManagedCount + clientManagedCount + sharedCount),
            "activeFlows" to listOf(
                "userProfile" to (_userProfileFlow.value != null),
                "userProfileDto" to (_userProfileDtoFlow.value != null),
                "theme" to _themeFlow.value,
                "notifications" to _notificationsEnabledFlow.value,
                "onboarding" to _onboardingCompletedFlow.value,
                "dataCollection" to _dataCollectionOptedInFlow.value
            ).toMap()
        )
    }
    
    /**
     * ✅ MODERN: Typesafe preference observation - eliminates unchecked cast warnings
     * Replaces generic observePreference<T> with specific type functions
     */
    fun observeStringPreference(key: String, defaultValue: String): Flow<String> {
        return observe(key).map { it as? String ?: defaultValue }.distinctUntilChanged()
    }
    
    fun observeBooleanPreference(key: String, defaultValue: Boolean): Flow<Boolean> {
        return observe(key).map { it as? Boolean ?: defaultValue }.distinctUntilChanged()
    }
    
    fun observeIntPreference(key: String, defaultValue: Int): Flow<Int> {
        return observe(key).map { it as? Int ?: defaultValue }.distinctUntilChanged()
    }
    
    fun observeLongPreference(key: String, defaultValue: Long): Flow<Long> {
        return observe(key).map { it as? Long ?: defaultValue }.distinctUntilChanged()
    }
    
    fun observeFloatPreference(key: String, defaultValue: Float): Flow<Float> {
        return observe(key).map { it as? Float ?: defaultValue }.distinctUntilChanged()
    }
    
    /**
     * @deprecated Use specific type functions (observeStringPreference, observeBooleanPreference, etc.) 
     * to avoid unchecked cast warnings. This generic version will be removed in future versions.
     */
    @Deprecated(
        message = "Use specific type functions to avoid unchecked cast warnings",
        replaceWith = ReplaceWith("observeStringPreference(key, defaultValue) // or appropriate type function"),
        level = DeprecationLevel.WARNING
    )
    @Suppress("UNCHECKED_CAST") // Suppressed only for deprecated legacy function
    fun <T> observePreference(key: String, defaultValue: T): Flow<T> {
        return observe(key).map { it as? T ?: defaultValue }.distinctUntilChanged()
    }
    
    // === Legacy API Compatibility (now A+ with atomic operations) ===
    
    /**
     * Saves a user profile to both atomic cache and SharedPreferences.
     */
    suspend fun saveProfileToPrefs(userId: String, user: User?) {
        if (user == null) {
            invalidateUserPreferences(userId)
        } else {
            cacheUserProfile(userId, user, CacheSource.MANUAL)
        }
    }
    
    /**
     * Loads a user profile from cache first, then SharedPreferences fallback.
     */
    suspend fun loadProfileFromPrefs(userId: String): User? {
        // Try atomic cache first
        val cached = get("user_profile_$userId") as? User
        if (cached != null) return cached
        
        // Fallback to SharedPreferences
        return loadProfileFromPrefsOnly(userId)?.also { profile ->
            // Cache the loaded profile
            cacheUserProfile(userId, profile, CacheSource.MANUAL)
        }
    }
    
    private suspend fun loadProfileFromPrefsOnly(userId: String): User? {
        return withContext(ioDispatcher) {
            try {
                val jsonString = sharedPreferences.getString(PREF_USER_PROFILE_KEY_PREFIX + userId, null)
                if (jsonString != null) {
                    objectMapper.readValue(jsonString, User::class.java)
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading profile from prefs for $userId: ${e.message}", e)
                null
            }
        }
    }
    
    suspend fun getThemePreference(defaultValue: String = "system"): String {
        return get(PREF_THEME) as? String 
            ?: sharedPreferences.getString(PREF_THEME, defaultValue) 
            ?: defaultValue
    }
    
    suspend fun setThemePreference(theme: String) {
        updatePreferenceAtomic(PREF_THEME, theme)
    }
    
    suspend fun getNotificationsEnabled(defaultValue: Boolean = false): Boolean { // ✅ FIX: Default false
        return get(PREF_NOTIFICATIONS_ENABLED) as? Boolean 
            ?: sharedPreferences.getBoolean(PREF_NOTIFICATIONS_ENABLED, defaultValue)
    }
    
    suspend fun setNotificationsEnabled(enabled: Boolean) {
        updatePreferenceAtomic(PREF_NOTIFICATIONS_ENABLED, enabled)
    }
    
    suspend fun isOnboardingCompleted(defaultValue: Boolean = false): Boolean {
        return get(PREF_ONBOARDING_COMPLETED) as? Boolean 
            ?: sharedPreferences.getBoolean(PREF_ONBOARDING_COMPLETED, defaultValue)
    }
    
    suspend fun setOnboardingCompleted(completed: Boolean) {
        updatePreferenceAtomic(PREF_ONBOARDING_COMPLETED, completed)
    }
    
    suspend fun isDataCollectionOptedIn(defaultValue: Boolean = false): Boolean {
        return get(PREF_DATA_COLLECTION_OPTED_IN) as? Boolean 
            ?: sharedPreferences.getBoolean(PREF_DATA_COLLECTION_OPTED_IN, defaultValue)
    }
    
    suspend fun setDataCollectionOptedIn(optedIn: Boolean) {
        updatePreferenceAtomic(PREF_DATA_COLLECTION_OPTED_IN, optedIn)
    }

    @Suppress("UNCHECKED_CAST")
    suspend fun <T : Any> getPreferenceSetting(key: String, defaultValue: T): T {
        return when (defaultValue) {
            is String -> getThemePreference(defaultValue) as T
            is Boolean -> when (key) {
                "notifications_enabled" -> getNotificationsEnabled(defaultValue as Boolean) as T
                "onboarding_completed" -> isOnboardingCompleted(defaultValue as Boolean) as T
                "data_collection_opted_in" -> isDataCollectionOptedIn(defaultValue as Boolean) as T
                else -> defaultValue
            }
            else -> defaultValue
        }
    }

    suspend fun <T : Any> setPreferenceSetting(key: String, value: T) {
        when (value) {
            is String -> when (key) {
                "theme" -> setThemePreference(value)
                else -> updatePreferenceAtomic(key, value)
            }
            is Boolean -> when (key) {
                "notifications_enabled" -> setNotificationsEnabled(value)
                "onboarding_completed" -> setOnboardingCompleted(value)
                "data_collection_opted_in" -> setDataCollectionOptedIn(value)
                else -> updatePreferenceAtomic(key, value)
            }
            else -> updatePreferenceAtomic(key, value)
        }
    }
    
    // === Private Helper Methods ===
    
    private suspend fun persistUserProfileToPrefs(userId: String, user: User) {
        withContext(ioDispatcher) {
            try {
                val jsonString = objectMapper.writeValueAsString(user)
                sharedPreferences.edit {
                    putString(PREF_USER_PROFILE_KEY_PREFIX + userId, jsonString)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error saving profile to prefs for $userId: ${e.message}", e)
            }
        }
    }
    
    private suspend fun persistPreferenceToPrefs(key: String, value: Any) {
        withContext(ioDispatcher) {
            sharedPreferences.edit {
                when (value) {
                    is String -> putString(key, value)
                    is Boolean -> putBoolean(key, value)
                    is Int -> putInt(key, value)
                    is Long -> putLong(key, value)
                    is Float -> putFloat(key, value)
                    else -> {
                        // For complex objects, serialize to JSON
                        try {
                            val jsonString = objectMapper.writeValueAsString(value)
                            putString(key, jsonString)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error serializing preference $key", e)
                        }
                    }
                }
            }
        }
    }
    
    private fun updateReactiveFlow(key: String, value: Any) {
        when (key) {
            PREF_THEME -> _themeFlow.value = value as? String ?: "system"
            PREF_NOTIFICATIONS_ENABLED -> _notificationsEnabledFlow.value = value as? Boolean != false
            PREF_ONBOARDING_COMPLETED -> _onboardingCompletedFlow.value = value as? Boolean == true
            PREF_DATA_COLLECTION_OPTED_IN -> _dataCollectionOptedInFlow.value = value as? Boolean == true
        }
    }
    
    private suspend fun clearProfileFromPrefs(userId: String) {
        withContext(ioDispatcher) {
            sharedPreferences.edit { remove(PREF_USER_PROFILE_KEY_PREFIX + userId) }
        }
    }

    // ===== CACHE SYSTEM BOUNDARY ADAPTATION: DTO-BASED METHODS =====

    /**
     * ✅ CACHE SYSTEM BOUNDARY ADAPTATION: Load UserProfileDto from SharedPreferences
     * Used by LocalDataSource for DTO↔SSoT conversion boundary
     */
    suspend fun loadProfileDtoFromPrefs(userId: String): UserProfileDto? {
        return withContext(ioDispatcher) {
            try {
                val jsonString = sharedPreferences.getString(PREF_USER_PROFILE_KEY_PREFIX + userId, null)
                if (jsonString != null) {
                    objectMapper.readValue(jsonString, UserProfileDto::class.java)
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading DTO profile from prefs for $userId: ${e.message}", e)
                null
            }
        }
    }

    /**
     * ✅ CACHE SYSTEM BOUNDARY ADAPTATION: Save UserProfileDto to SharedPreferences
     * Used by LocalDataSource for DTO↔SSoT conversion boundary
     */
    suspend fun saveProfileDtoToPrefs(userId: String, userProfileDto: UserProfileDto) {
        withContext(ioDispatcher) {
            try {
                val jsonString = objectMapper.writeValueAsString(userProfileDto)
                sharedPreferences.edit {
                    putString(PREF_USER_PROFILE_KEY_PREFIX + userId, jsonString)
                }

                // Update reactive DTO flow
                _userProfileDtoFlow.value = userProfileDto

                Log.d(TAG, "Saved DTO profile to prefs for $userId")
            } catch (e: Exception) {
                Log.e(TAG, "Error saving DTO profile to prefs for $userId: ${e.message}", e)
            }
        }
    }

    /**
     * ✅ CACHE MANAGEMENT: Override clear to also reset reactive flows
     * Extends AtomicCacheSystem.clear() with domain-specific cleanup
     */
    override suspend fun clear() {
        try {
            // Call parent clear method to clear atomic cache
            super.clear()

            // Clear all reactive flows
            _userProfileFlow.value = null
            _userProfileDtoFlow.value = null
            _themeFlow.value = "system"
            _notificationsEnabledFlow.value = false // ✅ FIX: Default false when clearing cache
            _onboardingCompletedFlow.value = false
            _dataCollectionOptedInFlow.value = false

            Log.d(TAG, "PreferenceCacheSystem cache cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing PreferenceCacheSystem cache", e)
        }
    }

    /**
     * ✅ LIFECYCLE: Cleanup resources when cache system is destroyed
     * Required by CacheLifecycleManager for standardization
     */
    fun cleanup() {
        try {
            // Reset all reactive flows to default values
            _userProfileFlow.value = null
            _userProfileDtoFlow.value = null
            _themeFlow.value = "system"
            _notificationsEnabledFlow.value = false // ✅ FIX: Default false during cleanup
            _onboardingCompletedFlow.value = false
            _dataCollectionOptedInFlow.value = false

            Log.d(TAG, "PreferenceCacheSystem cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during PreferenceCacheSystem cleanup", e)
        }
    }
}
