// Auto-generated from UserUsageStats.kt
// TypeScript equivalent of Android domain.model.UserUsageStats

/**
 * Domain model generated from Kotlin UserUsageStats
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface UserUsageStats {
  deliveryCount?: number | null;
  tipCount?: number | null;
  addressCount?: number | null;
  lastUsageDate?: Date | null;
  totalRuns?: number | null;
  activeDaysCount?: number | null;
  totalTips?: number | null;
  featureUsage: Map<string;
}