package com.autogratuity.data.datasource.remote

import android.util.Log
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.util_kt.documentSnapshotToUserProfileDto
import com.autogratuity.data.model.util_kt.wrapUserProfileDtoForFirestore
import com.autogratuity.data.model.util_kt.wrapUserProfileFieldsForFirestore
import com.autogratuity.data.util.AuthenticationStateCoordinator
import com.autogratuity.data.util.PageResult
import com.autogratuity.data.util.ValidationEngine
import com.autogratuity.debug.ClarityArchitectureMonitor
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.SetOptions
import com.google.firebase.firestore.Transaction
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import java.time.OffsetDateTime
import javax.inject.Inject
import com.autogratuity.data.model.generated_kt.User_profile as UserProfileDto

/**
 * Interface for remote data operations related to User Profiles.
 * All methods operate with UserProfileDto objects.
 */
interface UserRemoteDataSource {
    suspend fun getUserProfileById(userId: String): Result<UserProfileDto?>
    suspend fun saveUserProfile(userId: String, userProfile: UserProfileDto): Result<Unit>
    suspend fun updateUserProfileFields(userId: String, updates: Map<String, Any>): Result<Unit>
    suspend fun deleteUserProfile(userId: String): Result<Unit>

    fun observeUserProfileById(userId: String): Flow<UserProfileDto?>

    // Query operations
    suspend fun userExistsByEmail(email: String): Result<Boolean>

    // Pagination operations
    suspend fun loadUserPage(userId: String, pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<UserProfileDto, DocumentSnapshot>>
    suspend fun loadActiveUserPage(pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<UserProfileDto, DocumentSnapshot>>
    suspend fun loadPremiumUserPage(pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<UserProfileDto, DocumentSnapshot>>

    // Transaction support
    fun saveUserProfileInTransaction(transaction: Transaction, userId: String, userProfile: UserProfileDto)
    fun getUserProfileDocumentReference(userId: String): DocumentReference
}

@ExperimentalCoroutinesApi
class UserRemoteDataSourceImpl @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val ioDispatcher: CoroutineDispatcher,
    private val validationEngine: ValidationEngine,
    private val authStateCoordinator: AuthenticationStateCoordinator
) : UserRemoteDataSource {

    private val TAG = "UserRemoteDataSourceImpl"

    companion object {
        private const val USERS_COLLECTION = "users"
    }

    override fun getUserProfileDocumentReference(userId: String): DocumentReference {
        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for getUserProfileDocumentReference.")
        }
        return firestore.collection(USERS_COLLECTION).document(userId)
    }

    override suspend fun getUserProfileById(userId: String): Result<UserProfileDto?> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            Log.w(TAG, "Cannot fetch user profile: userId is blank.")
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank"))
        }

        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$USERS_COLLECTION/$userId"

        try {
            Log.d(TAG, "getUserProfileById: Fetching user profile for user $userId from path: $fullPath")

            val documentSnapshot = getUserProfileDocumentReference(userId).get().await()
            val firestoreDuration = firestoreStartTime.elapsedNow()
            val documentExists = documentSnapshot.exists()
            val dataSize = if (documentExists) documentSnapshot.data?.toString()?.length ?: 0 else 0

            // ENHANCED: Comprehensive Firestore read monitoring
            ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = USERS_COLLECTION,
                documentId = userId,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                queryType = "GET_DOCUMENT",
                queryFilters = listOf("user_profile_access"),
                resultCount = if (documentExists) 1 else 0,
                userId = userId,
                cacheSource = "SERVER"
            )

            if (documentExists) {
                val profile = documentSnapshotToUserProfileDto(documentSnapshot)

                // NOTE: DTO validation removed per Clarity Architecture principles
                // DTOs from Firestore contain encrypted PII that won't pass standard validation (e.g., email format)
                // Validation should only occur on decrypted SSoT models after mapping in UserMapper

                // ENHANCED: Add session correlation for user profile fetch
                ClarityArchitectureMonitor.addSessionEvent("user_profile_fetch:$userId")

                Log.d(TAG, "getUserProfileById: Successfully fetched user profile for user $userId")
                Log.d(TAG, "  Firestore Path: $fullPath")
                Log.d(TAG, "  Document Size: $dataSize bytes")
                Log.d(TAG, "  Firestore Duration: ${firestoreDuration.inWholeMilliseconds}ms")

                // Flag slow Firestore operations
                if (firestoreDuration.inWholeMilliseconds > 1000) {
                    Log.w(TAG, "SLOW FIRESTORE READ: $fullPath took ${firestoreDuration.inWholeMilliseconds}ms")
                }

                Result.Success(profile)
            } else {
                Log.d(TAG, "User profile not found for ID: $userId at path: $fullPath")
                Result.Success(null)
            }
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()

            // ENHANCED: Monitor failed Firestore operation
            ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = USERS_COLLECTION,
                documentId = userId,
                duration = firestoreDuration,
                success = false,
                dataSizeBytes = 0,
                error = e,
                fullPath = fullPath,
                queryType = "GET_DOCUMENT",
                queryFilters = listOf("user_profile_access"),
                resultCount = 0,
                userId = userId,
                cacheSource = "ERROR"
            )

            Log.e(TAG, "Error fetching user profile for ID: $userId from path: $fullPath", e)
            Log.e(TAG, "  Firestore Duration: ${firestoreDuration.inWholeMilliseconds}ms")
            Log.e(TAG, "  Error Type: ${e.javaClass.simpleName}")

            Result.Error(e)
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun observeUserProfileById(userId: String): Flow<UserProfileDto?> {
        if (userId.isBlank()) {
            Log.w(TAG, "Cannot observe user profile: userId is blank.")
            return flowOf(null)
        }
        return callbackFlow {
            val docRef = getUserProfileDocumentReference(userId)
            val listenerRegistration = docRef.addSnapshotListener { documentSnapshot, error ->
                if (error != null) {
                    Log.e(TAG, "Error in observeUserProfileById flow for ID: $userId", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (documentSnapshot != null && documentSnapshot.exists()) {
                    try {
                        val profile = documentSnapshotToUserProfileDto(documentSnapshot)
                        trySend(profile).isSuccess
                    } catch (e: Exception) {
                        Log.e(TAG, "Error converting document to UserProfileDto for ID: $userId", e)
                        trySend(null).isSuccess
                    }
                } else {
                    Log.d(TAG, "User profile snapshot does not exist for ID: $userId")
                    trySend(null).isSuccess
                }
            }
            awaitClose {
                Log.d(TAG, "ObserveUserProfileById: Cancelling listener for $userId")
                listenerRegistration.remove()
            }
        }.flowOn(ioDispatcher)
    }

    override suspend fun saveUserProfile(userId: String, userProfile: UserProfileDto): Result<Unit> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank when saving a user profile."))
        }

        // CRITICAL FIX: Wait for authentication readiness per session-and-auth.md
        Log.d(TAG, "Waiting for authentication readiness before Firestore write...")
        val authState = authStateCoordinator.waitForAuthentication(timeoutMs = 3000)
        when (authState) {
            is AuthenticationStateCoordinator.AuthReadyState.Authenticated -> {
                if (authState.userId != userId) {
                    return@withContext Result.Error(IllegalArgumentException("Authentication mismatch: authenticated as ${authState.userId} but trying to save profile for $userId"))
                }
                Log.d(TAG, "Authentication confirmed for user: ${authState.userId}")
            }
            is AuthenticationStateCoordinator.AuthReadyState.Unauthenticated -> {
                return@withContext Result.Error(IllegalStateException("User not authenticated"))
            }
            else -> {
                return@withContext Result.Error(IllegalStateException("Authentication not ready after timeout"))
            }
        }

        // NOTE: DTO validation removed per Clarity Architecture principles
        // Validation should only occur on SSoT models in Repository layer (already done)
        // and when converting DTOs FROM Firestore to SSoT in UserMapper.mapToDomain()
        // DTOs TO Firestore contain encrypted PII that won't pass standard validation (e.g., email format)

        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$USERS_COLLECTION/$userId"

        try {
            // Ensure the userId within the userProfile object matches the document ID
            val profileToSave = if (userProfile.userId == userId) userProfile else userProfile.copy(userId = userId)

            // ENHANCED: Wrap UserProfileDto in profileData structure for Firestore
            val wrappedProfile = wrapUserProfileDtoForFirestore(profileToSave)
            val dataSize = wrappedProfile.toString().length

            // 🔍 COMPREHENSIVE DEBUG MONITORING: Diagnose Firestore security rules failure
            ClarityArchitectureMonitor.addSessionEvent("user_profile_write_debug:$userId")
            ClarityArchitectureMonitor.logEvent("[DEBUG] === FIRESTORE WRITE DIAGNOSIS START ===")
            ClarityArchitectureMonitor.logEvent("[DEBUG] Writing to Firestore Path: $fullPath")
            ClarityArchitectureMonitor.logEvent("[DEBUG] Auth UID: ${FirebaseAuth.getInstance().currentUser?.uid}")
            ClarityArchitectureMonitor.logEvent("[DEBUG] Auth UID Match: ${FirebaseAuth.getInstance().currentUser?.uid == userId}")
            ClarityArchitectureMonitor.logEvent("[DEBUG] Profile DTO userId: ${profileToSave.userId}")
            ClarityArchitectureMonitor.logEvent("[DEBUG] Profile DTO userId null check: ${profileToSave.userId != null}")
            ClarityArchitectureMonitor.logEvent("[DEBUG] UserId comparison: DTO[${profileToSave.userId}] == Path[$userId] = ${profileToSave.userId == userId}")

            // Structure validation
            ClarityArchitectureMonitor.logEvent("[DEBUG] Wrapped Structure Keys: ${wrappedProfile.keys}")
            ClarityArchitectureMonitor.logEvent("[DEBUG] Has 'profileData' key: ${wrappedProfile.containsKey("profileData")}")

            if (wrappedProfile["profileData"] is Map<*, *>) {
                val profileData = wrappedProfile["profileData"] as Map<*, *>
                ClarityArchitectureMonitor.logEvent("[DEBUG] ProfileData Keys: ${profileData.keys.toList()}")
                ClarityArchitectureMonitor.logEvent("[DEBUG] ProfileData has 'userId': ${profileData.containsKey("userId")}")
                ClarityArchitectureMonitor.logEvent("[DEBUG] ProfileData userId value: ${profileData["userId"]}")
                ClarityArchitectureMonitor.logEvent("[DEBUG] ProfileData userId null check: ${profileData["userId"] != null}")
                ClarityArchitectureMonitor.logEvent("[DEBUG] ProfileData userId matches auth: ${profileData["userId"] == FirebaseAuth.getInstance().currentUser?.uid}")

                // Firestore Security Rules Requirements Check
                val hasRequiredStructure = wrappedProfile.containsKey("profileData")
                val profileDataMap = profileData
                val hasUserIdField = profileDataMap.containsKey("userId")
                val userIdNotNull = profileDataMap["userId"] != null
                val userIdMatches = profileDataMap["userId"] == FirebaseAuth.getInstance().currentUser?.uid

                ClarityArchitectureMonitor.logEvent("[DEBUG] === SECURITY RULES VALIDATION ===")
                ClarityArchitectureMonitor.logEvent("[DEBUG] Rule Check 1 - hasRequiredFields(request.resource.data, ['profileData']): $hasRequiredStructure")
                ClarityArchitectureMonitor.logEvent("[DEBUG] Rule Check 2 - hasRequiredFields(request.resource.data.profileData, ['userId']): $hasUserIdField")
                ClarityArchitectureMonitor.logEvent("[DEBUG] Rule Check 3 - request.resource.data.profileData.userId not null: $userIdNotNull")
                ClarityArchitectureMonitor.logEvent("[DEBUG] Rule Check 4 - request.resource.data.profileData.userId == request.auth.uid: $userIdMatches")

                val allRulesPassed = hasRequiredStructure && hasUserIdField && userIdNotNull && userIdMatches
                ClarityArchitectureMonitor.logEvent("[DEBUG] All Security Rules Passed: $allRulesPassed")

                // Additional field diagnostics for troubleshooting
                ClarityArchitectureMonitor.logEvent("[DEBUG] === FIELD DIAGNOSTICS ===")
                val criticalFields = listOf("userId", "email", "displayName", "accountStatus", "version")
                criticalFields.forEach { field ->
                    val hasField = profileDataMap.containsKey(field)
                    val fieldValue = profileDataMap[field]
                    val isNull = fieldValue == null
                    ClarityArchitectureMonitor.logEvent("[DEBUG] Field '$field': exists=$hasField, value=$fieldValue, isNull=$isNull")
                }

                // Check for potential null value removal by Firestore
                val nullFields = profileDataMap.filter { it.value == null }.keys
                if (nullFields.isNotEmpty()) {
                    ClarityArchitectureMonitor.logEvent("[DEBUG] ⚠️ NULL FIELDS (will be removed by Firestore): ${nullFields.toList()}")
                }

            } else {
                ClarityArchitectureMonitor.logEvent("[DEBUG] ❌ CRITICAL ERROR: profileData is not a Map! Type: ${wrappedProfile["profileData"]?.javaClass?.simpleName}")
            }

            ClarityArchitectureMonitor.logEvent("[DEBUG] Data Size: $dataSize bytes")
            ClarityArchitectureMonitor.logEvent("[DEBUG] === FIRESTORE SERIALIZATION FIX ===")
            ClarityArchitectureMonitor.logEvent("[DEBUG] Using simple Map structure (legacy Java approach)")
            ClarityArchitectureMonitor.logEvent("[DEBUG] Converted complex DTO to primitive types for Firestore")
            ClarityArchitectureMonitor.logEvent("[DEBUG] === FIRESTORE WRITE DIAGNOSIS END ===")

            Log.d(TAG, "saveUserProfile: Saving user profile with simple Map structure for user $userId")

            // 🔍 CRITICAL: Add comprehensive logging around actual Firestore write operation
            ClarityArchitectureMonitor.logEvent("[DEBUG] === FIRESTORE WRITE OPERATION START ===")
            ClarityArchitectureMonitor.logEvent("[DEBUG] About to call Firestore .set() operation")
            ClarityArchitectureMonitor.logEvent("[DEBUG] Document path: $fullPath")
            ClarityArchitectureMonitor.logEvent("[DEBUG] Current thread: ${Thread.currentThread().name}")

            val writeStartTime = kotlin.time.TimeSource.Monotonic.markNow()
            Log.d(TAG, "⏱️ FIRESTORE WRITE: Starting .set() operation at ${System.currentTimeMillis()}")

            try {
                // Add timeout to prevent hanging
                withTimeout(10000) { // 10 second timeout
                    getUserProfileDocumentReference(userId).set(wrappedProfile).await()
                }
                val writeEndTime = writeStartTime.elapsedNow()

                Log.d(TAG, "✅ FIRESTORE WRITE SUCCESS: Document written in ${writeEndTime.inWholeMilliseconds}ms")
                ClarityArchitectureMonitor.logEvent("[DEBUG] ✅ FIRESTORE WRITE COMPLETED SUCCESSFULLY")
                ClarityArchitectureMonitor.logEvent("[DEBUG] Write duration: ${writeEndTime.inWholeMilliseconds}ms")
                ClarityArchitectureMonitor.logEvent("[DEBUG] Document successfully written to: $fullPath")
                ClarityArchitectureMonitor.logEvent("[DEBUG] === FIRESTORE WRITE OPERATION END (SUCCESS) ===")

            } catch (writeException: Exception) {
                val writeEndTime = writeStartTime.elapsedNow()

                Log.e(TAG, "❌ FIRESTORE WRITE FAILED: ${writeException.message} after ${writeEndTime.inWholeMilliseconds}ms")
                ClarityArchitectureMonitor.logEvent("[ERROR] ❌ FIRESTORE WRITE FAILED")
                ClarityArchitectureMonitor.logEvent("[ERROR] Write duration before failure: ${writeEndTime.inWholeMilliseconds}ms")
                ClarityArchitectureMonitor.logEvent("[ERROR] Write exception type: ${writeException.javaClass.simpleName}")
                ClarityArchitectureMonitor.logEvent("[ERROR] Write exception message: ${writeException.message}")
                ClarityArchitectureMonitor.logEvent("[ERROR] === FIRESTORE WRITE OPERATION END (FAILED) ===")

                // Re-throw to be caught by outer try-catch
                throw writeException
            }

            val firestoreDuration = firestoreStartTime.elapsedNow()

            Log.d(TAG, "🎉 COMPLETE SUCCESS: User profile save operation completed successfully")
            ClarityArchitectureMonitor.logEvent("[SUCCESS] 🎉 ENTIRE USER PROFILE SAVE OPERATION COMPLETED")
            ClarityArchitectureMonitor.logEvent("[SUCCESS] Total operation duration: ${firestoreDuration.inWholeMilliseconds}ms")
            ClarityArchitectureMonitor.logEvent("[SUCCESS] User document saved at: $fullPath")

            // FIRESTORE WRITE SUCCESS MONITORING
            ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USERS_COLLECTION,
                documentId = userId,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                writeType = "SET",
                fieldsUpdated = listOf("profileData"),
                userId = userId,
                documentData = wrappedProfile
            )

            Log.d(TAG, "User profile saved for ID: $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()

            // 🚨 COMPREHENSIVE FIRESTORE ERROR ANALYSIS - ClarityArchitectureMonitor Style
            ClarityArchitectureMonitor.logEvent("[ERROR] === FIRESTORE WRITE FAILURE ANALYSIS ===")
            ClarityArchitectureMonitor.logEvent("[ERROR] Exception Type: ${e.javaClass.simpleName}")
            ClarityArchitectureMonitor.logEvent("[ERROR] Exception Message: ${e.message}")
            ClarityArchitectureMonitor.logEvent("[ERROR] Duration before failure: ${firestoreDuration.inWholeMilliseconds}ms")
            ClarityArchitectureMonitor.logEvent("[ERROR] Write attempt path: $fullPath")
            ClarityArchitectureMonitor.logEvent("[ERROR] Auth status: ${FirebaseAuth.getInstance().currentUser != null}")
            ClarityArchitectureMonitor.logEvent("[ERROR] Auth UID: ${FirebaseAuth.getInstance().currentUser?.uid}")

            // Security Rules specific error analysis
            val errorMessage = e.message?.lowercase() ?: ""
            when {
                errorMessage.contains("permission") || errorMessage.contains("denied") -> {
                    ClarityArchitectureMonitor.logEvent("[ERROR] 🔒 SECURITY RULES VIOLATION DETECTED")
                    ClarityArchitectureMonitor.logEvent("[ERROR] Likely cause: hasRequiredFields() or userId validation failed")
                    ClarityArchitectureMonitor.logEvent("[ERROR] Check: profileData structure, userId field presence, auth UID match")
                }
                errorMessage.contains("timeout") || errorMessage.contains("deadline") -> {
                    ClarityArchitectureMonitor.logEvent("[ERROR] ⏱️ NETWORK TIMEOUT DETECTED")
                    ClarityArchitectureMonitor.logEvent("[ERROR] Duration: ${firestoreDuration.inWholeMilliseconds}ms suggests network/server issues")
                }
                errorMessage.contains("quota") || errorMessage.contains("limit") -> {
                    ClarityArchitectureMonitor.logEvent("[ERROR] 💾 QUOTA/LIMIT EXCEEDED")
                }
                errorMessage.contains("invalid") || errorMessage.contains("format") -> {
                    ClarityArchitectureMonitor.logEvent("[ERROR] 📋 DATA FORMAT ERROR")
                    ClarityArchitectureMonitor.logEvent("[ERROR] Possible null fields causing key removal by Firestore")
                }
                else -> {
                    ClarityArchitectureMonitor.logEvent("[ERROR] ❓ UNKNOWN ERROR TYPE - requires investigation")
                }
            }

            // Stack trace for detailed analysis
            ClarityArchitectureMonitor.logEvent("[ERROR] Stack trace: ${e.stackTraceToString().take(500)}")
            ClarityArchitectureMonitor.logEvent("[ERROR] === FIRESTORE WRITE FAILURE ANALYSIS END ===")

            // FIRESTORE WRITE ERROR MONITORING
            ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USERS_COLLECTION,
                documentId = userId,
                duration = firestoreDuration,
                success = false,
                error = e,
                fullPath = fullPath,
                writeType = "SET",
                fieldsUpdated = listOf("profileData"),
                userId = userId
            )

            Log.e(TAG, "Error saving user profile for ID: $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun updateUserProfileFields(userId: String, updates: Map<String, Any>): Result<Unit> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank when updating user profile fields."))
        }
        if (updates.isEmpty()) {
            Log.w(TAG, "Update map is empty for user profile fields update on ID: $userId")
            return@withContext Result.Success(Unit)
        }

        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$USERS_COLLECTION/$userId"

        try {
            // Ensure metadata.updatedAt is always updated if not already present in updates
            val finalUpdates = updates.toMutableMap()
            if (!finalUpdates.containsKey("metadata.updatedAt")) {
                finalUpdates["metadata.updatedAt"] = OffsetDateTime.now()
            }

            // ENHANCED: Wrap fields with profileData prefix for nested Firestore structure
            val wrappedUpdates = wrapUserProfileFieldsForFirestore(finalUpdates)
            val dataSize = wrappedUpdates.toString().length

            Log.d(TAG, "updateUserProfileFields: Updating ${finalUpdates.size} fields with profileData prefix")
            getUserProfileDocumentReference(userId).set(wrappedUpdates, SetOptions.merge()).await()

            val firestoreDuration = firestoreStartTime.elapsedNow()

            // FIRESTORE WRITE SUCCESS MONITORING
            ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USERS_COLLECTION,
                documentId = userId,
                duration = firestoreDuration,
                success = true,
                dataSizeBytes = dataSize,
                fullPath = fullPath,
                writeType = "UPDATE",
                fieldsUpdated = finalUpdates.keys.toList(),
                userId = userId,
                documentData = wrappedUpdates
            )

            Log.d(TAG, "User profile fields updated for ID: $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()

            // FIRESTORE WRITE ERROR MONITORING
            ClarityArchitectureMonitor.monitorFirestoreWrite(
                collection = USERS_COLLECTION,
                documentId = userId,
                duration = firestoreDuration,
                success = false,
                error = e,
                fullPath = fullPath,
                writeType = "UPDATE",
                fieldsUpdated = updates.keys.toList(),
                userId = userId
            )

            Log.e(TAG, "Error updating user profile fields for ID: $userId", e)
            Result.Error(e)
        }
    }

    override suspend fun deleteUserProfile(userId: String): Result<Unit> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank when deleting a user profile."))
        }
        try {
            getUserProfileDocumentReference(userId).delete().await()
            Log.d(TAG, "User profile deleted for ID: $userId")
            Result.Success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting user profile for ID: $userId", e)
            Result.Error(e)
        }
    }

    override fun saveUserProfileInTransaction(transaction: Transaction, userId: String, userProfile: UserProfileDto) {
        if (userId.isBlank()) {
            throw IllegalArgumentException("User ID cannot be blank for saveUserProfileInTransaction.")
        }
        val userProfileRef = getUserProfileDocumentReference(userId)
        // Ensure the userId within the userProfile object matches the document ID
        val profileToSave = if (userProfile.userId == userId) userProfile else userProfile.copy(userId = userId)

        // ENHANCED: Wrap UserProfileDto in profileData structure for Firestore transaction
        val wrappedProfile = wrapUserProfileDtoForFirestore(profileToSave)

        transaction.set(userProfileRef, wrappedProfile)
        Log.d(TAG, "User profile for $userId prepared for save in transaction with profileData wrapper.")
    }

    override suspend fun userExistsByEmail(email: String): Result<Boolean> = withContext(ioDispatcher) {
        if (email.isBlank()) {
            Log.w(TAG, "userExistsByEmail: email is blank.")
            return@withContext Result.Error(IllegalArgumentException("Email cannot be blank"))
        }
        try {
            val query = firestore.collection(USERS_COLLECTION)
                .whereEqualTo("email", email)
                .limit(1)

            val querySnapshot = query.get().await()

            val exists = !querySnapshot.isEmpty
            Log.d(TAG, "userExistsByEmail: Email $email exists: $exists")
            Result.Success(exists)
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if user exists by email: $email", e)
            Result.Error(e)
        }
    }

    // Pagination operations following AddressRemoteDataSource patterns exactly

    private fun buildUserQuery(pageSize: Int, pageKey: DocumentSnapshot?): Result<Query> {
        return try {
            var query = firestore.collection(USERS_COLLECTION)
                .orderBy("profileData.metadata.lastModified")
                .limit(pageSize.toLong())

            pageKey?.let { key ->
                query = query.startAfter(key)
            }

            Result.Success(query)
        } catch (e: Exception) {
            Log.e(TAG, "buildUserQuery: Error building query", e)
            Result.Error(e)
        }
    }

    override suspend fun loadUserPage(userId: String, pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<UserProfileDto, DocumentSnapshot>> = withContext(ioDispatcher) {
        if (userId.isBlank()) {
            Log.w(TAG, "loadUserPage: userId is blank. Cannot query.")
            return@withContext Result.Error(IllegalArgumentException("User ID cannot be blank"))
        }

        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$USERS_COLLECTION/paginated_query"

        try {
            Log.d(TAG, "loadUserPage: Loading user page for user $userId with size: $pageSize, key: ${pageKey?.id}")

            val queryResult = buildUserQuery(pageSize, pageKey)
            when (queryResult) {
                is Result.Success -> {
                    val query = queryResult.data
                    val querySnapshot = query.get().await()
                    val firestoreDuration = firestoreStartTime.elapsedNow()
                    val resultCount = querySnapshot.documents.size
                    val dataSize = querySnapshot.documents.sumOf { it.data?.toString()?.length ?: 0 }

                    // COMPREHENSIVE FIRESTORE READ MONITORING
                    ClarityArchitectureMonitor.monitorFirestoreRead(
                        collection = USERS_COLLECTION,
                        documentId = "paginated_query",
                        duration = firestoreDuration,
                        success = true,
                        dataSizeBytes = dataSize,
                        fullPath = fullPath,
                        queryType = "PAGINATED_QUERY",
                        queryFilters = listOf("user_pagination", "orderBy_lastModified"),
                        resultCount = resultCount,
                        userId = userId,
                        cacheSource = "SERVER"
                    )

                    // SESSION CORRELATION: Add to current session
                    ClarityArchitectureMonitor.addSessionEvent("user_page_load:$pageSize")

                    val userProfileDtos = querySnapshot.documents.mapNotNull { document ->
                        try {
                            documentSnapshotToUserProfileDto(document)
                        } catch (e: Exception) {
                            Log.w(TAG, "Error converting document to UserProfileDto: ${document.id}", e)
                            null
                        }
                    }

                    val nextPageKey = if (querySnapshot.documents.size == pageSize) {
                        querySnapshot.documents.lastOrNull()
                    } else {
                        null
                    }

                    Log.d(TAG, "loadUserPage: Successfully loaded ${userProfileDtos.size} user profiles")
                    Log.d(TAG, "  Firestore Duration: ${firestoreDuration.inWholeMilliseconds}ms")
                    Log.d(TAG, "  Data Size: $dataSize bytes")
                    Log.d(TAG, "  Next Page Available: ${nextPageKey != null}")

                    Result.Success(PageResult(userProfileDtos, nextPageKey))
                }
                is Result.Error -> {
                    val firestoreDuration = firestoreStartTime.elapsedNow()

                    // FIRESTORE ERROR MONITORING
                    ClarityArchitectureMonitor.monitorFirestoreRead(
                        collection = USERS_COLLECTION,
                        documentId = "paginated_query",
                        duration = firestoreDuration,
                        success = false,
                        error = queryResult.exception,
                        fullPath = fullPath,
                        queryType = "PAGINATED_QUERY",
                        queryFilters = listOf("user_pagination"),
                        userId = userId,
                        cacheSource = "ERROR"
                    )

                    queryResult
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("QueryBuilder returned Loading unexpectedly"))
                }
            }
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()

            // FIRESTORE ERROR MONITORING
            ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = USERS_COLLECTION,
                documentId = "paginated_query",
                duration = firestoreDuration,
                success = false,
                error = e,
                fullPath = fullPath,
                queryType = "PAGINATED_QUERY",
                queryFilters = listOf("user_pagination"),
                userId = userId,
                cacheSource = "ERROR"
            )

            Log.e(TAG, "loadUserPage: Error loading user page for user $userId", e)
            Result.Error(e)
        }
    }

    private fun buildActiveUserQuery(pageSize: Int, pageKey: DocumentSnapshot?): Result<Query> {
        return try {
            var query = firestore.collection(USERS_COLLECTION)
                .whereEqualTo("profileData.metadata.isActive", true)
                .orderBy("profileData.metadata.lastModified")
                .limit(pageSize.toLong())

            pageKey?.let { key ->
                query = query.startAfter(key)
            }

            Result.Success(query)
        } catch (e: Exception) {
            Log.e(TAG, "buildActiveUserQuery: Error building active user query", e)
            Result.Error(e)
        }
    }

    override suspend fun loadActiveUserPage(pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<UserProfileDto, DocumentSnapshot>> = withContext(ioDispatcher) {
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$USERS_COLLECTION/active_users_query"

        try {
            Log.d(TAG, "loadActiveUserPage: Loading active user page with size: $pageSize, key: ${pageKey?.id}")

            val queryResult = buildActiveUserQuery(pageSize, pageKey)
            when (queryResult) {
                is Result.Success -> {
                    val query = queryResult.data
                    val querySnapshot = query.get().await()
                    val firestoreDuration = firestoreStartTime.elapsedNow()
                    val resultCount = querySnapshot.documents.size
                    val dataSize = querySnapshot.documents.sumOf { it.data?.toString()?.length ?: 0 }

                    // COMPREHENSIVE FIRESTORE READ MONITORING
                    ClarityArchitectureMonitor.monitorFirestoreRead(
                        collection = USERS_COLLECTION,
                        documentId = "active_users_query",
                        duration = firestoreDuration,
                        success = true,
                        dataSizeBytes = dataSize,
                        fullPath = fullPath,
                        queryType = "FILTERED_PAGINATED_QUERY",
                        queryFilters = listOf("active_users", "isActive_true", "orderBy_lastModified"),
                        resultCount = resultCount,
                        userId = "system",
                        cacheSource = "SERVER"
                    )

                    // SESSION CORRELATION
                    ClarityArchitectureMonitor.addSessionEvent("active_user_page_load:$pageSize")

                    val userProfileDtos = querySnapshot.documents.mapNotNull { document ->
                        try {
                            documentSnapshotToUserProfileDto(document)
                        } catch (e: Exception) {
                            Log.w(TAG, "Error converting active user document to UserProfileDto: ${document.id}", e)
                            null
                        }
                    }

                    val nextPageKey = if (querySnapshot.documents.size == pageSize) {
                        querySnapshot.documents.lastOrNull()
                    } else {
                        null
                    }

                    Log.d(TAG, "loadActiveUserPage: Successfully loaded ${userProfileDtos.size} active user profiles")
                    Result.Success(PageResult(userProfileDtos, nextPageKey))
                }
                is Result.Error -> queryResult
                is Result.Loading -> Result.Error(IllegalStateException("QueryBuilder returned Loading unexpectedly"))
            }
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()

            ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = USERS_COLLECTION,
                documentId = "active_users_query",
                duration = firestoreDuration,
                success = false,
                error = e,
                fullPath = fullPath,
                queryType = "FILTERED_PAGINATED_QUERY",
                queryFilters = listOf("active_users"),
                userId = "system",
                cacheSource = "ERROR"
            )

            Log.e(TAG, "loadActiveUserPage: Error loading active user page", e)
            Result.Error(e)
        }
    }

    private fun buildPremiumUserQuery(pageSize: Int, pageKey: DocumentSnapshot?): Result<Query> {
        return try {
            var query = firestore.collection(USERS_COLLECTION)
                .whereNotEqualTo("profileData.subscription", null)
                .orderBy("profileData.subscription.lastModified")
                .limit(pageSize.toLong())

            pageKey?.let { key ->
                query = query.startAfter(key)
            }

            Result.Success(query)
        } catch (e: Exception) {
            Log.e(TAG, "buildPremiumUserQuery: Error building premium user query", e)
            Result.Error(e)
        }
    }

    override suspend fun loadPremiumUserPage(pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<UserProfileDto, DocumentSnapshot>> = withContext(ioDispatcher) {
        val firestoreStartTime = kotlin.time.TimeSource.Monotonic.markNow()
        val fullPath = "$USERS_COLLECTION/premium_users_query"

        try {
            Log.d(TAG, "loadPremiumUserPage: Loading premium user page with size: $pageSize, key: ${pageKey?.id}")

            val queryResult = buildPremiumUserQuery(pageSize, pageKey)
            when (queryResult) {
                is Result.Success -> {
                    val query = queryResult.data
                    val querySnapshot = query.get().await()
                    val firestoreDuration = firestoreStartTime.elapsedNow()
                    val resultCount = querySnapshot.documents.size
                    val dataSize = querySnapshot.documents.sumOf { it.data?.toString()?.length ?: 0 }

                    // COMPREHENSIVE FIRESTORE READ MONITORING
                    ClarityArchitectureMonitor.monitorFirestoreRead(
                        collection = USERS_COLLECTION,
                        documentId = "premium_users_query",
                        duration = firestoreDuration,
                        success = true,
                        dataSizeBytes = dataSize,
                        fullPath = fullPath,
                        queryType = "SUBSCRIPTION_FILTERED_QUERY",
                        queryFilters = listOf("premium_users", "subscription_exists", "orderBy_subscription_lastModified"),
                        resultCount = resultCount,
                        userId = "system",
                        cacheSource = "SERVER"
                    )

                    // SESSION CORRELATION
                    ClarityArchitectureMonitor.addSessionEvent("premium_user_page_load:$pageSize")

                    val userProfileDtos = querySnapshot.documents.mapNotNull { document ->
                        try {
                            documentSnapshotToUserProfileDto(document)
                        } catch (e: Exception) {
                            Log.w(TAG, "Error converting premium user document to UserProfileDto: ${document.id}", e)
                            null
                        }
                    }

                    val nextPageKey = if (querySnapshot.documents.size == pageSize) {
                        querySnapshot.documents.lastOrNull()
                    } else {
                        null
                    }

                    Log.d(TAG, "loadPremiumUserPage: Successfully loaded ${userProfileDtos.size} premium user profiles")
                    Result.Success(PageResult(userProfileDtos, nextPageKey))
                }
                is Result.Error -> queryResult
                is Result.Loading -> Result.Error(IllegalStateException("QueryBuilder returned Loading unexpectedly"))
            }
        } catch (e: Exception) {
            val firestoreDuration = firestoreStartTime.elapsedNow()

            ClarityArchitectureMonitor.monitorFirestoreRead(
                collection = USERS_COLLECTION,
                documentId = "premium_users_query",
                duration = firestoreDuration,
                success = false,
                error = e,
                fullPath = fullPath,
                queryType = "SUBSCRIPTION_FILTERED_QUERY",
                queryFilters = listOf("premium_users"),
                userId = "system",
                cacheSource = "ERROR"
            )

            Log.e(TAG, "loadPremiumUserPage: Error loading premium user page", e)
            Result.Error(e)
        }
    }
}
