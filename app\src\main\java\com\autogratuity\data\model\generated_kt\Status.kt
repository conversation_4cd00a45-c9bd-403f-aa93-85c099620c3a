/*
 * Status.kt
 *
 * This code was generated by json-kotlin-schema-codegen - JSON Schema Code Generator
 * See https://github.com/pwall567/json-kotlin-schema-codegen
 *
 * It is not advisable to modify generated code as any modifications will be lost
 * when the generation process is re-run.
 *
 * Generated by json-kotlin-schema-codegen. DO NOT EDIT.
 */
package com.autogratuity.data.model.generated_kt

import java.time.OffsetDateTime

/**
 * Represents status information for a Delivery.
 */
data class Status(
    /** The current state of the delivery (e.g., created, completed). */
    val state: String? = null,
    /** Indicates if a tip has been received. */
    val isTipped: Boolean? = null,
    /** Indicates if the delivery is marked as completed. */
    val isCompleted: Boolean? = null,
    /** Indicates if the delivery information has been verified. */
    val isVerified: Boolean? = null,
    /** Reason for cancellation, if applicable. */
    val cancellationReason: String? = null,
    /** Source of the verification information. */
    val verificationSource: String? = null,
    /** Timestamp of the verification. */
    val verificationTimestamp: OffsetDateTime? = null,
    /** Flag indicating if future deliveries should be avoided (potentially deprecated/moved). */
    val doNotDeliver: Boolean? = null,
    /** Reason why doNotDeliver was set, if applicable. EXPLICIT_IMPORT means text in import. RULE_BASED means tip rules. MANUAL_TOGGLE means user UI interaction. */
    val dndReason: String? = null
)
