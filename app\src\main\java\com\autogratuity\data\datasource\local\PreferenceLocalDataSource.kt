package com.autogratuity.data.datasource.local

import com.autogratuity.data.mapper.UserMapper
import com.autogratuity.data.model.Result
import com.autogratuity.data.repository.preference.PreferenceCacheSystem
import com.autogratuity.domain.model.User
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Interface for local data operations (caching) related to User Preferences.
 * All methods operate with SSoT User models following AddressLocalDataSource pattern.
 */
interface PreferenceLocalDataSource {
    suspend fun getUserById(userId: String): Result<User?>
    suspend fun saveUser(userId: String, user: User): Result<Unit>
    suspend fun deleteUser(userId: String): Result<Unit>
    suspend fun clearAllCaches(): Result<Unit>
    fun observeById(userId: String): Flow<User?>
}

@ExperimentalCoroutinesApi
class PreferenceLocalDataSourceImpl @Inject constructor(
    private val preferenceCacheSystem: PreferenceCacheSystem,
    private val userMapper: UserMapper,
    private val ioDispatcher: CoroutineDispatcher
) : PreferenceLocalDataSource {

    override suspend fun getUserById(userId: String): Result<User?> = withContext(ioDispatcher) {
        try {
            // ✅ CACHE SYSTEM BOUNDARY ADAPTATION: LocalDataSource handles DTO↔SSoT conversion
            val userProfileDto = preferenceCacheSystem.loadProfileDtoFromPrefs(userId)
            if (userProfileDto != null) {
                // Convert DTO → Domain using mapper
                val mapResult = userMapper.mapToDomain(userId, userProfileDto)
                when (mapResult) {
                    is Result.Success -> Result.Success(mapResult.data)
                    is Result.Error -> Result.Error(mapResult.exception)
                    is Result.Loading -> Result.Error(IllegalStateException("Mapper returned Loading"))
                }
            } else {
                Result.Success(null)
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun saveUser(userId: String, user: User): Result<Unit> = withContext(ioDispatcher) {
        try {
            // ✅ CACHE SYSTEM BOUNDARY ADAPTATION: LocalDataSource handles DTO↔SSoT conversion
            // Convert Domain → DTO using mapper
            val mapResult = userMapper.mapToDto(user)
            when (mapResult) {
                is Result.Success -> {
                    // Save DTO to optimized cache system
                    preferenceCacheSystem.saveProfileDtoToPrefs(userId, mapResult.data)
                    Result.Success(Unit)
                }
                is Result.Error -> Result.Error(mapResult.exception)
                is Result.Loading -> Result.Error(IllegalStateException("Mapper returned Loading"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun deleteUser(userId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            // ✅ FIXED: Use PreferenceCacheSystem invalidation
            preferenceCacheSystem.invalidateUserPreferences(userId)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun clearAllCaches(): Result<Unit> = withContext(ioDispatcher) {
        try {
            // ✅ FIXED: Use PreferenceCacheSystem clear
            preferenceCacheSystem.clear()
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override fun observeById(userId: String): Flow<User?> {
        // ✅ STANDARDIZED: Cache System Boundary Adaptation with DTO→Domain conversion
        return preferenceCacheSystem.userProfileDtoFlow.map { userProfileDto ->
            if (userProfileDto != null) {
                val mapResult = userMapper.mapToDomain(userId, userProfileDto)
                when (mapResult) {
                    is Result.Success -> mapResult.data
                    is Result.Error -> {
                        // Log error but don't crash the reactive stream
                        null
                    }
                    is Result.Loading -> null
                }
            } else null
        }
    }
}