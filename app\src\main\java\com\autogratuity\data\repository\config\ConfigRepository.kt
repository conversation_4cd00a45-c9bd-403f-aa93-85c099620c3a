package com.autogratuity.data.repository.config

// Import SSoT domain models (not DTOs)
import com.autogratuity.data.model.Result
import com.autogratuity.domain.model.AppConfig
import com.autogratuity.domain.model.NotificationPatterns
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * Data layer interface for Config repository operations.
 * Follows DeliveryRepository pattern: aligns closely with domain interface but serves as internal data layer contract.
 * All operations use SSoT domain models, never DTOs.
 */
interface ConfigRepository {

    // ===== CORE APP CONFIG OPERATIONS =====

    /**
     * Retrieves the current application configuration.
     * @param forceRefresh Whether to bypass cache and fetch from remote
     * @return Result containing AppConfig or error
     */
    suspend fun getAppConfig(forceRefresh: Boolean): Result<AppConfig?>

    /**
     * Updates the application configuration.
     * @param appConfig The AppConfig SSoT model to update
     * @return Result indicating success or error
     */
    suspend fun updateAppConfig(appConfig: AppConfig): Result<Unit>

    /**
     * Updates specific fields of the application configuration.
     * @param fields Map of field paths to new values
     * @return Result indicating success or error
     */
    suspend fun updateAppConfigFields(fields: Map<String, Any>): Result<Unit>

    // ===== CORE NOTIFICATION PATTERNS OPERATIONS =====

    /**
     * Retrieves the current notification parsing patterns.
     * @return Result containing NotificationPatterns or error
     */
    suspend fun getNotificationPatterns(): Result<NotificationPatterns?>

    /**
     * Updates the notification parsing patterns.
     * @param patterns The NotificationPatterns SSoT model to update
     * @return Result indicating success or error
     */
    suspend fun updateNotificationPatterns(patterns: NotificationPatterns): Result<Unit>

    // ===== REACTIVE FLOW OPERATIONS =====

    /**
     * Observes application configuration changes in real-time.
     * @return Flow emitting Result<AppConfig?> updates
     */
    fun observeAppConfig(): Flow<Result<AppConfig?>>

    /**
     * Observes notification patterns changes in real-time.
     * @return Flow emitting Result<NotificationPatterns?> updates
     */
    fun observeNotificationPatterns(): Flow<Result<NotificationPatterns?>>

    // ===== CONFIGURATION VALUE OPERATIONS =====

    /**
     * Gets a configuration value as a string.
     * @param key The configuration key
     * @param defaultValue Default value if not found
     * @return Result containing the configuration value
     */
    suspend fun getConfigValue(key: String, defaultValue: String): Result<String>

    /**
     * Gets a configuration value as a boolean.
     * @param key The configuration key
     * @param defaultValue Default value if not found
     * @return Result containing the configuration value
     */
    suspend fun getConfigBoolean(key: String, defaultValue: Boolean): Result<Boolean>

    /**
     * Increments a counter value in the configuration.
     * @param counterKey The counter key to increment
     * @return Result indicating success or error
     */
    suspend fun incrementCounter(counterKey: String): Result<Unit>

    // ===== UTILITY OPERATIONS =====

    /**
     * Updates the last active timestamp for the current device.
     * @return Result indicating success or error
     */
    suspend fun updateDeviceLastActive(): Result<Unit>

    /**
     * Prefetches critical configuration data for improved performance.
     * @return Result indicating success or error
     */
    suspend fun prefetchCriticalData(): Result<Unit>

    // ===== CACHE MANAGEMENT =====

    /**
     * Clears all cached configuration data.
     * @return Result indicating success or error
     */
    suspend fun clearCache(): Result<Unit>

    /**
     * Invalidates specific configuration cache.
     * @param key The configuration key to invalidate
     * @return Result indicating success or error
     */
    suspend fun invalidateCache(key: String): Result<Unit>

    // ===== REPOSITORY LIFECYCLE =====

    /**
     * Initializes the repository.
     * @return Result indicating success or error
     */
    suspend fun initialize(): Result<Unit>

    /**
     * Cleans up resources used by the repository.
     * @return Result indicating success or error
     */
    suspend fun cleanup(): Result<Unit>

    // ===== VALIDATION AND UTILITY =====

    /**
     * Validates application configuration before saving.
     * @param appConfig The AppConfig SSoT model to validate
     * @return Result indicating if valid or error with validation details
     */
    suspend fun validateAppConfig(appConfig: AppConfig): Result<Unit>

    /**
     * Creates a default application configuration.
     * @return Result containing default AppConfig SSoT model
     */
    suspend fun createDefaultAppConfig(): Result<AppConfig>

    /**
     * Creates default notification patterns.
     * @return Result containing default NotificationPatterns SSoT model
     */
    suspend fun createDefaultNotificationPatterns(): Result<NotificationPatterns>

    // NEW: Required methods for modernized services
    // Alias for observeNotificationPatterns to match service expectations
    fun getNotificationPatternsFlow(): Flow<NotificationPatterns?> = observeNotificationPatterns().map { result ->
        when (result) {
            is Result.Success -> result.data
            else -> null
        }
    }

    /**
     * Return a no-op operation that just completes immediately.
     */
    suspend fun noOpSuspending(): Result<Unit>
}