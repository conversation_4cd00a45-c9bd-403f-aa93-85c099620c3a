package com.autogratuity.data.mapper

// ===== OPTIMIZED IMPORTS =====
// DTO Models with aliases
import com.autogratuity.data.model.generated_kt.Delivery as DeliveryDto
import com.autogratuity.data.model.generated_kt.Status as StatusDto
import com.autogratuity.data.model.generated_kt.Times as TimesDto
import com.autogratuity.data.model.generated_kt.Amounts as AmountsDto
import com.autogratuity.data.model.generated_kt.Reference as ReferenceDto
import com.autogratuity.data.model.generated_kt.Platform as PlatformDto
import com.autogratuity.data.model.generated_kt.Metadata as MetadataDto
import com.autogratuity.data.model.generated_kt.Delivery_stats
import com.autogratuity.data.model.generated_kt.User_profile

// Domain Models (SSoT)
import com.autogratuity.domain.model.Delivery
import com.autogratuity.domain.model.DeliveryDetails
import com.autogratuity.domain.model.DeliveryStats
import com.autogratuity.domain.model.SimpleAddress
import com.autogratuity.domain.model.StatisticsPeriod

// Data & Result Types
import com.autogratuity.data.model.Result
import com.autogratuity.data.model.SingleValidationResult
import com.autogratuity.data.util.ValidationEngine

// Security & Encryption
import com.autogratuity.data.security.EncryptionUtils
import com.autogratuity.data.security.CryptoResult
import com.autogratuity.data.repository.core.RepositoryException

// Standard Libraries
import javax.inject.Inject
import java.time.OffsetDateTime
import java.util.Date
import java.util.Calendar
import android.util.Log

import kotlin.time.TimeSource
// Performance monitoring
import com.autogratuity.debug.ClarityArchitectureMonitor
import kotlinx.coroutines.ExperimentalCoroutinesApi

@ExperimentalCoroutinesApi
class DeliveryMapper @Inject constructor(
    private val encryptionUtils: EncryptionUtils,
    private val deliveryStatsMapper: DeliveryStatsMapper,
    private val validationEngine: ValidationEngine
) {
    
    companion object {
        private const val TAG = "DeliveryMapper"
    }

    // Define sensitive fields according to GOLDENSTANDARDS.MD
    // These are paths within the Delivery.DeliveryData object
    // NOTE: Currently used for documentation/reference. Encryption is handled manually per field.
    // TODO: Consider implementing automated field processing using this list in future iterations.
    @Suppress("unused") // Suppressing warning as this is reference documentation for sensitive fields
    private val sensitiveDeliveryDataFields = setOf(
        "notes", // DeliveryData.notes
        "address.fullAddress", // DeliveryData.SimpleAddress.fullAddress
        "address.latitude", // DeliveryData.SimpleAddress.latitude - will be stored as encrypted string
        "address.longitude" // DeliveryData.SimpleAddress.longitude - will be stored as encrypted string
        // "orderId" // Consider if DeliveryData.orderId should be encrypted
    )

    // Helper to handle CryptoResult for a single field decryption
    private suspend fun decryptField(encrypted: String?, fieldName: String): String? {
        return encrypted?.let {
            if (encryptionUtils.isEncrypted(it)) {
                when (val cryptoOutcome = encryptionUtils.decrypt(it)) {
                    is CryptoResult.Success -> cryptoOutcome.data
                    is CryptoResult.DecryptionFailure -> throw RepositoryException.CryptographicError("Failed to decrypt $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "decryption_failure")
                    is CryptoResult.AuthenticationFailure -> throw RepositoryException.CryptographicError("Authentication failure during decryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "authentication_failure")
                    is CryptoResult.InitializationFailure -> throw RepositoryException.CryptographicError("Crypto init failure during decryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "initialization_failure")
                    is CryptoResult.InputValidationFailure -> throw RepositoryException.CryptographicError("Input validation failure during decryption of $fieldName: ${cryptoOutcome.message}", null, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "input_validation_failure")
                    is CryptoResult.KeyFailure -> throw RepositoryException.CryptographicError("Key failure during decryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "key_failure")
                    is CryptoResult.TimeoutFailure -> throw RepositoryException.CryptographicError("Timeout during decryption of $fieldName: ${cryptoOutcome.message}", null, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "timeout_failure")
                    // Add other specific CryptoResult.Failure types if needed
                    is CryptoResult.Failure -> throw RepositoryException.CryptographicError("Generic crypto failure during decryption of $fieldName: ${cryptoOutcome.message}", null, operation = "decrypt", fieldName = fieldName, cryptoErrorType = "generic_failure") // Fallback for other Failure types
                }
            } else {
                // If not prefixed, assume it's plaintext (e.g. legacy data or error). This might need policy decision.
                // For now, returning as is. Consider logging a warning or failing if plaintext is unexpected for a PII field.
                it
            }
        }
    }

    // Helper to handle CryptoResult for a single field encryption
    private suspend fun encryptField(plaintext: String?, fieldName: String): String? {
        return plaintext?.let {
            when (val cryptoOutcome = encryptionUtils.encrypt(it)) {
                is CryptoResult.Success -> cryptoOutcome.data
                is CryptoResult.EncryptionFailure -> throw RepositoryException.CryptographicError("Failed to encrypt $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "encryption_failure")
                is CryptoResult.InitializationFailure -> throw RepositoryException.CryptographicError("Crypto init failure during encryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "initialization_failure")
                is CryptoResult.InputValidationFailure -> throw RepositoryException.CryptographicError("Input validation failure during encryption of $fieldName: ${cryptoOutcome.message}", null, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "input_validation_failure")
                is CryptoResult.KeyFailure -> throw RepositoryException.CryptographicError("Key failure during encryption of $fieldName: ${cryptoOutcome.message}", cryptoOutcome.cause, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "key_failure")
                is CryptoResult.TimeoutFailure -> throw RepositoryException.CryptographicError("Timeout during encryption of $fieldName: ${cryptoOutcome.message}", null, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "timeout_failure")
                // Add other specific CryptoResult.Failure types if needed
                is CryptoResult.Failure -> throw RepositoryException.CryptographicError("Generic crypto failure during encryption of $fieldName: ${cryptoOutcome.message}", null, operation = "encrypt", fieldName = fieldName, cryptoErrorType = "generic_failure") // Fallback for other Failure types
            }
        }
    }



    suspend fun mapToDomain(dtoId: String, dataDto: DeliveryDto.DeliveryData?): Result<Delivery> {
        val mappingStartTime = TimeSource.Monotonic.markNow()
        val validationErrors = mutableListOf<String>()
        val fieldMismatches = mutableListOf<String>()
        var piiFieldsProcessed = 0

        if (dataDto == null) return Result.Error(IllegalArgumentException("DeliveryData DTO cannot be null for mapping to domain"))

        Log.d(TAG, "🔄 MAPPING: Starting DTO->Domain conversion for delivery $dtoId (userId: ${dataDto.userId}, orderId: ${dataDto.orderId})")

        return try {
            // ✅ ARCHITECTURAL FIX: Log validation issues but don't fail completely
            // This prevents deliveries from being filtered out of the list due to data quality issues
            if (dataDto.userId.isBlank()) {
                Log.w(TAG, "⚠️ DATA QUALITY: Delivery $dtoId has blank userId - this may cause issues but will be included in list")
                validationErrors.add("userId is blank")
            }
            if (dataDto.orderId.isBlank()) {
                Log.w(TAG, "⚠️ DATA QUALITY: Delivery $dtoId has blank orderId - this may cause issues but will be included in list")
                validationErrors.add("orderId is blank")
            }

            val decryptedNotes = decryptField(dataDto.notes, "notes")

            val plainAddress = dataDto.address.let { addressDto ->
                SimpleAddress(
                    id = addressDto.id,
                    fullAddress = addressDto.fullAddress,
                    latitude = addressDto.latitude,
                    longitude = addressDto.longitude,
                    placeId = addressDto.placeId
                )
            }

            val itemsDomain = dataDto.items?.mapNotNull { item ->
                @Suppress("UNCHECKED_CAST")
                item as? Map<String, Any>
            }

            val domainDetails = DeliveryDetails(
                userId = dataDto.userId.takeIf { it.isNotBlank() } ?: "MISSING_USER_ID",
                orderId = dataDto.orderId.takeIf { it.isNotBlank() } ?: "MISSING_ORDER_ID",
                notes = decryptedNotes,
                address = plainAddress,
                status = dataDto.status,
                times = dataDto.times,
                amounts = dataDto.amounts,
                reference = dataDto.reference,
                platform = dataDto.platform,
                items = itemsDomain,
                metadata = dataDto.metadata
            )

            // ✅ ARCHITECTURAL INVARIANT: Create domain delivery with guaranteed non-null details
            // The domain model now enforces that details can never be null
            val domainDelivery = Delivery(
                id = dtoId,
                details = domainDetails // Non-null by design - compilation enforced
            )

            // ✅ DIAGNOSTIC: Log the created domain delivery structure
            Log.d(TAG, "Created domain delivery - ID: $dtoId, details null: ${false}")
            if (true) {
                Log.d(TAG, "Domain details - userId: ${domainDetails.userId}, orderId: ${domainDetails.orderId}, status: ${domainDetails.status?.isCompleted}, times null: ${domainDetails.times == null}")
                if (domainDetails.times != null) {
                    Log.d(TAG, "Domain times - completedAt null: ${domainDetails.times.completedAt == null}")
                }
            }

            // Count PII fields processed
            if (dataDto.notes != null) piiFieldsProcessed++
            if (dataDto.address.fullAddress.isNotEmpty()) piiFieldsProcessed++
            if (dataDto.address.placeId != null) piiFieldsProcessed++

            // Validate required fields
            if (dtoId.isBlank()) validationErrors.add("Delivery ID is blank")
            if (dataDto.userId.isBlank()) validationErrors.add("User ID is missing")
            if (dataDto.orderId.isBlank()) validationErrors.add("Order ID is missing")

            // ✅ VALIDATE DOMAIN OUTPUT
            Log.d(TAG, "About to validate domain delivery - delivery.details null: ${false}")

            // ✅ ARCHITECTURAL FIX: Simple validation - no defensive copying needed
            // Domain model now guarantees details are non-null, eliminating the root cause
            Log.d(TAG, "Validating domain delivery - ID: $dtoId, Thread: ${Thread.currentThread().id}")

            val domainValidationResult = validationEngine.validateDelivery(domainDelivery)
            if (!domainValidationResult.isValid) {
                Log.w(TAG, "Domain Delivery validation failed in mapper for delivery $dtoId: ${domainValidationResult.errors}")
                validationErrors.addAll(domainValidationResult.errors.map { "Domain validation: $it" })

                // Log but don't fail - this is a data quality issue that should be tracked
                ClarityArchitectureMonitor.addSessionEvent("delivery_mapping_validation_warning:$dtoId")
            }

            if (domainValidationResult.warnings.isNotEmpty()) {
                Log.w(TAG, "Domain Delivery validation warnings in mapper for delivery $dtoId: ${domainValidationResult.warnings}")
            }

            val mappingDuration = mappingStartTime.elapsedNow()
            
            // 📊 COMPREHENSIVE BEFORE/AFTER STATE LOGGING
            val inputState = mapOf(
                "dto_id" to dtoId,
                "dto_userId" to dataDto.userId,
                "dto_orderId" to dataDto.orderId,
                "dto_addressId" to dataDto.address.id,
                "dto_tipAmount" to dataDto.amounts?.tipAmount,
                "dto_status" to dataDto.status?.state,
                "dto_notes" to (if (dataDto.notes.isNullOrEmpty()) "empty" else "has_content")
            )
            
            val outputState = mapOf(
                "domain_id" to domainDelivery.id,
                "domain_userId" to domainDelivery.details.userId,
                "domain_orderId" to domainDelivery.details.orderId,
                "domain_addressId" to domainDelivery.details.address?.id,
                "domain_tipAmount" to domainDelivery.details.amounts?.tipAmount,
                "domain_status" to domainDelivery.details.status?.state,
                "domain_notes" to (if (domainDelivery.details.notes.isNullOrEmpty()) "empty" else "has_content")
            )
            
            val fieldTransformations = listOf(
                "notes: DECRYPT",
                "address.fullAddress: PLAIN_TEXT",
                "address.placeId: PLAIN_TEXT",
                "address.coordinates: PLAIN_TEXT",
                "amounts: CURRENCY_CONVERSION",
                "status: STATE_MAPPING",
                "timestamps: DATE_PARSING"
            )
            
            val businessLogicApplied = listOf(
                "notes_decryption",
                "field_validation",
                "address_plain_mapping",
                "tip_calculation",
                "status_mapping",
                "timestamp_conversion"
            )

            Log.d("DeliveryMapper", "=== DELIVERY MAPPING TRANSFORMATION ===")
            Log.d("DeliveryMapper", "Input DTO State: $inputState")
            Log.d("DeliveryMapper", "Output Domain State: $outputState")
            Log.d("DeliveryMapper", "Field Transformations: $fieldTransformations")
            Log.d("DeliveryMapper", "Business Logic Applied: $businessLogicApplied")
            Log.d("DeliveryMapper", "PII Fields Processed: $piiFieldsProcessed")
            Log.d("DeliveryMapper", "Mapping Duration: ${mappingDuration.inWholeMilliseconds}ms")
            
            // 🔍 SESSION CORRELATION: Add mapping success to session
            ClarityArchitectureMonitor.addSessionEvent("delivery_mapping_success:$dtoId")

            // 🚨 COMPREHENSIVE MAPPING MONITORING (always monitor for complete visibility)
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "DeliveryMapper",
                entityType = "Delivery",
                duration = mappingDuration,
                success = true,
                piiFieldsProcessed = piiFieldsProcessed,
                validationErrors = validationErrors,
                fieldMismatches = fieldMismatches,
                entityId = dtoId,
                userId = dataDto.userId,
                dataSize = dataDto.toString().length,
                encryptionTime = mappingDuration,
                fieldsTransformed = fieldTransformations.size,
                businessLogicApplied = businessLogicApplied,
                cacheUpdated = false
            )

            Result.Success(domainDelivery)
        } catch (e: RepositoryException.CryptographicError) {
            // 🔍 SESSION CORRELATION: Add mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("delivery_mapping_failure:$dtoId:crypto_error")
            
            // Always monitor cryptographic errors
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "DeliveryMapper",
                entityType = "Delivery",
                duration = mappingStartTime.elapsedNow(),
                success = false,
                piiFieldsProcessed = piiFieldsProcessed,
                validationErrors = validationErrors + "Cryptographic error: ${e.message}",
                fieldMismatches = fieldMismatches,
                error = e,
                entityId = dtoId,
                userId = dataDto.userId,
                dataSize = dataDto.toString().length,
                businessLogicApplied = listOf("pii_decryption_failed")
            )
            Result.Error(e)
        } catch (e: Exception) {
            // 🔍 SESSION CORRELATION: Add mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("delivery_mapping_failure:$dtoId:exception")
            
            // Always monitor mapping errors
            ClarityArchitectureMonitor.monitorDtoToSsotMapping(
                mapperClass = "DeliveryMapper",
                entityType = "Delivery",
                duration = mappingStartTime.elapsedNow(),
                success = false,
                piiFieldsProcessed = piiFieldsProcessed,
                validationErrors = validationErrors + "Mapping error: ${e.message}",
                fieldMismatches = fieldMismatches,
                error = e,
                entityId = dtoId,
                userId = dataDto.userId,
                dataSize = dataDto.toString().length,
                businessLogicApplied = listOf("mapping_failed")
            )
            Result.Error(Exception("Error mapping DeliveryDTO (ID: $dtoId) to domain: ${e.message}", e))
        }
    }



    suspend fun mapToDtoData(ssot: Delivery): Result<DeliveryDto.DeliveryData> {
        val mappingStartTime = TimeSource.Monotonic.markNow()
        var encryptedFieldsCount = 0
        var validationErrors = mutableListOf<String>()
        
        return try {
            Log.d(TAG, "CONVERSION: Delivery SSoT -> DTO | ID: ${ssot.id} | User: ${ssot.details.userId}")
            Log.d(TAG, "  Input: orderId=${ssot.details.orderId} | status=${ssot.details.status?.state} | tipAmount=${ssot.details.amounts?.tipAmount}")

            // 🔧 CRITICAL: Validate cloud function requirements early
            if (ssot.details.amounts?.tipAmount == null) {
                Log.w(TAG, "  ⚠️ CLOUD FUNCTION WARNING: Delivery ${ssot.id} missing tipAmount - will be skipped for tip counting")
            }
            if (ssot.details.times?.completedAt == null) {
                Log.w(TAG, "  ⚠️ CLOUD FUNCTION WARNING: Delivery ${ssot.id} missing completedAt - may have sorting issues")
            }
            
            val plainAddress = ssot.details.address?.let { address ->
                // 🔧 CRITICAL: Validate address has required fields for cloud function compatibility
                if (address.id.isBlank()) {
                    throw IllegalArgumentException("Address ID cannot be blank - delivery ${ssot.id} violates data integrity rule: NO DELIVERY WITHOUT ADDRESS")
                }
                if (address.fullAddress.isNullOrBlank()) {
                    throw IllegalArgumentException("Address fullAddress cannot be blank - delivery ${ssot.id} violates data integrity rule")
                }

                Log.d(TAG, "  Converting address: ${address.fullAddress.take(50)}... (ID: ${address.id})")
                DeliveryDto.SimpleAddress(
                    id = address.id,
                    fullAddress = address.fullAddress,
                    latitude = address.latitude ?: 0.0,
                    longitude = address.longitude ?: 0.0,
                    placeId = address.placeId
                )
            } ?: throw IllegalArgumentException("Address cannot be null - delivery ${ssot.id} violates core data integrity rule: NO DELIVERY WITHOUT ADDRESS")

            val itemsDto = ssot.details.items?.map {
                DeliveryDto.Item()
            }

            val details = ssot.details
            val now = OffsetDateTime.now()
            val nowString = now.toString() // Jackson-compatible string timestamp

            val deliveryDataDto = DeliveryDto.DeliveryData(
                userId = details.userId,
                orderId = details.orderId,
                notes = encryptField(details.notes, "notes"),
                address = plainAddress, // ✅ FIX: No longer throws since we provide fallback above
                status = details.status?.let { status ->
                    StatusDto(
                        state = status.state,
                        isTipped = status.isTipped,
                        isCompleted = status.isCompleted,
                        isVerified = status.isVerified,
                        cancellationReason = status.cancellationReason,
                        verificationSource = status.verificationSource,
                        verificationTimestamp = status.verificationTimestamp,
                        doNotDeliver = status.doNotDeliver,
                        dndReason = status.dndReason
                    )
                } ?: StatusDto(
                    // ✅ CRITICAL FIX: Create proper default status - deliveries start as incomplete
                    state = "CREATED",
                    isTipped = false,
                    isCompleted = false, // ✅ FIX: Deliveries start incomplete, must be explicitly completed
                    isVerified = false,
                    doNotDeliver = false
                ),
                times = details.times?.let { times ->
                    TimesDto(
                        acceptedAt = times.acceptedAt,
                        completedAt = times.completedAt,
                        tippedAt = times.tippedAt
                    )
                } ?: TimesDto(
                    // ✅ Create default times if null - timestamps can be null for times (different from metadata)
                    acceptedAt = null,
                    completedAt = null,
                    tippedAt = null
                ),
                amounts = details.amounts?.let { amounts ->
                    // ✅ CRITICAL FIX: Preserve null tipAmount for pending deliveries
                    val tipAmt: Double? = if (details.status?.isCompleted == true) amounts.tipAmount else null
                    AmountsDto(
                        basePay = amounts.basePay,
                        tipAmount = tipAmt,
                        tipPercentage = amounts.tipPercentage,
                        totalAmount = amounts.totalAmount,
                        currencyCode = amounts.currencyCode,
                        estimatedPay = amounts.estimatedPay,
                        finalPay = amounts.finalPay,
                        distanceMiles = amounts.distanceMiles
                    )
                } ?: run {
                    // ✅ CRITICAL FIX: Create default amounts with null tipAmount for pending deliveries
                    val tipAmt: Double? = if (details.status?.isCompleted == true) 0.0 else null
                    AmountsDto(
                        basePay = 0.0,
                        tipAmount = tipAmt,
                        tipPercentage = 0.0,
                        totalAmount = 0.0,
                        currencyCode = "USD"
                    )
                },
                reference = details.reference?.let { reference ->
                    // 🔧 CRITICAL: Ensure addressId is always populated for cloud function queries
                    val addressId = reference.addressId?.takeIf { it.isNotBlank() } ?: plainAddress.id
                    if (addressId.isBlank()) {
                        throw IllegalArgumentException("Reference addressId cannot be blank - delivery ${ssot.id} violates data integrity rule: CLOUD FUNCTION REQUIRES deliveryData.reference.addressId")
                    }

                    Log.d(TAG, "  Reference addressId: $addressId (${if (reference.addressId.isNullOrBlank()) "auto-populated from address.id" else "from existing reference"})")

                    ReferenceDto(
                        addressId = addressId,
                        orderId = reference.orderId ?: details.orderId,
                        externalId = reference.externalId,
                        platformOrderId = reference.platformOrderId
                    )
                } ?: run {
                    // 🔧 CRITICAL: Create reference if completely missing - cloud function requires this field
                    if (plainAddress.id.isBlank()) {
                        throw IllegalArgumentException("Cannot create reference with blank addressId - delivery ${ssot.id} violates data integrity rule: CLOUD FUNCTION REQUIRES deliveryData.reference.addressId")
                    }

                    Log.d(TAG, "  Created missing reference with addressId: ${plainAddress.id}")

                    ReferenceDto(
                        addressId = plainAddress.id,
                        orderId = details.orderId,
                        externalId = null,
                        platformOrderId = null
                    )
                },
                platform = details.platform?.let { platform ->
                    PlatformDto(
                        name = platform.name,
                        iconUrl = platform.iconUrl
                    )
                } ?: PlatformDto(
                    // ✅ Create default platform if null
                    name = "Shipt" // Default platform
                ),
                items = itemsDto,
                metadata = details.metadata?.let { metadata ->
                    MetadataDto(
                        createdAt = metadata.createdAt,
                        updatedAt = metadata.updatedAt,
                        importedAt = metadata.importedAt,
                        source = metadata.source,
                        importId = metadata.importId,
                        captureId = metadata.captureId,
                        version = metadata.version
                    )
                } ?: MetadataDto(
                    // ✅ CRITICAL FIX: Set proper timestamps - null timestamps break Firestore queries and tip tracking
                    createdAt = OffsetDateTime.now(),
                    updatedAt = OffsetDateTime.now(),
                    importedAt = null,
                    source = "manual",
                    importId = null,
                    captureId = null,
                    version = 1L
                )
            )
            
            // Validate critical fields
            if (details.userId.isBlank()) {
                validationErrors.add("userId is null or blank")
            }
            if (details.orderId.isBlank()) {
                validationErrors.add("orderId is null or blank")
            }
            if (false) {
                validationErrors.add("address is null")
            }
            
            val mappingDuration = mappingStartTime.elapsedNow()
            
            // 📊 COMPREHENSIVE BEFORE/AFTER STATE LOGGING
            val inputState = mapOf(
                "ssot_id" to ssot.id,
                "ssot_userId" to details.userId,
                "ssot_orderId" to details.orderId,
                "ssot_tipAmount" to details.amounts?.tipAmount,
                "ssot_status" to details.status?.state,
                "ssot_notes" to (if (details.notes.isNullOrEmpty()) "empty" else "has_content"),
                "ssot_addressId" to details.address.id
            )
            
            val outputState = mapOf(
                "dto_userId" to deliveryDataDto.userId,
                "dto_orderId" to deliveryDataDto.orderId,
                "dto_tipAmount" to deliveryDataDto.amounts?.tipAmount,
                "dto_status" to deliveryDataDto.status?.state,
                "dto_notes" to (if (deliveryDataDto.notes.isNullOrEmpty()) "empty" else "encrypted"),
                "dto_addressId" to deliveryDataDto.address.id
            )
            
            val fieldTransformations = listOf(
                "notes: ENCRYPT",
                "address.fullAddress: PLAIN_TEXT",
                "address.placeId: PLAIN_TEXT",
                "address.coordinates: PLAIN_TEXT",
                "amounts: CURRENCY_CONVERSION",
                "status: STATE_MAPPING",
                "times: TIMESTAMP_MAPPING"
            )
            
            val businessLogicApplied = listOf(
                "notes_encryption",
                "field_validation",
                "address_plain_mapping",
                "status_mapping",
                "amounts_calculation"
            )

            Log.d(TAG, "=== DELIVERY SSoT -> DTO TRANSFORMATION ===")
            Log.d(TAG, "Input SSoT State: $inputState")
            Log.d(TAG, "Output DTO State: $outputState")
            Log.d(TAG, "Field Transformations: $fieldTransformations")
            Log.d(TAG, "Business Logic Applied: $businessLogicApplied")
            Log.d(TAG, "PII Fields Encrypted: $encryptedFieldsCount")
            Log.d(TAG, "Mapping Duration: ${mappingDuration.inWholeMilliseconds}ms")
            
            // 🔍 SESSION CORRELATION: Add SSoT->DTO mapping success to session
            ClarityArchitectureMonitor.addSessionEvent("delivery_ssot_to_dto_success:${ssot.id}")

            // ENHANCED: Monitor successful SSoT to DTO conversion
            ClarityArchitectureMonitor.monitorSsotToDtoMapping(
                mapperClass = "DeliveryMapper",
                entityType = "Delivery",
                duration = mappingDuration,
                success = true,
                piiFieldsProcessed = encryptedFieldsCount,
                validationErrors = validationErrors,
                fieldMismatches = emptyList(),
                entityId = ssot.id,
                userId = details.userId,
                dataSize = deliveryDataDto.toString().length
            )
            
            Log.d(TAG, "CONVERSION SUCCESS: Delivery SSoT -> DTO | ID: ${ssot.id}")
            Log.d(TAG, "  Output: encrypted_fields=$encryptedFieldsCount | validation_errors=${validationErrors.size} | duration=${mappingDuration.inWholeMilliseconds}ms")
            Log.d(TAG, "  DTO size: ${deliveryDataDto.toString().length} bytes")
            Log.d(TAG, "  Business logic: address_mapping, status_mapping, amounts_calculation")
            
            Result.Success(deliveryDataDto)
        } catch (e: RepositoryException.CryptographicError) {
            val mappingDuration = mappingStartTime.elapsedNow()
            
            // 🔍 SESSION CORRELATION: Add SSoT->DTO mapping failure to session
            ClarityArchitectureMonitor.addSessionEvent("delivery_ssot_to_dto_failure:${ssot.id}:crypto_error")
            
            // ENHANCED: Monitor cryptographic error
            ClarityArchitectureMonitor.monitorSsotToDtoMapping(
                mapperClass = "DeliveryMapper",
                entityType = "Delivery",
                duration = mappingDuration,
                success = false,
                piiFieldsProcessed = encryptedFieldsCount,
                validationErrors = validationErrors + "Cryptographic error: ${e.message}",
                fieldMismatches = emptyList(),
                error = e,
                entityId = ssot.id,
                userId = ssot.details.userId,
                dataSize = 0
            )
            
            Log.e(TAG, "CONVERSION FAILED: Delivery SSoT -> DTO | ID: ${ssot.id} | Cryptographic error", e)
            Log.e(TAG, "  Encrypted fields processed: $encryptedFieldsCount")
            Log.e(TAG, "  Duration: ${mappingDuration.inWholeMilliseconds}ms")
            
            Result.Error(e)
        } catch (e: Exception) {
            val mappingDuration = mappingStartTime.elapsedNow()
            
            // ENHANCED: Monitor general mapping error
            ClarityArchitectureMonitor.monitorSsotToDtoMapping(
                mapperClass = "DeliveryMapper",
                entityType = "Delivery",
                duration = mappingDuration,
                success = false,
                piiFieldsProcessed = encryptedFieldsCount,
                validationErrors = validationErrors + "Mapping error: ${e.message}",
                fieldMismatches = emptyList(),
                error = e,
                entityId = ssot.id,
                userId = ssot.details.userId,
                dataSize = 0
            )
            
            Log.e(TAG, "CONVERSION FAILED: Delivery SSoT -> DTO | ID: ${ssot.id} | Mapping error", e)
            Log.e(TAG, "  Encrypted fields processed: $encryptedFieldsCount")
            Log.e(TAG, "  Validation errors: $validationErrors")
            Log.e(TAG, "  Duration: ${mappingDuration.inWholeMilliseconds}ms")
            
            Result.Error(Exception("Error mapping Delivery SSoT (ID: ${ssot.id}) to DTO: ${e.message}", e))
        }
    }

    suspend fun mapToDto(ssot: Delivery): Result<DeliveryDto> {
        if (ssot.id.isBlank()) {
            return Result.Error(IllegalArgumentException("SSoT Delivery must have an ID to be mapped to a full DTO"))
        }
        return when (val deliveryDataResult = mapToDtoData(ssot)) {
            is Result.Success -> Result.Success(DeliveryDto(id = ssot.id, deliveryData = deliveryDataResult.data))
            is Result.Error -> Result.Error(deliveryDataResult.exception)
            is Result.Loading -> Result.Error(Exception("mapToDtoData returned Loading state unexpectedly"))
        }
    }

    // ===== BUSINESS LOGIC FUNCTIONS =====
    // Moved from DtoUtils.kt to follow architectural pattern where mappers contain domain business logic



    /**
     * Calculate tip percentage from order amount and tip amount
     */
    fun calculateTipPercentage(orderAmount: Double?, tipAmount: Double?): Double? {
        return if (orderAmount != null && tipAmount != null && orderAmount > 0) {
            (tipAmount / orderAmount) * 100
        } else {
            null
        }
    }

    /**
     * Round a double value to 2 decimal places
     */
    fun roundTo2DecimalPlaces(value: Double): Double {
        return kotlin.math.round(value * 100.0) / 100.0
    }

    /**
     * ✅ DEPRECATED: Client-side DND evaluation violates SSOT principles
     *
     * This method has been converted to a no-op to prevent SSOT violations.
     * DND evaluation is now handled exclusively by address-stats-updater.ts cloud function.
     *
     * @param status The current status object.
     * @param deliveryData The delivery data to check against rules.
     * @param userProfile The user profile containing DND rules.
     * @return The unchanged status object (DND evaluation delegated to cloud).
     *
     * @deprecated Use cloud function triggering instead of client-side DND evaluation
     */
    @Deprecated(
        message = "Client-side DND evaluation violates SSOT principles. Use cloud function triggering instead.",
        replaceWith = ReplaceWith("status // Cloud function handles DND evaluation"),
        level = DeprecationLevel.WARNING
    )
    fun applyDoNotDeliverRules(
        status: StatusDto,
        deliveryData: DeliveryDto.DeliveryData,
        userProfile: User_profile?
    ): StatusDto {
        // ✅ SSOT COMPLIANCE: Return status unchanged
        // Cloud function (address-stats-updater.ts) will handle DND evaluation
        Log.d(TAG, "DND evaluation delegated to cloud function for delivery ${deliveryData.orderId}")
        return status // No client-side modification
    }

    /**
     * Create a default delivery for a user
     */
    fun createDefaultDelivery(userId: String): Delivery {
        val now = OffsetDateTime.now()

        val defaultDetails = DeliveryDetails(
            userId = userId,
            orderId = "",
            notes = null,
            address = SimpleAddress(
                id = "",
                fullAddress = "",
                latitude = null,
                longitude = null,
                placeId = null
            ),
            status = StatusDto(
                state = "CREATED",
                isCompleted = false,
                isTipped = false,
                isVerified = false,
                doNotDeliver = false
            ),
            times = TimesDto(
                acceptedAt = null, // Keep null to avoid Jackson serialization issues
                completedAt = null, // Keep null to avoid Jackson serialization issues
                tippedAt = null
            ),
            amounts = AmountsDto(
                basePay = 0.0,
                tipAmount = null, // ✅ CRITICAL FIX: Default deliveries are pending, so tipAmount should be null
                tipPercentage = 0.0,
                totalAmount = 0.0,
                currencyCode = "USD"
            ),
            reference = null,
            platform = null,
            items = null,
            metadata = MetadataDto(
                // ✅ CRITICAL FIX: Set proper timestamps - null timestamps break Firestore queries and tip tracking
                createdAt = OffsetDateTime.now(),
                updatedAt = OffsetDateTime.now(),
                importedAt = null,
                source = "manual",
                importId = null,
                captureId = null,
                version = 1L
            )
        )

        return Delivery(
            id = "",
            details = defaultDetails
        )
    }

    /**
     * Ensure a delivery has a valid reference object
     */
    fun ensureValidReference(delivery: Delivery): Delivery {
        val deliveryData = delivery.details

        if (deliveryData.reference != null) {
            return delivery // Already has a reference
        }

        // Create a new reference with available data
        val newReference = ReferenceDto(
            addressId = deliveryData.address?.id,
            orderId = deliveryData.orderId,
            externalId = null
        )

        val updatedDetails = deliveryData.copy(reference = newReference)
        return delivery.copy(details = updatedDetails)
    }

    /**
     * Convert Date to OffsetDateTime
     */
    fun dateToOffsetDateTime(date: Date?): OffsetDateTime? {
        return date?.toInstant()?.atOffset(java.time.ZoneOffset.UTC)
    }

    /**
     * Convert OffsetDateTime to Date
     */
    fun offsetDateTimeToDate(offsetDateTime: OffsetDateTime?): Date? {
        return offsetDateTime?.let { Date.from(it.toInstant()) }
    }

    /**
     * Transform user usage stats to delivery stats format
     */
    fun transformUsageStatsToDeliveryStats(usageStats: User_profile.UserUsageStats?): Delivery_stats {
        if (usageStats == null) {
            return createDefaultDeliveryStatsLocal()
        }

        return Delivery_stats(
            deliveryCount = usageStats.deliveryCount ?: 0L,
            tipCount = usageStats.totalTips?.toLong() ?: 0L,
            totalTips = usageStats.totalTips ?: 0.0,
            averageTipAmount = 0.0, // Not available in UserUsageStats
            highestTip = 0.0, // Not available in UserUsageStats
            pendingCount = 0L, // Not available in UserUsageStats
            averageTimeMinutes = 0.0, // Not available in UserUsageStats
            lastDeliveryDate = usageStats.lastUsageDate,
            lastDeliveryTimestamp = usageStats.lastUsageDate
        )
    }

    /**
     * Transform user usage stats to delivery stats format (overload with user profile)
     */
    fun transformUsageStatsToDeliveryStats(userProfile: User_profile?): Delivery_stats {
        return transformUsageStatsToDeliveryStats(userProfile?.usageStats)
    }

    /**
     * Convert DTO Delivery_stats to domain DeliveryStats
     */
    fun mapDeliveryStatsToDomain(dto: Delivery_stats, period: StatisticsPeriod = StatisticsPeriod.ALL_TIME): DeliveryStats {
        return DeliveryStats(
            deliveryCount = dto.deliveryCount ?: 0L,
            tipCount = dto.tipCount ?: 0L,
            totalTips = dto.totalTips ?: 0.0,
            averageTipAmount = dto.averageTipAmount ?: 0.0,
            highestTip = dto.highestTip ?: 0.0,
            pendingCount = dto.pendingCount ?: 0L,
            averageTimeMinutes = dto.averageTimeMinutes ?: 0.0,
            lastDeliveryDate = dto.lastDeliveryDate,
            period = period
        )
    }



    /**
     * Create default delivery stats for local use
     */
    fun createDefaultDeliveryStatsLocal(): Delivery_stats {
        return Delivery_stats(
            deliveryCount = 0L,
            tipCount = 0L,
            totalTips = 0.0,
            averageTipAmount = 0.0,
            highestTip = 0.0,
            pendingCount = 0L,
            averageTimeMinutes = 0.0,
            lastDeliveryDate = null,
            lastDeliveryTimestamp = null
        )
    }

    /**
     * Calculates tip percentage based on order amount and tip amount.
     * Moved from DtoUtils.kt to follow architectural pattern where mappers contain domain business logic.
     *
     * @param orderAmount The original order amount.
     * @param tipAmount The tip amount.
     * @return The calculated tip percentage, or 0 if orderAmount is 0 or null.
     */
    fun calculateTipPercentageFromAmounts(orderAmount: Double?, tipAmount: Double?): Double {
        val order = orderAmount ?: 0.0
        val tip = tipAmount ?: 0.0

        return if (order > 0) {
            kotlin.math.round((tip / order) * 100 * 100.0) / 100.0
        } else {
            0.0
        }
    }

    /**
     * Creates a new Delivery DTO instance with default values.
     * Moved from DtoUtils.kt to follow architectural pattern where mappers contain domain business logic.
     *
     * @param userId The user ID to associate with the delivery.
     * @return A new Delivery DTO instance with default values set.
     */
    fun createDefaultDeliveryDto(userId: String): DeliveryDto {
        val now = OffsetDateTime.now()
        return DeliveryDto(
            id = "", // Required id parameter - will be set when saved to Firestore
            deliveryData = DeliveryDto.DeliveryData(
                userId = userId,
                orderId = "",  // Required parameter, can't be null
                address = DeliveryDto.SimpleAddress(
                    id = "",  // Required parameter
                    fullAddress = "",  // Required parameter
                    latitude = 0.0,  // Required parameter
                    longitude = 0.0,  // Required parameter
                    placeId = null
                ),
                reference = ReferenceDto(
                    addressId = null,
                    orderId = null,
                    externalId = null
                ),
                platform = PlatformDto(name = null),
                amounts = AmountsDto(
                    basePay = 0.0,
                    tipAmount = null, // ✅ CRITICAL FIX: Default deliveries are pending, so tipAmount should be null
                    tipPercentage = 0.0,
                    totalAmount = 0.0,
                    currencyCode = "USD"
                ),
                times = TimesDto(
                    acceptedAt = null,
                    completedAt = null,
                    tippedAt = null
                ),
                status = StatusDto(
                    state = "CREATED",
                    isCompleted = false,
                    isTipped = false,
                    isVerified = false,
                    doNotDeliver = false
                ),
                metadata = MetadataDto(
                    // ✅ CRITICAL FIX: Set proper timestamps - null timestamps break Firestore queries and tip tracking
                    createdAt = OffsetDateTime.now(),
                    updatedAt = OffsetDateTime.now(),
                    importedAt = null,
                    source = "manual",
                    importId = null,
                    captureId = null,
                    version = 1L
                )
            )
        )
    }

    /**
     * Transforms User_profile.UserUsageStats into a Delivery_stats object.
     * Moved from DtoUtils.kt to follow architectural pattern where mappers contain domain business logic.
     *
     * @param usageStats The UserUsageStats from a User_profile.
     * @return A Delivery_stats object, or null if usageStats is null.
     */
    fun transformUsageStatsToDeliveryStatsFromStats(usageStats: User_profile.UserUsageStats?): Delivery_stats? {
        if (usageStats == null) {
            return null
        }

        return Delivery_stats(
            deliveryCount = usageStats.deliveryCount,
            lastDeliveryDate = usageStats.lastUsageDate,
            tipCount = null,
            totalTips = null,
            averageTipAmount = null,
            highestTip = null,
            pendingCount = null,
            averageTimeMinutes = null,
            lastDeliveryTimestamp = usageStats.lastUsageDate
        )
    }

    /**
     * Transforms a User_profile object into a map of Delivery_stats objects.
     * Moved from DtoUtils.kt to follow architectural pattern where mappers contain domain business logic.
     *
     * @param userProfile The User_profile object containing usage statistics
     * @return A map of time period keys to Delivery_stats objects
     */
    fun transformUsageStatsToDeliveryStatsMap(userProfile: User_profile): Map<String, Delivery_stats> {
        val result = mutableMapOf<String, Delivery_stats>()

        val usageStats = userProfile.usageStats ?: return result

        val baseStats = createDefaultDeliveryStatsLocal()

        val allTimeStats = baseStats.copy(
            deliveryCount = usageStats.deliveryCount ?: 0,
            totalTips = usageStats.totalTips ?: 0.0,
            averageTipAmount = if ((usageStats.deliveryCount ?: 0) > 0 && (usageStats.totalTips ?: 0.0) > 0) {
                (usageStats.totalTips ?: 0.0) / (usageStats.deliveryCount ?: 1)
            } else {
                0.0
            },
            lastDeliveryDate = usageStats.lastUsageDate // Keep as-is, don't default to now()
        )

        result["all_time"] = allTimeStats
        return result
    }

    /**
     * Ensures that a delivery object has a valid reference object.
     * Moved from DtoUtils.kt to follow architectural pattern where mappers contain domain business logic.
     *
     * @param delivery The delivery DTO to validate and update.
     * @return The delivery DTO with a valid reference object.
     */
    fun ensureValidReferenceDto(delivery: DeliveryDto): DeliveryDto {
        val deliveryData = delivery.deliveryData

        if (deliveryData.reference != null) {
            return delivery
        }

        val newReference = ReferenceDto(
            addressId = null,
            orderId = null,
            externalId = null
        )

        val updatedDeliveryData = deliveryData.copy(reference = newReference)

        return delivery.copy(deliveryData = updatedDeliveryData)
    }

    // ===== DATE UTILITY FUNCTIONS =====
    // Moved from DeliveryRepositoryImpl.kt to follow architectural pattern where mappers contain domain business logic

    /**
     * Gets the start of day for a given date.
     * Moved from DeliveryRepositoryImpl.kt DateUtils object.
     *
     * @param date The date to get start of day for.
     * @return The start of day date.
     */
    fun getStartOfDay(date: Date): Date {
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.time
    }

    /**
     * Gets the end of day for a given date.
     * Moved from DeliveryRepositoryImpl.kt DateUtils object.
     *
     * @param date The date to get end of day for.
     * @return The end of day date.
     */
    fun getEndOfDay(date: Date): Date {
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        return calendar.time
    }

    /**
     * Gets the start and end of day for a given date.
     * Moved from DeliveryRepositoryImpl.kt DateUtils object.
     *
     * @param date The date to get start and end of day for.
     * @return A pair of start and end of day dates.
     */
    fun getStartAndEndOfDayLocal(date: Date): Pair<Date, Date> {
        return Pair(getStartOfDay(date), getEndOfDay(date))
    }

    /**
     * Get date range for last week (7 days back from now)
     * Moved from DeliveryRepositoryImpl.kt to follow architectural pattern where mappers contain business logic
     */
    fun getLastWeekDateRange(): Pair<Date, Date> {
        val calendar = Calendar.getInstance()
        val endDate = calendar.time // Now
        calendar.add(Calendar.DAY_OF_YEAR, -7) // 7 days back
        val startDate = calendar.time
        return Pair(startDate, endDate)
    }

    /**
     * Get date range for last month (1 month back from now)
     * Moved from DeliveryRepositoryImpl.kt to follow architectural pattern where mappers contain business logic
     */
    fun getLastMonthDateRange(): Pair<Date, Date> {
        val calendar = Calendar.getInstance()
        val endDate = calendar.time // Now
        calendar.add(Calendar.MONTH, -1) // 1 month back
        val startDate = calendar.time
        return Pair(startDate, endDate)
    }

    // ===== STATS CALCULATION FUNCTIONS =====
    // Moved from DeliveryRepositoryImpl.kt to follow architectural pattern where mappers contain domain business logic











    // ===== TIP PROCESSING BUSINESS LOGIC =====
    // Following architectural pattern where mappers contain domain business logic

    /**
     * Process tip information and create updated delivery with tip data
     * Business logic for tip processing, validation, and delivery updates
     */
    fun processDeliveryTip(
        existingDelivery: Delivery?,
        userId: String,
        orderId: String,
        tipAmount: Double,
        timestamp: OffsetDateTime
    ): Delivery {
        return if (existingDelivery != null) {
            // Update existing delivery with tip
            updateDeliveryWithTipData(existingDelivery, tipAmount, timestamp)
        } else {
            // Create new delivery with tip information
            createDeliveryWithTipData(userId, orderId, tipAmount, timestamp)
        }
    }

    /**
     * Update an existing delivery with tip information
     * Business logic for merging tip data into existing delivery
     */
    private fun updateDeliveryWithTipData(
        delivery: Delivery,
        tipAmount: Double,
        timestamp: OffsetDateTime
    ): Delivery {
        val currentDetails = delivery.details
        val now = OffsetDateTime.now()

        // Update amounts with tip calculation
        val basePay = currentDetails.amounts?.basePay ?: 0.0
        val updatedAmounts = AmountsDto(
            basePay = basePay,
            tipAmount = tipAmount,
            tipPercentage = calculateTipPercentage(basePay, tipAmount),
            totalAmount = basePay + tipAmount,
            currencyCode = currentDetails.amounts?.currencyCode ?: "USD",
            estimatedPay = currentDetails.amounts?.estimatedPay,
            finalPay = basePay + tipAmount,
            distanceMiles = currentDetails.amounts?.distanceMiles
        )

        // Update times
        val updatedTimes = TimesDto(
            acceptedAt = currentDetails.times?.acceptedAt,
            completedAt = currentDetails.times?.completedAt,
            tippedAt = timestamp
        )

        // Update status
        val updatedStatus = StatusDto(
            state = currentDetails.status?.state ?: "tip_received",
            isTipped = true,
            isCompleted = currentDetails.status?.isCompleted == true,
            isVerified = currentDetails.status?.isVerified == true,
            cancellationReason = currentDetails.status?.cancellationReason,
            verificationSource = currentDetails.status?.verificationSource,
            verificationTimestamp = currentDetails.status?.verificationTimestamp,
            doNotDeliver = currentDetails.status?.doNotDeliver == true,
            dndReason = currentDetails.status?.dndReason
        )

        // Update metadata
        val updatedMetadata = MetadataDto(
            createdAt = currentDetails.metadata?.createdAt ?: now,
            updatedAt = now,
            importedAt = currentDetails.metadata?.importedAt,
            source = currentDetails.metadata?.source ?: "tip_notification",
            importId = currentDetails.metadata?.importId,
            captureId = currentDetails.metadata?.captureId,
            version = (currentDetails.metadata?.version ?: 0L) + 1L
        )

        // Create updated delivery details
        val updatedDetails = currentDetails.copy(
            amounts = updatedAmounts,
            times = updatedTimes,
            status = updatedStatus,
            metadata = updatedMetadata
        )

        return delivery.copy(details = updatedDetails)
    }

    /**
     * Create a new delivery with tip information
     * Business logic for creating delivery from tip notification
     */
    private fun createDeliveryWithTipData(
        userId: String,
        orderId: String,
        tipAmount: Double,
        timestamp: OffsetDateTime
    ): Delivery {
        val now = OffsetDateTime.now()

        // Create amounts
        val amounts = AmountsDto(
            basePay = 0.0, // Will be updated when full delivery info is available
            tipAmount = tipAmount,
            tipPercentage = null, // Cannot calculate without base pay
            totalAmount = tipAmount,
            currencyCode = "USD",
            estimatedPay = null,
            finalPay = tipAmount,
            distanceMiles = null
        )

        // Create times
        val times = TimesDto(
            acceptedAt = null,
            completedAt = null,
            tippedAt = timestamp
        )

        // Create status
        val status = StatusDto(
            state = "tip_received",
            isTipped = true,
            isCompleted = false,
            isVerified = false,
            cancellationReason = null,
            verificationSource = null,
            verificationTimestamp = null,
            doNotDeliver = false,
            dndReason = null
        )

        // Create reference
        val reference = ReferenceDto(
            addressId = null,
            orderId = orderId,
            externalId = null
        )

        // Create platform
        val platform = PlatformDto(
            name = "Shipt", // Default platform
            iconUrl = null
        )

        // Create metadata
        val metadata = MetadataDto(
            createdAt = now,
            updatedAt = now,
            importedAt = null,
            source = "tip_notification",
            importId = null,
            captureId = null,
            version = 1L
        )

        // Create default address (will be updated when address info is available)
        val defaultAddress = SimpleAddress(
            id = "",
            fullAddress = "Unknown Address",
            latitude = null,
            longitude = null,
            placeId = null
        )

        // Create delivery details
        val details = DeliveryDetails(
            userId = userId,
            orderId = orderId,
            notes = null,
            address = defaultAddress,
            status = status,
            times = times,
            amounts = amounts,
            reference = reference,
            platform = platform,
            items = null,
            metadata = metadata
        )

        return Delivery(
            id = "", // Will be set by repository when saved
            details = details
        )
    }

    /**
     * Validate tip amount according to business rules
     */
    fun validateTipAmount(amount: Double): Boolean {
        return amount >= 0 && amount < 1000.0 && amount.isFinite()
    }

    /**
     * Check if tip processing should be allowed for a delivery
     * Business rules for tip duplication, validation, etc.
     */
    fun shouldProcessTip(
        existingDelivery: Delivery?,
        newTipAmount: Double
    ): Pair<Boolean, String?> {
        // Basic validation
        if (!validateTipAmount(newTipAmount)) {
            return false to "Invalid tip amount: $$newTipAmount"
        }

        // Check for existing tip
        if (existingDelivery != null) {
            val currentTipAmount = existingDelivery.details.amounts?.tipAmount ?: 0.0
            if (currentTipAmount > 0 && currentTipAmount != newTipAmount) {
                // Business decision: allow tip updates
                return true to "Updating existing tip from $$currentTipAmount to $$newTipAmount"
            }
        }

        return true to null
    }

    /**
     * Comprehensive delivery validation following business rules
     * Validates a single delivery according to domain business logic
     */
    fun validateDelivery(delivery: Delivery): Result<SingleValidationResult> {
        val warnings = mutableListOf<String>()
        val errors = mutableListOf<String>()
        
        try {
            // Basic validation
            if (false) {
                errors.add("Delivery ${delivery.id} has null details")
                return Result.Success(SingleValidationResult(false, warnings, errors))
            }
            
            val details = delivery.details
            
            // User ID validation
            if (details.userId.isBlank()) {
                errors.add("Delivery ${delivery.id} has blank userId")
            }
            
            // Order ID validation
            if (details.orderId.isBlank()) {
                errors.add("Delivery ${delivery.id} has blank orderId")
            }
            
            // Address reference validation
            val addressId = details.reference?.addressId
            if (addressId.isNullOrBlank()) {
                warnings.add("Delivery ${delivery.id} missing address reference")
            }
            
            // Tip validation for completed deliveries
            if (details.status?.isCompleted == true) {
                val tipAmount = details.amounts?.tipAmount

                // ✅ CRITICAL FIX: Allow received zero-dollar tips for completed deliveries
                if (tipAmount == null) {
                    warnings.add("Completed delivery ${delivery.id} missing tip amount (should be 0.0 for received zero tips)")
                } else {
                    // Validate tip amount is reasonable (including 0.0 for received zero tips)
                    if (!validateTipAmount(tipAmount)) {
                        errors.add("Delivery ${delivery.id} has invalid tip amount: $tipAmount")
                    }
                }

                // Check completion timestamp
                val completionTime = details.times?.completedAt
                if (completionTime == null) {
                    warnings.add("Completed delivery ${delivery.id} missing completion timestamp")
                }
            }
            
            // Geocoded address validation
            if (details.address != null) {
                if (details.address.latitude == null || details.address.longitude == null) {
                    warnings.add("Delivery ${delivery.id} missing geocoded coordinates")
                }
            }
            
            return Result.Success(SingleValidationResult(
                isValid = errors.isEmpty(),
                warnings = warnings,
                errors = errors
            ))
        } catch (e: Exception) {
            return Result.Error(Exception("Error validating delivery ${delivery.id}: ${e.message}", e))
        }
    }

    /**
     * Calculates delivery statistics from a list of delivery DTOs.
     * Moved from DtoUtils.kt to follow architectural pattern where mappers contain domain business logic.
     *
     * @param deliveries The list of delivery DTOs to calculate statistics for.
     * @return The calculated statistics using the generated_kt.Delivery_stats model.
     */
    fun calculateDeliveryStatsFromDtos(deliveries: List<DeliveryDto>): Delivery_stats {
        if (deliveries.isEmpty()) {
            // Return an empty/default Delivery_stats object
            return Delivery_stats(
                deliveryCount = 0L,
                tipCount = null,
                totalTips = null,
                highestTip = null,
                averageTipAmount = null,
                pendingCount = null,
                averageTimeMinutes = null,
                lastDeliveryDate = null,
                lastDeliveryTimestamp = null
            )
        }

        var calculatedTotalTips = 0.0
        var calculatedDeliveryCount = 0L
        var calculatedTipCount = 0L
        var calculatedMaxTip: Double? = null
        var calculatedLastDeliveryDate: OffsetDateTime? = null

        for (delivery in deliveries) {
            val deliveryData = delivery.deliveryData
            calculatedDeliveryCount++

            val tip = deliveryData.amounts?.tipAmount ?: 0.0
            if (tip > 0) {
                calculatedTotalTips += tip
                calculatedTipCount++
                calculatedMaxTip = if (calculatedMaxTip == null || tip > calculatedMaxTip) tip else calculatedMaxTip
            }

            // Determine effective date for lastDeliveryDate
            // Prefer completedAt, fallback to metadata.createdAt
            val effectiveDate = deliveryData.times?.completedAt ?: deliveryData.metadata?.createdAt
            if (effectiveDate != null) {
                if (calculatedLastDeliveryDate == null || effectiveDate.isAfter(calculatedLastDeliveryDate)) {
                    calculatedLastDeliveryDate = effectiveDate
                }
            }
        }

        val calculatedAverageTipAmount = if (calculatedTipCount > 0) (calculatedTotalTips / calculatedTipCount) else null

        return Delivery_stats(
            deliveryCount = calculatedDeliveryCount,
            tipCount = if (calculatedDeliveryCount > 0) calculatedTipCount else null,
            totalTips = if (calculatedDeliveryCount > 0) kotlin.math.round(calculatedTotalTips * 100.0) / 100.0 else null,
            highestTip = calculatedMaxTip?.let { kotlin.math.round(it * 100.0) / 100.0 },
            lastDeliveryDate = calculatedLastDeliveryDate,
            averageTipAmount = calculatedAverageTipAmount?.let { kotlin.math.round(it * 100.0) / 100.0 },
            pendingCount = null,
            averageTimeMinutes = null,
            lastDeliveryTimestamp = calculatedLastDeliveryDate
        )
    }
}